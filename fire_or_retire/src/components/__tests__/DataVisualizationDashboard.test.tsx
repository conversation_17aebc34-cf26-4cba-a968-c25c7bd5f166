import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import DataVisualizationDashboard from '../DataVisualizationDashboard';

// Mock D3 to avoid DOM manipulation issues in tests
vi.mock('d3', () => ({
  select: vi.fn(() => ({
    selectAll: vi.fn(() => ({
      remove: vi.fn(),
      data: vi.fn(() => ({
        enter: vi.fn(() => ({
          append: vi.fn(() => ({
            attr: vi.fn(() => ({ attr: vi.fn() })),
            style: vi.fn(() => ({ style: vi.fn() })),
            on: vi.fn(),
          })),
        })),
      })),
    })),
    append: vi.fn(() => ({
      attr: vi.fn(() => ({ attr: vi.fn() })),
      style: vi.fn(() => ({ style: vi.fn() })),
    })),
    attr: vi.fn(() => ({ attr: vi.fn() })),
  })),
  scaleTime: vi.fn(() => ({
    domain: vi.fn(() => ({ range: vi.fn() })),
  })),
  scaleLinear: vi.fn(() => ({
    domain: vi.fn(() => ({
      nice: vi.fn(() => ({ range: vi.fn() })),
      range: vi.fn(),
    })),
  })),
  extent: vi.fn(() => [new Date('2023-01-01'), new Date('2023-12-31')]),
  line: vi.fn(() => ({
    x: vi.fn(() => ({ y: vi.fn(() => ({ curve: vi.fn() })) })),
  })),
  area: vi.fn(() => ({
    x: vi.fn(() => ({
      y0: vi.fn(() => ({
        y1: vi.fn(() => ({ curve: vi.fn() })),
      })),
    })),
  })),
  axisBottom: vi.fn(() => ({
    tickFormat: vi.fn(() => ({ ticks: vi.fn() })),
  })),
  axisLeft: vi.fn(() => ({
    tickFormat: vi.fn(() => ({ ticks: vi.fn() })),
  })),
  timeFormat: vi.fn(() => vi.fn()),
  curveMonotoneX: {},
  easeLinear: {},
}));

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
Object.defineProperty(window, 'localStorage', { value: mockLocalStorage });

// Mock useLocalStorage hook
vi.mock('../../hooks/useLocalStorage', () => ({
  useLocalStorage: vi.fn((key, defaultValue) => [defaultValue, vi.fn()]),
}));

// Mock react-i18next
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
    i18n: { changeLanguage: vi.fn() },
  }),
}));

const mockUserData = {
  currentAge: 30,
  retirementAge: 65,
  currentSavings: 50000,
  monthlyIncome: 8000,
  monthlyExpenses: 5000,
  expectedReturn: 7,
  inflationRate: 2,
  safeWithdrawalRate: 4,
};

const mockExpenses = [
  { id: 1, category: 'Housing', amount: 2000, isActive: true },
  { id: 2, category: 'Food', amount: 800, isActive: true },
];

const mockInvestments = [
  { id: 1, name: 'Stock Portfolio', amount: 30000, type: 'stocks' },
  { id: 2, name: 'Bond Portfolio', amount: 20000, type: 'bonds' },
];

const mockSavingsGoals = [
  { id: 1, name: 'Emergency Fund', targetAmount: 30000, currentAmount: 15000 },
  { id: 2, name: 'House Deposit', targetAmount: 100000, currentAmount: 25000 },
];

describe('DataVisualizationDashboard', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockLocalStorage.getItem.mockReturnValue(null);
  });

  it('renders dashboard header correctly', () => {
    render(
      <DataVisualizationDashboard
        darkMode={false}
        userData={mockUserData}
        expenses={mockExpenses}
        investments={mockInvestments}
        savingsGoals={mockSavingsGoals}
      />
    );

    expect(
      screen.getByText('📊 Data Visualization Dashboard')
    ).toBeInTheDocument();
    expect(
      screen.getByText(
        'Comprehensive financial data analysis and interactive charts'
      )
    ).toBeInTheDocument();
  });

  it('displays dashboard statistics correctly', () => {
    render(
      <DataVisualizationDashboard
        darkMode={false}
        userData={mockUserData}
        expenses={mockExpenses}
        investments={mockInvestments}
        savingsGoals={mockSavingsGoals}
      />
    );

    // Check if statistics are displayed
    expect(screen.getByText('Savings Rate')).toBeInTheDocument();
    expect(screen.getByText('FIRE Progress')).toBeInTheDocument();
    expect(screen.getByText('Expenses')).toBeInTheDocument();
    expect(screen.getByText('Investments')).toBeInTheDocument();
    expect(screen.getByText('Goals')).toBeInTheDocument();

    // Check calculated values
    expect(screen.getByText('37.5%')).toBeInTheDocument(); // Savings rate
    expect(screen.getByText('CHF 2,800')).toBeInTheDocument(); // Total expenses
    expect(screen.getByText('CHF 50,000')).toBeInTheDocument(); // Total investments
    expect(screen.getByText('CHF 130,000')).toBeInTheDocument(); // Total savings goals
  });

  it('switches between different views', async () => {
    render(
      <DataVisualizationDashboard
        darkMode={false}
        userData={mockUserData}
        expenses={mockExpenses}
        investments={mockInvestments}
        savingsGoals={mockSavingsGoals}
      />
    );

    // Check default view
    expect(screen.getByText('📈 Enhanced')).toHaveClass('bg-blue-600');

    // Switch to mobile view
    fireEvent.click(screen.getByText('📱 Mobile'));
    await waitFor(() => {
      expect(screen.getByText('📱 Mobile')).toHaveClass('bg-blue-600');
    });

    // Switch to performance view
    fireEvent.click(screen.getByText('⚡ Performance'));
    await waitFor(() => {
      expect(screen.getByText('⚡ Performance')).toHaveClass('bg-blue-600');
    });
  });

  it('toggles dashboard settings', () => {
    render(
      <DataVisualizationDashboard
        darkMode={false}
        userData={mockUserData}
        expenses={mockExpenses}
        investments={mockInvestments}
        savingsGoals={mockSavingsGoals}
      />
    );

    // Find settings checkboxes
    const performanceMonitorCheckbox =
      screen.getByLabelText(/Performance Monitor/i);
    const mobileOptimizationCheckbox =
      screen.getByLabelText(/Mobile Optimization/i);
    const animationsCheckbox = screen.getByLabelText(/Animations/i);
    const interactivityCheckbox = screen.getByLabelText(/Interactivity/i);

    // Test toggling settings
    fireEvent.click(performanceMonitorCheckbox);
    fireEvent.click(mobileOptimizationCheckbox);
    fireEvent.click(animationsCheckbox);
    fireEvent.click(interactivityCheckbox);

    // Verify checkboxes are toggled (implementation would depend on actual state management)
    expect(performanceMonitorCheckbox).toBeInTheDocument();
    expect(mobileOptimizationCheckbox).toBeInTheDocument();
    expect(animationsCheckbox).toBeInTheDocument();
    expect(interactivityCheckbox).toBeInTheDocument();
  });

  it('renders in dark mode correctly', () => {
    render(
      <DataVisualizationDashboard
        darkMode={true}
        userData={mockUserData}
        expenses={mockExpenses}
        investments={mockInvestments}
        savingsGoals={mockSavingsGoals}
      />
    );

    // Check if dark mode classes are applied
    const dashboardHeader = screen
      .getByText('📊 Data Visualization Dashboard')
      .closest('div');
    expect(dashboardHeader).toHaveClass('bg-gray-800');
  });

  it('handles empty data gracefully', () => {
    render(
      <DataVisualizationDashboard
        darkMode={false}
        userData={mockUserData}
        expenses={[]}
        investments={[]}
        savingsGoals={[]}
      />
    );

    // Should still render without errors
    expect(
      screen.getByText('📊 Data Visualization Dashboard')
    ).toBeInTheDocument();

    // Check that zero values are displayed correctly
    expect(screen.getByText('CHF 0')).toBeInTheDocument(); // Should appear multiple times for empty arrays
  });

  it('calculates financial metrics correctly', () => {
    render(
      <DataVisualizationDashboard
        darkMode={false}
        userData={mockUserData}
        expenses={mockExpenses}
        investments={mockInvestments}
        savingsGoals={mockSavingsGoals}
      />
    );

    // Verify savings rate calculation: (8000 - 5000) / 8000 * 100 = 37.5%
    expect(screen.getByText('37.5%')).toBeInTheDocument();

    // Verify FIRE progress calculation: 50000 / (5000 * 12 * 25) * 100 = 3.3%
    expect(screen.getByText('3.3%')).toBeInTheDocument();
  });

  it('displays performance issues when detected', async () => {
    render(
      <DataVisualizationDashboard
        darkMode={false}
        userData={mockUserData}
        expenses={mockExpenses}
        investments={mockInvestments}
        savingsGoals={mockSavingsGoals}
      />
    );

    // Enable performance monitor
    const performanceMonitorCheckbox =
      screen.getByLabelText(/Performance Monitor/i);
    fireEvent.click(performanceMonitorCheckbox);

    // Performance issues would be displayed if detected
    // This would require actual performance monitoring to trigger
  });
});
