import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';

interface SmartDashboardProps {
  darkMode: boolean;
  userData: {
    currentAge: number;
    retirementAge: number;
    currentSavings: number;
    monthlyIncome: number;
    monthlyExpenses: number;
    expenses: any[];
    savingsGoals: any[];
    investments: any[];
    additionalIncomes: any[];
  };
}

interface Insight {
  id: string;
  type: 'success' | 'warning' | 'info' | 'error';
  title: string;
  description: string;
  action?: {
    label: string;
    onClick: () => void;
  };
  priority: 'high' | 'medium' | 'low';
  category: 'savings' | 'expenses' | 'investments' | 'taxes' | 'goals';
}

interface Recommendation {
  id: string;
  title: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  effort: 'easy' | 'medium' | 'hard';
  category: string;
  icon: string;
}

const SmartDashboard: React.FC<SmartDashboardProps> = ({
  darkMode,
  userData,
}) => {
  const { t } = useTranslation();
  const [insights, setInsights] = useState<Insight[]>([]);
  const [recommendations, setRecommendations] = useState<Recommendation[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  useEffect(() => {
    generateInsights();
    generateRecommendations();
  }, [userData]);

  const generateInsights = () => {
    const newInsights: Insight[] = [];

    // Calculate key metrics
    const savingsRate =
      ((userData.monthlyIncome - userData.monthlyExpenses) /
        userData.monthlyIncome) *
      100;
    const emergencyFundMonths =
      userData.currentSavings / userData.monthlyExpenses;
    const yearsToRetirement = userData.retirementAge - userData.currentAge;
    const fireTarget = userData.monthlyExpenses * 12 * 25; // 4% rule
    const currentFireProgress = (userData.currentSavings / fireTarget) * 100;

    // Savings Rate Analysis
    if (savingsRate >= 50) {
      newInsights.push({
        id: 'savings-rate-excellent',
        type: 'success',
        title: 'Excellent Savings Rate! 🎉',
        description: `Your ${savingsRate.toFixed(1)}% savings rate puts you on the fast track to FIRE. You're saving more than most Swiss households.`,
        priority: 'high',
        category: 'savings',
      });
    } else if (savingsRate >= 20) {
      newInsights.push({
        id: 'savings-rate-good',
        type: 'info',
        title: 'Good Savings Rate 👍',
        description: `Your ${savingsRate.toFixed(1)}% savings rate is above average. Consider optimizing expenses to reach 30%+ for faster FIRE.`,
        priority: 'medium',
        category: 'savings',
      });
    } else {
      newInsights.push({
        id: 'savings-rate-low',
        type: 'warning',
        title: 'Low Savings Rate ⚠️',
        description: `Your ${savingsRate.toFixed(1)}% savings rate may delay FIRE goals. Swiss average is 18.7%. Focus on expense optimization.`,
        priority: 'high',
        category: 'savings',
      });
    }

    // Emergency Fund Analysis
    if (emergencyFundMonths < 3) {
      newInsights.push({
        id: 'emergency-fund-low',
        type: 'error',
        title: 'Emergency Fund Too Low! 🚨',
        description: `You have ${emergencyFundMonths.toFixed(1)} months of expenses saved. Aim for 3-6 months for financial security.`,
        priority: 'high',
        category: 'savings',
      });
    } else if (emergencyFundMonths >= 6) {
      newInsights.push({
        id: 'emergency-fund-good',
        type: 'success',
        title: 'Strong Emergency Fund 💪',
        description: `${emergencyFundMonths.toFixed(1)} months of expenses saved provides excellent financial security.`,
        priority: 'low',
        category: 'savings',
      });
    }

    // FIRE Progress Analysis
    if (currentFireProgress >= 75) {
      newInsights.push({
        id: 'fire-progress-excellent',
        type: 'success',
        title: 'Almost There! 🔥',
        description: `You're ${currentFireProgress.toFixed(1)}% of the way to FIRE. Financial independence is within reach!`,
        priority: 'high',
        category: 'goals',
      });
    } else if (currentFireProgress >= 25) {
      newInsights.push({
        id: 'fire-progress-good',
        type: 'info',
        title: 'Making Progress 📈',
        description: `${currentFireProgress.toFixed(1)}% progress toward FIRE. Stay consistent with your savings plan.`,
        priority: 'medium',
        category: 'goals',
      });
    }

    // Investment Analysis
    const totalInvestments = userData.investments.reduce(
      (sum, inv) => sum + (inv.currentValue || 0),
      0
    );
    const investmentRatio = totalInvestments / userData.currentSavings;

    if (investmentRatio < 0.5 && userData.currentSavings > 50000) {
      newInsights.push({
        id: 'investment-ratio-low',
        type: 'warning',
        title: 'Low Investment Allocation 📊',
        description: `Only ${(investmentRatio * 100).toFixed(1)}% of your savings are invested. Consider increasing allocation for growth.`,
        priority: 'medium',
        category: 'investments',
      });
    }

    // Swiss-Specific Insights
    const pillar3aGoals = userData.savingsGoals.filter(
      goal => goal.type === 'pillar_3a'
    );
    if (pillar3aGoals.length === 0) {
      newInsights.push({
        id: 'pillar-3a-missing',
        type: 'warning',
        title: 'Missing Pillar 3a Goal 🇨🇭',
        description:
          'Set up Pillar 3a savings to get tax deductions up to CHF 7,056 in 2024.',
        priority: 'high',
        category: 'taxes',
      });
    }

    setInsights(newInsights);
  };

  const generateRecommendations = () => {
    const newRecommendations: Recommendation[] = [];

    const savingsRate =
      ((userData.monthlyIncome - userData.monthlyExpenses) /
        userData.monthlyIncome) *
      100;
    const emergencyFundMonths =
      userData.currentSavings / userData.monthlyExpenses;

    // Expense Optimization
    const highExpenseCategories = userData.expenses
      .filter(exp => exp.isActive)
      .reduce(
        (acc, exp) => {
          const monthlyAmount =
            exp.frequency === 'monthly'
              ? exp.amount
              : exp.frequency === 'annually'
                ? exp.amount / 12
                : exp.amount;
          acc[exp.category] = (acc[exp.category] || 0) + monthlyAmount;
          return acc;
        },
        {} as Record<string, number>
      );

    const totalExpenses = Object.values(highExpenseCategories).reduce(
      (sum, amount) => sum + amount,
      0
    );

    Object.entries(highExpenseCategories).forEach(([category, amount]) => {
      const percentage = (amount / totalExpenses) * 100;
      if (percentage > 30) {
        newRecommendations.push({
          id: `optimize-${category}`,
          title: `Optimize ${category} expenses`,
          description: `${category} represents ${percentage.toFixed(1)}% of your budget. Look for savings opportunities.`,
          impact: 'high',
          effort: 'medium',
          category: 'expenses',
          icon: '💸',
        });
      }
    });

    // Investment Recommendations
    if (userData.currentSavings > 10000 && userData.investments.length === 0) {
      newRecommendations.push({
        id: 'start-investing',
        title: 'Start investing for growth',
        description:
          'With CHF 10k+ in savings, consider low-cost ETFs for long-term growth.',
        impact: 'high',
        effort: 'medium',
        category: 'investments',
        icon: '📈',
      });
    }

    // Swiss Tax Optimization
    newRecommendations.push({
      id: 'pillar-3a-max',
      title: 'Maximize Pillar 3a contributions',
      description:
        'Contribute CHF 7,056 annually for tax savings and retirement planning.',
      impact: 'high',
      effort: 'easy',
      category: 'taxes',
      icon: '🏛️',
    });

    // Emergency Fund
    if (emergencyFundMonths < 3) {
      newRecommendations.push({
        id: 'build-emergency-fund',
        title: 'Build emergency fund first',
        description:
          'Prioritize 3-6 months of expenses in a high-yield savings account.',
        impact: 'high',
        effort: 'easy',
        category: 'savings',
        icon: '🛡️',
      });
    }

    // Income Optimization
    if (userData.additionalIncomes.length === 0) {
      newRecommendations.push({
        id: 'additional-income',
        title: 'Explore additional income streams',
        description:
          'Consider freelancing, rental income, or side business to accelerate FIRE.',
        impact: 'medium',
        effort: 'hard',
        category: 'income',
        icon: '💼',
      });
    }

    setRecommendations(newRecommendations);
  };

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'success':
        return '✅';
      case 'warning':
        return '⚠️';
      case 'error':
        return '🚨';
      case 'info':
        return 'ℹ️';
      default:
        return '💡';
    }
  };

  const getInsightColor = (type: string) => {
    switch (type) {
      case 'success':
        return 'border-green-200 bg-green-50 dark:bg-green-900/20';
      case 'warning':
        return 'border-yellow-200 bg-yellow-50 dark:bg-yellow-900/20';
      case 'error':
        return 'border-red-200 bg-red-50 dark:bg-red-900/20';
      case 'info':
        return 'border-blue-200 bg-blue-50 dark:bg-blue-900/20';
      default:
        return 'border-gray-200 bg-gray-50 dark:bg-gray-800';
    }
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high':
        return 'text-red-600 bg-red-100';
      case 'medium':
        return 'text-yellow-600 bg-yellow-100';
      case 'low':
        return 'text-green-600 bg-green-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const filteredInsights =
    selectedCategory === 'all'
      ? insights
      : insights.filter(insight => insight.category === selectedCategory);

  const filteredRecommendations =
    selectedCategory === 'all'
      ? recommendations
      : recommendations.filter(rec => rec.category === selectedCategory);

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div className='flex items-center justify-between'>
        <div>
          <h2 className='text-2xl font-bold'>🧠 Smart Dashboard</h2>
          <p className='text-gray-600'>
            AI-powered insights and personalized recommendations
          </p>
        </div>

        {/* Category Filter */}
        <select
          value={selectedCategory}
          onChange={e => setSelectedCategory(e.target.value)}
          className={`px-3 py-2 rounded-md border ${
            darkMode
              ? 'bg-gray-700 border-gray-600 text-white'
              : 'bg-white border-gray-300 text-gray-900'
          }`}
        >
          <option value='all'>All Categories</option>
          <option value='savings'>💰 Savings</option>
          <option value='expenses'>💸 Expenses</option>
          <option value='investments'>📈 Investments</option>
          <option value='taxes'>🏛️ Taxes</option>
          <option value='goals'>🎯 Goals</option>
        </select>
      </div>

      {/* Key Insights */}
      <div>
        <h3 className='text-lg font-semibold mb-4'>🔍 Key Insights</h3>
        <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
          {filteredInsights.map(insight => (
            <div
              key={insight.id}
              className={`p-4 rounded-lg border ${getInsightColor(insight.type)}`}
            >
              <div className='flex items-start gap-3'>
                <span className='text-xl'>{getInsightIcon(insight.type)}</span>
                <div className='flex-1'>
                  <h4 className='font-semibold mb-1'>{insight.title}</h4>
                  <p className='text-sm text-gray-600 dark:text-gray-300'>
                    {insight.description}
                  </p>
                  {insight.action && (
                    <button
                      onClick={insight.action.onClick}
                      className='mt-2 text-sm text-blue-600 hover:text-blue-800'
                    >
                      {insight.action.label} →
                    </button>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Recommendations */}
      <div>
        <h3 className='text-lg font-semibold mb-4'>💡 Smart Recommendations</h3>
        <div className='space-y-3'>
          {filteredRecommendations.map(rec => (
            <div
              key={rec.id}
              className={`p-4 rounded-lg border ${
                darkMode
                  ? 'border-gray-700 bg-gray-800'
                  : 'border-gray-200 bg-white'
              }`}
            >
              <div className='flex items-start justify-between'>
                <div className='flex items-start gap-3 flex-1'>
                  <span className='text-xl'>{rec.icon}</span>
                  <div>
                    <h4 className='font-semibold mb-1'>{rec.title}</h4>
                    <p className='text-sm text-gray-600 dark:text-gray-300 mb-2'>
                      {rec.description}
                    </p>
                    <div className='flex gap-2'>
                      <span
                        className={`px-2 py-1 text-xs rounded-full ${getImpactColor(rec.impact)}`}
                      >
                        {rec.impact} impact
                      </span>
                      <span className='px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-600'>
                        {rec.effort} effort
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Quick Actions */}
      <div
        className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-50'}`}
      >
        <h3 className='text-lg font-semibold mb-3'>⚡ Quick Actions</h3>
        <div className='grid grid-cols-2 md:grid-cols-4 gap-3'>
          <button className='p-3 text-center rounded-lg bg-blue-600 text-white hover:bg-blue-700'>
            <div className='text-xl mb-1'>📊</div>
            <div className='text-sm'>View Analysis</div>
          </button>
          <button className='p-3 text-center rounded-lg bg-green-600 text-white hover:bg-green-700'>
            <div className='text-xl mb-1'>🎯</div>
            <div className='text-sm'>Set Goals</div>
          </button>
          <button className='p-3 text-center rounded-lg bg-purple-600 text-white hover:bg-purple-700'>
            <div className='text-xl mb-1'>📈</div>
            <div className='text-sm'>Add Investment</div>
          </button>
          <button className='p-3 text-center rounded-lg bg-orange-600 text-white hover:bg-orange-700'>
            <div className='text-xl mb-1'>💾</div>
            <div className='text-sm'>Save Snapshot</div>
          </button>
        </div>
      </div>
    </div>
  );
};

export default SmartDashboard;
