import React, { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocalStorage } from '../hooks/useLocalStorage';
import ChartControls, {
  ChartTypeOption,
  ExportFormat,
  TimeframeOption,
  exportChartAsPNG,
  exportChartAsSVG,
  exportDataAsCSV,
  exportDataAsJSON,
} from './ChartControls';
import EnhancedD3Chart from './EnhancedD3Chart';

interface EnhancedHistoricalChartsProps {
  darkMode: boolean;
  userData: {
    currentAge: number;
    retirementAge: number;
    currentSavings: number;
    monthlyIncome: number;
    monthlyExpenses: number;
    expectedReturn: number;
    inflationRate: number;
    safeWithdrawalRate: number;
  };
  expenses?: any[];
  investments?: any[];
  savingsGoals?: any[];
}

interface HistoricalDataPoint {
  date: string;
  age: number;
  netWorth: number;
  monthlyIncome: number;
  monthlyExpenses: number;
  monthlySavings: number;
  savingsRate: number;
  fireProgress: number;
  investmentValue: number;
  emergencyFund: number;
}

interface ChartMetric {
  key: keyof HistoricalDataPoint;
  label: string;
  color: string;
  format: 'currency' | 'percentage' | 'number';
  enabled: boolean;
}

const EnhancedHistoricalCharts: React.FC<EnhancedHistoricalChartsProps> = ({
  darkMode,
  userData,
  expenses = [],
  investments = [],
  savingsGoals = [],
}) => {
  const { t } = useTranslation();
  const [historicalData, setHistoricalData] = useLocalStorage<
    HistoricalDataPoint[]
  >('historical_data', []);
  const [selectedTimeframe, setSelectedTimeframe] =
    useState<TimeframeOption>('1Y');
  const [selectedChartType, setSelectedChartType] =
    useState<ChartTypeOption>('line');
  const [selectedMetrics, setSelectedMetrics] = useState<string[]>([
    'netWorth',
    'savingsRate',
    'fireProgress',
  ]);
  const [isGeneratingData, setIsGeneratingData] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);

  // Available metrics configuration
  const availableMetrics: ChartMetric[] = useMemo(
    () => [
      {
        key: 'netWorth',
        label: 'Net Worth',
        color: '#10B981',
        format: 'currency',
        enabled: true,
      },
      {
        key: 'monthlyIncome',
        label: 'Monthly Income',
        color: '#3B82F6',
        format: 'currency',
        enabled: true,
      },
      {
        key: 'monthlyExpenses',
        label: 'Monthly Expenses',
        color: '#EF4444',
        format: 'currency',
        enabled: true,
      },
      {
        key: 'monthlySavings',
        label: 'Monthly Savings',
        color: '#8B5CF6',
        format: 'currency',
        enabled: true,
      },
      {
        key: 'savingsRate',
        label: 'Savings Rate',
        color: '#F59E0B',
        format: 'percentage',
        enabled: true,
      },
      {
        key: 'fireProgress',
        label: 'FIRE Progress',
        color: '#06B6D4',
        format: 'percentage',
        enabled: true,
      },
      {
        key: 'investmentValue',
        label: 'Investment Value',
        color: '#84CC16',
        format: 'currency',
        enabled: true,
      },
      {
        key: 'emergencyFund',
        label: 'Emergency Fund',
        color: '#F97316',
        format: 'currency',
        enabled: true,
      },
    ],
    []
  );

  // Filter data based on selected timeframe
  const filteredData = useMemo(() => {
    if (!historicalData.length) return [];

    const now = new Date();
    let startDate: Date;

    switch (selectedTimeframe) {
      case '1M':
        startDate = new Date(
          now.getFullYear(),
          now.getMonth() - 1,
          now.getDate()
        );
        break;
      case '3M':
        startDate = new Date(
          now.getFullYear(),
          now.getMonth() - 3,
          now.getDate()
        );
        break;
      case '6M':
        startDate = new Date(
          now.getFullYear(),
          now.getMonth() - 6,
          now.getDate()
        );
        break;
      case '1Y':
        startDate = new Date(
          now.getFullYear() - 1,
          now.getMonth(),
          now.getDate()
        );
        break;
      case '2Y':
        startDate = new Date(
          now.getFullYear() - 2,
          now.getMonth(),
          now.getDate()
        );
        break;
      case 'ALL':
      default:
        return historicalData;
    }

    return historicalData.filter(point => new Date(point.date) >= startDate);
  }, [historicalData, selectedTimeframe]);

  // Transform data for chart consumption
  const chartData = useMemo(() => {
    return selectedMetrics
      .map(metricKey => {
        const metric = availableMetrics.find(m => m.key === metricKey);
        if (!metric) return null;

        return {
          metric: metricKey,
          label: metric.label,
          color: metric.color,
          format: metric.format,
          data: filteredData.map(point => ({
            date: new Date(point.date),
            value: point[metric.key] as number,
            label: metric.label,
            category: metricKey,
          })),
        };
      })
      .filter(Boolean);
  }, [selectedMetrics, availableMetrics, filteredData]);

  // Generate sample historical data
  const generateSampleData = useCallback(async () => {
    setIsGeneratingData(true);

    try {
      const sampleData: HistoricalDataPoint[] = [];
      const startDate = new Date();
      startDate.setFullYear(startDate.getFullYear() - 2);

      for (let i = 0; i < 365; i++) {
        const date = new Date(startDate);
        date.setDate(date.getDate() + i);

        const monthlyIncome =
          userData.monthlyIncome * (1 + Math.random() * 0.1 - 0.05);
        const monthlyExpenses =
          userData.monthlyExpenses * (1 + Math.random() * 0.15 - 0.075);
        const monthlySavings = monthlyIncome - monthlyExpenses;
        const savingsRate = (monthlySavings / monthlyIncome) * 100;
        const netWorth = userData.currentSavings + monthlySavings * i * 0.1;
        const fireTarget = monthlyExpenses * 12 * 25;
        const fireProgress = (netWorth / fireTarget) * 100;

        sampleData.push({
          date: date.toISOString(),
          age: userData.currentAge + i / 365,
          netWorth: Math.max(0, netWorth),
          monthlyIncome,
          monthlyExpenses,
          monthlySavings,
          savingsRate: Math.max(0, savingsRate),
          fireProgress: Math.min(100, Math.max(0, fireProgress)),
          investmentValue: netWorth * 0.7,
          emergencyFund: Math.min(netWorth * 0.3, monthlyExpenses * 6),
        });
      }

      setHistoricalData(sampleData);
    } catch (error) {
      console.error('Error generating sample data:', error);
    } finally {
      setIsGeneratingData(false);
    }
  }, [userData, setHistoricalData]);

  // Handle metric toggle
  const handleMetricToggle = useCallback((metricKey: string) => {
    setSelectedMetrics(prev =>
      prev.includes(metricKey)
        ? prev.filter(key => key !== metricKey)
        : [...prev, metricKey]
    );
  }, []);

  // Handle chart export
  const handleExport = useCallback(
    (format: ExportFormat) => {
      const chartContainer = document.querySelector(
        '.enhanced-chart-container'
      );
      const svgElement = chartContainer?.querySelector('svg') as SVGSVGElement;

      if (!svgElement && (format === 'png' || format === 'svg')) {
        console.error('SVG element not found for export');
        return;
      }

      const timestamp = new Date().toISOString().split('T')[0];
      const filename = `fire-calculator-chart-${timestamp}`;

      switch (format) {
        case 'png':
          exportChartAsPNG(svgElement, `${filename}.png`);
          break;
        case 'svg':
          exportChartAsSVG(svgElement, `${filename}.svg`);
          break;
        case 'csv':
          exportDataAsCSV(filteredData, `${filename}.csv`);
          break;
        case 'json':
          exportDataAsJSON(filteredData, `${filename}.json`);
          break;
        case 'pdf':
          // PDF export would require additional library like jsPDF
          console.log('PDF export not yet implemented');
          break;
      }
    },
    [filteredData]
  );

  // Handle fullscreen toggle
  const handleFullscreen = useCallback(() => {
    setIsFullscreen(prev => !prev);
  }, []);

  // Data point interaction handlers
  const handleDataPointClick = useCallback((data: any) => {
    console.log('Data point clicked:', data);
    // Could open detailed view or drill-down
  }, []);

  const handleDataPointHover = useCallback((data: any) => {
    // Could show additional context or highlight related data
  }, []);

  if (!historicalData.length) {
    return (
      <div
        className={`p-6 rounded-lg border ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}
      >
        <div className='text-center'>
          <h3 className='text-lg font-semibold mb-4'>
            📈 Historical Financial Tracking
          </h3>
          <p className={`mb-6 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
            No historical data available. Generate sample data to see your
            financial trends over time.
          </p>
          <button
            onClick={generateSampleData}
            disabled={isGeneratingData}
            className='px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed'
          >
            {isGeneratingData ? '⏳ Generating...' : '📊 Generate Sample Data'}
          </button>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`${isFullscreen ? 'fixed inset-0 z-50 p-4' : ''} ${
        darkMode ? 'bg-gray-900' : 'bg-white'
      }`}
    >
      <div className={`${isFullscreen ? 'h-full' : ''} space-y-4`}>
        {/* Chart Controls */}
        <ChartControls
          darkMode={darkMode}
          selectedTimeframe={selectedTimeframe}
          selectedChartType={selectedChartType}
          selectedMetrics={selectedMetrics}
          availableMetrics={availableMetrics}
          onTimeframeChange={setSelectedTimeframe}
          onChartTypeChange={setSelectedChartType}
          onMetricToggle={handleMetricToggle}
          onExport={handleExport}
          onFullscreen={handleFullscreen}
          showZoomControls={false} // Will implement zoom in next iteration
        />

        {/* Charts Container */}
        <div
          className={`enhanced-chart-container grid gap-6 ${
            isFullscreen ? 'h-full grid-rows-1' : 'grid-cols-1'
          }`}
        >
          {chartData.map(
            (chart, index) =>
              chart && (
                <div
                  key={chart.metric}
                  className={`${
                    darkMode
                      ? 'bg-gray-800 border-gray-700'
                      : 'bg-white border-gray-200'
                  } rounded-lg border p-4 w-full flex flex-col`}
                >
                  <h4
                    className={`text-lg font-semibold mb-4 flex-shrink-0 ${
                      darkMode ? 'text-gray-200' : 'text-gray-800'
                    }`}
                  >
                    {chart.label}
                  </h4>
                  <div className='flex-1 min-h-0'>
                    <EnhancedD3Chart
                      data={chart.data}
                      config={{
                        type: selectedChartType,
                        width: isFullscreen ? 1800 : 1200,
                        height: isFullscreen ? 600 : 400,
                        color: chart.color,
                        gradient: selectedChartType === 'area',
                        animated: true,
                        interactive: true,
                        responsive: true,
                      }}
                      darkMode={darkMode}
                      onDataPointClick={handleDataPointClick}
                      onDataPointHover={handleDataPointHover}
                      className='w-full h-full'
                    />
                  </div>
                </div>
              )
          )}
        </div>

        {/* Data Summary */}
        {!isFullscreen && (
          <div
            className={`p-4 rounded-lg border ${
              darkMode
                ? 'bg-gray-800 border-gray-700'
                : 'bg-gray-50 border-gray-200'
            }`}
          >
            <h4 className='text-lg font-semibold mb-3'>📊 Data Summary</h4>
            <div className='grid grid-cols-2 md:grid-cols-4 gap-4 text-sm'>
              <div>
                <div
                  className={`font-medium ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}
                >
                  Data Points
                </div>
                <div className='text-lg font-semibold'>
                  {filteredData.length}
                </div>
              </div>
              <div>
                <div
                  className={`font-medium ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}
                >
                  Timeframe
                </div>
                <div className='text-lg font-semibold'>{selectedTimeframe}</div>
              </div>
              <div>
                <div
                  className={`font-medium ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}
                >
                  Active Metrics
                </div>
                <div className='text-lg font-semibold'>
                  {selectedMetrics.length}
                </div>
              </div>
              <div>
                <div
                  className={`font-medium ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}
                >
                  Chart Type
                </div>
                <div className='text-lg font-semibold capitalize'>
                  {selectedChartType}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default EnhancedHistoricalCharts;
