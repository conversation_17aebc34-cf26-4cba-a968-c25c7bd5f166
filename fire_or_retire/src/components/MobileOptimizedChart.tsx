import * as d3 from 'd3';
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';

interface TouchPoint {
  x: number;
  y: number;
  timestamp: number;
}

interface MobileChartProps {
  data: Array<{ date: Date; value: number; label?: string }>;
  darkMode: boolean;
  width?: number;
  height?: number;
  color?: string;
  onDataPointSelect?: (data: any) => void;
  className?: string;
}

const MobileOptimizedChart: React.FC<MobileChartProps> = ({
  data,
  darkMode,
  width = 350,
  height = 200,
  color = '#3B82F6',
  onDataPointSelect,
  className = '',
}) => {
  const svgRef = useRef<SVGSVGElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [touchState, setTouchState] = useState<{
    isActive: boolean;
    startPoint: TouchPoint | null;
    currentPoint: TouchPoint | null;
    scale: number;
    translateX: number;
  }>({
    isActive: false,
    startPoint: null,
    currentPoint: null,
    scale: 1,
    translateX: 0,
  });

  // Responsive dimensions for mobile
  const responsiveDimensions = useMemo(() => {
    if (!containerRef.current) return { width, height };

    const containerWidth = containerRef.current.clientWidth;
    const isMobile = containerWidth < 768;

    return {
      width: isMobile ? Math.min(containerWidth - 20, 350) : width,
      height: isMobile ? 180 : height,
    };
  }, [width, height]);

  // Format currency for mobile (compact format)
  const formatCurrency = useCallback((value: number) => {
    if (value >= 1000000) {
      return `CHF ${(value / 1000000).toFixed(1)}M`;
    } else if (value >= 1000) {
      return `CHF ${(value / 1000).toFixed(0)}k`;
    }
    return `CHF ${value.toFixed(0)}`;
  }, []);

  // Handle touch start
  const handleTouchStart = useCallback((event: TouchEvent) => {
    event.preventDefault();
    const touch = event.touches[0];
    const rect = svgRef.current?.getBoundingClientRect();
    if (!rect) return;

    const touchPoint: TouchPoint = {
      x: touch.clientX - rect.left,
      y: touch.clientY - rect.top,
      timestamp: Date.now(),
    };

    setTouchState(prev => ({
      ...prev,
      isActive: true,
      startPoint: touchPoint,
      currentPoint: touchPoint,
    }));
  }, []);

  // Handle touch move
  const handleTouchMove = useCallback(
    (event: TouchEvent) => {
      event.preventDefault();
      if (!touchState.isActive || !touchState.startPoint) return;

      const touch = event.touches[0];
      const rect = svgRef.current?.getBoundingClientRect();
      if (!rect) return;

      const currentPoint: TouchPoint = {
        x: touch.clientX - rect.left,
        y: touch.clientY - rect.top,
        timestamp: Date.now(),
      };

      // Calculate pan distance
      const deltaX = currentPoint.x - touchState.startPoint.x;

      setTouchState(prev => ({
        ...prev,
        currentPoint,
        translateX: deltaX,
      }));
    },
    [touchState.isActive, touchState.startPoint]
  );

  // Handle touch end
  const handleTouchEnd = useCallback(
    (event: TouchEvent) => {
      event.preventDefault();

      if (!touchState.startPoint || !touchState.currentPoint) {
        setTouchState(prev => ({ ...prev, isActive: false }));
        return;
      }

      const deltaX = Math.abs(
        touchState.currentPoint.x - touchState.startPoint.x
      );
      const deltaY = Math.abs(
        touchState.currentPoint.y - touchState.startPoint.y
      );
      const deltaTime =
        touchState.currentPoint.timestamp - touchState.startPoint.timestamp;

      // Detect tap vs pan
      if (deltaX < 10 && deltaY < 10 && deltaTime < 300) {
        // This is a tap - find nearest data point
        const margin = { top: 20, right: 20, bottom: 30, left: 40 };
        const innerWidth =
          responsiveDimensions.width - margin.left - margin.right;
        const innerHeight =
          responsiveDimensions.height - margin.top - margin.bottom;

        const xScale = d3
          .scaleTime()
          .domain(d3.extent(data, d => d.date) as [Date, Date])
          .range([0, innerWidth]);

        const tapX = touchState.currentPoint.x - margin.left;
        const tapDate = xScale.invert(tapX);

        // Find closest data point
        const closestPoint = data.reduce((closest, point) => {
          const pointDistance = Math.abs(
            point.date.getTime() - tapDate.getTime()
          );
          const closestDistance = Math.abs(
            closest.date.getTime() - tapDate.getTime()
          );
          return pointDistance < closestDistance ? point : closest;
        });

        onDataPointSelect?.(closestPoint);
      }

      setTouchState(prev => ({
        ...prev,
        isActive: false,
        startPoint: null,
        currentPoint: null,
        translateX: 0,
      }));
    },
    [touchState, data, responsiveDimensions, onDataPointSelect]
  );

  // Render chart
  useEffect(() => {
    if (!svgRef.current || !data.length) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove();

    const { width: chartWidth, height: chartHeight } = responsiveDimensions;
    const margin = { top: 20, right: 20, bottom: 30, left: 40 };
    const innerWidth = chartWidth - margin.left - margin.right;
    const innerHeight = chartHeight - margin.top - margin.bottom;

    // Update SVG dimensions
    svg.attr('width', chartWidth).attr('height', chartHeight);

    // Create scales
    const xScale = d3
      .scaleTime()
      .domain(d3.extent(data, d => d.date) as [Date, Date])
      .range([0, innerWidth]);

    const yScale = d3
      .scaleLinear()
      .domain(d3.extent(data, d => d.value) as [number, number])
      .nice()
      .range([innerHeight, 0]);

    // Create main group
    const g = svg
      .append('g')
      .attr('transform', `translate(${margin.left},${margin.top})`);

    // Add gradient for mobile
    const gradient = svg
      .append('defs')
      .append('linearGradient')
      .attr('id', 'mobile-chart-gradient')
      .attr('gradientUnits', 'userSpaceOnUse')
      .attr('x1', 0)
      .attr('y1', 0)
      .attr('x2', 0)
      .attr('y2', innerHeight);

    gradient
      .append('stop')
      .attr('offset', '0%')
      .attr('stop-color', color)
      .attr('stop-opacity', 0.6);

    gradient
      .append('stop')
      .attr('offset', '100%')
      .attr('stop-color', color)
      .attr('stop-opacity', 0.1);

    // Create line generator
    const line = d3
      .line<any>()
      .x(d => xScale(d.date))
      .y(d => yScale(d.value))
      .curve(d3.curveMonotoneX);

    // Create area generator
    const area = d3
      .area<any>()
      .x(d => xScale(d.date))
      .y0(innerHeight)
      .y1(d => yScale(d.value))
      .curve(d3.curveMonotoneX);

    // Add area
    g.append('path')
      .datum(data)
      .attr('fill', 'url(#mobile-chart-gradient)')
      .attr('d', area);

    // Add line
    const path = g
      .append('path')
      .datum(data)
      .attr('fill', 'none')
      .attr('stroke', color)
      .attr('stroke-width', 2)
      .attr('d', line);

    // Animate line drawing
    const totalLength = path.node()?.getTotalLength() || 0;
    path
      .attr('stroke-dasharray', `${totalLength} ${totalLength}`)
      .attr('stroke-dashoffset', totalLength)
      .transition()
      .duration(1000)
      .ease(d3.easeLinear)
      .attr('stroke-dashoffset', 0);

    // Add touch-friendly data points
    g.selectAll('.data-point')
      .data(data)
      .enter()
      .append('circle')
      .attr('class', 'data-point')
      .attr('cx', d => xScale(d.date))
      .attr('cy', d => yScale(d.value))
      .attr('r', 6) // Larger for touch
      .attr('fill', color)
      .attr('stroke', 'white')
      .attr('stroke-width', 2)
      .style('opacity', 0.8);

    // Add all years for mobile
    const dataRange = d3.extent(data, d => d.date) as [Date, Date];
    const startYear = dataRange[0].getFullYear();
    const endYear = dataRange[1].getFullYear();

    // Generate all years in the range
    const allYears = [];
    for (let year = startYear; year <= endYear; year++) {
      allYears.push(new Date(year, 0, 1));
    }

    const xAxis = d3
      .axisBottom(xScale)
      .tickFormat(d3.timeFormat('%Y'))
      .tickValues(allYears);

    const yAxis = d3
      .axisLeft(yScale)
      .tickFormat(d => formatCurrency(d as number))
      .ticks(3); // Fewer ticks for mobile

    const xAxisGroup = g
      .append('g')
      .attr('class', 'x-axis')
      .attr('transform', `translate(0,${innerHeight})`)
      .call(xAxis);

    // Style x-axis labels for mobile
    const xAxisLabels = xAxisGroup
      .selectAll('text')
      .style('fill', darkMode ? '#9CA3AF' : '#6B7280')
      .style('font-size', '10px')
      .style('text-anchor', 'middle');

    // Rotate labels if there are many years
    const yearCount = endYear - startYear + 1;
    if (yearCount > 5) {
      xAxisLabels
        .style('text-anchor', 'end')
        .attr('dx', '-.5em')
        .attr('dy', '.15em')
        .attr('transform', 'rotate(-45)');
    }

    g.append('g')
      .attr('class', 'y-axis')
      .call(yAxis)
      .selectAll('text')
      .style('fill', darkMode ? '#9CA3AF' : '#6B7280')
      .style('font-size', '12px');

    // Style axes
    g.selectAll('.domain, .tick line').style(
      'stroke',
      darkMode ? '#4B5563' : '#E5E7EB'
    );

    // Add touch event listeners
    const svgElement = svgRef.current;
    svgElement.addEventListener('touchstart', handleTouchStart, {
      passive: false,
    });
    svgElement.addEventListener('touchmove', handleTouchMove, {
      passive: false,
    });
    svgElement.addEventListener('touchend', handleTouchEnd, { passive: false });

    return () => {
      svgElement.removeEventListener('touchstart', handleTouchStart);
      svgElement.removeEventListener('touchmove', handleTouchMove);
      svgElement.removeEventListener('touchend', handleTouchEnd);
    };
  }, [
    data,
    darkMode,
    color,
    responsiveDimensions,
    formatCurrency,
    handleTouchStart,
    handleTouchMove,
    handleTouchEnd,
  ]);

  return (
    <div ref={containerRef} className={`relative ${className}`}>
      <svg
        ref={svgRef}
        className={`${darkMode ? 'bg-gray-900/30' : 'bg-white/30'} rounded-lg touch-none`}
        style={{
          transform: touchState.isActive
            ? `translateX(${touchState.translateX}px)`
            : 'none',
          transition: touchState.isActive ? 'none' : 'transform 0.3s ease',
        }}
      />

      {/* Touch indicator */}
      {touchState.isActive && (
        <div className='absolute top-2 right-2 px-2 py-1 bg-blue-600 text-white text-xs rounded'>
          📱 Touch Active
        </div>
      )}

      {/* Mobile-specific controls */}
      <div className='flex justify-center mt-2 space-x-2'>
        <div
          className={`text-xs px-2 py-1 rounded ${
            darkMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-100 text-gray-600'
          }`}
        >
          👆 Tap data points
        </div>
        <div
          className={`text-xs px-2 py-1 rounded ${
            darkMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-100 text-gray-600'
          }`}
        >
          👈👉 Swipe to pan
        </div>
      </div>
    </div>
  );
};

export default MobileOptimizedChart;

// Mobile chart utilities
export const isMobileDevice = () => {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
    navigator.userAgent
  );
};

export const getTouchEventCoordinates = (
  event: TouchEvent,
  element: Element
) => {
  const rect = element.getBoundingClientRect();
  const touch = event.touches[0] || event.changedTouches[0];
  return {
    x: touch.clientX - rect.left,
    y: touch.clientY - rect.top,
  };
};

export const detectSwipeDirection = (
  startPoint: TouchPoint,
  endPoint: TouchPoint
) => {
  const deltaX = endPoint.x - startPoint.x;
  const deltaY = endPoint.y - startPoint.y;
  const absDeltaX = Math.abs(deltaX);
  const absDeltaY = Math.abs(deltaY);

  if (absDeltaX > absDeltaY) {
    return deltaX > 0 ? 'right' : 'left';
  } else {
    return deltaY > 0 ? 'down' : 'up';
  }
};
