import React, { Component, ReactNode } from 'react';
import { debugLogger } from '../utils/debug-logger';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: React.ErrorInfo | null;
  errorId: string | null;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // Log to debug logger with comprehensive details
    const errorId = debugLogger.log(
      'error',
      'react-boundary',
      'React Error Boundary caught error',
      {
        error: {
          name: error.name,
          message: error.message,
          stack: error.stack,
        },
        errorInfo: {
          componentStack: errorInfo.componentStack,
        },
        props: this.props,
        state: this.state,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        localStorage: this.getLocalStorageSnapshot(),
      }
    );

    this.setState({
      error,
      errorInfo,
      errorId,
    });

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Log to console for development
    console.group('🚨 React Error Boundary');
    console.error('Error:', error);
    console.error('Error Info:', errorInfo);
    console.error('Error ID:', errorId);
    console.groupEnd();
  }

  private getLocalStorageSnapshot(): Record<string, any> {
    try {
      const snapshot: Record<string, any> = {};
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith('swissBudgetPro_')) {
          try {
            const value = localStorage.getItem(key);
            snapshot[key] = value ? JSON.parse(value) : null;
          } catch {
            snapshot[key] = '<invalid JSON>';
          }
        }
      }
      return snapshot;
    } catch {
      return { error: 'Could not access localStorage' };
    }
  }

  private handleRetry = () => {
    debugLogger.log('info', 'react-boundary', 'User clicked retry button', {
      errorId: this.state.errorId,
    });

    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
    });
  };

  private handleRefresh = () => {
    debugLogger.log('info', 'react-boundary', 'User clicked refresh button', {
      errorId: this.state.errorId,
    });

    window.location.reload();
  };

  private handleExportError = () => {
    debugLogger.log('info', 'react-boundary', 'User exported error details', {
      errorId: this.state.errorId,
    });

    const errorData = {
      errorId: this.state.errorId,
      timestamp: new Date().toISOString(),
      error: {
        name: this.state.error?.name,
        message: this.state.error?.message,
        stack: this.state.error?.stack,
      },
      errorInfo: {
        componentStack: this.state.errorInfo?.componentStack,
      },
      userAgent: navigator.userAgent,
      url: window.location.href,
      localStorage: this.getLocalStorageSnapshot(),
      debugLogs: debugLogger.getRecentLogs(50),
    };

    const blob = new Blob([JSON.stringify(errorData, null, 2)], {
      type: 'application/json',
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `swiss-budget-pro-error-${this.state.errorId}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  render() {
    if (this.state.hasError) {
      // Use custom fallback if provided
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className='min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 flex items-center justify-center p-4'>
          <div className='max-w-2xl w-full bg-white/10 backdrop-blur-lg rounded-2xl border border-white/20 shadow-2xl p-8'>
            {/* Header */}
            <div className='text-center mb-8'>
              <div className='text-6xl mb-4'>⚠️</div>
              <h1 className='text-3xl font-bold text-white mb-2'>
                Application Error
              </h1>
              <p className='text-blue-200'>
                Swiss Budget Pro encountered an unexpected error. Your data is
                safe and the issue has been logged.
              </p>
              {this.state.errorId && (
                <div className='mt-4 text-sm text-gray-400'>
                  Error ID:{' '}
                  <code className='bg-gray-800 px-2 py-1 rounded'>
                    {this.state.errorId}
                  </code>
                </div>
              )}
            </div>

            {/* Action Buttons */}
            <div className='space-y-4 mb-6'>
              <button
                onClick={this.handleRetry}
                className='w-full px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors'
              >
                🔄 Try Again
              </button>

              <button
                onClick={this.handleRefresh}
                className='w-full px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors'
              >
                🔃 Refresh Page
              </button>

              <button
                onClick={this.handleExportError}
                className='w-full px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg font-medium transition-colors'
              >
                📋 Export Error Details
              </button>
            </div>

            {/* Error Details */}
            <details className='bg-black/20 rounded-lg p-4'>
              <summary className='cursor-pointer text-white font-medium'>
                🔍 Technical Details
              </summary>
              <div className='mt-4 text-sm text-gray-300 space-y-3'>
                <div>
                  <strong className='text-white'>Error Message:</strong>
                  <div className='mt-1 bg-red-900/30 border border-red-700 rounded p-2 text-red-300'>
                    {this.state.error?.message || 'Unknown error'}
                  </div>
                </div>

                {this.state.error?.stack && (
                  <div>
                    <strong className='text-white'>Stack Trace:</strong>
                    <pre className='mt-1 text-xs bg-gray-800 p-2 rounded overflow-auto max-h-40 text-gray-300'>
                      {this.state.error.stack}
                    </pre>
                  </div>
                )}

                {this.state.errorInfo?.componentStack && (
                  <div>
                    <strong className='text-white'>Component Stack:</strong>
                    <pre className='mt-1 text-xs bg-gray-800 p-2 rounded overflow-auto max-h-40 text-gray-300'>
                      {this.state.errorInfo.componentStack}
                    </pre>
                  </div>
                )}
              </div>
            </details>

            {/* Help Text */}
            <div className='mt-6 text-center text-sm text-blue-200'>
              <p>
                If the problem persists, please{' '}
                <a
                  href='https://github.com/forkrul/fire_or_retire/issues'
                  target='_blank'
                  rel='noopener noreferrer'
                  className='text-blue-300 hover:text-blue-200 underline'
                >
                  report this issue on GitHub
                </a>{' '}
                with the exported error details.
              </p>
            </div>

            {/* Test-friendly error indicator */}
            <div
              data-testid='error-boundary'
              data-error-id={this.state.errorId}
              data-error-message={this.state.error?.message}
              className='hidden'
            />
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
