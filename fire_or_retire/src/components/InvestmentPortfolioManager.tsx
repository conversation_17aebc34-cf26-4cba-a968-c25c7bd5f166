import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocalStorage } from '../hooks/useLocalStorage';
import type { Investment, InvestmentCategory } from '../types/investments';

interface InvestmentPortfolioManagerProps {
  darkMode: boolean;
  onPortfolioChange: (investments: Investment[]) => void;
}

const SWISS_INVESTMENT_CATEGORIES: InvestmentCategory[] = [
  {
    id: 'stocks_etf',
    name: 'Stocks & ETFs',
    icon: '📈',
    swissSpecific: false,
    description: 'Individual stocks and exchange-traded funds',
    riskLevel: 'high',
    expectedReturn: 7.0,
  },
  {
    id: 'bonds',
    name: 'Bonds',
    icon: '🏛️',
    swissSpecific: false,
    description: 'Government and corporate bonds',
    riskLevel: 'low',
    expectedReturn: 2.5,
  },
  {
    id: 'swiss_stocks',
    name: 'Swiss Stocks',
    icon: '🇨🇭',
    swissSpecific: true,
    description: 'Swiss Market Index (SMI) and Swiss stocks',
    riskLevel: 'medium',
    expectedReturn: 6.5,
  },
  {
    id: 'real_estate',
    name: 'Real Estate',
    icon: '🏠',
    swissSpecific: false,
    description: 'REITs and real estate investments',
    riskLevel: 'medium',
    expectedReturn: 5.5,
  },
  {
    id: 'commodities',
    name: 'Commodities',
    icon: '🥇',
    swissSpecific: false,
    description: 'Gold, silver, and other commodities',
    riskLevel: 'high',
    expectedReturn: 4.0,
  },
  {
    id: 'crypto',
    name: 'Cryptocurrency',
    icon: '₿',
    swissSpecific: false,
    description: 'Bitcoin, Ethereum, and other cryptocurrencies',
    riskLevel: 'very_high',
    expectedReturn: 15.0,
  },
];

const DEFAULT_INVESTMENTS: Investment[] = [
  {
    id: '1',
    name: 'Vanguard Total World Stock ETF',
    symbol: 'VT',
    category: 'stocks_etf',
    type: 'etf',
    currentValue: 25000,
    purchasePrice: 20000,
    quantity: 250,
    currency: 'CHF',
    broker: 'Interactive Brokers',
    isActive: true,
    swissWithholdingTax: 0.35,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: '2',
    name: 'iShares Core SPI ETF',
    symbol: 'CSPI',
    category: 'swiss_stocks',
    type: 'etf',
    currentValue: 15000,
    purchasePrice: 12000,
    quantity: 150,
    currency: 'CHF',
    broker: 'PostFinance',
    isActive: true,
    swissWithholdingTax: 0.35,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
];

const InvestmentPortfolioManager: React.FC<InvestmentPortfolioManagerProps> = ({
  darkMode,
  onPortfolioChange,
}) => {
  const { t } = useTranslation();
  const [investments, setInvestments] = useLocalStorage<Investment[]>(
    'investments',
    DEFAULT_INVESTMENTS
  );
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingInvestment, setEditingInvestment] = useState<Investment | null>(
    null
  );
  const [filterCategory, setFilterCategory] = useState<string>('all');
  const [sortBy, setSortBy] = useState<'name' | 'value' | 'return' | 'risk'>(
    'value'
  );

  useEffect(() => {
    onPortfolioChange(investments);
  }, [investments, onPortfolioChange]);

  const totalValue = investments
    .filter(inv => inv.isActive)
    .reduce((sum, inv) => sum + inv.currentValue, 0);

  const totalGainLoss = investments
    .filter(inv => inv.isActive)
    .reduce((sum, inv) => sum + (inv.currentValue - inv.purchasePrice), 0);

  const totalReturnPercentage =
    investments
      .filter(inv => inv.isActive)
      .reduce((sum, inv) => sum + inv.purchasePrice, 0) > 0
      ? (totalGainLoss /
          investments
            .filter(inv => inv.isActive)
            .reduce((sum, inv) => sum + inv.purchasePrice, 0)) *
        100
      : 0;

  const filteredInvestments = investments
    .filter(inv => filterCategory === 'all' || inv.category === filterCategory)
    .sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'value':
          return b.currentValue - a.currentValue;
        case 'return':
          return (
            b.currentValue -
            b.purchasePrice -
            (a.currentValue - a.purchasePrice)
          );
        case 'risk':
          const riskOrder = { low: 1, medium: 2, high: 3, very_high: 4 };
          const aRisk =
            SWISS_INVESTMENT_CATEGORIES.find(cat => cat.id === a.category)
              ?.riskLevel || 'medium';
          const bRisk =
            SWISS_INVESTMENT_CATEGORIES.find(cat => cat.id === b.category)
              ?.riskLevel || 'medium';
          return (
            riskOrder[bRisk as keyof typeof riskOrder] -
            riskOrder[aRisk as keyof typeof riskOrder]
          );
        default:
          return 0;
      }
    });

  const handleAddInvestment = (
    investment: Omit<Investment, 'id' | 'createdAt' | 'updatedAt'>
  ) => {
    const newInvestment: Investment = {
      ...investment,
      id: Date.now().toString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    setInvestments([...investments, newInvestment]);
    setShowAddForm(false);
  };

  const handleEditInvestment = (investment: Investment) => {
    const updatedInvestments = investments.map(inv =>
      inv.id === investment.id
        ? { ...investment, updatedAt: new Date().toISOString() }
        : inv
    );
    setInvestments(updatedInvestments);
    setEditingInvestment(null);
  };

  const handleDeleteInvestment = (id: string) => {
    setInvestments(investments.filter(inv => inv.id !== id));
  };

  const handleToggleActive = (id: string) => {
    const updatedInvestments = investments.map(inv =>
      inv.id === id
        ? {
            ...inv,
            isActive: !inv.isActive,
            updatedAt: new Date().toISOString(),
          }
        : inv
    );
    setInvestments(updatedInvestments);
  };

  const formatCurrency = (amount: number, currency: string = 'CHF') => {
    return new Intl.NumberFormat('de-CH', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  const formatPercentage = (percentage: number) => {
    return `${percentage >= 0 ? '+' : ''}${percentage.toFixed(2)}%`;
  };

  const getRiskColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'low':
        return 'text-green-600';
      case 'medium':
        return 'text-yellow-600';
      case 'high':
        return 'text-orange-600';
      case 'very_high':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  return (
    <div className='space-y-6'>
      {/* Portfolio Overview */}
      <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
        <div
          className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
        >
          <h3 className='text-sm font-medium text-gray-500 mb-1'>
            Total Portfolio Value
          </h3>
          <p className='text-2xl font-bold'>{formatCurrency(totalValue)}</p>
        </div>
        <div
          className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
        >
          <h3 className='text-sm font-medium text-gray-500 mb-1'>
            Total Gain/Loss
          </h3>
          <p
            className={`text-2xl font-bold ${totalGainLoss >= 0 ? 'text-green-600' : 'text-red-600'}`}
          >
            {formatCurrency(totalGainLoss)}
          </p>
        </div>
        <div
          className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
        >
          <h3 className='text-sm font-medium text-gray-500 mb-1'>
            Total Return
          </h3>
          <p
            className={`text-2xl font-bold ${totalReturnPercentage >= 0 ? 'text-green-600' : 'text-red-600'}`}
          >
            {formatPercentage(totalReturnPercentage)}
          </p>
        </div>
      </div>

      {/* Controls */}
      <div className='flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between'>
        <div className='flex flex-col sm:flex-row gap-4'>
          <select
            value={filterCategory}
            onChange={e => setFilterCategory(e.target.value)}
            className={`px-3 py-2 rounded-md border ${
              darkMode
                ? 'bg-gray-700 border-gray-600 text-white'
                : 'bg-white border-gray-300 text-gray-900'
            }`}
          >
            <option value='all'>All Categories</option>
            {SWISS_INVESTMENT_CATEGORIES.map(category => (
              <option key={category.id} value={category.id}>
                {category.icon} {category.name}
              </option>
            ))}
          </select>

          <select
            value={sortBy}
            onChange={e => setSortBy(e.target.value as any)}
            className={`px-3 py-2 rounded-md border ${
              darkMode
                ? 'bg-gray-700 border-gray-600 text-white'
                : 'bg-white border-gray-300 text-gray-900'
            }`}
          >
            <option value='value'>Sort by Value</option>
            <option value='name'>Sort by Name</option>
            <option value='return'>Sort by Return</option>
            <option value='risk'>Sort by Risk</option>
          </select>
        </div>

        <button
          onClick={() => setShowAddForm(true)}
          className='px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors'
        >
          + Add Investment
        </button>
      </div>

      {/* Investment List */}
      <div className='space-y-3'>
        {filteredInvestments.map(investment => {
          const category = SWISS_INVESTMENT_CATEGORIES.find(
            cat => cat.id === investment.category
          );
          const gainLoss = investment.currentValue - investment.purchasePrice;
          const returnPercentage = (gainLoss / investment.purchasePrice) * 100;

          return (
            <div
              key={investment.id}
              className={`p-4 rounded-lg border ${
                darkMode
                  ? 'bg-gray-800 border-gray-700'
                  : 'bg-white border-gray-200'
              } ${!investment.isActive ? 'opacity-50' : ''}`}
            >
              <div className='flex items-center justify-between'>
                <div className='flex-1'>
                  <div className='flex items-center gap-3'>
                    <span className='text-2xl'>{category?.icon}</span>
                    <div>
                      <h4 className='font-semibold'>{investment.name}</h4>
                      <p className='text-sm text-gray-500'>
                        {investment.symbol} • {category?.name} •{' '}
                        {investment.broker}
                      </p>
                    </div>
                  </div>
                </div>

                <div className='text-right'>
                  <p className='font-semibold'>
                    {formatCurrency(investment.currentValue)}
                  </p>
                  <p
                    className={`text-sm ${gainLoss >= 0 ? 'text-green-600' : 'text-red-600'}`}
                  >
                    {formatCurrency(gainLoss)} (
                    {formatPercentage(returnPercentage)})
                  </p>
                  <p
                    className={`text-xs ${getRiskColor(category?.riskLevel || 'medium')}`}
                  >
                    Risk: {category?.riskLevel?.replace('_', ' ').toUpperCase()}
                  </p>
                </div>

                <div className='flex items-center gap-2 ml-4'>
                  <button
                    onClick={() => handleToggleActive(investment.id)}
                    className={`p-2 rounded ${
                      investment.isActive
                        ? 'text-green-600 hover:bg-green-100'
                        : 'text-gray-400 hover:bg-gray-100'
                    }`}
                    title={investment.isActive ? 'Deactivate' : 'Activate'}
                  >
                    {investment.isActive ? '✓' : '○'}
                  </button>
                  <button
                    onClick={() => setEditingInvestment(investment)}
                    className='p-2 text-blue-600 hover:bg-blue-100 rounded'
                    title='Edit'
                  >
                    ✏️
                  </button>
                  <button
                    onClick={() => handleDeleteInvestment(investment.id)}
                    className='p-2 text-red-600 hover:bg-red-100 rounded'
                    title='Delete'
                  >
                    🗑️
                  </button>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {filteredInvestments.length === 0 && (
        <div
          className={`text-center py-8 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}
        >
          <p>No investments found. Add your first investment to get started!</p>
        </div>
      )}

      {/* Add/Edit Investment Modal */}
      {(showAddForm || editingInvestment) && (
        <InvestmentForm
          darkMode={darkMode}
          investment={editingInvestment}
          onSave={
            editingInvestment ? handleEditInvestment : handleAddInvestment
          }
          onCancel={() => {
            setShowAddForm(false);
            setEditingInvestment(null);
          }}
        />
      )}
    </div>
  );
};

// Investment Form Component
interface InvestmentFormProps {
  darkMode: boolean;
  investment?: Investment | null;
  onSave: (investment: any) => void;
  onCancel: () => void;
}

const InvestmentForm: React.FC<InvestmentFormProps> = ({
  darkMode,
  investment,
  onSave,
  onCancel,
}) => {
  const [formData, setFormData] = useState({
    name: investment?.name || '',
    symbol: investment?.symbol || '',
    category: investment?.category || 'stocks_etf',
    type: investment?.type || 'etf',
    currentValue: investment?.currentValue || 0,
    purchasePrice: investment?.purchasePrice || 0,
    quantity: investment?.quantity || 0,
    currency: investment?.currency || 'CHF',
    broker: investment?.broker || '',
    swissWithholdingTax: investment?.swissWithholdingTax || 0.35,
    dividendYield: investment?.dividendYield || 0,
    notes: investment?.notes || '',
    isActive: investment?.isActive ?? true,
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (investment) {
      onSave({ ...investment, ...formData });
    } else {
      onSave(formData);
    }
  };

  const handleChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50'>
      <div
        className={`max-w-2xl w-full max-h-[90vh] overflow-y-auto rounded-lg ${
          darkMode ? 'bg-gray-800' : 'bg-white'
        }`}
      >
        <div className='p-6'>
          <h2 className='text-xl font-bold mb-6'>
            {investment ? 'Edit Investment' : 'Add New Investment'}
          </h2>

          <form onSubmit={handleSubmit} className='space-y-4'>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <div>
                <label className='block text-sm font-medium mb-1'>
                  Investment Name *
                </label>
                <input
                  type='text'
                  value={formData.name}
                  onChange={e => handleChange('name', e.target.value)}
                  className={`w-full px-3 py-2 rounded-md border ${
                    darkMode
                      ? 'bg-gray-700 border-gray-600 text-white'
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                  required
                />
              </div>

              <div>
                <label className='block text-sm font-medium mb-1'>Symbol</label>
                <input
                  type='text'
                  value={formData.symbol}
                  onChange={e => handleChange('symbol', e.target.value)}
                  className={`w-full px-3 py-2 rounded-md border ${
                    darkMode
                      ? 'bg-gray-700 border-gray-600 text-white'
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                  placeholder='e.g., VT, AAPL'
                />
              </div>

              <div>
                <label className='block text-sm font-medium mb-1'>
                  Category *
                </label>
                <select
                  value={formData.category}
                  onChange={e => handleChange('category', e.target.value)}
                  className={`w-full px-3 py-2 rounded-md border ${
                    darkMode
                      ? 'bg-gray-700 border-gray-600 text-white'
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                  required
                >
                  {SWISS_INVESTMENT_CATEGORIES.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.icon} {category.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className='block text-sm font-medium mb-1'>Type *</label>
                <select
                  value={formData.type}
                  onChange={e => handleChange('type', e.target.value)}
                  className={`w-full px-3 py-2 rounded-md border ${
                    darkMode
                      ? 'bg-gray-700 border-gray-600 text-white'
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                  required
                >
                  <option value='stock'>Stock</option>
                  <option value='etf'>ETF</option>
                  <option value='bond'>Bond</option>
                  <option value='reit'>REIT</option>
                  <option value='commodity'>Commodity</option>
                  <option value='crypto'>Cryptocurrency</option>
                  <option value='mutual_fund'>Mutual Fund</option>
                  <option value='other'>Other</option>
                </select>
              </div>

              <div>
                <label className='block text-sm font-medium mb-1'>
                  Current Value (CHF) *
                </label>
                <input
                  type='number'
                  step='0.01'
                  value={formData.currentValue}
                  onChange={e =>
                    handleChange(
                      'currentValue',
                      parseFloat(e.target.value) || 0
                    )
                  }
                  className={`w-full px-3 py-2 rounded-md border ${
                    darkMode
                      ? 'bg-gray-700 border-gray-600 text-white'
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                  required
                />
              </div>

              <div>
                <label className='block text-sm font-medium mb-1'>
                  Purchase Price (CHF) *
                </label>
                <input
                  type='number'
                  step='0.01'
                  value={formData.purchasePrice}
                  onChange={e =>
                    handleChange(
                      'purchasePrice',
                      parseFloat(e.target.value) || 0
                    )
                  }
                  className={`w-full px-3 py-2 rounded-md border ${
                    darkMode
                      ? 'bg-gray-700 border-gray-600 text-white'
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                  required
                />
              </div>

              <div>
                <label className='block text-sm font-medium mb-1'>
                  Quantity
                </label>
                <input
                  type='number'
                  step='0.001'
                  value={formData.quantity}
                  onChange={e =>
                    handleChange('quantity', parseFloat(e.target.value) || 0)
                  }
                  className={`w-full px-3 py-2 rounded-md border ${
                    darkMode
                      ? 'bg-gray-700 border-gray-600 text-white'
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                />
              </div>

              <div>
                <label className='block text-sm font-medium mb-1'>Broker</label>
                <input
                  type='text'
                  value={formData.broker}
                  onChange={e => handleChange('broker', e.target.value)}
                  className={`w-full px-3 py-2 rounded-md border ${
                    darkMode
                      ? 'bg-gray-700 border-gray-600 text-white'
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                  placeholder='e.g., Interactive Brokers, PostFinance'
                />
              </div>
            </div>

            <div>
              <label className='block text-sm font-medium mb-1'>Notes</label>
              <textarea
                value={formData.notes}
                onChange={e => handleChange('notes', e.target.value)}
                rows={3}
                className={`w-full px-3 py-2 rounded-md border ${
                  darkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
                placeholder='Additional notes about this investment...'
              />
            </div>

            <div className='flex items-center gap-2'>
              <input
                type='checkbox'
                id='isActive'
                checked={formData.isActive}
                onChange={e => handleChange('isActive', e.target.checked)}
                className='rounded'
              />
              <label htmlFor='isActive' className='text-sm'>
                Active investment (include in portfolio calculations)
              </label>
            </div>

            <div className='flex gap-3 pt-4'>
              <button
                type='submit'
                className='flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors'
              >
                {investment ? 'Update Investment' : 'Add Investment'}
              </button>
              <button
                type='button'
                onClick={onCancel}
                className={`flex-1 px-4 py-2 rounded-md border transition-colors ${
                  darkMode
                    ? 'border-gray-600 text-gray-300 hover:bg-gray-700'
                    : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                }`}
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default InvestmentPortfolioManager;
