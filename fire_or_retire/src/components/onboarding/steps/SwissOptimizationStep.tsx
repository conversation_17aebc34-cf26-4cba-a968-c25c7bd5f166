import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useSwissOptimization } from '../../../hooks/useSwissOptimization';
import { useGamification } from '../../../hooks/useGamification';
import { OnboardingContext } from '../OnboardingFlowManager';

interface SwissOptimizationStepProps {
  context: OnboardingContext;
  onNext: (data?: any) => void;
  onPrevious: () => void;
  onExit: () => void;
  onSkip: () => void;
}

interface OptimizationResult {
  type: 'pillar3a' | 'canton' | 'healthcare';
  currentValue: number;
  optimizedValue: number;
  annualSavings: number;
  xpReward: number;
  implemented: boolean;
}

const SwissOptimizationStep: React.FC<SwissOptimizationStepProps> = ({
  context,
  onNext,
  onPrevious,
  onExit,
  onSkip,
}) => {
  const { t } = useTranslation();
  const [currentOptimization, setCurrentOptimization] = useState<'pillar3a' | 'canton' | 'healthcare'>('pillar3a');
  const [optimizationResults, setOptimizationResults] = useState<OptimizationResult[]>([]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [totalXPEarned, setTotalXPEarned] = useState(0);
  const [implementedOptimizations, setImplementedOptimizations] = useState<string[]>([]);

  const {
    analyzePillar3a,
    compareCantons,
    optimizeHealthcare,
    implementOptimization,
  } = useSwissOptimization();

  const { awardSwissOptimizationXP } = useGamification(context.user.id);

  // Run initial analysis when component mounts
  useEffect(() => {
    runSwissAnalysis();
  }, []);

  const runSwissAnalysis = useCallback(async () => {
    setIsAnalyzing(true);
    try {
      const results: OptimizationResult[] = [];

      // Pillar 3a Analysis
      if (context.user.pillar3aStatus !== 'maxed') {
        const pillar3aAnalysis = await analyzePillar3a({
          currentContribution: context.user.currentPillar3aContribution || 0,
          income: context.user.incomeRange?.mid || 80000,
          canton: context.user.canton,
          age: context.user.age || 35,
        });

        results.push({
          type: 'pillar3a',
          currentValue: pillar3aAnalysis.currentContribution,
          optimizedValue: pillar3aAnalysis.recommendedContribution,
          annualSavings: pillar3aAnalysis.taxSavings,
          xpReward: 100,
          implemented: false,
        });
      }

      // Canton Comparison
      const cantonAnalysis = await compareCantons({
        currentCanton: context.user.canton,
        income: context.user.incomeRange?.mid || 80000,
        familyStatus: context.user.familyStatus || 'single',
        includeNeighboring: true,
      });

      if (cantonAnalysis.potentialSavings > 1000) {
        results.push({
          type: 'canton',
          currentValue: cantonAnalysis.currentTaxBurden,
          optimizedValue: cantonAnalysis.optimizedTaxBurden,
          annualSavings: cantonAnalysis.potentialSavings,
          xpReward: 50,
          implemented: false,
        });
      }

      // Healthcare Optimization
      const healthcareAnalysis = await optimizeHealthcare({
        currentDeductible: context.user.healthcareDeductible || 300,
        expectedHealthCosts: context.user.expectedHealthCosts || 2000,
        age: context.user.age || 35,
        canton: context.user.canton,
      });

      if (healthcareAnalysis.potentialSavings > 200) {
        results.push({
          type: 'healthcare',
          currentValue: healthcareAnalysis.currentAnnualCost,
          optimizedValue: healthcareAnalysis.optimizedAnnualCost,
          annualSavings: healthcareAnalysis.potentialSavings,
          xpReward: 30,
          implemented: false,
        });
      }

      setOptimizationResults(results);
    } catch (error) {
      console.error('Swiss analysis failed:', error);
    } finally {
      setIsAnalyzing(false);
    }
  }, [context.user, analyzePillar3a, compareCantons, optimizeHealthcare]);

  const handleImplementOptimization = useCallback(async (optimization: OptimizationResult) => {
    try {
      await implementOptimization(optimization.type, {
        userId: context.user.id,
        optimizationData: optimization,
      });

      // Award XP for implementation
      await awardSwissOptimizationXP(optimization.type, optimization.xpReward);

      // Update state
      setOptimizationResults(prev =>
        prev.map(opt =>
          opt.type === optimization.type
            ? { ...opt, implemented: true }
            : opt
        )
      );

      setImplementedOptimizations(prev => [...prev, optimization.type]);
      setTotalXPEarned(prev => prev + optimization.xpReward);

      // Haptic feedback on mobile
      if ('vibrate' in navigator) {
        navigator.vibrate([50, 50, 100]);
      }

    } catch (error) {
      console.error('Failed to implement optimization:', error);
    }
  }, [context.user.id, implementOptimization, awardSwissOptimizationXP]);

  const handleContinue = useCallback(() => {
    const stepData = {
      swissOptimizations: optimizationResults,
      implementedOptimizations,
      totalSwissXP: totalXPEarned,
      swissOptimizationScore: Math.round(
        (implementedOptimizations.length / optimizationResults.length) * 100
      ),
    };

    onNext(stepData);
  }, [optimizationResults, implementedOptimizations, totalXPEarned, onNext]);

  const renderOptimizationCard = (optimization: OptimizationResult) => {
    const getOptimizationIcon = (type: string) => {
      switch (type) {
        case 'pillar3a': return '🏛️';
        case 'canton': return '🗺️';
        case 'healthcare': return '🏥';
        default: return '💡';
      }
    };

    const getOptimizationTitle = (type: string) => {
      switch (type) {
        case 'pillar3a': return t('swiss.pillar3aOptimization', 'Pillar 3a Optimization');
        case 'canton': return t('swiss.cantonComparison', 'Canton Tax Comparison');
        case 'healthcare': return t('swiss.healthcareOptimization', 'Healthcare Optimization');
        default: return 'Optimization';
      }
    };

    const getOptimizationDescription = (type: string) => {
      switch (type) {
        case 'pillar3a':
          return t('swiss.pillar3aDescription', 'Maximize your tax-deductible retirement savings');
        case 'canton':
          return t('swiss.cantonDescription', 'Compare tax rates with neighboring cantons');
        case 'healthcare':
          return t('swiss.healthcareDescription', 'Optimize your healthcare deductible strategy');
        default:
          return 'Swiss financial optimization opportunity';
      }
    };

    return (
      <div 
        key={optimization.type}
        className={`optimization-card ${optimization.implemented ? 'implemented' : ''}`}
      >
        <div className="optimization-header">
          <div className="optimization-icon">
            {getOptimizationIcon(optimization.type)}
          </div>
          <div className="optimization-info">
            <h3 className="optimization-title">
              {getOptimizationTitle(optimization.type)}
            </h3>
            <p className="optimization-description">
              {getOptimizationDescription(optimization.type)}
            </p>
          </div>
          <div className="optimization-xp">
            +{optimization.xpReward} XP
          </div>
        </div>

        <div className="optimization-details">
          <div className="optimization-comparison">
            <div className="comparison-item current">
              <span className="comparison-label">{t('swiss.current', 'Current')}</span>
              <span className="comparison-value">
                CHF {optimization.currentValue.toLocaleString()}
              </span>
            </div>
            <div className="comparison-arrow">→</div>
            <div className="comparison-item optimized">
              <span className="comparison-label">{t('swiss.optimized', 'Optimized')}</span>
              <span className="comparison-value">
                CHF {optimization.optimizedValue.toLocaleString()}
              </span>
            </div>
          </div>

          <div className="optimization-savings">
            <div className="savings-amount">
              <span className="savings-label">{t('swiss.annualSavings', 'Annual Savings')}</span>
              <span className="savings-value">
                CHF {optimization.annualSavings.toLocaleString()}
              </span>
            </div>
          </div>
        </div>

        <div className="optimization-actions">
          {optimization.implemented ? (
            <div className="implemented-status">
              <span className="implemented-icon">✅</span>
              <span className="implemented-text">
                {t('swiss.implemented', 'Optimization Implemented')}
              </span>
            </div>
          ) : (
            <button
              className="implement-btn"
              onClick={() => handleImplementOptimization(optimization)}
            >
              {t('swiss.implement', 'Implement Optimization')}
            </button>
          )}
        </div>
      </div>
    );
  };

  const totalPotentialSavings = optimizationResults.reduce(
    (sum, opt) => sum + opt.annualSavings,
    0
  );

  const totalPotentialXP = optimizationResults.reduce(
    (sum, opt) => sum + opt.xpReward,
    0
  );

  if (isAnalyzing) {
    return (
      <div className="swiss-optimization-step analyzing">
        <div className="analyzing-content">
          <div className="analyzing-icon">🇨🇭</div>
          <h2>{t('swiss.analyzing', 'Analyzing Your Swiss Financial Situation')}</h2>
          <p>{t('swiss.analyzingDescription', 'We\'re reviewing your profile to find the best Swiss optimization opportunities...')}</p>
          <div className="analyzing-spinner" />
          <div className="analyzing-steps">
            <div className="analyzing-step">
              <span className="step-icon">🏛️</span>
              <span className="step-text">{t('swiss.analyzingPillar3a', 'Analyzing Pillar 3a opportunities')}</span>
            </div>
            <div className="analyzing-step">
              <span className="step-icon">🗺️</span>
              <span className="step-text">{t('swiss.analyzingCanton', 'Comparing canton tax rates')}</span>
            </div>
            <div className="analyzing-step">
              <span className="step-icon">🏥</span>
              <span className="step-text">{t('swiss.analyzingHealthcare', 'Optimizing healthcare costs')}</span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="swiss-optimization-step">
      <div className="step-header">
        <h1 className="step-title">
          🇨🇭 {t('swiss.optimizationTitle', 'Swiss Financial Optimization')}
        </h1>
        <p className="step-subtitle">
          {t('swiss.optimizationSubtitle', 'Discover personalized ways to save money and earn XP using Swiss-specific financial strategies')}
        </p>
      </div>

      <div className="optimization-summary">
        <div className="summary-card">
          <div className="summary-item">
            <span className="summary-label">{t('swiss.potentialSavings', 'Potential Annual Savings')}</span>
            <span className="summary-value">CHF {totalPotentialSavings.toLocaleString()}</span>
          </div>
          <div className="summary-item">
            <span className="summary-label">{t('swiss.potentialXP', 'Potential XP Rewards')}</span>
            <span className="summary-value">+{totalPotentialXP} XP</span>
          </div>
          <div className="summary-item">
            <span className="summary-label">{t('swiss.optimizationsFound', 'Optimizations Found')}</span>
            <span className="summary-value">{optimizationResults.length}</span>
          </div>
        </div>
      </div>

      <div className="optimizations-list">
        {optimizationResults.map(renderOptimizationCard)}
      </div>

      {optimizationResults.length === 0 && (
        <div className="no-optimizations">
          <div className="no-optimizations-icon">🎉</div>
          <h3>{t('swiss.noOptimizations', 'Great Job!')}</h3>
          <p>
            {t('swiss.noOptimizationsDescription', 
              'Your Swiss financial setup is already well-optimized. You\'re making smart choices!'
            )}
          </p>
          <div className="bonus-xp">
            <span className="bonus-text">{t('swiss.optimizationBonus', 'Optimization Bonus')}</span>
            <span className="bonus-value">+25 XP</span>
          </div>
        </div>
      )}

      {totalXPEarned > 0 && (
        <div className="xp-earned-summary">
          <div className="xp-celebration">
            <span className="xp-icon">⚡</span>
            <span className="xp-text">
              {t('swiss.xpEarned', 'You earned {{xp}} XP from Swiss optimizations!', {
                xp: totalXPEarned
              })}
            </span>
          </div>
        </div>
      )}

      <div className="step-actions">
        <button 
          className="action-btn secondary"
          onClick={onPrevious}
        >
          {t('common.previous', 'Previous')}
        </button>

        <div className="primary-actions">
          {optimizationResults.length > 0 && implementedOptimizations.length === 0 && (
            <button 
              className="action-btn tertiary"
              onClick={onSkip}
            >
              {t('swiss.skipOptimizations', 'Skip for Now')}
            </button>
          )}

          <button 
            className="action-btn primary"
            onClick={handleContinue}
          >
            {implementedOptimizations.length > 0
              ? t('swiss.continueWithOptimizations', 'Continue with {{count}} Optimizations', {
                  count: implementedOptimizations.length
                })
              : t('common.continue', 'Continue')
            }
          </button>
        </div>
      </div>

      <div className="step-info">
        <p className="info-text">
          {t('swiss.optimizationInfo', 
            'These optimizations are based on current Swiss regulations and your personal profile. Always consult with a qualified advisor for major financial decisions.'
          )}
        </p>
      </div>
    </div>
  );
};

export default SwissOptimizationStep;
