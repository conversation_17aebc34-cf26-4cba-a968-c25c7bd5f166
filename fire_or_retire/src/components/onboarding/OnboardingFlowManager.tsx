import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useOnboardingStateMachine } from '../../hooks/useOnboardingStateMachine';
import { usePersonalization } from '../../hooks/usePersonalization';
import { useAnalytics } from '../../hooks/useAnalytics';

// Onboarding Step Components
import LandingStep from './steps/LandingStep';
import RegistrationStep from './steps/RegistrationStep';
import ProfileBasicStep from './steps/ProfileBasicStep';
import ProfileSwissStep from './steps/ProfileSwissStep';
import ProfileGamificationStep from './steps/ProfileGamificationStep';
import SwissOptimizationStep from './steps/SwissOptimizationStep';
import FirstGoalStep from './steps/FirstGoalStep';
import GamificationIntroStep from './steps/GamificationIntroStep';
import CommunityWelcomeStep from './steps/CommunityWelcomeStep';
import ChallengeSetupStep from './steps/ChallengeSetupStep';
import CompletedStep from './steps/CompletedStep';

export enum OnboardingStep {
  LANDING = 'landing',
  REGISTRATION = 'registration',
  PROFILE_BASIC = 'profile_basic',
  PROFILE_SWISS = 'profile_swiss',
  PROFILE_GAMIFICATION = 'profile_gamification',
  SWISS_OPTIMIZATION = 'swiss_optimization',
  FIRST_GOAL = 'first_goal',
  GAMIFICATION_INTRO = 'gamification_intro',
  COMMUNITY_WELCOME = 'community_welcome',
  CHALLENGE_SETUP = 'challenge_setup',
  COMPLETED = 'completed',
}

export interface OnboardingContext {
  user: Partial<User>;
  progress: OnboardingProgress;
  personalization: PersonalizationData;
  timeSpent: number;
  exitPoints: OnboardingStep[];
  abTestVariant: string;
  stepStartTime: number;
}

interface OnboardingFlowManagerProps {
  initialStep?: OnboardingStep;
  userId?: string;
  onComplete?: (userData: User) => void;
  onExit?: (step: OnboardingStep, context: OnboardingContext) => void;
  className?: string;
}

const OnboardingFlowManager: React.FC<OnboardingFlowManagerProps> = ({
  initialStep = OnboardingStep.LANDING,
  userId,
  onComplete,
  onExit,
  className = '',
}) => {
  const { t } = useTranslation();
  const [currentStep, setCurrentStep] = useState<OnboardingStep>(initialStep);
  const [context, setContext] = useState<OnboardingContext>({
    user: {},
    progress: { completedSteps: [], currentStep: initialStep, totalSteps: 11 },
    personalization: { persona: null, preferences: {}, recommendations: [] },
    timeSpent: 0,
    exitPoints: [],
    abTestVariant: '',
    stepStartTime: Date.now(),
  });

  const {
    transition,
    canTransition,
    getNextStep,
    getPreviousStep,
    saveProgress,
    loadProgress,
  } = useOnboardingStateMachine();

  const {
    personalizeContent,
    identifyPersona,
    generateRecommendations,
    updatePreferences,
  } = usePersonalization();

  const {
    trackStep,
    trackInteraction,
    trackCompletion,
    trackExit,
    optimizeFlow,
  } = useAnalytics();

  // Load existing progress on mount
  useEffect(() => {
    const loadExistingProgress = async () => {
      if (userId) {
        try {
          const savedProgress = await loadProgress(userId);
          if (savedProgress) {
            setContext(savedProgress);
            setCurrentStep(savedProgress.progress.currentStep);
          }
        } catch (error) {
          console.error('Failed to load onboarding progress:', error);
        }
      }
    };

    loadExistingProgress();
  }, [userId, loadProgress]);

  // Track step timing and analytics
  useEffect(() => {
    const stepStartTime = Date.now();
    setContext(prev => ({ ...prev, stepStartTime }));

    // Track step entry
    trackStep(userId || 'anonymous', currentStep, 0, []);

    return () => {
      // Track step exit and time spent
      const timeSpent = Date.now() - stepStartTime;
      setContext(prev => ({
        ...prev,
        timeSpent: prev.timeSpent + timeSpent,
      }));
    };
  }, [currentStep, userId, trackStep]);

  // Auto-save progress periodically
  useEffect(() => {
    const saveInterval = setInterval(async () => {
      if (userId && context.user.email) {
        await saveProgress(userId, context);
      }
    }, 30000); // Save every 30 seconds

    return () => clearInterval(saveInterval);
  }, [userId, context, saveProgress]);

  // Handle step transitions
  const handleNext = useCallback(async (stepData?: any) => {
    try {
      // Update context with step data
      const updatedContext = {
        ...context,
        user: { ...context.user, ...stepData },
        progress: {
          ...context.progress,
          completedSteps: [...context.progress.completedSteps, currentStep],
        },
      };

      // Check if transition is allowed
      if (!canTransition(currentStep, 'next', updatedContext)) {
        console.warn('Transition not allowed from', currentStep);
        return;
      }

      // Get next step (may be personalized)
      const nextStep = await getNextStep(currentStep, updatedContext);
      
      // Update personalization
      const persona = identifyPersona(updatedContext.user);
      const recommendations = await generateRecommendations(updatedContext.user);
      const personalizedContent = await personalizeContent(persona, nextStep);

      const finalContext = {
        ...updatedContext,
        personalization: {
          persona,
          preferences: updatedContext.personalization.preferences,
          recommendations,
          content: personalizedContent,
        },
        progress: {
          ...updatedContext.progress,
          currentStep: nextStep,
        },
      };

      // Save progress
      if (userId) {
        await saveProgress(userId, finalContext);
      }

      // Update state
      setContext(finalContext);
      setCurrentStep(nextStep);

      // Track transition
      trackInteraction(userId || 'anonymous', 'step_transition', {
        from: currentStep,
        to: nextStep,
        data: stepData,
      });

      // Check for completion
      if (nextStep === OnboardingStep.COMPLETED) {
        await trackCompletion(userId || 'anonymous', finalContext);
        onComplete?.(finalContext.user as User);
      }

    } catch (error) {
      console.error('Failed to transition to next step:', error);
    }
  }, [
    currentStep,
    context,
    canTransition,
    getNextStep,
    identifyPersona,
    generateRecommendations,
    personalizeContent,
    saveProgress,
    userId,
    trackInteraction,
    trackCompletion,
    onComplete,
  ]);

  const handlePrevious = useCallback(async () => {
    try {
      const previousStep = getPreviousStep(currentStep, context);
      
      if (previousStep) {
        const updatedContext = {
          ...context,
          progress: {
            ...context.progress,
            currentStep: previousStep,
            completedSteps: context.progress.completedSteps.filter(
              step => step !== currentStep
            ),
          },
        };

        setContext(updatedContext);
        setCurrentStep(previousStep);

        if (userId) {
          await saveProgress(userId, updatedContext);
        }

        trackInteraction(userId || 'anonymous', 'step_back', {
          from: currentStep,
          to: previousStep,
        });
      }
    } catch (error) {
      console.error('Failed to go to previous step:', error);
    }
  }, [currentStep, context, getPreviousStep, saveProgress, userId, trackInteraction]);

  const handleExit = useCallback(async () => {
    try {
      const exitContext = {
        ...context,
        exitPoints: [...context.exitPoints, currentStep],
      };

      await trackExit(userId || 'anonymous', currentStep, exitContext);
      onExit?.(currentStep, exitContext);
    } catch (error) {
      console.error('Failed to track exit:', error);
    }
  }, [currentStep, context, userId, trackExit, onExit]);

  const handleSkip = useCallback(async () => {
    // Allow skipping certain optional steps
    const skippableSteps = [
      OnboardingStep.PROFILE_GAMIFICATION,
      OnboardingStep.COMMUNITY_WELCOME,
    ];

    if (skippableSteps.includes(currentStep)) {
      trackInteraction(userId || 'anonymous', 'step_skip', {
        step: currentStep,
      });
      await handleNext({ skipped: true });
    }
  }, [currentStep, userId, trackInteraction, handleNext]);

  // Render current step component
  const renderCurrentStep = () => {
    const commonProps = {
      context,
      onNext: handleNext,
      onPrevious: handlePrevious,
      onExit: handleExit,
      onSkip: handleSkip,
    };

    switch (currentStep) {
      case OnboardingStep.LANDING:
        return <LandingStep {...commonProps} />;
      case OnboardingStep.REGISTRATION:
        return <RegistrationStep {...commonProps} />;
      case OnboardingStep.PROFILE_BASIC:
        return <ProfileBasicStep {...commonProps} />;
      case OnboardingStep.PROFILE_SWISS:
        return <ProfileSwissStep {...commonProps} />;
      case OnboardingStep.PROFILE_GAMIFICATION:
        return <ProfileGamificationStep {...commonProps} />;
      case OnboardingStep.SWISS_OPTIMIZATION:
        return <SwissOptimizationStep {...commonProps} />;
      case OnboardingStep.FIRST_GOAL:
        return <FirstGoalStep {...commonProps} />;
      case OnboardingStep.GAMIFICATION_INTRO:
        return <GamificationIntroStep {...commonProps} />;
      case OnboardingStep.COMMUNITY_WELCOME:
        return <CommunityWelcomeStep {...commonProps} />;
      case OnboardingStep.CHALLENGE_SETUP:
        return <ChallengeSetupStep {...commonProps} />;
      case OnboardingStep.COMPLETED:
        return <CompletedStep {...commonProps} />;
      default:
        return <div>Unknown step: {currentStep}</div>;
    }
  };

  // Calculate progress percentage
  const progressPercentage = Math.round(
    (context.progress.completedSteps.length / context.progress.totalSteps) * 100
  );

  return (
    <div className={`onboarding-flow-manager ${className}`}>
      {/* Progress Header */}
      <div className="onboarding-header">
        <div className="progress-container">
          <div className="progress-bar">
            <div 
              className="progress-fill"
              style={{ width: `${progressPercentage}%` }}
            />
          </div>
          <div className="progress-text">
            {t('onboarding.progress', 'Step {{current}} of {{total}}', {
              current: context.progress.completedSteps.length + 1,
              total: context.progress.totalSteps,
            })}
          </div>
        </div>

        {/* Exit Button */}
        <button 
          className="exit-button"
          onClick={handleExit}
          aria-label={t('onboarding.exit', 'Exit onboarding')}
        >
          ×
        </button>
      </div>

      {/* Step Content */}
      <div className="onboarding-content">
        {renderCurrentStep()}
      </div>

      {/* Debug Info (Development Only) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="onboarding-debug">
          <details>
            <summary>Debug Info</summary>
            <pre>{JSON.stringify({ currentStep, context }, null, 2)}</pre>
          </details>
        </div>
      )}
    </div>
  );
};

export default OnboardingFlowManager;
