import React, { useState, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import SwissTaxOptimizationEngine from './SwissTaxOptimizationEngine';
import SwissTaxDeductionOptimizer from './SwissTaxDeductionOptimizer';
import { calculateSocialInsurance } from '../utils/swiss-social-insurance';
import {
  calculateSwissIncomeTax,
  calculateWealthTax,
  type CantonCode,
  type CivilStatus,
} from '../utils/swiss-tax-calculations';

interface SwissTaxPlanningDashboardProps {
  darkMode: boolean;
  userData: {
    currentAge: number;
    monthlyIncome: number;
    currentSavings: number;
    canton: CantonCode;
    maritalStatus: CivilStatus;
    children: number;
  };
}

interface TaxPlanningScenario {
  id: string;
  name: string;
  description: string;
  grossIncome: number;
  deductions: {
    pillar3a: number;
    professional: number;
    insurance: number;
    medical: number;
    donations: number;
    interest: number;
  };
  socialInsurance: any;
  incomeTax: any;
  wealthTax: any;
  netIncome: number;
  totalTaxBurden: number;
  effectiveRate: number;
  savings: number;
}

const SwissTaxPlanningDashboard: React.FC<SwissTaxPlanningDashboardProps> = ({
  darkMode,
  userData,
}) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<
    'overview' | 'optimization' | 'deductions' | 'scenarios'
  >('overview');
  const [isAnalyzing, setIsAnalyzing] = useState(true);
  const [taxScenarios, setTaxScenarios] = useState<TaxPlanningScenario[]>([]);
  const [selectedScenario, setSelectedScenario] = useState<string>('current');

  const annualIncome = userData.monthlyIncome * 12;

  // Calculate current tax situation
  const currentTaxSituation = useMemo(() => {
    const socialInsurance = calculateSocialInsurance(
      annualIncome,
      userData.currentAge
    );
    const taxableIncome = annualIncome - socialInsurance.totalEmployee - 7056; // Basic deductions

    const incomeTax = calculateSwissIncomeTax(
      taxableIncome,
      userData.canton,
      userData.maritalStatus,
      1.0,
      7056
    );

    const wealthTax = calculateWealthTax(
      userData.currentSavings,
      userData.canton,
      userData.maritalStatus
    );

    const totalTaxBurden =
      socialInsurance.totalEmployee + incomeTax.total + wealthTax.total;
    const netIncome = annualIncome - totalTaxBurden;
    const effectiveRate = (totalTaxBurden / annualIncome) * 100;

    return {
      grossIncome: annualIncome,
      socialInsurance,
      incomeTax,
      wealthTax,
      totalTaxBurden,
      netIncome,
      effectiveRate,
      taxableIncome,
    };
  }, [
    annualIncome,
    userData.currentAge,
    userData.canton,
    userData.maritalStatus,
    userData.currentSavings,
  ]);

  // Generate tax planning scenarios
  useEffect(() => {
    const generateScenarios = () => {
      const scenarios: TaxPlanningScenario[] = [];

      // Current scenario
      scenarios.push({
        id: 'current',
        name: 'Current Situation',
        description: 'Your current tax situation with minimal optimization',
        grossIncome: annualIncome,
        deductions: {
          pillar3a: 3000, // Assumed current
          professional: 2000, // Basic professional expenses
          insurance: annualIncome * 0.05, // 5% for insurance
          medical: 0,
          donations: 0,
          interest: 0,
        },
        socialInsurance: currentTaxSituation.socialInsurance,
        incomeTax: currentTaxSituation.incomeTax,
        wealthTax: currentTaxSituation.wealthTax,
        netIncome: currentTaxSituation.netIncome,
        totalTaxBurden: currentTaxSituation.totalTaxBurden,
        effectiveRate: currentTaxSituation.effectiveRate,
        savings: 0,
      });

      // Optimized scenario
      const optimizedDeductions = {
        pillar3a: 7056, // Maximum Pillar 3a
        professional: Math.min(annualIncome * 0.2, 4000), // Maximum professional expenses
        insurance: annualIncome * 0.08, // Optimized insurance
        medical: annualIncome * 0.03, // Some medical expenses
        donations: annualIncome * 0.05, // 5% donations
        interest: 0,
      };

      const totalOptimizedDeductions = Object.values(
        optimizedDeductions
      ).reduce((sum, val) => sum + val, 0);
      const optimizedSocialInsurance = calculateSocialInsurance(
        annualIncome,
        userData.currentAge
      );
      const optimizedTaxableIncome =
        annualIncome -
        optimizedSocialInsurance.totalEmployee -
        totalOptimizedDeductions;

      const optimizedIncomeTax = calculateSwissIncomeTax(
        optimizedTaxableIncome,
        userData.canton,
        userData.maritalStatus,
        1.0,
        totalOptimizedDeductions
      );

      const optimizedTotalTaxBurden =
        optimizedSocialInsurance.totalEmployee +
        optimizedIncomeTax.total +
        currentTaxSituation.wealthTax.total;
      const optimizedNetIncome = annualIncome - optimizedTotalTaxBurden;
      const optimizedEffectiveRate =
        (optimizedTotalTaxBurden / annualIncome) * 100;

      scenarios.push({
        id: 'optimized',
        name: 'Optimized (Same Canton)',
        description: 'Maximized deductions and tax-efficient strategies',
        grossIncome: annualIncome,
        deductions: optimizedDeductions,
        socialInsurance: optimizedSocialInsurance,
        incomeTax: optimizedIncomeTax,
        wealthTax: currentTaxSituation.wealthTax,
        netIncome: optimizedNetIncome,
        totalTaxBurden: optimizedTotalTaxBurden,
        effectiveRate: optimizedEffectiveRate,
        savings: currentTaxSituation.totalTaxBurden - optimizedTotalTaxBurden,
      });

      // High income scenario
      const highIncome = annualIncome * 1.5;
      const highIncomeSocialInsurance = calculateSocialInsurance(
        highIncome,
        userData.currentAge
      );
      const highIncomeTaxableIncome =
        highIncome -
        highIncomeSocialInsurance.totalEmployee -
        totalOptimizedDeductions;

      const highIncomeIncomeTax = calculateSwissIncomeTax(
        highIncomeTaxableIncome,
        userData.canton,
        userData.maritalStatus,
        1.0,
        totalOptimizedDeductions
      );

      const highIncomeWealthTax = calculateWealthTax(
        userData.currentSavings * 1.5,
        userData.canton,
        userData.maritalStatus
      );

      const highIncomeTotalTaxBurden =
        highIncomeSocialInsurance.totalEmployee +
        highIncomeIncomeTax.total +
        highIncomeWealthTax.total;
      const highIncomeNetIncome = highIncome - highIncomeTotalTaxBurden;
      const highIncomeEffectiveRate =
        (highIncomeTotalTaxBurden / highIncome) * 100;

      scenarios.push({
        id: 'high-income',
        name: 'High Income Scenario (+50%)',
        description: 'Impact of 50% income increase on tax burden',
        grossIncome: highIncome,
        deductions: {
          ...optimizedDeductions,
          professional: Math.min(highIncome * 0.2, 4000),
          insurance: highIncome * 0.08,
          donations: highIncome * 0.05,
        },
        socialInsurance: highIncomeSocialInsurance,
        incomeTax: highIncomeIncomeTax,
        wealthTax: highIncomeWealthTax,
        netIncome: highIncomeNetIncome,
        totalTaxBurden: highIncomeTotalTaxBurden,
        effectiveRate: highIncomeEffectiveRate,
        savings: 0,
      });

      return scenarios;
    };

    setIsAnalyzing(true);
    setTimeout(() => {
      setTaxScenarios(generateScenarios());
      setIsAnalyzing(false);
    }, 1500);
  }, [userData, annualIncome, currentTaxSituation]);

  const selectedScenarioData = taxScenarios.find(
    s => s.id === selectedScenario
  );

  if (isAnalyzing) {
    return (
      <div
        className={`p-6 rounded-lg border ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}
      >
        <div className='text-center'>
          <div className='animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4'></div>
          <h3
            className={`text-lg font-semibold mb-2 ${darkMode ? 'text-white' : 'text-gray-900'}`}
          >
            🏛️ Swiss Tax Planning Dashboard
          </h3>
          <p className={`${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
            Analyzing comprehensive Swiss tax planning strategies...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div
        className={`p-6 rounded-lg border ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}
      >
        <h3
          className={`text-xl font-bold mb-2 ${darkMode ? 'text-white' : 'text-gray-900'}`}
        >
          🏛️ Swiss Tax Planning Dashboard
        </h3>
        <p className={`${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
          Comprehensive Swiss tax planning with social insurance, deductions,
          and optimization strategies
        </p>
      </div>

      {/* Navigation Tabs */}
      <div
        className={`p-6 rounded-lg border ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}
      >
        <div className='flex flex-wrap gap-2'>
          {[
            {
              id: 'overview',
              name: '📊 Overview',
              description: 'Tax situation summary',
            },
            {
              id: 'optimization',
              name: '🎯 Optimization',
              description: 'Tax optimization strategies',
            },
            {
              id: 'deductions',
              name: '📋 Deductions',
              description: 'Deduction optimizer',
            },
            {
              id: 'scenarios',
              name: '📈 Scenarios',
              description: 'Tax planning scenarios',
            },
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                activeTab === tab.id
                  ? darkMode
                    ? 'bg-blue-600 text-white'
                    : 'bg-blue-600 text-white'
                  : darkMode
                    ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
              title={tab.description}
            >
              {tab.name}
            </button>
          ))}
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div className='space-y-6'>
          {/* Current Tax Situation Overview */}
          <div
            className={`p-6 rounded-lg border ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}
          >
            <h4
              className={`text-lg font-semibold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}
            >
              💰 Current Tax Situation
            </h4>
            <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4'>
              <div
                className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}
              >
                <div
                  className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}
                >
                  Gross Annual Income
                </div>
                <div
                  className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}
                >
                  CHF {currentTaxSituation.grossIncome.toLocaleString()}
                </div>
              </div>
              <div
                className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}
              >
                <div
                  className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}
                >
                  Social Insurance
                </div>
                <div
                  className={`text-xl font-bold ${darkMode ? 'text-red-400' : 'text-red-600'}`}
                >
                  CHF{' '}
                  {currentTaxSituation.socialInsurance.totalEmployee.toLocaleString()}
                </div>
              </div>
              <div
                className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}
              >
                <div
                  className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}
                >
                  Income + Wealth Tax
                </div>
                <div
                  className={`text-xl font-bold ${darkMode ? 'text-red-400' : 'text-red-600'}`}
                >
                  CHF{' '}
                  {(
                    currentTaxSituation.incomeTax.total +
                    currentTaxSituation.wealthTax.total
                  ).toLocaleString()}
                </div>
              </div>
              <div
                className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}
              >
                <div
                  className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}
                >
                  Net Annual Income
                </div>
                <div
                  className={`text-xl font-bold ${darkMode ? 'text-green-400' : 'text-green-600'}`}
                >
                  CHF {currentTaxSituation.netIncome.toLocaleString()}
                </div>
              </div>
            </div>

            <div className='mt-4 grid grid-cols-1 md:grid-cols-2 gap-4'>
              <div
                className={`p-4 rounded-lg ${darkMode ? 'bg-blue-900/20 border border-blue-700' : 'bg-blue-50 border border-blue-200'}`}
              >
                <div
                  className={`text-sm ${darkMode ? 'text-blue-300' : 'text-blue-600'}`}
                >
                  Total Tax Burden
                </div>
                <div
                  className={`text-2xl font-bold ${darkMode ? 'text-blue-400' : 'text-blue-700'}`}
                >
                  CHF {currentTaxSituation.totalTaxBurden.toLocaleString()}
                </div>
                <div
                  className={`text-sm ${darkMode ? 'text-blue-300' : 'text-blue-600'}`}
                >
                  {currentTaxSituation.effectiveRate.toFixed(1)}% effective rate
                </div>
              </div>
              <div
                className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}
              >
                <div
                  className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}
                >
                  Tax Breakdown
                </div>
                <div className='space-y-1 text-sm'>
                  <div className='flex justify-between'>
                    <span
                      className={`${darkMode ? 'text-gray-300' : 'text-gray-600'}`}
                    >
                      Social Insurance:
                    </span>
                    <span
                      className={`${darkMode ? 'text-white' : 'text-gray-900'}`}
                    >
                      {(
                        (currentTaxSituation.socialInsurance.totalEmployee /
                          currentTaxSituation.totalTaxBurden) *
                        100
                      ).toFixed(1)}
                      %
                    </span>
                  </div>
                  <div className='flex justify-between'>
                    <span
                      className={`${darkMode ? 'text-gray-300' : 'text-gray-600'}`}
                    >
                      Income Tax:
                    </span>
                    <span
                      className={`${darkMode ? 'text-white' : 'text-gray-900'}`}
                    >
                      {(
                        (currentTaxSituation.incomeTax.total /
                          currentTaxSituation.totalTaxBurden) *
                        100
                      ).toFixed(1)}
                      %
                    </span>
                  </div>
                  <div className='flex justify-between'>
                    <span
                      className={`${darkMode ? 'text-gray-300' : 'text-gray-600'}`}
                    >
                      Wealth Tax:
                    </span>
                    <span
                      className={`${darkMode ? 'text-white' : 'text-gray-900'}`}
                    >
                      {(
                        (currentTaxSituation.wealthTax.total /
                          currentTaxSituation.totalTaxBurden) *
                        100
                      ).toFixed(1)}
                      %
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Social Insurance Breakdown */}
          <div
            className={`p-6 rounded-lg border ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}
          >
            <h4
              className={`text-lg font-semibold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}
            >
              🛡️ Social Insurance Breakdown
            </h4>
            <div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
              <div
                className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}
              >
                <div
                  className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}
                >
                  AHV/IV/EO
                </div>
                <div
                  className={`text-lg font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}
                >
                  CHF{' '}
                  {currentTaxSituation.socialInsurance.ahv.employee.toLocaleString()}
                </div>
                <div
                  className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}
                >
                  4.35% rate
                </div>
              </div>
              <div
                className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}
              >
                <div
                  className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}
                >
                  ALV
                </div>
                <div
                  className={`text-lg font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}
                >
                  CHF{' '}
                  {currentTaxSituation.socialInsurance.alv.employee.toLocaleString()}
                </div>
                <div
                  className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}
                >
                  1.1% rate
                </div>
              </div>
              <div
                className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}
              >
                <div
                  className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}
                >
                  NBU
                </div>
                <div
                  className={`text-lg font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}
                >
                  CHF{' '}
                  {currentTaxSituation.socialInsurance.nbu.employee.toLocaleString()}
                </div>
                <div
                  className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}
                >
                  ~1.0% rate
                </div>
              </div>
              <div
                className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}
              >
                <div
                  className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}
                >
                  Pension Fund
                </div>
                <div
                  className={`text-lg font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}
                >
                  CHF{' '}
                  {currentTaxSituation.socialInsurance.pensionFund.employee.toLocaleString()}
                </div>
                <div
                  className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}
                >
                  Age-based rate
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'optimization' && (
        <SwissTaxOptimizationEngine
          darkMode={darkMode}
          userData={{
            currentAge: userData.currentAge,
            monthlyIncome: userData.monthlyIncome,
            currentSavings: userData.currentSavings,
            canton: userData.canton,
            maritalStatus: userData.maritalStatus,
            children: userData.children,
            pillar3aContribution: 3000, // Default assumption
          }}
        />
      )}

      {activeTab === 'deductions' && (
        <SwissTaxDeductionOptimizer
          darkMode={darkMode}
          userData={{
            annualIncome,
            canton: userData.canton,
            maritalStatus: userData.maritalStatus,
            children: userData.children,
            currentPillar3a: 3000,
            professionalExpenses: 2000,
            insurancePremiums: annualIncome * 0.05,
            medicalExpenses: 0,
            donations: 0,
            interestExpenses: 0,
          }}
          onDeductionChange={deductions => {
            console.log('Deduction changes:', deductions);
          }}
        />
      )}

      {activeTab === 'scenarios' && (
        <div className='space-y-6'>
          {/* Scenario Selection */}
          <div
            className={`p-6 rounded-lg border ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}
          >
            <h4
              className={`text-lg font-semibold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}
            >
              📈 Tax Planning Scenarios
            </h4>
            <div className='flex flex-wrap gap-2 mb-4'>
              {taxScenarios.map(scenario => (
                <button
                  key={scenario.id}
                  onClick={() => setSelectedScenario(scenario.id)}
                  className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                    selectedScenario === scenario.id
                      ? darkMode
                        ? 'bg-blue-600 text-white'
                        : 'bg-blue-600 text-white'
                      : darkMode
                        ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {scenario.name}
                  {scenario.savings > 0 && (
                    <span
                      className={`ml-2 text-xs px-2 py-1 rounded ${
                        selectedScenario === scenario.id
                          ? 'bg-white/20'
                          : 'bg-green-100 text-green-800'
                      }`}
                    >
                      +CHF {Math.round(scenario.savings)}
                    </span>
                  )}
                </button>
              ))}
            </div>

            {/* Selected Scenario Details */}
            {selectedScenarioData && (
              <div
                className={`p-4 rounded-lg border ${darkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'}`}
              >
                <h5
                  className={`text-lg font-semibold mb-2 ${darkMode ? 'text-white' : 'text-gray-900'}`}
                >
                  {selectedScenarioData.name}
                </h5>
                <p
                  className={`text-sm mb-4 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}
                >
                  {selectedScenarioData.description}
                </p>

                <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
                  <div>
                    <div
                      className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}
                    >
                      Gross Income
                    </div>
                    <div
                      className={`text-lg font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}
                    >
                      CHF {selectedScenarioData.grossIncome.toLocaleString()}
                    </div>
                  </div>
                  <div>
                    <div
                      className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}
                    >
                      Total Tax Burden
                    </div>
                    <div
                      className={`text-lg font-bold ${darkMode ? 'text-red-400' : 'text-red-600'}`}
                    >
                      CHF {selectedScenarioData.totalTaxBurden.toLocaleString()}
                    </div>
                  </div>
                  <div>
                    <div
                      className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}
                    >
                      Net Income
                    </div>
                    <div
                      className={`text-lg font-bold ${darkMode ? 'text-green-400' : 'text-green-600'}`}
                    >
                      CHF {selectedScenarioData.netIncome.toLocaleString()}
                    </div>
                  </div>
                </div>

                {selectedScenarioData.savings > 0 && (
                  <div
                    className={`mt-4 p-3 rounded-lg ${darkMode ? 'bg-green-900/20 border border-green-700' : 'bg-green-50 border border-green-200'}`}
                  >
                    <div
                      className={`text-sm font-medium ${darkMode ? 'text-green-300' : 'text-green-800'}`}
                    >
                      Annual Tax Savings: CHF{' '}
                      {selectedScenarioData.savings.toLocaleString()}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default SwissTaxPlanningDashboard;
