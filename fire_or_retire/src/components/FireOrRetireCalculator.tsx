/**
 * Fire or Retire Calculator - Main Application Component
 * Swiss Budget Pro - Financial Planning Application
 */

import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useFinancialLogger, useLogger } from '../hooks/useLogger';
import {
  calculateSwissIncomeTax,
  calculateWealthTax,
} from '../utils/swiss-tax-calculations';
import AdditionalIncomeManager from './AdditionalIncomeManager';
import CryptocurrencyPortfolioManager from './CryptocurrencyPortfolioManager';
import DataExportImport from './DataExportImport';
import ExpenseManager from './ExpenseManager';
import HistoricalTrackingCharts from './HistoricalTrackingCharts';
import InvestmentPortfolioManager from './InvestmentPortfolioManager';
import LanguageSwitcher from './LanguageSwitcher';
import MonteCarloSimulation from './MonteCarloSimulation';
import SafeWithdrawalRateAnalysis from './SafeWithdrawalRateAnalysis';
import SavingsGoalsManager from './SavingsGoalsManager';

// Import components

interface FireOrRetireCalculatorProps {
  darkMode?: boolean;
}

// Helper function to safely render components that might not exist
const SafeComponentRenderer = ({
  componentPath: _componentPath,
  props: _props,
  fallback = null,
}: {
  componentPath: string;
  props: any; // eslint-disable-line @typescript-eslint/no-explicit-any
  fallback?: React.ReactNode;
}) => {
  try {
    // For now, return fallback since many components don't exist yet
    return fallback;
  } catch (error) {
    // eslint-disable-next-line no-console
    console.warn(`Component ${_componentPath} not found:`, error);
    return fallback;
  }
};

// Error Boundary Component
class _ErrorBoundary extends React.Component<
  { children: React.ReactNode; darkMode?: boolean },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode; darkMode?: boolean }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // Use console.error for Error Boundary since hooks can't be used in class components
    console.error('🚨 React Error Boundary - FireOrRetireCalculator', {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      errorBoundary: 'FireOrRetireCalculator',
      timestamp: new Date().toISOString(),
    });

    // Dispatch custom event for our logger to catch
    window.dispatchEvent(
      new CustomEvent('react-error', {
        detail: {
          message: error.message,
          stack: error.stack,
          componentStack: errorInfo.componentStack,
          errorBoundary: 'FireOrRetireCalculator',
        },
      })
    );
  }

  render() {
    if (this.state.hasError) {
      return (
        <div
          className={`min-h-screen flex items-center justify-center p-4 ${
            this.props.darkMode
              ? 'bg-gradient-to-br from-gray-900 via-purple-900/25 to-blue-900/25 text-gray-200'
              : 'bg-gradient-to-br from-blue-50 via-purple-50/40 to-pink-50 text-gray-800'
          }`}
        >
          <div
            className={`max-w-md w-full p-6 rounded-xl text-center ${
              this.props.darkMode ? 'bg-gray-800/60' : 'bg-white/80 shadow-sm'
            }`}
          >
            <div className='text-6xl mb-4'>⚠️</div>
            <h2 className='text-2xl font-bold mb-4'>Something went wrong</h2>
            <p
              className={`mb-4 ${this.props.darkMode ? 'text-gray-400' : 'text-gray-600'}`}
            >
              The Fire or Retire Calculator encountered an error. Please refresh
              the page to try again.
            </p>
            <button
              onClick={() => window.location.reload()}
              className='px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors'
            >
              Refresh Page
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

const FireOrRetireCalculatorComponent: React.FC<
  FireOrRetireCalculatorProps
> = ({ darkMode: initialDarkMode = true }) => {
  // Initialize logging hooks
  const {
    logInfo,
    logError,
    logWarn: _logWarn,
    trackUserAction: _trackAction,
    measureAsync: _measureAsync,
  } = useLogger('FireOrRetireCalculator');
  const {
    logCalculation: _logCalculation,
    measureCalculation: _measureCalculation,
  } = useFinancialLogger('FIRE');

  logInfo('Component initializing', { darkMode: initialDarkMode });

  const { t, i18n } = useTranslation();
  const [darkMode, setDarkMode] = useState(initialDarkMode);
  const [activeTab, setActiveTab] = useState('dashboard');
  const [secondaryTab, _setSecondaryTab] = useState('income');
  const [showDebugPanel, setShowDebugPanel] = useState(false);

  // State for expenses, savings goals, investments, and additional income
  const [expenses, setExpenses] = useState<any[]>([]); // eslint-disable-line @typescript-eslint/no-explicit-any
  const [savingsGoals, setSavingsGoals] = useState<any[]>([]); // eslint-disable-line @typescript-eslint/no-explicit-any
  const [investments, setInvestments] = useState<any[]>([]); // eslint-disable-line @typescript-eslint/no-explicit-any
  const [additionalIncomes, setAdditionalIncomes] = useState<any[]>([]); // eslint-disable-line @typescript-eslint/no-explicit-any

  // UX state
  const [_showOnboarding, setShowOnboarding] = useState(false);
  const [_showSmartDashboard, _setShowSmartDashboard] = useState(false);

  // Onboarding completion handler
  const handleOnboardingComplete = (data: any) => {
    // Apply onboarding data to state
    if (data.personalInfo) {
      setCurrentAge(data.personalInfo.age);
      setRetirementAge(data.personalInfo.retirementAge);
    }
    if (data.financialInfo) {
      setMonthlyIncome(data.financialInfo.monthlyIncome);
      setCurrentSavings(data.financialInfo.currentSavings);
      setMonthlyExpenses(data.financialInfo.monthlyExpenses);
    }
    setShowOnboarding(false);
  };

  console.log('📊 Component state initialized:', {
    darkMode,
    activeTab,
    secondaryTab,
    showDebugPanel,
  });

  // Financial state with localStorage persistence
  const [currentAge, setCurrentAge] = useState(() => {
    try {
      const saved = localStorage.getItem('fire-current-age');
      const value = saved ? parseInt(saved) || 30 : 30;
      console.log('✅ Loaded currentAge from localStorage:', value);
      return value;
    } catch (error) {
      logError('localStorage Load - currentAge', error, {
        key: 'fire-current-age',
      });
      return 30;
    }
  });
  const [retirementAge, setRetirementAge] = useState(() => {
    try {
      const saved = localStorage.getItem('fire-retirement-age');
      return saved ? parseInt(saved) || 65 : 65;
    } catch (error) {
      console.warn('Failed to load retirement age from localStorage:', error);
      return 65;
    }
  });
  const [currentSavings, setCurrentSavings] = useState(() => {
    try {
      const saved = localStorage.getItem('fire-current-savings');
      return saved ? parseInt(saved) || 50000 : 50000;
    } catch (error) {
      console.warn('Failed to load current savings from localStorage:', error);
      return 50000;
    }
  });
  const [monthlyIncome, setMonthlyIncome] = useState(() => {
    try {
      const saved = localStorage.getItem('fire-monthly-income');
      return saved ? parseInt(saved) || 8000 : 8000;
    } catch (error) {
      console.warn('Failed to load monthly income from localStorage:', error);
      return 8000;
    }
  });
  const [monthlyExpenses, setMonthlyExpenses] = useState(() => {
    try {
      const saved = localStorage.getItem('fire-monthly-expenses');
      return saved ? parseInt(saved) || 5000 : 5000;
    } catch (error) {
      console.warn('Failed to load monthly expenses from localStorage:', error);
      return 5000;
    }
  });
  const [expectedReturn, setExpectedReturn] = useState(() => {
    try {
      const saved = localStorage.getItem('fire-expected-return');
      return saved ? parseFloat(saved) || 7 : 7;
    } catch (error) {
      console.warn('Failed to load expected return from localStorage:', error);
      return 7;
    }
  });
  const [inflationRate, setInflationRate] = useState(() => {
    try {
      const saved = localStorage.getItem('fire-inflation-rate');
      return saved ? parseFloat(saved) || 2 : 2;
    } catch (error) {
      console.warn('Failed to load inflation rate from localStorage:', error);
      return 2;
    }
  });
  const [safeWithdrawalRate, setSafeWithdrawalRate] = useState(() => {
    try {
      const saved = localStorage.getItem('fire-withdrawal-rate');
      return saved ? parseFloat(saved) || 4 : 4;
    } catch (error) {
      console.warn('Failed to load withdrawal rate from localStorage:', error);
      return 4;
    }
  });

  // Additional financial state for comprehensive features
  const [currentPension, setCurrentPension] = useState(() => {
    try {
      const saved = localStorage.getItem('fire-current-pension');
      return saved ? parseInt(saved) || 0 : 0;
    } catch (error) {
      logError('localStorage Load - currentPension', error, {
        key: 'fire-current-pension',
      });
      return 0;
    }
  });

  const [grossMonthlyIncome, setGrossMonthlyIncome] = useState(() => {
    try {
      const saved = localStorage.getItem('fire-gross-monthly-income');
      return saved ? parseInt(saved) || 10000 : 10000;
    } catch (error) {
      logError('localStorage Load - grossMonthlyIncome', error, {
        key: 'fire-gross-monthly-income',
      });
      return 10000;
    }
  });

  const [incomePercentage, setIncomePercentage] = useState(() => {
    try {
      const saved = localStorage.getItem('fire-income-percentage');
      return saved ? parseInt(saved) || 100 : 100;
    } catch (error) {
      logError('localStorage Load - incomePercentage', error, {
        key: 'fire-income-percentage',
      });
      return 100;
    }
  });

  const [companyIncome, setCompanyIncome] = useState(() => {
    try {
      const saved = localStorage.getItem('fire-company-income');
      return saved ? parseInt(saved) || 0 : 0;
    } catch (error) {
      logError('localStorage Load - companyIncome', error, {
        key: 'fire-company-income',
      });
      return 0;
    }
  });

  const [companyIncomeStartYear, setCompanyIncomeStartYear] = useState(() => {
    try {
      const saved = localStorage.getItem('fire-company-income-start-year');
      return saved ? parseInt(saved) || 2025 : 2025;
    } catch (error) {
      logError('localStorage Load - companyIncomeStartYear', error, {
        key: 'fire-company-income-start-year',
      });
      return 2025;
    }
  });

  const [companyIncomeGrowthRate, setCompanyIncomeGrowthRate] = useState(() => {
    try {
      const saved = localStorage.getItem('fire-company-income-growth-rate');
      return saved ? parseFloat(saved) || 5 : 5;
    } catch (error) {
      logError('localStorage Load - companyIncomeGrowthRate', error, {
        key: 'fire-company-income-growth-rate',
      });
      return 5;
    }
  });

  // Swiss tax state
  const [canton, setCanton] = useState(() => {
    try {
      const saved = localStorage.getItem('fire-canton');
      return saved || 'ZH';
    } catch (error) {
      logError('localStorage Load - canton', error, {
        key: 'fire-canton',
      });
      return 'ZH';
    }
  });

  const [maritalStatus, setMaritalStatus] = useState(() => {
    try {
      const saved = localStorage.getItem('fire-marital-status');
      return saved || 'single';
    } catch (error) {
      logError('localStorage Load - maritalStatus', error, {
        key: 'fire-marital-status',
      });
      return 'single';
    }
  });

  const [netWorth, setNetWorth] = useState(() => {
    try {
      const saved = localStorage.getItem('fire-net-worth');
      return saved ? parseInt(saved) || 200000 : 200000;
    } catch (error) {
      logError('localStorage Load - netWorth', error, {
        key: 'fire-net-worth',
      });
      return 200000;
    }
  });

  const [pillar3aContribution, setPillar3aContribution] = useState(() => {
    try {
      const saved = localStorage.getItem('fire-pillar3a-contribution');
      return saved ? parseInt(saved) || 7056 : 7056;
    } catch (error) {
      logError('localStorage Load - pillar3aContribution', error, {
        key: 'fire-pillar3a-contribution',
      });
      return 7056;
    }
  });

  // Save to localStorage when values change
  useEffect(() => {
    try {
      localStorage.setItem('fire-current-age', currentAge.toString());
      console.log('✅ Saved currentAge to localStorage:', currentAge);
    } catch (error) {
      logError('localStorage Save - currentAge', error, {
        key: 'fire-current-age',
        value: currentAge,
      });
    }
  }, [currentAge]);

  useEffect(() => {
    try {
      localStorage.setItem('fire-retirement-age', retirementAge.toString());
    } catch (error) {
      console.warn('Failed to save retirement age to localStorage:', error);
    }
  }, [retirementAge]);

  useEffect(() => {
    try {
      localStorage.setItem('fire-current-savings', currentSavings.toString());
    } catch (error) {
      console.warn('Failed to save current savings to localStorage:', error);
    }
  }, [currentSavings]);

  useEffect(() => {
    try {
      localStorage.setItem('fire-monthly-income', monthlyIncome.toString());
    } catch (error) {
      console.warn('Failed to save monthly income to localStorage:', error);
    }
  }, [monthlyIncome]);

  useEffect(() => {
    try {
      localStorage.setItem('fire-monthly-expenses', monthlyExpenses.toString());
    } catch (error) {
      console.warn('Failed to save monthly expenses to localStorage:', error);
    }
  }, [monthlyExpenses]);

  useEffect(() => {
    try {
      localStorage.setItem('fire-expected-return', expectedReturn.toString());
    } catch (error) {
      console.warn('Failed to save expected return to localStorage:', error);
    }
  }, [expectedReturn]);

  useEffect(() => {
    try {
      localStorage.setItem('fire-inflation-rate', inflationRate.toString());
    } catch (error) {
      console.warn('Failed to save inflation rate to localStorage:', error);
    }
  }, [inflationRate]);

  useEffect(() => {
    try {
      localStorage.setItem(
        'fire-withdrawal-rate',
        safeWithdrawalRate.toString()
      );
    } catch (error) {
      console.warn('Failed to save withdrawal rate to localStorage:', error);
    }
  }, [safeWithdrawalRate]);

  useEffect(() => {
    try {
      localStorage.setItem('fire-current-pension', currentPension.toString());
      console.log('✅ Saved currentPension to localStorage:', currentPension);
    } catch (error) {
      logError('localStorage Save - currentPension', error, {
        key: 'fire-current-pension',
        value: currentPension,
      });
    }
  }, [currentPension]);

  useEffect(() => {
    try {
      localStorage.setItem(
        'fire-gross-monthly-income',
        grossMonthlyIncome.toString()
      );
    } catch (error) {
      logError('localStorage Save - grossMonthlyIncome', error, {
        key: 'fire-gross-monthly-income',
        value: grossMonthlyIncome,
      });
    }
  }, [grossMonthlyIncome]);

  useEffect(() => {
    try {
      localStorage.setItem(
        'fire-income-percentage',
        incomePercentage.toString()
      );
    } catch (error) {
      logError('localStorage Save - incomePercentage', error, {
        key: 'fire-income-percentage',
        value: incomePercentage,
      });
    }
  }, [incomePercentage]);

  useEffect(() => {
    try {
      localStorage.setItem('fire-company-income', companyIncome.toString());
    } catch (error) {
      logError('localStorage Save - companyIncome', error, {
        key: 'fire-company-income',
        value: companyIncome,
      });
    }
  }, [companyIncome]);

  useEffect(() => {
    try {
      localStorage.setItem(
        'fire-company-income-start-year',
        companyIncomeStartYear.toString()
      );
    } catch (error) {
      logError('localStorage Save - companyIncomeStartYear', error, {
        key: 'fire-company-income-start-year',
        value: companyIncomeStartYear,
      });
    }
  }, [companyIncomeStartYear]);

  useEffect(() => {
    try {
      localStorage.setItem(
        'fire-company-income-growth-rate',
        companyIncomeGrowthRate.toString()
      );
    } catch (error) {
      logError('localStorage Save - companyIncomeGrowthRate', error, {
        key: 'fire-company-income-growth-rate',
        value: companyIncomeGrowthRate,
      });
    }
  }, [companyIncomeGrowthRate]);

  // Save Swiss tax state to localStorage
  useEffect(() => {
    try {
      localStorage.setItem('fire-canton', canton);
    } catch (error) {
      logError('localStorage Save - canton', error, {
        key: 'fire-canton',
        value: canton,
      });
    }
  }, [canton]);

  useEffect(() => {
    try {
      localStorage.setItem('fire-marital-status', maritalStatus);
    } catch (error) {
      logError('localStorage Save - maritalStatus', error, {
        key: 'fire-marital-status',
        value: maritalStatus,
      });
    }
  }, [maritalStatus]);

  useEffect(() => {
    try {
      localStorage.setItem('fire-net-worth', netWorth.toString());
    } catch (error) {
      logError('localStorage Save - netWorth', error, {
        key: 'fire-net-worth',
        value: netWorth,
      });
    }
  }, [netWorth]);

  useEffect(() => {
    try {
      localStorage.setItem(
        'fire-pillar3a-contribution',
        pillar3aContribution.toString()
      );
    } catch (error) {
      logError('localStorage Save - pillar3aContribution', error, {
        key: 'fire-pillar3a-contribution',
        value: pillar3aContribution,
      });
    }
  }, [pillar3aContribution]);

  // Enhanced calculations with pension and company income
  const effectiveGrossIncome = (grossMonthlyIncome * incomePercentage) / 100;
  const monthlyPensionContribution = effectiveGrossIncome * 0.17; // 17% of gross income

  const currentYear = new Date().getFullYear();
  const companyIncomeStartAge = Math.max(
    currentAge,
    currentAge + (companyIncomeStartYear - currentYear)
  );

  // Calculate current company income if applicable
  const currentCompanyIncomeMonthly =
    currentAge >= companyIncomeStartAge ? companyIncome : 0;

  // Monthly income is post-expenses (net available for savings/investments)
  const effectiveMonthlyIncome = (monthlyIncome * incomePercentage) / 100;

  // Total current income including all sources (post-expenses)
  const totalCurrentIncome =
    effectiveMonthlyIncome + currentCompanyIncomeMonthly;

  // Monthly savings is the post-expense income minus additional expenses
  const monthlySavings = totalCurrentIncome - monthlyExpenses;
  const savingsRate =
    effectiveGrossIncome > 0
      ? (totalCurrentIncome / effectiveGrossIncome) * 100
      : 0;
  const yearsToRetirement = retirementAge - currentAge;

  // Enhanced FIRE calculation including pension
  const totalCurrentAssets = currentSavings + currentPension;
  const targetFIREAmount = (monthlyExpenses * 12) / (safeWithdrawalRate / 100);
  const fireProgress =
    totalCurrentAssets > 0 ? (totalCurrentAssets / targetFIREAmount) * 100 : 0;

  // Pension projection with compound growth
  const annualPensionContribution = monthlyPensionContribution * 12;
  const pensionGrowthRate = expectedReturn / 100;
  const pensionProjection =
    yearsToRetirement > 0
      ? currentPension * Math.pow(1 + pensionGrowthRate, yearsToRetirement) +
        (pensionGrowthRate > 0
          ? (annualPensionContribution *
              (Math.pow(1 + pensionGrowthRate, yearsToRetirement) - 1)) /
            pensionGrowthRate
          : annualPensionContribution * yearsToRetirement)
      : currentPension;
  const totalProjectedAssets = currentSavings + pensionProjection;

  // Swiss tax calculations using existing utility functions
  const calculateSwissTaxes = () => {
    try {
      // Use the imported functions directly
      const annualGrossIncome = effectiveGrossIncome * 12;
      const deductions = pillar3aContribution + 2000; // Basic professional expenses

      // Calculate income tax
      const incomeTaxResult = calculateSwissIncomeTax(
        annualGrossIncome,
        canton,
        maritalStatus,
        1.0, // Municipal multiplier
        deductions
      );

      // Calculate wealth tax
      const wealthTaxResult = calculateWealthTax(
        netWorth,
        canton,
        maritalStatus
      );

      return {
        incomeTax: incomeTaxResult,
        wealthTax: wealthTaxResult,
        totalAnnualTax: incomeTaxResult.total + wealthTaxResult.total,
        effectiveTaxRate: incomeTaxResult.effectiveRate * 100,
        netAnnualIncome: annualGrossIncome - incomeTaxResult.total,
      };
    } catch (error) {
      logError('Swiss Tax Calculation', error, {
        canton,
        maritalStatus,
        grossIncome: effectiveGrossIncome * 12,
        netWorth,
      });
      return {
        incomeTax: {
          federal: 0,
          cantonal: 0,
          municipal: 0,
          total: 0,
          effectiveRate: 0,
        },
        wealthTax: { cantonal: 0, municipal: 0, total: 0 },
        totalAnnualTax: 0,
        effectiveTaxRate: 0,
        netAnnualIncome: effectiveGrossIncome * 12,
      };
    }
  };

  const swissTaxData = calculateSwissTaxes();

  // Smart Input Component that clears on focus
  const SmartInput = ({
    value,
    onChange,
    type = 'number',
    step,
    className,
    placeholder,
    ...props
  }: any) => {
    const [isFocused, setIsFocused] = useState(false);
    const [tempValue, setTempValue] = useState('');

    const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
      setIsFocused(true);
      setTempValue('');
      e.target.select(); // Select all text on focus
    };

    const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
      setIsFocused(false);
      if (tempValue === '') {
        // If user didn't type anything, keep original value
        setTempValue(value.toString());
      } else {
        // Apply the new value
        const newValue =
          type === 'number' ? parseFloat(tempValue) || 0 : tempValue;
        onChange({ target: { value: newValue } });
      }
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setTempValue(e.target.value);
      if (!isFocused) {
        // If not focused, update immediately (for programmatic changes)
        const newValue =
          type === 'number' ? parseFloat(e.target.value) || 0 : e.target.value;
        onChange({ target: { value: newValue } });
      }
    };

    const displayValue = isFocused ? tempValue : value.toString();

    return (
      <input
        type={type}
        step={step}
        value={displayValue}
        onChange={handleChange}
        onFocus={handleFocus}
        onBlur={handleBlur}
        className={className}
        placeholder={placeholder}
        {...props}
      />
    );
  };

  // Tab button component
  const TabButton = (
    { id: _id, label, icon, active, onClick }: any // eslint-disable-line @typescript-eslint/no-explicit-any
  ) => (
    <button
      onClick={onClick}
      className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
        active
          ? darkMode
            ? 'bg-blue-600 text-white shadow-lg'
            : 'bg-blue-600 text-white shadow-lg'
          : darkMode
            ? 'bg-gray-700 hover:bg-gray-600 text-gray-300'
            : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
      }`}
    >
      <span>{icon}</span>
      <span className='hidden sm:inline'>{label}</span>
    </button>
  );

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('de-CH', {
      style: 'currency',
      currency: 'CHF',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Debug Panel Component
  const DebugPanel = () => {
    const [debugErrors, setDebugErrors] = useState<any[]>([]);

    useEffect(() => {
      try {
        const errors = JSON.parse(
          localStorage.getItem('fire-debug-errors') || '[]'
        );
        setDebugErrors(errors);
      } catch (error) {
        console.warn('Failed to load debug errors:', error);
      }
    }, [showDebugPanel]);

    const clearErrors = () => {
      try {
        localStorage.removeItem('fire-debug-errors');
        setDebugErrors([]);
        console.log('🧹 Debug errors cleared');
      } catch (error) {
        console.warn('Failed to clear debug errors:', error);
      }
    };

    if (!showDebugPanel) return null;

    return (
      <div className='fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4'>
        <div
          className={`max-w-4xl w-full max-h-[80vh] overflow-hidden rounded-xl ${
            darkMode ? 'bg-gray-800 text-white' : 'bg-white text-gray-800'
          }`}
        >
          <div className='p-4 border-b border-gray-300 dark:border-gray-600 flex justify-between items-center'>
            <h3 className='text-xl font-bold'>🐛 Debug Panel</h3>
            <div className='flex space-x-2'>
              <button
                onClick={clearErrors}
                className='px-3 py-1 bg-red-600 hover:bg-red-700 text-white rounded text-sm'
              >
                Clear Errors
              </button>
              <button
                onClick={() => setShowDebugPanel(false)}
                className='px-3 py-1 bg-gray-600 hover:bg-gray-700 text-white rounded text-sm'
              >
                Close
              </button>
            </div>
          </div>
          <div className='p-4 overflow-y-auto max-h-[60vh]'>
            {debugErrors.length === 0 ? (
              <p className='text-green-600'>
                ✅ No errors logged! Application is running smoothly.
              </p>
            ) : (
              <div className='space-y-4'>
                {debugErrors.map((error, index) => (
                  <div
                    key={index}
                    className={`p-3 rounded border-l-4 border-red-500 ${
                      darkMode ? 'bg-red-900/20' : 'bg-red-50'
                    }`}
                  >
                    <div className='flex justify-between items-start mb-2'>
                      <h4 className='font-semibold text-red-600'>
                        {error.context}
                      </h4>
                      <span className='text-xs text-gray-500'>
                        {new Date(error.timestamp).toLocaleString()}
                      </span>
                    </div>
                    <p className='text-sm mb-2'>
                      <strong>Message:</strong> {error.error.message}
                    </p>
                    {error.additionalData && (
                      <details className='text-xs'>
                        <summary className='cursor-pointer text-gray-600'>
                          Additional Data
                        </summary>
                        <pre className='mt-2 p-2 bg-gray-100 dark:bg-gray-700 rounded overflow-x-auto'>
                          {JSON.stringify(error.additionalData, null, 2)}
                        </pre>
                      </details>
                    )}
                    {error.error.stack && (
                      <details className='text-xs mt-2'>
                        <summary className='cursor-pointer text-gray-600'>
                          Stack Trace
                        </summary>
                        <pre className='mt-2 p-2 bg-gray-100 dark:bg-gray-700 rounded overflow-x-auto text-xs'>
                          {error.error.stack}
                        </pre>
                      </details>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  // Render dashboard content
  const renderDashboard = () => (
    <div className='space-y-6'>
      {/* Key Metrics */}
      <div
        className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 p-6 rounded-xl ${
          darkMode ? 'bg-gray-800/60' : 'bg-white/80 shadow-sm'
        }`}
      >
        <div className='text-center'>
          <div className='text-3xl font-bold text-green-500'>
            {formatCurrency(totalCurrentAssets)}
          </div>
          <div
            className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}
          >
            {t('fire.metrics.totalAssets', 'Total Assets')}
          </div>
          <div
            className={`text-xs ${darkMode ? 'text-gray-500' : 'text-gray-500'}`}
          >
            Savings: {formatCurrency(currentSavings)} + Pension:{' '}
            {formatCurrency(currentPension)}
          </div>
        </div>
        <div className='text-center'>
          <div className='text-3xl font-bold text-blue-500'>
            {fireProgress.toFixed(1)}%
          </div>
          <div
            className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}
          >
            FIRE Progress
          </div>
        </div>
        <div className='text-center'>
          <div className='text-3xl font-bold text-purple-500'>
            {yearsToRetirement}
          </div>
          <div
            className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}
          >
            Years to Retirement
          </div>
        </div>
        <div className='text-center'>
          <div className='text-3xl font-bold text-yellow-500'>
            {savingsRate.toFixed(1)}%
          </div>
          <div
            className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}
          >
            Savings Rate
          </div>
        </div>
      </div>

      {/* FIRE Progress Bar */}
      <div
        className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800/60' : 'bg-white/80 shadow-sm'}`}
      >
        <h3 className='text-xl font-bold mb-4'>🎯 FIRE Progress</h3>
        <div
          className={`w-full bg-gray-200 rounded-full h-4 ${darkMode ? 'bg-gray-700' : ''}`}
        >
          <div
            className='bg-gradient-to-r from-green-500 to-blue-500 h-4 rounded-full transition-all duration-500'
            style={{ width: `${Math.min(fireProgress, 100)}%` }}
          ></div>
        </div>
        <div className='flex justify-between mt-2 text-sm'>
          <span>Current: {formatCurrency(totalCurrentAssets)}</span>
          <span>Target: {formatCurrency(targetFIREAmount)}</span>
        </div>
        <div className='mt-1 text-xs text-gray-500'>
          Projected at retirement: {formatCurrency(totalProjectedAssets)}
        </div>
      </div>

      {/* Quick Actions */}
      <div
        className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800/60' : 'bg-white/80 shadow-sm'}`}
      >
        <h3 className='text-xl font-bold mb-4'>⚡ Quick Actions</h3>
        <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
          <button
            onClick={() => setActiveTab('planning')}
            className='p-4 rounded-lg bg-blue-600 hover:bg-blue-700 text-white transition-colors'
          >
            <div className='text-2xl mb-2'>💰</div>
            <div className='font-medium'>Update Income & Expenses</div>
          </button>
          <button
            onClick={() => setActiveTab('analysis')}
            className='p-4 rounded-lg bg-purple-600 hover:bg-purple-700 text-white transition-colors'
          >
            <div className='text-2xl mb-2'>📊</div>
            <div className='font-medium'>View Projections</div>
          </button>
          <button
            onClick={() => setActiveTab('healthcare')}
            className='p-4 rounded-lg bg-green-600 hover:bg-green-700 text-white transition-colors'
          >
            <div className='text-2xl mb-2'>🏥</div>
            <div className='font-medium'>Optimize Healthcare</div>
          </button>
        </div>
      </div>

      {/* Smart Dashboard */}
      <div
        className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800/60' : 'bg-white/80 shadow-sm'}`}
      >
        <SafeComponentRenderer
          componentPath='./SmartDashboard'
          props={{
            darkMode: darkMode,
            userData: {
              currentAge,
              retirementAge,
              currentSavings,
              monthlyIncome,
              monthlyExpenses,
              expenses,
              savingsGoals,
              investments,
              additionalIncomes,
            },
          }}
          fallback={
            <div
              className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}
            >
              <p
                className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}
              >
                Smart Dashboard component is being developed...
              </p>
            </div>
          }
        />
      </div>

      {/* Progress Tracker */}
      <div
        className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800/60' : 'bg-white/80 shadow-sm'}`}
      >
        <SafeComponentRenderer
          componentPath='./ProgressTracker'
          props={{
            darkMode: darkMode,
            userData: {
              currentAge,
              retirementAge,
              currentSavings,
              monthlyIncome,
              monthlyExpenses,
              expenses,
              savingsGoals,
              investments,
              additionalIncomes,
            },
          }}
          fallback={
            <div
              className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}
            >
              <p
                className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}
              >
                Progress Tracker component is being developed...
              </p>
            </div>
          }
        />
      </div>
    </div>
  );

  // Render planning content
  const renderPlanning = () => (
    <div className='space-y-6'>
      <div
        className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800/60' : 'bg-white/80 shadow-sm'}`}
      >
        <h3 className='text-xl font-bold mb-4'>💰 Financial Planning</h3>

        <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
          {/* Basic Information */}
          <div className='space-y-4'>
            <h4 className='text-lg font-semibold'>Basic Information</h4>

            <div>
              <label
                className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}
              >
                Current Age
              </label>
              <input
                type='number'
                value={currentAge}
                onChange={e => setCurrentAge(parseInt(e.target.value) || 0)}
                className={`w-full px-3 py-2 rounded-lg border ${
                  darkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                } focus:outline-none focus:ring-2 focus:ring-blue-500`}
              />
            </div>

            <div>
              <label
                className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}
              >
                Target Retirement Age
              </label>
              <input
                type='number'
                value={retirementAge}
                onChange={e => setRetirementAge(parseInt(e.target.value) || 0)}
                className={`w-full px-3 py-2 rounded-lg border ${
                  darkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                } focus:outline-none focus:ring-2 focus:ring-blue-500`}
              />
            </div>

            <div>
              <label
                className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}
              >
                Current Savings (CHF)
              </label>
              <SmartInput
                type='number'
                value={currentSavings}
                onChange={e => setCurrentSavings(parseInt(e.target.value) || 0)}
                className={`w-full px-3 py-2 rounded-lg border ${
                  darkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                } focus:outline-none focus:ring-2 focus:ring-blue-500`}
              />
            </div>

            <div>
              <label
                className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}
              >
                Current Pension (CHF)
              </label>
              <SmartInput
                type='number'
                value={currentPension}
                onChange={e => setCurrentPension(parseInt(e.target.value) || 0)}
                className={`w-full px-3 py-2 rounded-lg border ${
                  darkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                } focus:outline-none focus:ring-2 focus:ring-blue-500`}
              />
            </div>
          </div>

          {/* Income & Expenses */}
          <div className='space-y-4'>
            <h4 className='text-lg font-semibold'>Income & Expenses</h4>

            <div>
              <label
                className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}
              >
                Gross Monthly Income (CHF)
              </label>
              <SmartInput
                type='number'
                value={grossMonthlyIncome}
                onChange={e =>
                  setGrossMonthlyIncome(parseInt(e.target.value) || 0)
                }
                className={`w-full px-3 py-2 rounded-lg border ${
                  darkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                } focus:outline-none focus:ring-2 focus:ring-blue-500`}
              />
              <div
                className={`text-xs mt-1 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}
              >
                Used for pension calculation (17% contribution)
              </div>
            </div>

            <div>
              <label
                className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}
              >
                Net Monthly Income (CHF)
              </label>
              <SmartInput
                type='number'
                value={monthlyIncome}
                onChange={e => setMonthlyIncome(parseInt(e.target.value) || 0)}
                className={`w-full px-3 py-2 rounded-lg border ${
                  darkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                } focus:outline-none focus:ring-2 focus:ring-blue-500`}
              />
              <div
                className={`text-xs mt-1 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}
              >
                After taxes and basic expenses (available for
                savings/investments)
              </div>
            </div>

            <div>
              <label
                className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}
              >
                Work Percentage (%)
              </label>
              <SmartInput
                type='number'
                value={incomePercentage}
                onChange={e =>
                  setIncomePercentage(parseInt(e.target.value) || 100)
                }
                min={0}
                max={100}
                className={`w-full px-3 py-2 rounded-lg border ${
                  darkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                } focus:outline-none focus:ring-2 focus:ring-blue-500`}
              />
              <div
                className={`text-xs mt-1 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}
              >
                Effective income: {formatCurrency(effectiveMonthlyIncome)}
              </div>
            </div>

            <div>
              <label
                className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}
              >
                Company Income (CHF/month)
              </label>
              <SmartInput
                type='number'
                value={companyIncome}
                onChange={e => setCompanyIncome(parseInt(e.target.value) || 0)}
                className={`w-full px-3 py-2 rounded-lg border ${
                  darkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                } focus:outline-none focus:ring-2 focus:ring-blue-500`}
              />
              <div
                className={`text-xs mt-1 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}
              >
                Starts at age {companyIncomeStartAge} ({companyIncomeStartYear})
              </div>
            </div>

            <div>
              <label
                className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}
              >
                Monthly Expenses (CHF)
              </label>
              <SmartInput
                type='number'
                value={monthlyExpenses}
                onChange={e =>
                  setMonthlyExpenses(parseInt(e.target.value) || 0)
                }
                className={`w-full px-3 py-2 rounded-lg border ${
                  darkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                } focus:outline-none focus:ring-2 focus:ring-blue-500`}
              />
            </div>

            <div>
              <label
                className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}
              >
                Monthly Pension Contribution (CHF)
              </label>
              <div
                className={`w-full px-3 py-2 rounded-lg border bg-gray-100 ${
                  darkMode
                    ? 'bg-gray-600 border-gray-500 text-gray-300'
                    : 'bg-gray-100 border-gray-300 text-gray-700'
                }`}
              >
                {formatCurrency(monthlyPensionContribution)}
              </div>
              <div
                className={`text-xs mt-1 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}
              >
                Automatically calculated as 17% of gross income
              </div>
            </div>

            <div
              className={`p-4 rounded-lg ${monthlySavings >= 0 ? 'bg-green-100 dark:bg-green-900/30' : 'bg-red-100 dark:bg-red-900/30'}`}
            >
              <div className='text-sm font-medium'>Financial Summary</div>
              <div className='grid grid-cols-2 gap-2 mt-2 text-sm'>
                <div>
                  <span className='text-gray-600'>Gross Income:</span>
                  <div className='font-semibold'>
                    {formatCurrency(effectiveGrossIncome)}
                  </div>
                </div>
                <div>
                  <span className='text-gray-600'>Pension (17%):</span>
                  <div className='font-semibold'>
                    {formatCurrency(monthlyPensionContribution)}
                  </div>
                </div>
                <div>
                  <span className='text-gray-600'>Net Income:</span>
                  <div className='font-semibold'>
                    {formatCurrency(totalCurrentIncome)}
                  </div>
                </div>
                <div>
                  <span className='text-gray-600'>Additional Expenses:</span>
                  <div className='font-semibold'>
                    {formatCurrency(monthlyExpenses)}
                  </div>
                </div>
                <div className='col-span-2 pt-2 border-t border-gray-300'>
                  <span className='text-gray-600'>Available for Savings:</span>
                  <div
                    className={`font-bold text-lg ${monthlySavings >= 0 ? 'text-green-600' : 'text-red-600'}`}
                  >
                    {formatCurrency(monthlySavings)}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Swiss Tax Information */}
        <div
          className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800/60' : 'bg-white/80 shadow-sm'}`}
        >
          <h3 className='text-xl font-bold mb-4'>🇨🇭 Swiss Tax Information</h3>

          <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
            {/* Tax Inputs */}
            <div className='space-y-4'>
              <h4 className='text-lg font-semibold'>Tax Details</h4>

              <div>
                <label
                  className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}
                >
                  Canton
                </label>
                <select
                  value={canton}
                  onChange={e => setCanton(e.target.value)}
                  className={`w-full px-3 py-2 rounded-lg border ${
                    darkMode
                      ? 'bg-gray-700 border-gray-600 text-white'
                      : 'bg-white border-gray-300 text-gray-900'
                  } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                >
                  <option value='ZH'>ZH - Zürich</option>
                  <option value='BE'>BE - Bern</option>
                  <option value='VD'>VD - Vaud</option>
                  <option value='GE'>GE - Genève</option>
                  <option value='ZG'>ZG - Zug</option>
                  <option value='BS'>BS - Basel-Stadt</option>
                  <option value='AG'>AG - Aargau</option>
                  <option value='SG'>SG - St. Gallen</option>
                  <option value='LU'>LU - Lucerne</option>
                  <option value='TI'>TI - Ticino</option>
                  <option value='VS'>VS - Valais</option>
                  <option value='FR'>FR - Fribourg</option>
                  <option value='SO'>SO - Solothurn</option>
                  <option value='BL'>BL - Basel-Landschaft</option>
                  <option value='SH'>SH - Schaffhausen</option>
                  <option value='AR'>AR - Appenzell Ausserrhoden</option>
                  <option value='GR'>GR - Graubünden</option>
                  <option value='TG'>TG - Thurgau</option>
                  <option value='NE'>NE - Neuchâtel</option>
                  <option value='JU'>JU - Jura</option>
                  <option value='SZ'>SZ - Schwyz</option>
                  <option value='NW'>NW - Nidwalden</option>
                  <option value='OW'>OW - Obwalden</option>
                  <option value='GL'>GL - Glarus</option>
                  <option value='UR'>UR - Uri</option>
                  <option value='AI'>AI - Appenzell Innerrhoden</option>
                </select>
              </div>

              <div>
                <label
                  className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}
                >
                  Marital Status
                </label>
                <select
                  value={maritalStatus}
                  onChange={e => setMaritalStatus(e.target.value)}
                  className={`w-full px-3 py-2 rounded-lg border ${
                    darkMode
                      ? 'bg-gray-700 border-gray-600 text-white'
                      : 'bg-white border-gray-300 text-gray-900'
                  } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                >
                  <option value='single'>Single</option>
                  <option value='married'>Married</option>
                  <option value='divorced'>Divorced</option>
                  <option value='widowed'>Widowed</option>
                </select>
              </div>

              <div>
                <label
                  className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}
                >
                  Net Worth (CHF)
                </label>
                <SmartInput
                  type='number'
                  value={netWorth}
                  onChange={e => setNetWorth(parseInt(e.target.value) || 0)}
                  className={`w-full px-3 py-2 rounded-lg border ${
                    darkMode
                      ? 'bg-gray-700 border-gray-600 text-white'
                      : 'bg-white border-gray-300 text-gray-900'
                  } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                />
                <div
                  className={`text-xs mt-1 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}
                >
                  Used for wealth tax calculation
                </div>
              </div>

              <div>
                <label
                  className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}
                >
                  Pillar 3a Contribution (CHF/year)
                </label>
                <SmartInput
                  type='number'
                  value={pillar3aContribution}
                  onChange={e =>
                    setPillar3aContribution(parseInt(e.target.value) || 0)
                  }
                  max={7056}
                  className={`w-full px-3 py-2 rounded-lg border ${
                    darkMode
                      ? 'bg-gray-700 border-gray-600 text-white'
                      : 'bg-white border-gray-300 text-gray-900'
                  } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                />
                <div
                  className={`text-xs mt-1 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}
                >
                  Maximum: CHF 7,056 (2024 limit)
                </div>
              </div>
            </div>

            {/* Tax Results */}
            <div className='space-y-4'>
              <h4 className='text-lg font-semibold'>Tax Calculation</h4>

              <div
                className={`p-4 rounded-lg ${darkMode ? 'bg-blue-900/30' : 'bg-blue-50'}`}
              >
                <div className='text-sm font-medium text-blue-600'>
                  Annual Tax Burden
                </div>
                <div className='text-2xl font-bold text-blue-600'>
                  {formatCurrency(swissTaxData.totalAnnualTax)}
                </div>
                <div className='text-xs text-blue-500 mt-1'>
                  Effective rate: {swissTaxData.effectiveTaxRate.toFixed(2)}%
                </div>
              </div>

              <div className='space-y-2 text-sm'>
                <div className='flex justify-between'>
                  <span>Federal Tax:</span>
                  <span className='font-medium'>
                    {formatCurrency(swissTaxData.incomeTax.federal)}
                  </span>
                </div>
                <div className='flex justify-between'>
                  <span>Cantonal Tax:</span>
                  <span className='font-medium'>
                    {formatCurrency(swissTaxData.incomeTax.cantonal)}
                  </span>
                </div>
                <div className='flex justify-between'>
                  <span>Municipal Tax:</span>
                  <span className='font-medium'>
                    {formatCurrency(swissTaxData.incomeTax.municipal)}
                  </span>
                </div>
                <div className='flex justify-between'>
                  <span>Wealth Tax:</span>
                  <span className='font-medium'>
                    {formatCurrency(swissTaxData.wealthTax.total)}
                  </span>
                </div>
                <div className='flex justify-between border-t pt-2 font-semibold'>
                  <span>Total Annual Tax:</span>
                  <span>{formatCurrency(swissTaxData.totalAnnualTax)}</span>
                </div>
              </div>

              <div
                className={`p-3 rounded-lg ${darkMode ? 'bg-green-900/30' : 'bg-green-50'}`}
              >
                <div className='text-sm font-medium text-green-600'>
                  Net Annual Income
                </div>
                <div className='text-xl font-bold text-green-600'>
                  {formatCurrency(swissTaxData.netAnnualIncome)}
                </div>
                <div className='text-xs text-green-500 mt-1'>
                  After taxes:{' '}
                  {formatCurrency(swissTaxData.netAnnualIncome / 12)}/month
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  // Render analysis content
  const renderAnalysis = () => (
    <div className='space-y-6'>
      <div
        className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800/60' : 'bg-white/80 shadow-sm'}`}
      >
        <h3 className='text-xl font-bold mb-4'>📊 Financial Analysis</h3>

        <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
          <div className='space-y-4'>
            <h4 className='text-lg font-semibold'>Investment Assumptions</h4>

            <div>
              <label
                className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}
              >
                Expected Annual Return (%)
              </label>
              <input
                type='number'
                step='0.1'
                value={expectedReturn}
                onChange={e =>
                  setExpectedReturn(parseFloat(e.target.value) || 0)
                }
                className={`w-full px-3 py-2 rounded-lg border ${
                  darkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                } focus:outline-none focus:ring-2 focus:ring-blue-500`}
              />
            </div>

            <div>
              <label
                className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}
              >
                Inflation Rate (%)
              </label>
              <input
                type='number'
                step='0.1'
                value={inflationRate}
                onChange={e =>
                  setInflationRate(parseFloat(e.target.value) || 0)
                }
                className={`w-full px-3 py-2 rounded-lg border ${
                  darkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                } focus:outline-none focus:ring-2 focus:ring-blue-500`}
              />
            </div>

            <div>
              <label
                className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}
              >
                Withdrawal Rate (%)
              </label>
              <input
                type='number'
                step='0.1'
                value={safeWithdrawalRate}
                onChange={e =>
                  setSafeWithdrawalRate(parseFloat(e.target.value) || 0)
                }
                className={`w-full px-3 py-2 rounded-lg border ${
                  darkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                } focus:outline-none focus:ring-2 focus:ring-blue-500`}
              />
            </div>
          </div>

          <div className='space-y-4'>
            <h4 className='text-lg font-semibold'>FIRE Projections</h4>

            <div
              className={`p-4 rounded-lg ${darkMode ? 'bg-blue-900/30' : 'bg-blue-50'}`}
            >
              <div className='text-sm font-medium text-blue-600'>
                Target FIRE Amount
              </div>
              <div className='text-2xl font-bold text-blue-600'>
                {formatCurrency(targetFIREAmount)}
              </div>
            </div>

            <div
              className={`p-4 rounded-lg ${darkMode ? 'bg-green-900/30' : 'bg-green-50'}`}
            >
              <div className='text-sm font-medium text-green-600'>
                Years to FIRE
              </div>
              <div className='text-2xl font-bold text-green-600'>
                {monthlySavings > 0
                  ? Math.ceil(
                      (targetFIREAmount - currentSavings) /
                        (monthlySavings * 12)
                    )
                  : '∞'}
              </div>
            </div>

            <div
              className={`p-4 rounded-lg ${darkMode ? 'bg-purple-900/30' : 'bg-purple-50'}`}
            >
              <div className='text-sm font-medium text-purple-600'>
                Monthly Retirement Income
              </div>
              <div className='text-2xl font-bold text-purple-600'>
                {formatCurrency(monthlyExpenses)}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Historical Tracking Charts */}
      <div
        className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800/60' : 'bg-white/80 shadow-sm'}`}
      >
        <h3 className='text-xl font-bold mb-4'>
          📈 Historical Tracking & Analytics
        </h3>
        <p className={`mb-6 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
          Track your financial progress over time with comprehensive charts and
          analytics
        </p>
        <HistoricalTrackingCharts
          darkMode={darkMode}
          userData={{
            currentAge,
            retirementAge,
            currentSavings,
            monthlyIncome,
            monthlyExpenses,
            expectedReturn,
            inflationRate,
            safeWithdrawalRate,
          }}
          expenses={expenses}
          investments={investments}
          savingsGoals={savingsGoals}
        />
      </div>
    </div>
  );

  // Render content based on active tab
  const renderContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return renderDashboard();
      case 'planning':
        return renderPlanning();
      case 'income':
        try {
          return (
            <div className='space-y-6'>
              <div
                className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800/60' : 'bg-white/80 shadow-sm'}`}
              >
                <h3 className='text-xl font-bold mb-4'>
                  💼 Additional Income Sources
                </h3>
                <p
                  className={`mb-6 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}
                >
                  Track and manage additional income sources beyond your primary
                  salary. Include freelance work, rental income, dividends, and
                  Swiss-specific income like AHV pension.
                </p>
                <AdditionalIncomeManager
                  darkMode={darkMode}
                  onIncomeChange={(
                    newIncomes: any // eslint-disable-line @typescript-eslint/no-explicit-any
                  ) => setAdditionalIncomes(newIncomes)}
                />
              </div>
            </div>
          );
        } catch (error) {
          console.error('Error rendering AdditionalIncomeManager:', error);
          return (
            <div className='space-y-6'>
              <div
                className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800/60' : 'bg-white/80 shadow-sm'}`}
              >
                <h3 className='text-xl font-bold mb-4'>
                  💼 Additional Income Sources
                </h3>
                <div className='text-center py-8 text-red-500'>
                  <p>Error loading Additional Income Manager</p>
                  <p className='text-sm mt-2'>Check console for details</p>
                </div>
              </div>
            </div>
          );
        }
      case 'expenses':
        try {
          return (
            <div className='space-y-6'>
              <div
                className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800/60' : 'bg-white/80 shadow-sm'}`}
              >
                <h3 className='text-xl font-bold mb-4'>
                  💸 Expense Management
                </h3>
                <p
                  className={`mb-6 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}
                >
                  Track and categorize your expenses with Swiss-specific
                  categories. Classify expenses as essential, important, or
                  nice-to-have to optimize your budget.
                </p>
                <ExpenseManager
                  darkMode={darkMode}
                  onExpensesChange={newExpenses => setExpenses(newExpenses)}
                />
              </div>
            </div>
          );
        } catch (error) {
          console.error('Error rendering ExpenseManager:', error);
          return (
            <div className='space-y-6'>
              <div
                className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800/60' : 'bg-white/80 shadow-sm'}`}
              >
                <h3 className='text-xl font-bold mb-4'>
                  💸 Expense Management
                </h3>
                <div className='text-center py-8 text-red-500'>
                  <p>Error loading Expense Manager</p>
                  <p className='text-sm mt-2'>Check console for details</p>
                </div>
              </div>
            </div>
          );
        }
      case 'savings':
        try {
          return (
            <div className='space-y-6'>
              <div
                className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800/60' : 'bg-white/80 shadow-sm'}`}
              >
                <h3 className='text-xl font-bold mb-4'>🎯 Savings Goals</h3>
                <p
                  className={`mb-6 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}
                >
                  Set and track multiple savings goals including emergency fund,
                  Pillar 3a, and other Swiss-specific financial objectives.
                </p>
                <SavingsGoalsManager
                  darkMode={darkMode}
                  monthlyIncome={totalCurrentIncome}
                  monthlyExpenses={monthlyExpenses}
                  onGoalsChange={newGoals => setSavingsGoals(newGoals)}
                />
              </div>
            </div>
          );
        } catch (error) {
          console.error('Error rendering SavingsGoalsManager:', error);
          return (
            <div className='space-y-6'>
              <div
                className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800/60' : 'bg-white/80 shadow-sm'}`}
              >
                <h3 className='text-xl font-bold mb-4'>🎯 Savings Goals</h3>
                <div className='text-center py-8 text-red-500'>
                  <p>Error loading Savings Goals Manager</p>
                  <p className='text-sm mt-2'>Check console for details</p>
                </div>
              </div>
            </div>
          );
        }
      case 'investments':
        try {
          return (
            <div className='space-y-6'>
              <div
                className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800/60' : 'bg-white/80 shadow-sm'}`}
              >
                <h3 className='text-xl font-bold mb-4'>
                  📈 Investment Portfolio
                </h3>
                <p
                  className={`mb-6 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}
                >
                  Track your investment portfolio including stocks, ETFs, bonds,
                  and Swiss-specific investments. Monitor performance,
                  allocation, and risk levels.
                </p>
                <InvestmentPortfolioManager
                  darkMode={darkMode}
                  onPortfolioChange={newInvestments =>
                    setInvestments(newInvestments)
                  }
                />
              </div>
            </div>
          );
        } catch (error) {
          console.error('Error rendering InvestmentPortfolioManager:', error);
          return (
            <div className='space-y-6'>
              <div
                className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800/60' : 'bg-white/80 shadow-sm'}`}
              >
                <h3 className='text-xl font-bold mb-4'>
                  📈 Investment Portfolio
                </h3>
                <div className='text-center py-8 text-red-500'>
                  <p>Error loading Investment Portfolio Manager</p>
                  <p className='text-sm mt-2'>Check console for details</p>
                </div>
              </div>
            </div>
          );
        }
      case 'analysis':
        return renderAnalysis();
      case 'tax':
        return (
          <div className='space-y-6'>
            <div
              className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800/60' : 'bg-white/80 shadow-sm'}`}
            >
              <h3 className='text-xl font-bold mb-4'>
                🇨🇭 Swiss Tax Calculator
              </h3>
              <p
                className={`mb-6 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}
              >
                Calculate your Swiss taxes across all 26 cantons. Compare tax
                burdens, optimize Pillar 3a contributions, and find the most
                tax-efficient canton.
              </p>
              {/* SwissTaxCalculator component will be rendered here */}
              <div className='text-center py-8 text-gray-500'>
                Swiss Tax Calculator component loading...
              </div>
            </div>
          </div>
        );
      case 'healthcare':
        return (
          <SafeComponentRenderer
            componentPath='./HealthcareCostOptimizer'
            props={{
              darkMode: darkMode,
              userData: {
                age: currentAge,
                canton: 'ZH', // Default canton, could be made configurable
                monthlyIncome: monthlyIncome,
                familySize: 1, // Could be made configurable
              },
              onSavingsCalculated: (savings: number) => {
                // Optional: integrate healthcare savings into overall FIRE calculations
                console.log('Healthcare savings calculated:', savings);
              },
              fireGoals: {
                targetAge: retirementAge,
                targetAmount: targetFIREAmount,
                currentAge: currentAge,
                currentSavings: currentSavings,
                monthlyContribution: monthlySavings,
                expectedReturn: expectedReturn / 100,
                inflationRate: inflationRate / 100,
              },
            }}
            fallback={
              <div
                className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800/60' : 'bg-white/80 shadow-sm'}`}
              >
                <h3 className='text-xl font-bold mb-4'>
                  🏥 Healthcare Cost Optimizer
                </h3>
                <div
                  className={`p-4 rounded-lg ${darkMode ? 'bg-red-900/20' : 'bg-red-50'} border border-red-200 dark:border-red-800`}
                >
                  <p className='text-red-600 dark:text-red-400'>
                    ⚠️ Healthcare Cost Optimizer is temporarily unavailable.
                    Please check back later.
                  </p>
                  <p className='text-sm text-red-500 dark:text-red-300 mt-2'>
                    This premium feature provides comprehensive Swiss healthcare
                    cost optimization with deductible analysis, premium
                    comparisons, and potential savings of CHF 1,000+ annually.
                  </p>
                </div>
              </div>
            }
          />
        );
      case 'company':
        return (
          <div className='space-y-6'>
            <div
              className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800/60' : 'bg-white/80 shadow-sm'}`}
            >
              <h3 className='text-xl font-bold mb-4'>
                🏢 Company Income Planning
              </h3>

              <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <div className='space-y-4'>
                  <h4 className='text-lg font-semibold'>Company Setup</h4>

                  <div>
                    <label
                      className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}
                    >
                      Monthly Company Income (CHF)
                    </label>
                    <SmartInput
                      type='number'
                      value={companyIncome}
                      onChange={e =>
                        setCompanyIncome(parseInt(e.target.value) || 0)
                      }
                      className={`w-full px-3 py-2 rounded-lg border ${
                        darkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                    />
                  </div>

                  <div>
                    <label
                      className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}
                    >
                      Start Year
                    </label>
                    <SmartInput
                      type='number'
                      value={companyIncomeStartYear}
                      onChange={e =>
                        setCompanyIncomeStartYear(
                          parseInt(e.target.value) || 2025
                        )
                      }
                      min={2024}
                      max={2050}
                      className={`w-full px-3 py-2 rounded-lg border ${
                        darkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                    />
                  </div>

                  <div>
                    <label
                      className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}
                    >
                      Annual Growth Rate (%)
                    </label>
                    <SmartInput
                      type='number'
                      step='0.1'
                      value={companyIncomeGrowthRate}
                      onChange={e =>
                        setCompanyIncomeGrowthRate(
                          parseFloat(e.target.value) || 0
                        )
                      }
                      className={`w-full px-3 py-2 rounded-lg border ${
                        darkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                    />
                  </div>
                </div>

                <div className='space-y-4'>
                  <h4 className='text-lg font-semibold'>Projections</h4>

                  <div
                    className={`p-4 rounded-lg ${darkMode ? 'bg-blue-900/30' : 'bg-blue-50'}`}
                  >
                    <div className='text-sm font-medium text-blue-600 mb-2'>
                      Current Status
                    </div>
                    <div className='space-y-2 text-sm'>
                      <div className='flex justify-between'>
                        <span>Start Age:</span>
                        <span className='font-semibold'>
                          {companyIncomeStartAge}
                        </span>
                      </div>
                      <div className='flex justify-between'>
                        <span>Current Income:</span>
                        <span className='font-semibold'>
                          {formatCurrency(currentCompanyIncomeMonthly)}
                        </span>
                      </div>
                      <div className='flex justify-between'>
                        <span>Years Active:</span>
                        <span className='font-semibold'>
                          {Math.max(0, retirementAge - companyIncomeStartAge)}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div
                    className={`p-4 rounded-lg ${darkMode ? 'bg-green-900/30' : 'bg-green-50'}`}
                  >
                    <div className='text-sm font-medium text-green-600 mb-2'>
                      Lifetime Projection
                    </div>
                    <div className='space-y-2 text-sm'>
                      <div className='flex justify-between'>
                        <span>Total Years:</span>
                        <span className='font-semibold'>
                          {Math.max(0, retirementAge - companyIncomeStartAge)}
                        </span>
                      </div>
                      <div className='flex justify-between'>
                        <span>Final Monthly Income:</span>
                        <span className='font-semibold'>
                          {formatCurrency(
                            companyIncome *
                              Math.pow(
                                1 + companyIncomeGrowthRate / 100,
                                Math.max(
                                  0,
                                  retirementAge - companyIncomeStartAge
                                )
                              )
                          )}
                        </span>
                      </div>
                      <div className='flex justify-between'>
                        <span>Estimated Total:</span>
                        <span className='font-semibold text-lg'>
                          {formatCurrency(
                            Array.from(
                              {
                                length: Math.max(
                                  0,
                                  retirementAge - companyIncomeStartAge
                                ),
                              },
                              (_, i) =>
                                companyIncome *
                                12 *
                                Math.pow(1 + companyIncomeGrowthRate / 100, i)
                            ).reduce((sum, val) => sum + val, 0)
                          )}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
      case 'advanced':
        return (
          <div className='space-y-6'>
            {/* Monte Carlo Simulation */}
            <div
              className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800/60' : 'bg-white/80 shadow-sm'}`}
            >
              <h3 className='text-xl font-bold mb-4'>
                🎲 Monte Carlo Simulation
              </h3>
              <p
                className={`mb-6 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}
              >
                Advanced probabilistic analysis of your FIRE plan using
                thousands of market scenarios.
              </p>
              <MonteCarloSimulation
                darkMode={darkMode}
                userData={{
                  currentAge,
                  retirementAge,
                  currentSavings,
                  monthlyIncome,
                  monthlyExpenses,
                  expectedReturn,
                  inflationRate,
                  safeWithdrawalRate,
                }}
              />
            </div>

            {/* Advanced Tax Calculator */}
            <div
              className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800/60' : 'bg-white/80 shadow-sm'}`}
            >
              <h3 className='text-xl font-bold mb-4'>
                🏛️ Advanced Tax Calculator
              </h3>
              <p
                className={`mb-6 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}
              >
                Comprehensive Swiss tax analysis with federal, cantonal, and
                wealth tax calculations.
              </p>
              <SafeComponentRenderer
                componentPath='./AdvancedTaxCalculator'
                props={{
                  darkMode: darkMode,
                  userData: {
                    monthlyIncome,
                    currentSavings,
                    canton: 'ZH',
                    civilStatus: 'single',
                  },
                }}
                fallback={
                  <div
                    className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}
                  >
                    <p
                      className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}
                    >
                      Advanced Tax Calculator component is being developed...
                    </p>
                  </div>
                }
              />
            </div>

            {/* Data Export/Import */}
            <div
              className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800/60' : 'bg-white/80 shadow-sm'}`}
            >
              <h3 className='text-xl font-bold mb-4'>
                💾 Data Export & Import
              </h3>
              <p
                className={`mb-6 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}
              >
                Backup your financial data or import from other sources.
              </p>
              <DataExportImport
                darkMode={darkMode}
                userData={{
                  currentAge,
                  retirementAge,
                  currentSavings,
                  monthlyIncome,
                  monthlyExpenses,
                  expenses,
                  savingsGoals,
                  investments,
                  additionalIncomes,
                }}
                onImportData={(data: any) => {
                  // Apply imported data to state
                  if (data.currentAge) setCurrentAge(data.currentAge);
                  if (data.retirementAge) setRetirementAge(data.retirementAge);
                  if (data.currentSavings)
                    setCurrentSavings(data.currentSavings);
                  if (data.monthlyIncome) setMonthlyIncome(data.monthlyIncome);
                  if (data.monthlyExpenses)
                    setMonthlyExpenses(data.monthlyExpenses);
                  if (data.expenses) setExpenses(data.expenses);
                  if (data.savingsGoals) setSavingsGoals(data.savingsGoals);
                  if (data.investments) setInvestments(data.investments);
                  if (data.additionalIncomes)
                    setAdditionalIncomes(data.additionalIncomes);
                }}
              />
            </div>

            {/* Performance Optimizer */}
            <div
              className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800/60' : 'bg-white/80 shadow-sm'}`}
            >
              <h3 className='text-xl font-bold mb-4'>
                ⚡ Performance Optimizer
              </h3>
              <p
                className={`mb-6 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}
              >
                Analyze and optimize application performance for better user
                experience.
              </p>
              <div
                className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}
              >
                <p
                  className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}
                >
                  Performance Optimizer component is being developed...
                </p>
              </div>
            </div>

            {/* Risk Assessment Metrics */}
            <div
              className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800/60' : 'bg-white/80 shadow-sm'}`}
            >
              <h3 className='text-xl font-bold mb-4'>
                ⚠️ Risk Assessment Metrics
              </h3>
              <p
                className={`mb-6 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}
              >
                Comprehensive analysis of financial risks and recommendations
                for risk mitigation
              </p>
              <div
                className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}
              >
                <p
                  className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}
                >
                  Risk Assessment Metrics component is being developed...
                </p>
              </div>
            </div>

            {/* Safe Withdrawal Rate Analysis */}
            <div
              className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800/60' : 'bg-white/80 shadow-sm'}`}
            >
              <h3 className='text-xl font-bold mb-4'>
                📊 Safe Withdrawal Rate Analysis
              </h3>
              <p
                className={`mb-6 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}
              >
                Advanced analysis of withdrawal rates and their probability of
                success over your retirement period
              </p>
              <SafeWithdrawalRateAnalysis
                darkMode={darkMode}
                userData={{
                  currentAge,
                  retirementAge,
                  currentSavings,
                  monthlyIncome,
                  monthlyExpenses,
                  expectedReturn,
                  inflationRate,
                  safeWithdrawalRate,
                }}
              />
            </div>
          </div>
        );
      case 'premium':
        return (
          <div className='space-y-6'>
            {/* Premium Features Header */}
            <div
              className={`p-6 rounded-xl ${darkMode ? 'bg-gradient-to-r from-purple-900/60 to-pink-900/60' : 'bg-gradient-to-r from-purple-50 to-pink-50'} border ${darkMode ? 'border-purple-700' : 'border-purple-200'}`}
            >
              <h2 className='text-2xl font-bold mb-2 flex items-center gap-2'>
                <span className='text-3xl'>💎</span>
                Premium Features
              </h2>
              <p className={`${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                Advanced financial planning tools for serious investors and
                financial optimization
              </p>
            </div>

            {/* Cryptocurrency Portfolio Manager */}
            <div
              className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800/60' : 'bg-white/80 shadow-sm'}`}
            >
              <h3 className='text-xl font-bold mb-4'>
                ₿ Cryptocurrency Portfolio Manager
              </h3>
              <p
                className={`mb-6 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}
              >
                Track your cryptocurrency investments with Swiss tax compliance
                features
              </p>
              <CryptocurrencyPortfolioManager darkMode={darkMode} />
            </div>

            {/* Real Estate Investment Tracker */}
            <div
              className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800/60' : 'bg-white/80 shadow-sm'}`}
            >
              <h3 className='text-xl font-bold mb-4'>
                🏠 Real Estate Investment Tracker
              </h3>
              <p
                className={`mb-6 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}
              >
                Track and analyze your Swiss real estate investments with yield
                calculations
              </p>
              <div
                className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}
              >
                <p
                  className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}
                >
                  Real Estate Investment Tracker component is being developed...
                </p>
              </div>
            </div>

            {/* Advanced Portfolio Rebalancing */}
            <div
              className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800/60' : 'bg-white/80 shadow-sm'}`}
            >
              <h3 className='text-xl font-bold mb-4'>
                ⚖️ Advanced Portfolio Rebalancing
              </h3>
              <p
                className={`mb-6 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}
              >
                Optimize your portfolio allocation with automated rebalancing
                strategies
              </p>
              <div
                className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}
              >
                <p
                  className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}
                >
                  Advanced Portfolio Rebalancing component is being developed...
                </p>
              </div>
            </div>

            {/* AI Financial Advisor */}
            <div
              className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800/60' : 'bg-white/80 shadow-sm'}`}
            >
              <h3 className='text-xl font-bold mb-4'>
                🤖 AI Financial Advisor
              </h3>
              <p
                className={`mb-6 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}
              >
                Personalized financial insights and recommendations powered by
                advanced AI analysis
              </p>
              <div
                className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}
              >
                <p
                  className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}
                >
                  AI Financial Advisor component is being developed...
                </p>
              </div>
            </div>

            {/* Canton Relocation Analysis */}
            <div
              className={`p-6 rounded-xl ${darkMode ? 'bg-gray-800/60' : 'bg-white/80 shadow-sm'}`}
            >
              <h3 className='text-xl font-bold mb-4'>
                🏛️ Canton Relocation Tax Optimizer
              </h3>
              <p
                className={`mb-6 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}
              >
                Analyze potential tax savings and lifestyle impacts of
                relocating to different Swiss cantons
              </p>
              <div
                className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}
              >
                <p
                  className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}
                >
                  Canton Relocation Analysis component is being developed...
                </p>
              </div>
            </div>
          </div>
        );
      default:
        return renderDashboard();
    }
  };

  return (
    <div
      className={`min-h-screen transition-all duration-500 font-sans ${
        darkMode
          ? 'bg-gradient-to-br from-gray-900 via-purple-900/25 to-blue-900/25 text-gray-200'
          : 'bg-gradient-to-br from-blue-50 via-purple-50/40 to-pink-50 text-gray-800'
      }`}
    >
      <div className='max-w-7xl mx-auto p-3 sm:p-4 md:p-6'>
        {/* Header */}
        <div className='mb-5 md:mb-8 relative'>
          <div className='flex flex-col sm:flex-row justify-between items-start mb-4 md:mb-6'>
            <div className='relative mb-3 sm:mb-0'>
              <h1
                className={`text-3xl sm:text-4xl md:text-5xl font-black mb-1.5 flex items-center bg-clip-text text-transparent ${
                  darkMode
                    ? 'bg-gradient-to-r from-cyan-400 via-purple-400 to-pink-400'
                    : 'bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600'
                }`}
              >
                <div className='relative mr-2 md:mr-3'>
                  <span className='text-green-500 animate-pulse text-3xl'>
                    🔥
                  </span>
                  <span className='absolute -top-0.5 -right-0.5 text-yellow-400 animate-bounce text-sm'>
                    ⭐
                  </span>
                </div>
                {t('fire.title', 'Fire or Retire Calculator')}
                <span className='text-xs md:text-sm ml-2 bg-gradient-to-r from-yellow-400 to-orange-400 px-1.5 py-0.5 rounded-full text-black font-bold'>
                  Pro
                </span>
              </h1>
              <p
                className={`text-sm md:text-base ${darkMode ? 'text-gray-400' : 'text-gray-600'} font-medium`}
              >
                🇨🇭{' '}
                {t(
                  'fire.subtitle',
                  'Swiss Budget Pro - Financial Planning Application'
                )}
              </p>
            </div>

            <div className='flex items-center space-x-3'>
              <LanguageSwitcher darkMode={darkMode} />
              <button
                onClick={() => setShowOnboarding(true)}
                className='p-2.5 rounded-lg bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white transition-all duration-300 transform hover:scale-110 shadow-sm'
                title='Setup Wizard - Personalize Your Plan'
              >
                <span className='text-lg'>🧙‍♂️</span>
              </button>
              <button
                onClick={() => setShowDebugPanel(true)}
                className='p-2.5 rounded-lg bg-gradient-to-r from-red-600 to-pink-600 hover:from-red-700 hover:to-pink-700 text-white transition-all duration-300 transform hover:scale-110 shadow-sm'
                title='Debug Panel - View Runtime Errors'
              >
                <span className='text-lg'>🐛</span>
              </button>
              <button
                onClick={() => setDarkMode(!darkMode)}
                className={`p-2.5 rounded-lg transition-all duration-300 transform hover:scale-110 ${
                  darkMode
                    ? 'bg-gradient-to-r from-yellow-400 to-orange-400 shadow-sm shadow-yellow-500/20'
                    : 'bg-gradient-to-r from-purple-600 to-blue-600 shadow-sm shadow-purple-500/20'
                }`}
                title={
                  darkMode ? 'Switch to Light Mode' : 'Switch to Dark Mode'
                }
              >
                <span className='text-white text-lg'>
                  {darkMode ? '☀️' : '🌙'}
                </span>
              </button>
              <a
                href='/admin'
                className='p-2.5 rounded-lg bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white transition-all duration-300 transform hover:scale-110 shadow-sm'
                title='Admin Panel'
              >
                <span className='text-lg'>⚙️</span>
              </a>
            </div>
          </div>

          {/* Key Metrics Bar */}
          <div
            className={`grid grid-cols-2 sm:grid-cols-4 gap-2 md:gap-3 text-xs md:text-sm p-2.5 md:p-3 rounded-lg mb-4 md:mb-6 ${
              darkMode ? 'bg-gray-800/60' : 'bg-white/80 shadow-sm'
            }`}
          >
            {[
              {
                icon: '📈',
                label: `${savingsRate.toFixed(1)}% ${t('fire.metrics.savingsRate', 'Savings Rate')}`,
                color: darkMode ? 'text-emerald-400' : 'text-emerald-600',
              },
              {
                icon: '🎯',
                label: `${fireProgress.toFixed(0)}% ${t('fire.metrics.fireProgress', 'FIRE Progress')}`,
                color: darkMode ? 'text-blue-400' : 'text-blue-600',
              },
              {
                icon: '📅',
                label: `${yearsToRetirement} ${t('fire.metrics.yearsToRetirement', 'Years to Retire')}`,
                color: darkMode ? 'text-purple-400' : 'text-purple-600',
              },
              {
                icon: '💰',
                label: `${formatCurrency(effectiveGrossIncome)} ${t('fire.metrics.grossIncome', 'Gross Income')}`,
                color: darkMode ? 'text-yellow-400' : 'text-yellow-600',
              },
            ].map(stat => (
              <div
                key={stat.label}
                className={`flex items-center ${stat.color}`}
              >
                <span className='mr-1 text-sm'>{stat.icon}</span>
                <span className='font-medium'>{stat.label}</span>
              </div>
            ))}
          </div>

          {/* Navigation Tabs */}
          <div className='flex flex-wrap gap-1.5 md:gap-2.5 mb-4'>
            <TabButton
              id='dashboard'
              label={t('fire.tabs.dashboard', 'Dashboard')}
              icon='🏠'
              active={activeTab === 'dashboard'}
              onClick={() => setActiveTab('dashboard')}
            />
            <TabButton
              id='planning'
              label={t('fire.tabs.planning', 'Planning')}
              icon='💰'
              active={activeTab === 'planning'}
              onClick={() => setActiveTab('planning')}
            />
            <TabButton
              id='income'
              label={t('fire.tabs.income', 'Income')}
              icon='💼'
              active={activeTab === 'income'}
              onClick={() => setActiveTab('income')}
            />
            <TabButton
              id='expenses'
              label={t('fire.tabs.expenses', 'Expenses')}
              icon='💸'
              active={activeTab === 'expenses'}
              onClick={() => setActiveTab('expenses')}
            />
            <TabButton
              id='savings'
              label={t('fire.tabs.savings', 'Savings Goals')}
              icon='🎯'
              active={activeTab === 'savings'}
              onClick={() => setActiveTab('savings')}
            />
            <TabButton
              id='investments'
              label={t('fire.tabs.investments', 'Investments')}
              icon='📈'
              active={activeTab === 'investments'}
              onClick={() => setActiveTab('investments')}
            />
            <TabButton
              id='analysis'
              label={t('fire.tabs.analysis', 'Analysis')}
              icon='📊'
              active={activeTab === 'analysis'}
              onClick={() => setActiveTab('analysis')}
            />
            <TabButton
              id='advanced'
              label={t('fire.tabs.advanced', 'Advanced')}
              icon='🔬'
              active={activeTab === 'advanced'}
              onClick={() => setActiveTab('advanced')}
            />
            <TabButton
              id='tax'
              label={t('fire.tabs.tax', 'Swiss Tax')}
              icon='🇨🇭'
              active={activeTab === 'tax'}
              onClick={() => setActiveTab('tax')}
            />
            <TabButton
              id='healthcare'
              label={t('fire.tabs.healthcare', 'Healthcare')}
              icon='🏥'
              active={activeTab === 'healthcare'}
              onClick={() => setActiveTab('healthcare')}
            />
            <TabButton
              id='company'
              label={t('fire.tabs.company', 'Company')}
              icon='🏢'
              active={activeTab === 'company'}
              onClick={() => setActiveTab('company')}
            />
            <TabButton
              id='premium'
              label={t('fire.tabs.premium', 'Premium')}
              icon='💎'
              active={activeTab === 'premium'}
              onClick={() => setActiveTab('premium')}
            />
          </div>
        </div>

        {/* Main Content */}
        {renderContent()}
      </div>

      {/* Debug Panel */}
      <DebugPanel />

      {/* Onboarding Wizard */}
      <SafeComponentRenderer
        componentPath='./OnboardingWizard'
        props={{
          darkMode: darkMode,
          onComplete: handleOnboardingComplete,
          onSkip: () => setShowOnboarding(false),
        }}
        fallback={null}
      />

      {/* Auto-save Manager */}
      <SafeComponentRenderer
        componentPath='./AutoSaveManager'
        props={{
          darkMode: darkMode,
          currentData: {
            expenses,
            savingsGoals,
            investments,
            additionalIncomes,
            userInputs: {
              currentAge,
              retirementAge,
              currentSavings,
              monthlyIncome,
              monthlyExpenses,
              expectedReturn,
              inflationRate,
              safeWithdrawalRate,
            },
          },
          onLoadSnapshot: (data: any) => {
            if (data.expenses) setExpenses(data.expenses);
            if (data.savingsGoals) setSavingsGoals(data.savingsGoals);
            if (data.investments) setInvestments(data.investments);
            if (data.additionalIncomes)
              setAdditionalIncomes(data.additionalIncomes);
            if (data.userInputs) {
              const inputs = data.userInputs;
              if (inputs.currentAge) setCurrentAge(inputs.currentAge);
              if (inputs.retirementAge) setRetirementAge(inputs.retirementAge);
              if (inputs.currentSavings)
                setCurrentSavings(inputs.currentSavings);
              if (inputs.monthlyIncome) setMonthlyIncome(inputs.monthlyIncome);
              if (inputs.monthlyExpenses)
                setMonthlyExpenses(inputs.monthlyExpenses);
              if (inputs.expectedReturn)
                setExpectedReturn(inputs.expectedReturn);
              if (inputs.inflationRate) setInflationRate(inputs.inflationRate);
              if (inputs.safeWithdrawalRate)
                setSafeWithdrawalRate(inputs.safeWithdrawalRate);
            }
          },
        }}
        fallback={null}
      />
    </div>
  );
};

// Export the main component
export { FireOrRetireCalculatorComponent as FireOrRetireCalculator };
export default FireOrRetireCalculatorComponent;
