import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocalStorage } from '../hooks/useLocalStorage';
import EnhancedHistoricalCharts from './EnhancedHistoricalCharts';
import ChartPerformanceMonitor from './ChartPerformanceMonitor';
import MobileOptimizedChart, { isMobileDevice } from './MobileOptimizedChart';
import ChartControls, {
  TimeframeOption,
  ChartTypeOption,
} from './ChartControls';

interface DataVisualizationDashboardProps {
  darkMode: boolean;
  userData: {
    currentAge: number;
    retirementAge: number;
    currentSavings: number;
    monthlyIncome: number;
    monthlyExpenses: number;
    expectedReturn: number;
    inflationRate: number;
    safeWithdrawalRate: number;
  };
  expenses?: any[];
  investments?: any[];
  savingsGoals?: any[];
  className?: string;
}

interface DashboardSettings {
  showPerformanceMonitor: boolean;
  enableMobileOptimization: boolean;
  autoDetectMobile: boolean;
  defaultChartType: ChartTypeOption;
  defaultTimeframe: TimeframeOption;
  enableAnimations: boolean;
  enableInteractivity: boolean;
}

const DataVisualizationDashboard: React.FC<DataVisualizationDashboardProps> = ({
  darkMode,
  userData,
  expenses = [],
  investments = [],
  savingsGoals = [],
  className = '',
}) => {
  const { t } = useTranslation();
  const [dashboardSettings, setDashboardSettings] =
    useLocalStorage<DashboardSettings>('dashboard_settings', {
      showPerformanceMonitor: false,
      enableMobileOptimization: true,
      autoDetectMobile: true,
      defaultChartType: 'line',
      defaultTimeframe: '1Y',
      enableAnimations: true,
      enableInteractivity: true,
    });

  const [activeView, setActiveView] = useState<
    'enhanced' | 'mobile' | 'performance'
  >('enhanced');
  const [selectedDataPoint, setSelectedDataPoint] = useState<any>(null);
  const [performanceIssues, setPerformanceIssues] = useState<any[]>([]);

  // Detect if we should use mobile optimization
  const shouldUseMobileOptimization = useMemo(() => {
    if (!dashboardSettings.enableMobileOptimization) return false;
    if (dashboardSettings.autoDetectMobile) return isMobileDevice();
    return false;
  }, [
    dashboardSettings.enableMobileOptimization,
    dashboardSettings.autoDetectMobile,
  ]);

  // Handle performance issues
  const handlePerformanceIssue = useCallback((metric: any) => {
    setPerformanceIssues(prev => {
      const existing = prev.find(issue => issue.name === metric.name);
      if (existing) {
        return prev.map(issue => (issue.name === metric.name ? metric : issue));
      }
      return [...prev, metric];
    });
  }, []);

  // Handle data point selection
  const handleDataPointSelect = useCallback((dataPoint: any) => {
    setSelectedDataPoint(dataPoint);
  }, []);

  // Update dashboard settings
  const updateSettings = useCallback(
    (updates: Partial<DashboardSettings>) => {
      setDashboardSettings(prev => ({ ...prev, ...updates }));
    },
    [setDashboardSettings]
  );

  // Calculate dashboard statistics
  const dashboardStats = useMemo(() => {
    const totalExpenses = expenses.reduce(
      (sum, expense) => sum + (expense.amount || 0),
      0
    );
    const totalInvestments = investments.reduce(
      (sum, investment) => sum + (investment.amount || 0),
      0
    );
    const totalSavingsGoals = savingsGoals.reduce(
      (sum, goal) => sum + (goal.targetAmount || 0),
      0
    );

    return {
      totalExpenses,
      totalInvestments,
      totalSavingsGoals,
      savingsRate:
        ((userData.monthlyIncome - userData.monthlyExpenses) /
          userData.monthlyIncome) *
        100,
      fireProgress:
        (userData.currentSavings / (userData.monthlyExpenses * 12 * 25)) * 100,
    };
  }, [userData, expenses, investments, savingsGoals]);

  // Auto-switch to mobile view on mobile devices
  useEffect(() => {
    if (shouldUseMobileOptimization && activeView === 'enhanced') {
      setActiveView('mobile');
    }
  }, [shouldUseMobileOptimization, activeView]);

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Dashboard Header */}
      <div
        className={`p-6 rounded-lg border ${
          darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
        }`}
      >
        <div className='flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4'>
          <div>
            <h2
              className={`text-2xl font-bold ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}
            >
              📊 Data Visualization Dashboard
            </h2>
            <p
              className={`mt-1 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}
            >
              Comprehensive financial data analysis and interactive charts
            </p>
          </div>

          {/* View Selector */}
          <div className='flex gap-2'>
            <button
              onClick={() => setActiveView('enhanced')}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                activeView === 'enhanced'
                  ? 'bg-blue-600 text-white'
                  : darkMode
                    ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              📈 Enhanced
            </button>
            <button
              onClick={() => setActiveView('mobile')}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                activeView === 'mobile'
                  ? 'bg-blue-600 text-white'
                  : darkMode
                    ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              📱 Mobile
            </button>
            <button
              onClick={() => setActiveView('performance')}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                activeView === 'performance'
                  ? 'bg-blue-600 text-white'
                  : darkMode
                    ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              ⚡ Performance
            </button>
          </div>
        </div>

        {/* Dashboard Statistics */}
        <div className='grid grid-cols-2 md:grid-cols-5 gap-4 mt-6'>
          <div
            className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}
          >
            <div
              className={`text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}
            >
              Savings Rate
            </div>
            <div className='text-xl font-bold text-green-600'>
              {dashboardStats.savingsRate.toFixed(1)}%
            </div>
          </div>
          <div
            className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}
          >
            <div
              className={`text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}
            >
              FIRE Progress
            </div>
            <div className='text-xl font-bold text-blue-600'>
              {dashboardStats.fireProgress.toFixed(1)}%
            </div>
          </div>
          <div
            className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}
          >
            <div
              className={`text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}
            >
              Expenses
            </div>
            <div className='text-xl font-bold text-red-600'>
              CHF {dashboardStats.totalExpenses.toLocaleString()}
            </div>
          </div>
          <div
            className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}
          >
            <div
              className={`text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}
            >
              Investments
            </div>
            <div className='text-xl font-bold text-purple-600'>
              CHF {dashboardStats.totalInvestments.toLocaleString()}
            </div>
          </div>
          <div
            className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}
          >
            <div
              className={`text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}
            >
              Goals
            </div>
            <div className='text-xl font-bold text-orange-600'>
              CHF {dashboardStats.totalSavingsGoals.toLocaleString()}
            </div>
          </div>
        </div>
      </div>

      {/* Performance Monitor */}
      {dashboardSettings.showPerformanceMonitor && (
        <ChartPerformanceMonitor
          darkMode={darkMode}
          enabled={true}
          onPerformanceIssue={handlePerformanceIssue}
        />
      )}

      {/* Performance Issues Alert */}
      {performanceIssues.length > 0 && (
        <div
          className={`p-4 rounded-lg border-l-4 border-yellow-500 ${
            darkMode
              ? 'bg-yellow-900/20 border-yellow-400'
              : 'bg-yellow-50 border-yellow-500'
          }`}
        >
          <div className='flex items-center'>
            <span className='text-yellow-500 mr-2'>⚠️</span>
            <h4
              className={`font-semibold ${darkMode ? 'text-yellow-200' : 'text-yellow-800'}`}
            >
              Performance Issues Detected
            </h4>
          </div>
          <div className='mt-2 space-y-1'>
            {performanceIssues.map((issue, index) => (
              <div
                key={index}
                className={`text-sm ${darkMode ? 'text-yellow-300' : 'text-yellow-700'}`}
              >
                {issue.name}: {issue.value} {issue.unit} (threshold:{' '}
                {issue.threshold} {issue.unit})
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Selected Data Point Info */}
      {selectedDataPoint && (
        <div
          className={`p-4 rounded-lg border ${
            darkMode
              ? 'bg-blue-900/20 border-blue-400'
              : 'bg-blue-50 border-blue-200'
          }`}
        >
          <div className='flex items-center justify-between'>
            <div>
              <h4
                className={`font-semibold ${darkMode ? 'text-blue-200' : 'text-blue-800'}`}
              >
                📍 Selected Data Point
              </h4>
              <div
                className={`text-sm mt-1 ${darkMode ? 'text-blue-300' : 'text-blue-700'}`}
              >
                Date: {selectedDataPoint.date?.toLocaleDateString('de-CH')} |
                Value: CHF {selectedDataPoint.value?.toLocaleString()}
                {selectedDataPoint.label && ` | ${selectedDataPoint.label}`}
              </div>
            </div>
            <button
              onClick={() => setSelectedDataPoint(null)}
              className={`text-sm px-2 py-1 rounded ${
                darkMode
                  ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              ✕
            </button>
          </div>
        </div>
      )}

      {/* Main Visualization Content */}
      <div className='space-y-6'>
        {activeView === 'enhanced' && (
          <EnhancedHistoricalCharts
            darkMode={darkMode}
            userData={userData}
            expenses={expenses}
            investments={investments}
            savingsGoals={savingsGoals}
          />
        )}

        {activeView === 'mobile' && (
          <div
            className={`p-6 rounded-lg border ${
              darkMode
                ? 'bg-gray-800 border-gray-700'
                : 'bg-white border-gray-200'
            }`}
          >
            <h3
              className={`text-lg font-semibold mb-4 ${darkMode ? 'text-gray-200' : 'text-gray-800'}`}
            >
              📱 Mobile-Optimized Charts
            </h3>
            <div className='space-y-6'>
              <MobileOptimizedChart
                data={[
                  {
                    date: new Date('2023-01-01'),
                    value: userData.currentSavings * 0.8,
                    label: 'Start',
                  },
                  {
                    date: new Date('2023-06-01'),
                    value: userData.currentSavings * 0.9,
                    label: 'Mid Year',
                  },
                  {
                    date: new Date('2023-12-01'),
                    value: userData.currentSavings,
                    label: 'Current',
                  },
                ]}
                darkMode={darkMode}
                color='#10B981'
                onDataPointSelect={handleDataPointSelect}
              />
            </div>
          </div>
        )}

        {activeView === 'performance' && (
          <div
            className={`p-6 rounded-lg border ${
              darkMode
                ? 'bg-gray-800 border-gray-700'
                : 'bg-white border-gray-200'
            }`}
          >
            <h3
              className={`text-lg font-semibold mb-4 ${darkMode ? 'text-gray-200' : 'text-gray-800'}`}
            >
              ⚡ Performance Analysis
            </h3>
            <ChartPerformanceMonitor
              darkMode={darkMode}
              enabled={true}
              onPerformanceIssue={handlePerformanceIssue}
            />
          </div>
        )}
      </div>

      {/* Dashboard Settings */}
      <div
        className={`p-4 rounded-lg border ${
          darkMode
            ? 'bg-gray-800 border-gray-700'
            : 'bg-gray-50 border-gray-200'
        }`}
      >
        <h4
          className={`text-sm font-semibold mb-3 ${darkMode ? 'text-gray-200' : 'text-gray-800'}`}
        >
          ⚙️ Dashboard Settings
        </h4>
        <div className='grid grid-cols-2 md:grid-cols-4 gap-4'>
          <label className='flex items-center space-x-2'>
            <input
              type='checkbox'
              checked={dashboardSettings.showPerformanceMonitor}
              onChange={e =>
                updateSettings({ showPerformanceMonitor: e.target.checked })
              }
              className='rounded'
            />
            <span
              className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}
            >
              Performance Monitor
            </span>
          </label>
          <label className='flex items-center space-x-2'>
            <input
              type='checkbox'
              checked={dashboardSettings.enableMobileOptimization}
              onChange={e =>
                updateSettings({ enableMobileOptimization: e.target.checked })
              }
              className='rounded'
            />
            <span
              className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}
            >
              Mobile Optimization
            </span>
          </label>
          <label className='flex items-center space-x-2'>
            <input
              type='checkbox'
              checked={dashboardSettings.enableAnimations}
              onChange={e =>
                updateSettings({ enableAnimations: e.target.checked })
              }
              className='rounded'
            />
            <span
              className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}
            >
              Animations
            </span>
          </label>
          <label className='flex items-center space-x-2'>
            <input
              type='checkbox'
              checked={dashboardSettings.enableInteractivity}
              onChange={e =>
                updateSettings({ enableInteractivity: e.target.checked })
              }
              className='rounded'
            />
            <span
              className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}
            >
              Interactivity
            </span>
          </label>
        </div>
      </div>
    </div>
  );
};

export default DataVisualizationDashboard;
