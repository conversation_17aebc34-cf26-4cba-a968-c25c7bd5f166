import React, { useEffect, useMemo, useState } from 'react';
import { LogEntry, logger, LogLevel, PerformanceMetric } from '../utils/logger';

interface LogViewerProps {
  isOpen: boolean;
  onClose: () => void;
}

const LogViewer: React.FC<LogViewerProps> = ({ isOpen, onClose }) => {
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [metrics, setMetrics] = useState<PerformanceMetric[]>([]);
  const [selectedLevel, setSelectedLevel] = useState<LogLevel | 'ALL'>('ALL');
  const [selectedCategory, setSelectedCategory] = useState<string>('ALL');
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState<
    'logs' | 'performance' | 'summary'
  >('logs');
  const [autoRefresh, setAutoRefresh] = useState(true);

  useEffect(() => {
    if (!isOpen) return;

    const refreshData = () => {
      const exportedData = logger.exportLogs();
      setLogs(exportedData.logs);
      setMetrics(exportedData.metrics);
    };

    refreshData();

    if (autoRefresh) {
      const interval = setInterval(refreshData, 1000);
      return () => clearInterval(interval);
    }
  }, [isOpen, autoRefresh]);

  const filteredLogs = useMemo(() => {
    return logs.filter(log => {
      if (selectedLevel !== 'ALL' && log.level !== selectedLevel) return false;
      if (selectedCategory !== 'ALL' && log.category !== selectedCategory)
        return false;
      if (
        searchTerm &&
        !log.message.toLowerCase().includes(searchTerm.toLowerCase()) &&
        !log.category.toLowerCase().includes(searchTerm.toLowerCase())
      )
        return false;
      return true;
    });
  }, [logs, selectedLevel, selectedCategory, searchTerm]);

  const categories = useMemo(() => {
    const cats = new Set(logs.map(log => log.category));
    return Array.from(cats).sort();
  }, [logs]);

  const performanceSummary = useMemo(() => {
    return logger.getPerformanceSummary();
  }, [metrics]);

  const getLogLevelColor = (level: LogLevel): string => {
    switch (level) {
      case LogLevel.DEBUG:
        return 'text-gray-500';
      case LogLevel.INFO:
        return 'text-blue-600';
      case LogLevel.WARN:
        return 'text-yellow-600';
      case LogLevel.ERROR:
        return 'text-red-600';
      case LogLevel.CRITICAL:
        return 'text-red-800 font-bold';
      default:
        return 'text-gray-600';
    }
  };

  const getLogLevelName = (level: LogLevel): string => {
    return LogLevel[level];
  };

  const exportLogs = () => {
    const data = {
      logs: filteredLogs,
      metrics,
      summary: performanceSummary,
      exportTime: new Date().toISOString(),
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], {
      type: 'application/json',
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `fire-or-retire-logs-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const clearLogs = () => {
    logger.clearLogs();
    setLogs([]);
    setMetrics([]);
  };

  if (!isOpen) return null;

  return (
    <div className='fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4'>
      <div className='bg-white rounded-lg shadow-xl w-full max-w-6xl h-5/6 flex flex-col'>
        {/* Header */}
        <div className='flex items-center justify-between p-4 border-b'>
          <h2 className='text-xl font-bold'>Runtime Logs & Performance</h2>
          <div className='flex items-center space-x-2'>
            <label className='flex items-center space-x-2'>
              <input
                type='checkbox'
                checked={autoRefresh}
                onChange={e => setAutoRefresh(e.target.checked)}
                className='rounded'
              />
              <span className='text-sm'>Auto Refresh</span>
            </label>
            <button
              onClick={exportLogs}
              className='px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700'
            >
              Export
            </button>
            <button
              onClick={clearLogs}
              className='px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700'
            >
              Clear
            </button>
            <button
              onClick={onClose}
              className='px-3 py-1 bg-gray-600 text-white rounded text-sm hover:bg-gray-700'
            >
              Close
            </button>
          </div>
        </div>

        {/* Tabs */}
        <div className='flex border-b'>
          {(['logs', 'performance', 'summary'] as const).map(tab => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab)}
              className={`px-4 py-2 font-medium capitalize ${
                activeTab === tab
                  ? 'border-b-2 border-blue-600 text-blue-600'
                  : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              {tab}
              {tab === 'logs' && (
                <span className='ml-1 text-xs'>({logs.length})</span>
              )}
              {tab === 'performance' && (
                <span className='ml-1 text-xs'>({metrics.length})</span>
              )}
            </button>
          ))}
        </div>

        {/* Content */}
        <div className='flex-1 overflow-hidden'>
          {activeTab === 'logs' && (
            <div className='h-full flex flex-col'>
              {/* Filters */}
              <div className='p-4 border-b bg-gray-50 flex flex-wrap gap-4'>
                <div>
                  <label className='block text-sm font-medium mb-1'>
                    Level
                  </label>
                  <select
                    value={selectedLevel}
                    onChange={e =>
                      setSelectedLevel(e.target.value as LogLevel | 'ALL')
                    }
                    className='border rounded px-2 py-1 text-sm'
                  >
                    <option value='ALL'>All Levels</option>
                    {Object.entries(LogLevel)
                      .filter(([key]) => isNaN(Number(key)))
                      .map(([key, value]) => (
                        <option key={key} value={value}>
                          {key}
                        </option>
                      ))}
                  </select>
                </div>
                <div>
                  <label className='block text-sm font-medium mb-1'>
                    Category
                  </label>
                  <select
                    value={selectedCategory}
                    onChange={e => setSelectedCategory(e.target.value)}
                    className='border rounded px-2 py-1 text-sm'
                  >
                    <option value='ALL'>All Categories</option>
                    {categories.map(cat => (
                      <option key={cat} value={cat}>
                        {cat}
                      </option>
                    ))}
                  </select>
                </div>
                <div className='flex-1'>
                  <label className='block text-sm font-medium mb-1'>
                    Search
                  </label>
                  <input
                    type='text'
                    value={searchTerm}
                    onChange={e => setSearchTerm(e.target.value)}
                    placeholder='Search logs...'
                    className='border rounded px-2 py-1 text-sm w-full'
                  />
                </div>
              </div>

              {/* Logs List */}
              <div className='flex-1 overflow-auto p-4'>
                <div className='space-y-2'>
                  {filteredLogs.map((log, index) => (
                    <div
                      key={index}
                      className='border rounded p-3 bg-gray-50 text-sm'
                    >
                      <div className='flex items-center justify-between mb-1'>
                        <div className='flex items-center space-x-2'>
                          <span
                            className={`font-medium ${getLogLevelColor(log.level)}`}
                          >
                            {getLogLevelName(log.level)}
                          </span>
                          <span className='text-blue-600 font-medium'>
                            [{log.category}]
                          </span>
                          <span className='text-gray-500'>
                            {new Date(log.timestamp).toLocaleTimeString()}
                          </span>
                        </div>
                      </div>
                      <div className='font-medium mb-1'>{log.message}</div>
                      {log.data && (
                        <details className='mt-2'>
                          <summary className='cursor-pointer text-gray-600'>
                            Data
                          </summary>
                          <pre className='mt-1 p-2 bg-gray-100 rounded text-xs overflow-auto'>
                            {JSON.stringify(log.data, null, 2)}
                          </pre>
                        </details>
                      )}
                      {log.stack && (
                        <details className='mt-2'>
                          <summary className='cursor-pointer text-red-600'>
                            Stack Trace
                          </summary>
                          <pre className='mt-1 p-2 bg-red-50 rounded text-xs overflow-auto text-red-800'>
                            {log.stack}
                          </pre>
                        </details>
                      )}
                    </div>
                  ))}
                  {filteredLogs.length === 0 && (
                    <div className='text-center text-gray-500 py-8'>
                      No logs match the current filters
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'performance' && (
            <div className='h-full overflow-auto p-4'>
              <div className='space-y-4'>
                <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4'>
                  {Object.entries(performanceSummary.averages).map(
                    ([category, average]) => (
                      <div key={category} className='bg-blue-50 p-4 rounded'>
                        <h3 className='font-medium text-blue-800 capitalize'>
                          {category}
                        </h3>
                        <p className='text-2xl font-bold text-blue-600'>
                          {Math.round(average as number)}ms
                        </p>
                        <p className='text-sm text-blue-600'>Average</p>
                      </div>
                    )
                  )}
                </div>

                {performanceSummary.slowOperations.length > 0 && (
                  <div>
                    <h3 className='font-bold text-red-600 mb-2'>
                      Slow Operations (&gt;1s)
                    </h3>
                    <div className='space-y-2'>
                      {performanceSummary.slowOperations.map(
                        (metric: PerformanceMetric, index: number) => (
                          <div key={index} className='bg-red-50 p-3 rounded'>
                            <div className='flex justify-between items-center'>
                              <span className='font-medium'>{metric.name}</span>
                              <span className='text-red-600 font-bold'>
                                {Math.round(metric.value)}ms
                              </span>
                            </div>
                            <div className='text-sm text-gray-600'>
                              {metric.category} •{' '}
                              {new Date(metric.timestamp).toLocaleTimeString()}
                            </div>
                          </div>
                        )
                      )}
                    </div>
                  </div>
                )}

                <div>
                  <h3 className='font-bold mb-2'>Recent Metrics</h3>
                  <div className='space-y-1'>
                    {metrics
                      .slice(-20)
                      .reverse()
                      .map((metric, index) => (
                        <div
                          key={index}
                          className='flex justify-between items-center py-1 border-b'
                        >
                          <span className='text-sm'>{metric.name}</span>
                          <div className='text-right'>
                            <span
                              className={`font-medium ${metric.value > 100 ? 'text-red-600' : 'text-green-600'}`}
                            >
                              {Math.round(metric.value)}ms
                            </span>
                            <div className='text-xs text-gray-500'>
                              {metric.category}
                            </div>
                          </div>
                        </div>
                      ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'summary' && (
            <div className='h-full overflow-auto p-4'>
              <div className='space-y-6'>
                <div>
                  <h3 className='font-bold mb-2'>Session Summary</h3>
                  <div className='grid grid-cols-2 md:grid-cols-4 gap-4'>
                    <div className='bg-gray-50 p-3 rounded'>
                      <div className='text-2xl font-bold'>{logs.length}</div>
                      <div className='text-sm text-gray-600'>Total Logs</div>
                    </div>
                    <div className='bg-red-50 p-3 rounded'>
                      <div className='text-2xl font-bold text-red-600'>
                        {logs.filter(l => l.level >= LogLevel.ERROR).length}
                      </div>
                      <div className='text-sm text-gray-600'>Errors</div>
                    </div>
                    <div className='bg-yellow-50 p-3 rounded'>
                      <div className='text-2xl font-bold text-yellow-600'>
                        {logs.filter(l => l.level === LogLevel.WARN).length}
                      </div>
                      <div className='text-sm text-gray-600'>Warnings</div>
                    </div>
                    <div className='bg-green-50 p-3 rounded'>
                      <div className='text-2xl font-bold text-green-600'>
                        {metrics.length}
                      </div>
                      <div className='text-sm text-gray-600'>Metrics</div>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className='font-bold mb-2'>Error Categories</h3>
                  <div className='space-y-2'>
                    {Object.entries(
                      logs
                        .filter(l => l.level >= LogLevel.ERROR)
                        .reduce(
                          (acc, log) => {
                            acc[log.category] = (acc[log.category] || 0) + 1;
                            return acc;
                          },
                          {} as Record<string, number>
                        )
                    ).map(([category, count]) => (
                      <div
                        key={category}
                        className='flex justify-between items-center py-1 border-b'
                      >
                        <span>{category}</span>
                        <span className='font-bold text-red-600'>{count}</span>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <h3 className='font-bold mb-2'>Performance by Category</h3>
                  <pre className='bg-gray-100 p-4 rounded text-sm overflow-auto'>
                    {JSON.stringify(performanceSummary, null, 2)}
                  </pre>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default LogViewer;
