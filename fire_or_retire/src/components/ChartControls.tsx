import React, { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';

export type TimeframeOption = '1M' | '3M' | '6M' | '1Y' | '2Y' | 'ALL';
export type ChartTypeOption = 'line' | 'area' | 'bar' | 'scatter';
export type ExportFormat = 'png' | 'svg' | 'pdf' | 'csv' | 'json';

interface ChartControlsProps {
  darkMode: boolean;
  selectedTimeframe: TimeframeOption;
  selectedChartType: ChartTypeOption;
  selectedMetrics: string[];
  availableMetrics: Array<{
    key: string;
    label: string;
    color: string;
    enabled: boolean;
  }>;
  onTimeframeChange: (timeframe: TimeframeOption) => void;
  onChartTypeChange: (chartType: ChartTypeOption) => void;
  onMetricToggle: (metricKey: string) => void;
  onExport?: (format: ExportFormat) => void;
  onZoomIn?: () => void;
  onZoomOut?: () => void;
  onResetZoom?: () => void;
  onFullscreen?: () => void;
  showExportOptions?: boolean;
  showZoomControls?: boolean;
  showChartTypeSelector?: boolean;
  className?: string;
}

const ChartControls: React.FC<ChartControlsProps> = ({
  darkMode,
  selectedTimeframe,
  selectedChartType,
  selectedMetrics,
  availableMetrics,
  onTimeframeChange,
  onChartTypeChange,
  onMetricToggle,
  onExport,
  onZoomIn,
  onZoomOut,
  onResetZoom,
  onFullscreen,
  showExportOptions = true,
  showZoomControls = true,
  showChartTypeSelector = true,
  className = '',
}) => {
  const { t } = useTranslation();
  const [showExportMenu, setShowExportMenu] = useState(false);

  const timeframeOptions: Array<{ value: TimeframeOption; label: string }> = [
    { value: '1M', label: '1 Month' },
    { value: '3M', label: '3 Months' },
    { value: '6M', label: '6 Months' },
    { value: '1Y', label: '1 Year' },
    { value: '2Y', label: '2 Years' },
    { value: 'ALL', label: 'All Time' },
  ];

  const chartTypeOptions: Array<{
    value: ChartTypeOption;
    label: string;
    icon: string;
  }> = [
    { value: 'line', label: 'Line Chart', icon: '📈' },
    { value: 'area', label: 'Area Chart', icon: '📊' },
    { value: 'bar', label: 'Bar Chart', icon: '📊' },
    { value: 'scatter', label: 'Scatter Plot', icon: '⚪' },
  ];

  const exportOptions: Array<{
    value: ExportFormat;
    label: string;
    icon: string;
  }> = [
    { value: 'png', label: 'PNG Image', icon: '🖼️' },
    { value: 'svg', label: 'SVG Vector', icon: '🎨' },
    { value: 'pdf', label: 'PDF Document', icon: '📄' },
    { value: 'csv', label: 'CSV Data', icon: '📋' },
    { value: 'json', label: 'JSON Data', icon: '💾' },
  ];

  const handleExport = useCallback(
    (format: ExportFormat) => {
      onExport?.(format);
      setShowExportMenu(false);
    },
    [onExport]
  );

  const buttonClass = `px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
    darkMode
      ? 'bg-gray-700 text-gray-200 hover:bg-gray-600 border-gray-600'
      : 'bg-white text-gray-700 hover:bg-gray-50 border-gray-300'
  } border`;

  const activeButtonClass = `px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
    darkMode
      ? 'bg-blue-600 text-white hover:bg-blue-700'
      : 'bg-blue-600 text-white hover:bg-blue-700'
  }`;

  return (
    <div
      className={`flex flex-wrap items-center gap-4 p-4 rounded-lg border ${
        darkMode ? 'bg-gray-800 border-gray-700' : 'bg-gray-50 border-gray-200'
      } ${className}`}
    >
      {/* Timeframe Selector */}
      <div className='flex items-center gap-2'>
        <label
          className={`text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}
        >
          📅 Timeframe:
        </label>
        <div className='flex gap-1'>
          {timeframeOptions.map(option => (
            <button
              key={option.value}
              onClick={() => onTimeframeChange(option.value)}
              className={
                selectedTimeframe === option.value
                  ? activeButtonClass
                  : buttonClass
              }
            >
              {option.label}
            </button>
          ))}
        </div>
      </div>

      {/* Chart Type Selector */}
      {showChartTypeSelector && (
        <div className='flex items-center gap-2'>
          <label
            className={`text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}
          >
            📊 Type:
          </label>
          <div className='flex gap-1'>
            {chartTypeOptions.map(option => (
              <button
                key={option.value}
                onClick={() => onChartTypeChange(option.value)}
                className={
                  selectedChartType === option.value
                    ? activeButtonClass
                    : buttonClass
                }
                title={option.label}
              >
                <span className='mr-1'>{option.icon}</span>
                <span className='hidden sm:inline'>{option.label}</span>
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Metric Toggles */}
      <div className='flex items-center gap-2'>
        <label
          className={`text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}
        >
          📈 Metrics:
        </label>
        <div className='flex flex-wrap gap-1'>
          {availableMetrics.map(metric => (
            <button
              key={metric.key}
              onClick={() => onMetricToggle(metric.key)}
              className={`px-2 py-1 rounded text-xs font-medium transition-colors border ${
                selectedMetrics.includes(metric.key)
                  ? 'bg-opacity-20 border-opacity-50 text-opacity-90'
                  : darkMode
                    ? 'bg-gray-700 text-gray-400 border-gray-600 hover:bg-gray-600'
                    : 'bg-white text-gray-600 border-gray-300 hover:bg-gray-50'
              }`}
              style={
                selectedMetrics.includes(metric.key)
                  ? {
                      backgroundColor: `${metric.color}20`,
                      borderColor: `${metric.color}80`,
                      color: metric.color,
                    }
                  : {}
              }
            >
              <span
                className='inline-block w-2 h-2 rounded-full mr-1'
                style={{ backgroundColor: metric.color }}
              />
              {metric.label}
            </button>
          ))}
        </div>
      </div>

      {/* Zoom Controls */}
      {showZoomControls && (
        <div className='flex items-center gap-1'>
          <button onClick={onZoomIn} className={buttonClass} title='Zoom In'>
            🔍+
          </button>
          <button onClick={onZoomOut} className={buttonClass} title='Zoom Out'>
            🔍-
          </button>
          <button
            onClick={onResetZoom}
            className={buttonClass}
            title='Reset Zoom'
          >
            🔄
          </button>
        </div>
      )}

      {/* Export Controls */}
      {showExportOptions && (
        <div className='relative'>
          <button
            onClick={() => setShowExportMenu(!showExportMenu)}
            className={buttonClass}
          >
            📤 Export
          </button>

          {showExportMenu && (
            <div
              className={`absolute top-full right-0 mt-1 py-1 rounded-lg shadow-lg border z-10 ${
                darkMode
                  ? 'bg-gray-800 border-gray-700'
                  : 'bg-white border-gray-200'
              }`}
            >
              {exportOptions.map(option => (
                <button
                  key={option.value}
                  onClick={() => handleExport(option.value)}
                  className={`w-full px-4 py-2 text-left text-sm hover:bg-opacity-10 transition-colors ${
                    darkMode
                      ? 'text-gray-200 hover:bg-gray-600'
                      : 'text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  <span className='mr-2'>{option.icon}</span>
                  {option.label}
                </button>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Fullscreen Toggle */}
      <button
        onClick={onFullscreen}
        className={buttonClass}
        title='Toggle Fullscreen'
      >
        ⛶
      </button>
    </div>
  );
};

export default ChartControls;

// Chart Export Utilities
export const exportChartAsPNG = async (
  svgElement: SVGSVGElement,
  filename = 'chart.png'
) => {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  if (!ctx) return;

  const svgData = new XMLSerializer().serializeToString(svgElement);
  const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
  const url = URL.createObjectURL(svgBlob);

  const img = new Image();
  img.onload = () => {
    canvas.width = img.width;
    canvas.height = img.height;
    ctx.drawImage(img, 0, 0);

    canvas.toBlob(blob => {
      if (blob) {
        const link = document.createElement('a');
        link.download = filename;
        link.href = URL.createObjectURL(blob);
        link.click();
        URL.revokeObjectURL(link.href);
      }
    });

    URL.revokeObjectURL(url);
  };
  img.src = url;
};

export const exportChartAsSVG = (
  svgElement: SVGSVGElement,
  filename = 'chart.svg'
) => {
  const svgData = new XMLSerializer().serializeToString(svgElement);
  const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
  const url = URL.createObjectURL(svgBlob);

  const link = document.createElement('a');
  link.download = filename;
  link.href = url;
  link.click();

  URL.revokeObjectURL(url);
};

export const exportDataAsCSV = (data: any[], filename = 'chart-data.csv') => {
  if (!data.length) return;

  const headers = Object.keys(data[0]);
  const csvContent = [
    headers.join(','),
    ...data.map(row =>
      headers
        .map(header => {
          const value = row[header];
          return typeof value === 'string' && value.includes(',')
            ? `"${value}"`
            : value;
        })
        .join(',')
    ),
  ].join('\n');

  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8' });
  const url = URL.createObjectURL(blob);

  const link = document.createElement('a');
  link.download = filename;
  link.href = url;
  link.click();

  URL.revokeObjectURL(url);
};

export const exportDataAsJSON = (data: any[], filename = 'chart-data.json') => {
  const jsonContent = JSON.stringify(data, null, 2);
  const blob = new Blob([jsonContent], {
    type: 'application/json;charset=utf-8',
  });
  const url = URL.createObjectURL(blob);

  const link = document.createElement('a');
  link.download = filename;
  link.href = url;
  link.click();

  URL.revokeObjectURL(url);
};
