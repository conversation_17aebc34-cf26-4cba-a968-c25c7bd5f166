/**
 * Configuration Import/Export Panel for Swiss Budget Pro Admin
 * Allows importing and exporting system configuration
 */

import React, { useState, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useAdminConfig } from '../../contexts/AdminConfigContext';

interface ConfigImportExportProps {
  darkMode?: boolean;
}

export const ConfigImportExport: React.FC<ConfigImportExportProps> = ({
  darkMode = true,
}) => {
  const { t } = useTranslation();
  const { config, exportConfig, importConfig } = useAdminConfig();
  const [importStatus, setImportStatus] = useState<
    'idle' | 'loading' | 'success' | 'error'
  >('idle');
  const [importError, setImportError] = useState<string>('');
  const [exportStatus, setExportStatus] = useState<
    'idle' | 'loading' | 'success'
  >('idle');
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleExport = async () => {
    setExportStatus('loading');

    try {
      const configJson = exportConfig();
      const blob = new Blob([configJson], { type: 'application/json' });
      const url = URL.createObjectURL(blob);

      const link = document.createElement('a');
      link.href = url;
      link.download = `swiss-budget-pro-config-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      URL.revokeObjectURL(url);
      setExportStatus('success');

      setTimeout(() => setExportStatus('idle'), 3000);
    } catch (error) {
      console.error('Export failed:', error);
      setExportStatus('idle');
    }
  };

  const handleImport = async (file: File) => {
    setImportStatus('loading');
    setImportError('');

    try {
      const text = await file.text();

      // Validate JSON format
      try {
        JSON.parse(text);
      } catch {
        throw new Error('Invalid JSON format');
      }

      await importConfig(text);
      setImportStatus('success');

      setTimeout(() => setImportStatus('idle'), 3000);
    } catch (error) {
      setImportError(error instanceof Error ? error.message : 'Import failed');
      setImportStatus('error');
    }
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleImport(file);
    }
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    const file = event.dataTransfer.files[0];
    if (file && file.type === 'application/json') {
      handleImport(file);
    }
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
  };

  const generateConfigPreview = () => {
    const configPreview = {
      version: config.version,
      lastModified: config.lastModified,
      sections: {
        inflation: Object.keys(config.inflation).length,
        marketData: Object.keys(config.marketData).length,
        tax: Object.keys(config.tax).length,
        healthcare: Object.keys(config.healthcare).length,
        system: Object.keys(config.system).length,
      },
      totalSize: JSON.stringify(config).length,
    };

    return configPreview;
  };

  const configPreview = generateConfigPreview();

  const cardClasses = `rounded-lg border p-6 ${
    darkMode ? 'bg-gray-800 border-gray-700' : 'bg-gray-50 border-gray-200'
  }`;

  const buttonClasses =
    'px-6 py-3 rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-opacity-50';

  return (
    <div className='space-y-8'>
      {/* Header */}
      <div>
        <h2 className='text-2xl font-bold mb-2'>
          📁 {t('admin.importExport.title', 'Configuration Import/Export')}
        </h2>
        <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
          {t(
            'admin.importExport.description',
            'Backup and restore system configuration settings'
          )}
        </p>
      </div>

      {/* Current Configuration Overview */}
      <div className={cardClasses}>
        <h3 className='text-lg font-semibold mb-4'>
          📊 {t('admin.importExport.currentConfig', 'Current Configuration')}
        </h3>

        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
          <div>
            <h4 className='font-medium mb-2'>
              {t('admin.importExport.version', 'Version')}
            </h4>
            <p
              className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}
            >
              {configPreview.version}
            </p>
          </div>

          <div>
            <h4 className='font-medium mb-2'>
              {t('admin.importExport.lastModified', 'Last Modified')}
            </h4>
            <p
              className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}
            >
              {new Date(configPreview.lastModified).toLocaleString()}
            </p>
          </div>

          <div>
            <h4 className='font-medium mb-2'>
              {t('admin.importExport.configSize', 'Configuration Size')}
            </h4>
            <p
              className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}
            >
              {(configPreview.totalSize / 1024).toFixed(1)} KB
            </p>
          </div>
        </div>

        <div className='mt-6'>
          <h4 className='font-medium mb-3'>
            {t('admin.importExport.sections', 'Configuration Sections')}
          </h4>
          <div className='grid grid-cols-2 md:grid-cols-5 gap-4'>
            {Object.entries(configPreview.sections).map(([section, count]) => (
              <div
                key={section}
                className={`p-3 rounded-lg text-center ${
                  darkMode ? 'bg-gray-700' : 'bg-gray-100'
                }`}
              >
                <div className='text-lg font-bold'>{count}</div>
                <div
                  className={`text-xs capitalize ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}
                >
                  {section}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Export Configuration */}
      <div className={cardClasses}>
        <h3 className='text-lg font-semibold mb-4'>
          📤 {t('admin.importExport.export', 'Export Configuration')}
        </h3>

        <div className='space-y-4'>
          <p
            className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}
          >
            {t(
              'admin.importExport.exportDescription',
              'Download the current configuration as a JSON file for backup or transfer to another system.'
            )}
          </p>

          <div className='flex items-center space-x-4'>
            <button
              onClick={handleExport}
              disabled={exportStatus === 'loading'}
              className={`${buttonClasses} ${
                exportStatus === 'loading'
                  ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                  : exportStatus === 'success'
                    ? 'bg-green-600 hover:bg-green-700 text-white focus:ring-green-500'
                    : 'bg-blue-600 hover:bg-blue-700 text-white focus:ring-blue-500'
              }`}
            >
              {exportStatus === 'loading' && '⏳ Exporting...'}
              {exportStatus === 'success' && '✅ Exported!'}
              {exportStatus === 'idle' && '📤 Export Configuration'}
            </button>

            {exportStatus === 'success' && (
              <span
                className={`text-sm ${darkMode ? 'text-green-400' : 'text-green-600'}`}
              >
                {t(
                  'admin.importExport.exportSuccess',
                  'Configuration exported successfully!'
                )}
              </span>
            )}
          </div>
        </div>
      </div>

      {/* Import Configuration */}
      <div className={cardClasses}>
        <h3 className='text-lg font-semibold mb-4'>
          📥 {t('admin.importExport.import', 'Import Configuration')}
        </h3>

        <div className='space-y-6'>
          <p
            className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}
          >
            {t(
              'admin.importExport.importDescription',
              'Upload a configuration JSON file to restore or update system settings.'
            )}
          </p>

          {/* Import Status */}
          {importStatus !== 'idle' && (
            <div
              className={`p-4 rounded-lg ${
                importStatus === 'loading'
                  ? darkMode
                    ? 'bg-blue-900/30 border border-blue-500/50'
                    : 'bg-blue-50 border border-blue-200'
                  : importStatus === 'success'
                    ? darkMode
                      ? 'bg-green-900/30 border border-green-500/50'
                      : 'bg-green-50 border border-green-200'
                    : darkMode
                      ? 'bg-red-900/30 border border-red-500/50'
                      : 'bg-red-50 border border-red-200'
              }`}
            >
              {importStatus === 'loading' && (
                <div className='flex items-center space-x-2'>
                  <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500'></div>
                  <span className='text-blue-500'>
                    {t(
                      'admin.importExport.importing',
                      'Importing configuration...'
                    )}
                  </span>
                </div>
              )}

              {importStatus === 'success' && (
                <div className='flex items-center space-x-2'>
                  <span className='text-green-500'>✅</span>
                  <span className='text-green-500'>
                    {t(
                      'admin.importExport.importSuccess',
                      'Configuration imported successfully!'
                    )}
                  </span>
                </div>
              )}

              {importStatus === 'error' && (
                <div className='flex items-center space-x-2'>
                  <span className='text-red-500'>❌</span>
                  <span className='text-red-500'>
                    {t('admin.importExport.importError', 'Import failed')}:{' '}
                    {importError}
                  </span>
                </div>
              )}
            </div>
          )}

          {/* Drop Zone */}
          <div
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
              darkMode
                ? 'border-gray-600 hover:border-gray-500 bg-gray-700/50'
                : 'border-gray-300 hover:border-gray-400 bg-gray-50'
            }`}
          >
            <div className='space-y-4'>
              <div className='text-4xl'>📁</div>
              <div>
                <p className='font-medium mb-2'>
                  {t(
                    'admin.importExport.dropZone',
                    'Drop configuration file here'
                  )}
                </p>
                <p
                  className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}
                >
                  {t(
                    'admin.importExport.dropZoneDesc',
                    'or click to select a file'
                  )}
                </p>
              </div>

              <button
                onClick={() => fileInputRef.current?.click()}
                className={`${buttonClasses} bg-gray-600 hover:bg-gray-700 text-white focus:ring-gray-500`}
              >
                📂 {t('admin.importExport.selectFile', 'Select File')}
              </button>

              <input
                ref={fileInputRef}
                type='file'
                accept='.json'
                onChange={handleFileSelect}
                className='hidden'
              />
            </div>
          </div>
        </div>
      </div>

      {/* Import/Export Guidelines */}
      <div className={cardClasses}>
        <h3 className='text-lg font-semibold mb-4'>
          📋 {t('admin.importExport.guidelines', 'Import/Export Guidelines')}
        </h3>

        <div className='space-y-4'>
          <div
            className={`p-4 rounded-lg ${darkMode ? 'bg-blue-900/30' : 'bg-blue-50'}`}
          >
            <h4 className='font-medium mb-2'>
              ⚠️ {t('admin.importExport.backupWarning', 'Backup Warning')}
            </h4>
            <p
              className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}
            >
              {t(
                'admin.importExport.backupWarningDesc',
                'Always export your current configuration before importing a new one. Imports will overwrite existing settings.'
              )}
            </p>
          </div>

          <div
            className={`p-4 rounded-lg ${darkMode ? 'bg-green-900/30' : 'bg-green-50'}`}
          >
            <h4 className='font-medium mb-2'>
              ✅ {t('admin.importExport.compatibility', 'Compatibility')}
            </h4>
            <p
              className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}
            >
              {t(
                'admin.importExport.compatibilityDesc',
                'Configuration files are compatible across different versions. Missing fields will use default values.'
              )}
            </p>
          </div>

          <div
            className={`p-4 rounded-lg ${darkMode ? 'bg-yellow-900/30' : 'bg-yellow-50'}`}
          >
            <h4 className='font-medium mb-2'>
              🔒 {t('admin.importExport.security', 'Security')}
            </h4>
            <p
              className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}
            >
              {t(
                'admin.importExport.securityDesc',
                'Only import configuration files from trusted sources. Malicious configurations could affect system behavior.'
              )}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
