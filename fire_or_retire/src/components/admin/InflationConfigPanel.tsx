/**
 * Inflation Configuration Panel for Swiss Budget Pro Admin
 * Allows configuration of inflation rates and economic assumptions
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useAdminConfig } from '../../contexts/AdminConfigContext';

interface InflationConfigPanelProps {
  darkMode?: boolean;
}

export const InflationConfigPanel: React.FC<InflationConfigPanelProps> = ({
  darkMode = true,
}) => {
  const { t } = useTranslation();
  const { config, updateSection } = useAdminConfig();
  const [localConfig, setLocalConfig] = useState(config.inflation);
  const [validationErrors, setValidationErrors] = useState<
    Record<string, string>
  >({});

  // Update local config when global config changes
  useEffect(() => {
    setLocalConfig(config.inflation);
  }, [config.inflation]);

  const validateField = (field: string, value: number): string | null => {
    switch (field) {
      case 'currentCPI':
      case 'coreCPI':
      case 'historicalAverage':
      case 'targetInflation':
        if (value < -10 || value > 20) {
          return t(
            'admin.validation.inflationRange',
            'Inflation rate must be between -10% and 20%'
          );
        }
        break;
      case 'housingCosts':
      case 'healthcareCosts':
      case 'energyCosts':
      case 'foodCosts':
        if (value < -20 || value > 50) {
          return t(
            'admin.validation.sectorInflationRange',
            'Sector inflation must be between -20% and 50%'
          );
        }
        break;
      case 'forecast12M':
      case 'forecast24M':
        if (value < -5 || value > 15) {
          return t(
            'admin.validation.forecastRange',
            'Forecast must be between -5% and 15%'
          );
        }
        break;
    }
    return null;
  };

  const handleFieldChange = (
    field: keyof typeof localConfig,
    value: string | number | boolean
  ) => {
    const numericValue = typeof value === 'string' ? parseFloat(value) : value;

    // Validate numeric fields
    if (typeof numericValue === 'number' && !isNaN(numericValue)) {
      const error = validateField(field, numericValue);
      setValidationErrors(prev => ({
        ...prev,
        [field]: error || '',
      }));
    }

    const updatedConfig = {
      ...localConfig,
      [field]: value,
      lastUpdated: new Date().toISOString(),
    };

    setLocalConfig(updatedConfig);
    updateSection('inflation', updatedConfig);
  };

  const handleSourceChange = (source: string) => {
    handleFieldChange('source', source);
  };

  const handleAutoUpdateToggle = () => {
    handleFieldChange('autoUpdate', !localConfig.autoUpdate);
  };

  const refreshFromSource = async () => {
    // In a real implementation, this would fetch from external APIs
    const mockData = {
      currentCPI: 1.6 + (Math.random() - 0.5) * 0.4,
      coreCPI: 1.4 + (Math.random() - 0.5) * 0.3,
      forecast12M: 1.8 + (Math.random() - 0.5) * 0.5,
      forecast24M: 2.0 + (Math.random() - 0.5) * 0.6,
      lastUpdated: new Date().toISOString(),
    };

    const updatedConfig = {
      ...localConfig,
      ...mockData,
    };

    setLocalConfig(updatedConfig);
    updateSection('inflation', updatedConfig);
  };

  const inputClasses = `w-full px-3 py-2 rounded-lg border ${
    darkMode
      ? 'bg-gray-700 border-gray-600 text-white focus:border-blue-500'
      : 'bg-white border-gray-300 text-gray-900 focus:border-blue-500'
  } focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50`;

  const labelClasses = `block text-sm font-medium mb-2 ${
    darkMode ? 'text-gray-300' : 'text-gray-700'
  }`;

  const cardClasses = `rounded-lg border p-6 ${
    darkMode ? 'bg-gray-800 border-gray-700' : 'bg-gray-50 border-gray-200'
  }`;

  const renderNumberInput = (
    field: keyof typeof localConfig,
    label: string,
    suffix: string = '%',
    step: number = 0.1,
    description?: string
  ) => {
    const hasError = validationErrors[field];

    return (
      <div className='space-y-2'>
        <label className={labelClasses}>
          {label}
          {description && (
            <span
              className={`block text-xs mt-1 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}
            >
              {description}
            </span>
          )}
        </label>
        <div className='relative'>
          <input
            type='number'
            step={step}
            value={localConfig[field] as number}
            onChange={e => handleFieldChange(field, e.target.value)}
            className={`${inputClasses} ${hasError ? 'border-red-500' : ''} pr-8`}
          />
          <span
            className={`absolute right-3 top-1/2 transform -translate-y-1/2 text-sm ${
              darkMode ? 'text-gray-400' : 'text-gray-500'
            }`}
          >
            {suffix}
          </span>
        </div>
        {hasError && <p className='text-red-500 text-xs'>{hasError}</p>}
      </div>
    );
  };

  return (
    <div className='space-y-8'>
      {/* Header */}
      <div>
        <h2 className='text-2xl font-bold mb-2'>
          📈 {t('admin.inflation.title', 'Inflation Configuration')}
        </h2>
        <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
          {t(
            'admin.inflation.description',
            'Configure inflation rates and economic assumptions for financial calculations'
          )}
        </p>
      </div>

      {/* Data Source and Auto-Update */}
      <div className={cardClasses}>
        <h3 className='text-lg font-semibold mb-4'>
          🔄 {t('admin.inflation.dataSource', 'Data Source & Updates')}
        </h3>

        <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
          <div>
            <label className={labelClasses}>
              {t('admin.inflation.source', 'Data Source')}
            </label>
            <select
              value={localConfig.source}
              onChange={e => handleSourceChange(e.target.value)}
              className={inputClasses}
            >
              <option value='Swiss National Bank'>
                Swiss National Bank (SNB)
              </option>
              <option value='Federal Statistical Office'>
                Federal Statistical Office (FSO)
              </option>
              <option value='OECD'>OECD</option>
              <option value='Manual Entry'>Manual Entry</option>
            </select>
          </div>

          <div>
            <label className={labelClasses}>
              {t('admin.inflation.lastUpdated', 'Last Updated')}
            </label>
            <div
              className={`${inputClasses} bg-gray-100 ${darkMode ? 'bg-gray-600' : ''}`}
            >
              {new Date(localConfig.lastUpdated).toLocaleString()}
            </div>
          </div>

          <div className='flex items-center space-x-3'>
            <input
              type='checkbox'
              id='autoUpdate'
              checked={localConfig.autoUpdate}
              onChange={handleAutoUpdateToggle}
              className='w-4 h-4 text-blue-600 rounded focus:ring-blue-500'
            />
            <label
              htmlFor='autoUpdate'
              className={labelClasses.replace('block', 'inline')}
            >
              {t('admin.inflation.autoUpdate', 'Auto-update from source')}
            </label>
          </div>

          <div>
            <button
              onClick={refreshFromSource}
              className='px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors'
            >
              🔄 {t('admin.inflation.refresh', 'Refresh Now')}
            </button>
          </div>
        </div>
      </div>

      {/* Current Inflation Rates */}
      <div className={cardClasses}>
        <h3 className='text-lg font-semibold mb-4'>
          📊 {t('admin.inflation.currentRates', 'Current Inflation Rates')}
        </h3>

        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
          {renderNumberInput(
            'currentCPI',
            t('admin.inflation.currentCPI', 'Current CPI'),
            '%',
            0.1,
            t(
              'admin.inflation.currentCPIDesc',
              'Overall consumer price inflation'
            )
          )}

          {renderNumberInput(
            'coreCPI',
            t('admin.inflation.coreCPI', 'Core CPI'),
            '%',
            0.1,
            t(
              'admin.inflation.coreCPIDesc',
              'Inflation excluding volatile items'
            )
          )}

          {renderNumberInput(
            'historicalAverage',
            t('admin.inflation.historicalAverage', 'Historical Average'),
            '%',
            0.1,
            t(
              'admin.inflation.historicalAverageDesc',
              'Long-term average inflation rate'
            )
          )}

          {renderNumberInput(
            'targetInflation',
            t('admin.inflation.targetInflation', 'SNB Target'),
            '%',
            0.1,
            t(
              'admin.inflation.targetInflationDesc',
              'Swiss National Bank inflation target'
            )
          )}
        </div>
      </div>

      {/* Sector-Specific Inflation */}
      <div className={cardClasses}>
        <h3 className='text-lg font-semibold mb-4'>
          🏢 {t('admin.inflation.sectorRates', 'Sector-Specific Inflation')}
        </h3>

        <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
          {renderNumberInput(
            'housingCosts',
            t('admin.inflation.housingCosts', 'Housing Costs'),
            '%',
            0.1,
            t(
              'admin.inflation.housingCostsDesc',
              'Rent and housing-related inflation'
            )
          )}

          {renderNumberInput(
            'healthcareCosts',
            t('admin.inflation.healthcareCosts', 'Healthcare Costs'),
            '%',
            0.1,
            t(
              'admin.inflation.healthcareCostsDesc',
              'Medical services and insurance inflation'
            )
          )}

          {renderNumberInput(
            'energyCosts',
            t('admin.inflation.energyCosts', 'Energy Costs'),
            '%',
            0.1,
            t(
              'admin.inflation.energyCostsDesc',
              'Electricity, gas, and fuel inflation'
            )
          )}

          {renderNumberInput(
            'foodCosts',
            t('admin.inflation.foodCosts', 'Food Costs'),
            '%',
            0.1,
            t('admin.inflation.foodCostsDesc', 'Food and beverage inflation')
          )}
        </div>
      </div>

      {/* Inflation Forecasts */}
      <div className={cardClasses}>
        <h3 className='text-lg font-semibold mb-4'>
          🔮 {t('admin.inflation.forecasts', 'Inflation Forecasts')}
        </h3>

        <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
          {renderNumberInput(
            'forecast12M',
            t('admin.inflation.forecast12M', '12-Month Forecast'),
            '%',
            0.1,
            t(
              'admin.inflation.forecast12MDesc',
              'Expected inflation in next 12 months'
            )
          )}

          {renderNumberInput(
            'forecast24M',
            t('admin.inflation.forecast24M', '24-Month Forecast'),
            '%',
            0.1,
            t(
              'admin.inflation.forecast24MDesc',
              'Expected inflation in next 24 months'
            )
          )}
        </div>
      </div>

      {/* Impact Summary */}
      <div className={cardClasses}>
        <h3 className='text-lg font-semibold mb-4'>
          💡 {t('admin.inflation.impact', 'Impact on Calculations')}
        </h3>

        <div className='space-y-4'>
          <div
            className={`p-4 rounded-lg ${darkMode ? 'bg-blue-900/30' : 'bg-blue-50'}`}
          >
            <h4 className='font-medium mb-2'>
              🎯 {t('admin.inflation.fireImpact', 'FIRE Calculations')}
            </h4>
            <p
              className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}
            >
              {t(
                'admin.inflation.fireImpactDesc',
                'Inflation affects real returns and purchasing power of retirement savings. Higher inflation requires larger FIRE numbers.'
              )}
            </p>
          </div>

          <div
            className={`p-4 rounded-lg ${darkMode ? 'bg-green-900/30' : 'bg-green-50'}`}
          >
            <h4 className='font-medium mb-2'>
              💰 {t('admin.inflation.expenseImpact', 'Expense Projections')}
            </h4>
            <p
              className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}
            >
              {t(
                'admin.inflation.expenseImpactDesc',
                'Sector-specific inflation rates are used to project future expenses in housing, healthcare, and other categories.'
              )}
            </p>
          </div>

          <div
            className={`p-4 rounded-lg ${darkMode ? 'bg-yellow-900/30' : 'bg-yellow-50'}`}
          >
            <h4 className='font-medium mb-2'>
              📈 {t('admin.inflation.returnImpact', 'Real Returns')}
            </h4>
            <p
              className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}
            >
              {t(
                'admin.inflation.returnImpactDesc',
                'Investment returns are adjusted for inflation to calculate real purchasing power growth over time.'
              )}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
