/**
 * Healthcare Configuration Panel for Swiss Budget Pro Admin
 * Allows configuration of healthcare premiums, deductibles, and subsidies
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useAdminConfig } from '../../contexts/AdminConfigContext';

interface HealthcareConfigPanelProps {
  darkMode?: boolean;
}

export const HealthcareConfigPanel: React.FC<HealthcareConfigPanelProps> = ({
  darkMode = true,
}) => {
  const { t } = useTranslation();
  const { config, updateSection } = useAdminConfig();
  const [localConfig, setLocalConfig] = useState(config.healthcare);
  const [validationErrors, setValidationErrors] = useState<
    Record<string, string>
  >({});
  const [selectedCanton, setSelectedCanton] = useState<string>('ZH');

  // Update local config when global config changes
  useEffect(() => {
    setLocalConfig(config.healthcare);
  }, [config.healthcare]);

  const validateAmount = (
    amount: number,
    min: number = 0,
    max: number = 10000
  ): string | null => {
    if (amount < min || amount > max) {
      return t(
        'admin.validation.amountRange',
        `Amount must be between ${min} and ${max}`
      );
    }
    return null;
  };

  const validatePercentage = (percentage: number): string | null => {
    if (percentage < 0 || percentage > 100) {
      return t(
        'admin.validation.percentageRange',
        'Percentage must be between 0% and 100%'
      );
    }
    return null;
  };

  const handleFieldChange = (
    section: string,
    field: string,
    value: string | number
  ) => {
    const numericValue = typeof value === 'string' ? parseFloat(value) : value;

    // Validate based on field type
    let error: string | null = null;
    if (field.includes('premium') || field.includes('average')) {
      error = validateAmount(numericValue, 100, 2000);
    } else if (field.includes('deductible')) {
      error = validateAmount(numericValue, 300, 2500);
    } else if (field.includes('threshold')) {
      error = validateAmount(numericValue, 10000, 200000);
    } else if (field.includes('inflation')) {
      error = validatePercentage(numericValue);
    }

    setValidationErrors(prev => ({
      ...prev,
      [`${section}.${field}`]: error || '',
    }));

    let updatedConfig = { ...localConfig };

    if (section === 'deductibles') {
      // Handle deductibles array
      const index = parseInt(field);
      updatedConfig.deductibles[index] = numericValue;
    } else if (section === 'premiumsByCanton') {
      // Handle cantonal premiums
      const [canton, premiumField] = field.split('.');
      if (premiumField === 'range') {
        // Handle range array
        const [rangeIndex, rangeValue] = value.toString().split(':');
        const currentRange = [...updatedConfig.premiumsByCanton[canton].range];
        currentRange[parseInt(rangeIndex)] = parseFloat(rangeValue);
        updatedConfig.premiumsByCanton[canton] = {
          ...updatedConfig.premiumsByCanton[canton],
          range: currentRange as [number, number],
        };
      } else {
        updatedConfig.premiumsByCanton[canton] = {
          ...updatedConfig.premiumsByCanton[canton],
          [premiumField]: numericValue,
        };
      }
    } else if (section === 'subsidyThresholds') {
      // Handle subsidy thresholds
      const [canton, householdType] = field.split('.');
      updatedConfig.subsidyThresholds[canton] = {
        ...updatedConfig.subsidyThresholds[canton],
        [householdType]: numericValue,
      };
    } else {
      // Handle direct field updates
      updatedConfig = {
        ...updatedConfig,
        [field]: value,
      };
    }

    updatedConfig.lastUpdated = new Date().toISOString();
    setLocalConfig(updatedConfig);
    updateSection('healthcare', updatedConfig);
  };

  const addDeductible = () => {
    const newDeductible = 1000; // Default new deductible
    const updatedConfig = {
      ...localConfig,
      deductibles: [...localConfig.deductibles, newDeductible].sort(
        (a, b) => a - b
      ),
      lastUpdated: new Date().toISOString(),
    };

    setLocalConfig(updatedConfig);
    updateSection('healthcare', updatedConfig);
  };

  const removeDeductible = (index: number) => {
    const updatedConfig = {
      ...localConfig,
      deductibles: localConfig.deductibles.filter((_, i) => i !== index),
      lastUpdated: new Date().toISOString(),
    };

    setLocalConfig(updatedConfig);
    updateSection('healthcare', updatedConfig);
  };

  const inputClasses = `w-full px-3 py-2 rounded-lg border ${
    darkMode
      ? 'bg-gray-700 border-gray-600 text-white focus:border-blue-500'
      : 'bg-white border-gray-300 text-gray-900 focus:border-blue-500'
  } focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50`;

  const labelClasses = `block text-sm font-medium mb-2 ${
    darkMode ? 'text-gray-300' : 'text-gray-700'
  }`;

  const cardClasses = `rounded-lg border p-6 ${
    darkMode ? 'bg-gray-800 border-gray-700' : 'bg-gray-50 border-gray-200'
  }`;

  const renderNumberInput = (
    section: string,
    field: string,
    label: string,
    value: number,
    suffix: string = 'CHF',
    step: number = 1
  ) => {
    const fieldKey = `${section}.${field}`;
    const hasError = validationErrors[fieldKey];

    return (
      <div className='space-y-2'>
        <label className={labelClasses}>{label}</label>
        <div className='relative'>
          <input
            type='number'
            step={step}
            value={value}
            onChange={e => handleFieldChange(section, field, e.target.value)}
            className={`${inputClasses} ${hasError ? 'border-red-500' : ''} ${suffix ? 'pr-16' : ''}`}
          />
          {suffix && (
            <span
              className={`absolute right-3 top-1/2 transform -translate-y-1/2 text-sm ${
                darkMode ? 'text-gray-400' : 'text-gray-500'
              }`}
            >
              {suffix}
            </span>
          )}
        </div>
        {hasError && <p className='text-red-500 text-xs'>{hasError}</p>}
      </div>
    );
  };

  const cantonNames: Record<string, string> = {
    ZH: 'Zurich',
    GE: 'Geneva',
    VD: 'Vaud',
    BE: 'Bern',
    ZG: 'Zug',
    BS: 'Basel-Stadt',
    BL: 'Basel-Landschaft',
    AG: 'Aargau',
  };

  return (
    <div className='space-y-8'>
      {/* Header */}
      <div>
        <h2 className='text-2xl font-bold mb-2'>
          🏥 {t('admin.healthcare.title', 'Healthcare Configuration')}
        </h2>
        <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
          {t(
            'admin.healthcare.description',
            'Configure healthcare premiums, deductibles, and subsidy thresholds for Swiss cantons'
          )}
        </p>
      </div>

      {/* Deductible Options */}
      <div className={cardClasses}>
        <div className='flex items-center justify-between mb-4'>
          <h3 className='text-lg font-semibold'>
            💰 {t('admin.healthcare.deductibles', 'Healthcare Deductibles')}
          </h3>
          <button
            onClick={addDeductible}
            className='px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors'
          >
            ➕ {t('admin.healthcare.addDeductible', 'Add Deductible')}
          </button>
        </div>

        <div className='grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4'>
          {localConfig.deductibles.map((deductible, index) => (
            <div
              key={index}
              className={`p-3 rounded-lg border ${
                darkMode ? 'border-gray-600' : 'border-gray-300'
              }`}
            >
              <div className='flex items-center justify-between mb-2'>
                <span className='text-sm font-medium'>#{index + 1}</span>
                {localConfig.deductibles.length > 1 && (
                  <button
                    onClick={() => removeDeductible(index)}
                    className='text-red-500 hover:text-red-700 text-xs'
                  >
                    🗑️
                  </button>
                )}
              </div>

              {renderNumberInput(
                'deductibles',
                index.toString(),
                '',
                deductible,
                'CHF',
                50
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Cantonal Premiums */}
      <div className={cardClasses}>
        <h3 className='text-lg font-semibold mb-4'>
          🇨🇭{' '}
          {t(
            'admin.healthcare.cantonalPremiums',
            'Cantonal Healthcare Premiums'
          )}
        </h3>

        <div className='mb-6'>
          <label className={labelClasses}>
            {t('admin.healthcare.selectCanton', 'Select Canton')}
          </label>
          <select
            value={selectedCanton}
            onChange={e => setSelectedCanton(e.target.value)}
            className={inputClasses}
          >
            {Object.entries(cantonNames).map(([code, name]) => (
              <option key={code} value={code}>
                {name} ({code})
              </option>
            ))}
          </select>
        </div>

        {selectedCanton && localConfig.premiumsByCanton[selectedCanton] && (
          <div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
            {renderNumberInput(
              'premiumsByCanton',
              `${selectedCanton}.average`,
              t('admin.healthcare.averagePremium', 'Average Premium'),
              localConfig.premiumsByCanton[selectedCanton].average,
              'CHF/month',
              1
            )}

            <div className='space-y-2'>
              <label className={labelClasses}>
                {t('admin.healthcare.premiumRange', 'Premium Range')}
              </label>
              <div className='grid grid-cols-2 gap-2'>
                {renderNumberInput(
                  'premiumsByCanton',
                  `${selectedCanton}.range.0`,
                  t('admin.healthcare.minPremium', 'Min'),
                  localConfig.premiumsByCanton[selectedCanton].range[0],
                  'CHF',
                  1
                )}

                {renderNumberInput(
                  'premiumsByCanton',
                  `${selectedCanton}.range.1`,
                  t('admin.healthcare.maxPremium', 'Max'),
                  localConfig.premiumsByCanton[selectedCanton].range[1],
                  'CHF',
                  1
                )}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Subsidy Thresholds */}
      <div className={cardClasses}>
        <h3 className='text-lg font-semibold mb-4'>
          🎯{' '}
          {t(
            'admin.healthcare.subsidyThresholds',
            'Premium Subsidy Thresholds'
          )}
        </h3>

        <div className='mb-6'>
          <p
            className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}
          >
            {t(
              'admin.healthcare.subsidyDescription',
              'Income thresholds for healthcare premium subsidies by household type'
            )}
          </p>
        </div>

        {selectedCanton && localConfig.subsidyThresholds[selectedCanton] && (
          <div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
            {renderNumberInput(
              'subsidyThresholds',
              `${selectedCanton}.single`,
              t('admin.healthcare.singleThreshold', 'Single Person'),
              localConfig.subsidyThresholds[selectedCanton].single,
              'CHF/year',
              1000
            )}

            {renderNumberInput(
              'subsidyThresholds',
              `${selectedCanton}.couple`,
              t('admin.healthcare.coupleThreshold', 'Couple'),
              localConfig.subsidyThresholds[selectedCanton].couple,
              'CHF/year',
              1000
            )}

            {renderNumberInput(
              'subsidyThresholds',
              `${selectedCanton}.family`,
              t('admin.healthcare.familyThreshold', 'Family'),
              localConfig.subsidyThresholds[selectedCanton].family,
              'CHF/year',
              1000
            )}
          </div>
        )}
      </div>

      {/* Healthcare Cost Inflation */}
      <div className={cardClasses}>
        <h3 className='text-lg font-semibold mb-4'>
          📈 {t('admin.healthcare.costInflation', 'Healthcare Cost Inflation')}
        </h3>

        <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
          {renderNumberInput(
            '',
            'costInflation',
            t(
              'admin.healthcare.annualInflation',
              'Annual Healthcare Inflation'
            ),
            localConfig.costInflation,
            '%',
            0.1
          )}

          <div>
            <label className={labelClasses}>
              {t('admin.healthcare.inflationDescription', 'Impact Description')}
            </label>
            <div
              className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}
            >
              <p
                className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}
              >
                {t(
                  'admin.healthcare.inflationImpact',
                  'Healthcare costs typically increase faster than general inflation, affecting long-term FIRE calculations.'
                )}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Data Source */}
      <div className={cardClasses}>
        <h3 className='text-lg font-semibold mb-4'>
          📊 {t('admin.healthcare.dataSource', 'Data Source')}
        </h3>

        <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
          <div>
            <label className={labelClasses}>
              {t('admin.healthcare.source', 'Source')}
            </label>
            <select
              value={localConfig.source}
              onChange={e => handleFieldChange('', 'source', e.target.value)}
              className={inputClasses}
            >
              <option value='Federal Office of Public Health'>
                Federal Office of Public Health (FOPH)
              </option>
              <option value='Cantonal Health Departments'>
                Cantonal Health Departments
              </option>
              <option value='Insurance Companies'>Insurance Companies</option>
              <option value='Manual Entry'>Manual Entry</option>
            </select>
          </div>

          <div>
            <label className={labelClasses}>
              {t('admin.healthcare.lastUpdated', 'Last Updated')}
            </label>
            <div
              className={`${inputClasses} bg-gray-100 ${darkMode ? 'bg-gray-600' : ''}`}
            >
              {new Date(localConfig.lastUpdated).toLocaleString()}
            </div>
          </div>
        </div>
      </div>

      {/* Impact Summary */}
      <div className={cardClasses}>
        <h3 className='text-lg font-semibold mb-4'>
          💡 {t('admin.healthcare.impact', 'Impact on Calculations')}
        </h3>

        <div className='space-y-4'>
          <div
            className={`p-4 rounded-lg ${darkMode ? 'bg-blue-900/30' : 'bg-blue-50'}`}
          >
            <h4 className='font-medium mb-2'>
              🎯{' '}
              {t(
                'admin.healthcare.optimizationImpact',
                'Deductible Optimization'
              )}
            </h4>
            <p
              className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}
            >
              {t(
                'admin.healthcare.optimizationDesc',
                'Deductible options are used to calculate optimal healthcare cost strategies based on individual risk profiles.'
              )}
            </p>
          </div>

          <div
            className={`p-4 rounded-lg ${darkMode ? 'bg-green-900/30' : 'bg-green-50'}`}
          >
            <h4 className='font-medium mb-2'>
              💰 {t('admin.healthcare.fireImpact', 'FIRE Calculations')}
            </h4>
            <p
              className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}
            >
              {t(
                'admin.healthcare.fireDesc',
                'Healthcare costs are projected using inflation rates and included in long-term expense planning for retirement.'
              )}
            </p>
          </div>

          <div
            className={`p-4 rounded-lg ${darkMode ? 'bg-yellow-900/30' : 'bg-yellow-50'}`}
          >
            <h4 className='font-medium mb-2'>
              🏛️ {t('admin.healthcare.subsidyImpact', 'Subsidy Eligibility')}
            </h4>
            <p
              className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}
            >
              {t(
                'admin.healthcare.subsidyDesc',
                'Income thresholds help determine eligibility for premium subsidies, affecting net healthcare costs.'
              )}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
