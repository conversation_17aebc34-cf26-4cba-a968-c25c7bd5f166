/**
 * Swiss Budget Pro Admin Configuration Page
 * Allows configuration of inflation rates, market assumptions, and other system defaults
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  AdminConfigProvider,
  useAdminConfig,
} from '../../contexts/AdminConfigContext';
import { InflationConfigPanel } from './InflationConfigPanel';
import { MarketDataConfigPanel } from './MarketDataConfigPanel';
import { TaxConfigPanel } from './TaxConfigPanel';
import { HealthcareConfigPanel } from './HealthcareConfigPanel';
import { SystemConfigPanel } from './SystemConfigPanel';
import { ConfigImportExport } from './ConfigImportExport';
import { ConfigAuditLog } from './ConfigAuditLog';
import { AdminAuthGuard } from './AdminAuthGuard';

interface AdminPageProps {
  darkMode?: boolean;
  onClose?: () => void;
}

const AdminPageContent: React.FC<AdminPageProps> = ({
  darkMode = true,
  onClose,
}) => {
  const { t } = useTranslation();
  const {
    config,
    updateConfig,
    resetToDefaults,
    exportConfig,
    importConfig,
    getAuditLog,
    isLoading,
    lastSaved,
  } = useAdminConfig();

  const [activeTab, setActiveTab] = useState<string>('inflation');
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [showResetConfirm, setShowResetConfirm] = useState(false);

  const tabs = [
    { id: 'inflation', label: t('admin.tabs.inflation'), icon: '📈' },
    { id: 'market', label: t('admin.tabs.market'), icon: '📊' },
    { id: 'tax', label: t('admin.tabs.tax'), icon: '💰' },
    { id: 'healthcare', label: t('admin.tabs.healthcare'), icon: '🏥' },
    { id: 'system', label: t('admin.tabs.system'), icon: '⚙️' },
    { id: 'import-export', label: t('admin.tabs.importExport'), icon: '📁' },
    { id: 'audit', label: t('admin.tabs.audit'), icon: '📋' },
  ];

  // Check for unsaved changes
  useEffect(() => {
    const checkUnsavedChanges = () => {
      const currentConfig = JSON.stringify(config);
      const savedConfig = localStorage.getItem('swissBudgetPro_adminConfig');
      setHasUnsavedChanges(currentConfig !== savedConfig);
    };

    checkUnsavedChanges();
  }, [config]);

  // Warn before leaving with unsaved changes
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasUnsavedChanges) {
        e.preventDefault();
        e.returnValue = '';
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [hasUnsavedChanges]);

  const handleSaveAll = async () => {
    try {
      await updateConfig(config);
      setHasUnsavedChanges(false);
      // Show success notification
      console.log('✅ Configuration saved successfully');
    } catch (error) {
      console.error('❌ Failed to save configuration:', error);
    }
  };

  const handleResetToDefaults = async () => {
    if (showResetConfirm) {
      try {
        await resetToDefaults();
        setHasUnsavedChanges(false);
        setShowResetConfirm(false);
        console.log('✅ Configuration reset to defaults');
      } catch (error) {
        console.error('❌ Failed to reset configuration:', error);
      }
    } else {
      setShowResetConfirm(true);
    }
  };

  const renderActiveTab = () => {
    switch (activeTab) {
      case 'inflation':
        return <InflationConfigPanel darkMode={darkMode} />;
      case 'market':
        return <MarketDataConfigPanel darkMode={darkMode} />;
      case 'tax':
        return <TaxConfigPanel darkMode={darkMode} />;
      case 'healthcare':
        return <HealthcareConfigPanel darkMode={darkMode} />;
      case 'system':
        return <SystemConfigPanel darkMode={darkMode} />;
      case 'import-export':
        return <ConfigImportExport darkMode={darkMode} />;
      case 'audit':
        return <ConfigAuditLog darkMode={darkMode} />;
      default:
        return <InflationConfigPanel darkMode={darkMode} />;
    }
  };

  const baseClasses = darkMode
    ? 'bg-gray-900 text-white'
    : 'bg-white text-gray-900';

  const cardClasses = darkMode
    ? 'bg-gray-800 border-gray-700'
    : 'bg-gray-50 border-gray-200';

  return (
    <div className={`min-h-screen ${baseClasses}`}>
      {/* Header */}
      <div
        className={`border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'} sticky top-0 z-10 ${baseClasses}`}
      >
        <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
          <div className='flex items-center justify-between h-16'>
            <div className='flex items-center space-x-4'>
              <div className='text-2xl'>⚙️</div>
              <div>
                <h1 className='text-xl font-bold'>
                  {t('admin.title', 'Swiss Budget Pro Admin')}
                </h1>
                <p
                  className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}
                >
                  {t('admin.subtitle', 'System Configuration & Management')}
                </p>
              </div>
            </div>

            <div className='flex items-center space-x-4'>
              {/* Save Status */}
              {lastSaved && (
                <div
                  className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}
                >
                  {t('admin.lastSaved', 'Last saved')}:{' '}
                  {new Date(lastSaved).toLocaleString()}
                </div>
              )}

              {/* Unsaved Changes Indicator */}
              {hasUnsavedChanges && (
                <div className='flex items-center space-x-2 text-yellow-500'>
                  <div className='w-2 h-2 bg-yellow-500 rounded-full animate-pulse'></div>
                  <span className='text-sm'>
                    {t('admin.unsavedChanges', 'Unsaved changes')}
                  </span>
                </div>
              )}

              {/* Action Buttons */}
              <div className='flex items-center space-x-2'>
                <button
                  onClick={handleSaveAll}
                  disabled={!hasUnsavedChanges || isLoading}
                  className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                    hasUnsavedChanges && !isLoading
                      ? 'bg-blue-600 hover:bg-blue-700 text-white'
                      : 'bg-gray-600 text-gray-400 cursor-not-allowed'
                  }`}
                >
                  {isLoading ? '💾 Saving...' : '💾 Save All'}
                </button>

                <button
                  onClick={handleResetToDefaults}
                  className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                    showResetConfirm
                      ? 'bg-red-600 hover:bg-red-700 text-white'
                      : 'bg-gray-600 hover:bg-gray-700 text-white'
                  }`}
                >
                  {showResetConfirm
                    ? '⚠️ Confirm Reset'
                    : '🔄 Reset to Defaults'}
                </button>

                {onClose && (
                  <button
                    onClick={onClose}
                    className='px-4 py-2 rounded-lg font-medium bg-gray-600 hover:bg-gray-700 text-white transition-colors'
                  >
                    ✕ Close
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div
        className={`border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}
      >
        <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
          <nav className='flex space-x-8 overflow-x-auto'>
            {tabs.map(tab => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors ${
                  activeTab === tab.id
                    ? `border-blue-500 ${darkMode ? 'text-blue-400' : 'text-blue-600'}`
                    : `border-transparent ${darkMode ? 'text-gray-400 hover:text-gray-300' : 'text-gray-500 hover:text-gray-700'}`
                }`}
              >
                <span>{tab.icon}</span>
                <span>{tab.label}</span>
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8'>
        <div className={`rounded-lg border ${cardClasses} p-6`}>
          {renderActiveTab()}
        </div>
      </div>

      {/* Reset Confirmation Modal */}
      {showResetConfirm && (
        <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50'>
          <div className={`rounded-lg p-6 max-w-md w-full mx-4 ${cardClasses}`}>
            <h3 className='text-lg font-bold mb-4'>
              ⚠️ {t('admin.resetConfirm.title', 'Reset to Defaults')}
            </h3>
            <p
              className={`mb-6 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}
            >
              {t(
                'admin.resetConfirm.message',
                'This will reset all configuration to default values. This action cannot be undone.'
              )}
            </p>
            <div className='flex space-x-4'>
              <button
                onClick={() => setShowResetConfirm(false)}
                className='flex-1 px-4 py-2 rounded-lg font-medium bg-gray-600 hover:bg-gray-700 text-white transition-colors'
              >
                {t('admin.resetConfirm.cancel', 'Cancel')}
              </button>
              <button
                onClick={handleResetToDefaults}
                className='flex-1 px-4 py-2 rounded-lg font-medium bg-red-600 hover:bg-red-700 text-white transition-colors'
              >
                {t('admin.resetConfirm.confirm', 'Reset All')}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Main Admin Page with Auth Guard
export const AdminPage: React.FC<AdminPageProps> = props => {
  return (
    <AdminAuthGuard>
      <AdminConfigProvider>
        <AdminPageContent {...props} />
      </AdminConfigProvider>
    </AdminAuthGuard>
  );
};

export default AdminPage;
