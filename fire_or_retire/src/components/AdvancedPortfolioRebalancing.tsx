import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocalStorage } from '../hooks/useLocalStorage';

interface AdvancedPortfolioRebalancingProps {
  darkMode: boolean;
  currentInvestments?: any[];
}

interface AssetAllocation {
  id: string;
  name: string;
  category: 'stocks' | 'bonds' | 'reits' | 'commodities' | 'crypto' | 'cash';
  currentValue: number;
  targetPercentage: number;
  currentPercentage: number;
  deviation: number;
  rebalanceAmount: number;
  action: 'buy' | 'sell' | 'hold';
}

interface RebalancingStrategy {
  id: string;
  name: string;
  description: string;
  frequency: 'monthly' | 'quarterly' | 'semiannual' | 'annual';
  threshold: number; // Percentage deviation threshold
  isActive: boolean;
}

interface PortfolioMetrics {
  totalValue: number;
  expectedReturn: number;
  volatility: number;
  sharpeRatio: number;
  diversificationScore: number;
  riskLevel: 'conservative' | 'moderate' | 'aggressive';
}

const AdvancedPortfolioRebalancing: React.FC<
  AdvancedPortfolioRebalancingProps
> = ({ darkMode, currentInvestments = [] }) => {
  const { t } = useTranslation();
  const [allocations, setAllocations] = useLocalStorage<AssetAllocation[]>(
    'portfolio_allocations',
    []
  );
  const [strategies, setStrategies] = useLocalStorage<RebalancingStrategy[]>(
    'rebalancing_strategies',
    [
      {
        id: '1',
        name: 'Conservative Rebalancing',
        description:
          'Rebalance when any asset deviates more than 5% from target',
        frequency: 'quarterly',
        threshold: 5,
        isActive: true,
      },
      {
        id: '2',
        name: 'Moderate Rebalancing',
        description:
          'Rebalance when any asset deviates more than 10% from target',
        frequency: 'semiannual',
        threshold: 10,
        isActive: false,
      },
      {
        id: '3',
        name: 'Aggressive Rebalancing',
        description:
          'Rebalance when any asset deviates more than 15% from target',
        frequency: 'annual',
        threshold: 15,
        isActive: false,
      },
    ]
  );
  const [portfolioMetrics, setPortfolioMetrics] =
    useState<PortfolioMetrics | null>(null);
  const [newAllocation, setNewAllocation] = useState<Partial<AssetAllocation>>({
    name: '',
    category: 'stocks',
    currentValue: 0,
    targetPercentage: 0,
  });

  // Asset categories with expected returns and volatility (simplified)
  const assetCategories = [
    {
      value: 'stocks',
      label: '📈 Stocks',
      expectedReturn: 7.0,
      volatility: 15.0,
    },
    { value: 'bonds', label: '🏛️ Bonds', expectedReturn: 3.5, volatility: 5.0 },
    {
      value: 'reits',
      label: '🏠 REITs',
      expectedReturn: 6.0,
      volatility: 12.0,
    },
    {
      value: 'commodities',
      label: '🥇 Commodities',
      expectedReturn: 4.5,
      volatility: 18.0,
    },
    {
      value: 'crypto',
      label: '₿ Cryptocurrency',
      expectedReturn: 15.0,
      volatility: 50.0,
    },
    { value: 'cash', label: '💰 Cash', expectedReturn: 1.0, volatility: 0.5 },
  ];

  // Calculate portfolio metrics
  useEffect(() => {
    if (allocations.length === 0) {
      setPortfolioMetrics(null);
      return;
    }

    const totalValue = allocations.reduce(
      (sum, allocation) => sum + allocation.currentValue,
      0
    );

    if (totalValue === 0) {
      setPortfolioMetrics(null);
      return;
    }

    // Calculate weighted expected return and volatility
    let weightedReturn = 0;
    let weightedVolatility = 0;
    let diversificationScore = 0;

    allocations.forEach(allocation => {
      const weight = allocation.currentValue / totalValue;
      const categoryData = assetCategories.find(
        cat => cat.value === allocation.category
      );

      if (categoryData) {
        weightedReturn += weight * categoryData.expectedReturn;
        weightedVolatility +=
          weight * weight * categoryData.volatility * categoryData.volatility;
      }
    });

    const portfolioVolatility = Math.sqrt(weightedVolatility);
    const sharpeRatio =
      portfolioVolatility > 0
        ? (weightedReturn - 1.0) / portfolioVolatility
        : 0; // Assuming 1% risk-free rate

    // Calculate diversification score (simplified)
    const uniqueCategories = new Set(allocations.map(a => a.category)).size;
    diversificationScore = Math.min(
      100,
      (uniqueCategories / assetCategories.length) * 100
    );

    // Determine risk level
    let riskLevel: 'conservative' | 'moderate' | 'aggressive' = 'moderate';
    if (portfolioVolatility < 8) riskLevel = 'conservative';
    else if (portfolioVolatility > 15) riskLevel = 'aggressive';

    setPortfolioMetrics({
      totalValue,
      expectedReturn: weightedReturn,
      volatility: portfolioVolatility,
      sharpeRatio,
      diversificationScore,
      riskLevel,
    });
  }, [allocations]);

  // Calculate rebalancing recommendations
  useEffect(() => {
    const activeStrategy = strategies.find(s => s.isActive);
    if (!activeStrategy || allocations.length === 0) return;

    const totalValue = allocations.reduce(
      (sum, allocation) => sum + allocation.currentValue,
      0
    );
    if (totalValue === 0) return;

    const updatedAllocations = allocations.map(allocation => {
      const currentPercentage = (allocation.currentValue / totalValue) * 100;
      const deviation = Math.abs(
        currentPercentage - allocation.targetPercentage
      );

      let action: 'buy' | 'sell' | 'hold' = 'hold';
      let rebalanceAmount = 0;

      if (deviation > activeStrategy.threshold) {
        const targetValue = (allocation.targetPercentage / 100) * totalValue;
        rebalanceAmount = targetValue - allocation.currentValue;
        action = rebalanceAmount > 0 ? 'buy' : 'sell';
      }

      return {
        ...allocation,
        currentPercentage,
        deviation,
        rebalanceAmount: Math.abs(rebalanceAmount),
        action,
      };
    });

    setAllocations(updatedAllocations);
  }, [strategies]);

  const addAllocation = () => {
    if (
      !newAllocation.name ||
      !newAllocation.currentValue ||
      !newAllocation.targetPercentage
    ) {
      return;
    }

    const allocation: AssetAllocation = {
      id: Date.now().toString(),
      name: newAllocation.name,
      category: (newAllocation.category as any) || 'stocks',
      currentValue: newAllocation.currentValue || 0,
      targetPercentage: newAllocation.targetPercentage || 0,
      currentPercentage: 0,
      deviation: 0,
      rebalanceAmount: 0,
      action: 'hold',
    };

    setAllocations([...allocations, allocation]);
    setNewAllocation({
      name: '',
      category: 'stocks',
      currentValue: 0,
      targetPercentage: 0,
    });
  };

  const removeAllocation = (id: string) => {
    setAllocations(allocations.filter(allocation => allocation.id !== id));
  };

  const updateStrategy = (id: string, isActive: boolean) => {
    setStrategies(
      strategies.map(strategy => ({
        ...strategy,
        isActive: strategy.id === id ? isActive : false,
      }))
    );
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('de-CH', {
      style: 'currency',
      currency: 'CHF',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (percentage: number) => {
    return `${percentage.toFixed(2)}%`;
  };

  const getCategoryIcon = (category: string) => {
    const categoryData = assetCategories.find(cat => cat.value === category);
    return categoryData?.label.split(' ')[0] || '📊';
  };

  const getActionColor = (action: string) => {
    switch (action) {
      case 'buy':
        return 'text-green-600';
      case 'sell':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  const getRiskLevelColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'conservative':
        return 'text-green-600';
      case 'moderate':
        return 'text-yellow-600';
      case 'aggressive':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div>
        <h2 className='text-2xl font-bold mb-2'>
          ⚖️ Advanced Portfolio Rebalancing
        </h2>
        <p className='text-gray-600 dark:text-gray-400'>
          Optimize your portfolio allocation with automated rebalancing
          strategies and risk analysis
        </p>
      </div>

      {/* Portfolio Metrics */}
      {portfolioMetrics && (
        <div
          className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
        >
          <h3 className='text-lg font-semibold mb-4'>📊 Portfolio Metrics</h3>

          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4'>
            <div className='text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg'>
              <div className='text-sm text-gray-600 dark:text-gray-400'>
                Total Value
              </div>
              <div className='text-2xl font-bold text-blue-600'>
                {formatCurrency(portfolioMetrics.totalValue)}
              </div>
            </div>

            <div className='text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg'>
              <div className='text-sm text-gray-600 dark:text-gray-400'>
                Expected Return
              </div>
              <div className='text-2xl font-bold text-green-600'>
                {formatPercentage(portfolioMetrics.expectedReturn)}
              </div>
            </div>

            <div className='text-center p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg'>
              <div className='text-sm text-gray-600 dark:text-gray-400'>
                Volatility
              </div>
              <div className='text-2xl font-bold text-yellow-600'>
                {formatPercentage(portfolioMetrics.volatility)}
              </div>
            </div>

            <div className='text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg'>
              <div className='text-sm text-gray-600 dark:text-gray-400'>
                Sharpe Ratio
              </div>
              <div className='text-2xl font-bold text-purple-600'>
                {portfolioMetrics.sharpeRatio.toFixed(2)}
              </div>
            </div>

            <div className='text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg'>
              <div className='text-sm text-gray-600 dark:text-gray-400'>
                Risk Level
              </div>
              <div
                className={`text-2xl font-bold ${getRiskLevelColor(portfolioMetrics.riskLevel)}`}
              >
                {portfolioMetrics.riskLevel.charAt(0).toUpperCase() +
                  portfolioMetrics.riskLevel.slice(1)}
              </div>
            </div>
          </div>

          <div className='mt-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg'>
            <div className='flex items-center justify-between'>
              <span className='text-sm font-medium'>Diversification Score</span>
              <span className='text-sm font-bold'>
                {formatPercentage(portfolioMetrics.diversificationScore)}
              </span>
            </div>
            <div className='mt-2 w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2'>
              <div
                className='bg-blue-600 h-2 rounded-full transition-all duration-300'
                style={{ width: `${portfolioMetrics.diversificationScore}%` }}
              ></div>
            </div>
          </div>
        </div>
      )}

      {/* Rebalancing Strategies */}
      <div
        className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
      >
        <h3 className='text-lg font-semibold mb-4'>
          🎯 Rebalancing Strategies
        </h3>

        <div className='space-y-3'>
          {strategies.map(strategy => (
            <div
              key={strategy.id}
              className={`p-4 border rounded-lg ${
                strategy.isActive
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                  : darkMode
                    ? 'border-gray-700 bg-gray-700'
                    : 'border-gray-200 bg-gray-50'
              }`}
            >
              <div className='flex items-center justify-between'>
                <div>
                  <h4 className='font-semibold'>{strategy.name}</h4>
                  <p className='text-sm text-gray-600 dark:text-gray-400'>
                    {strategy.description}
                  </p>
                  <div className='text-xs text-gray-500 mt-1'>
                    Frequency: {strategy.frequency} • Threshold:{' '}
                    {strategy.threshold}%
                  </div>
                </div>
                <label className='flex items-center'>
                  <input
                    type='radio'
                    name='strategy'
                    checked={strategy.isActive}
                    onChange={() => updateStrategy(strategy.id, true)}
                    className='mr-2'
                  />
                  <span className='text-sm'>Active</span>
                </label>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Add New Allocation */}
      <div
        className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
      >
        <h3 className='text-lg font-semibold mb-4'>➕ Add Asset Allocation</h3>

        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4'>
          <div>
            <label className='block text-sm font-medium mb-2'>Asset Name</label>
            <input
              type='text'
              value={newAllocation.name}
              onChange={e =>
                setNewAllocation(prev => ({ ...prev, name: e.target.value }))
              }
              className={`w-full px-3 py-2 rounded-md border ${
                darkMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              }`}
              placeholder='e.g., Swiss Stocks ETF'
            />
          </div>

          <div>
            <label className='block text-sm font-medium mb-2'>Category</label>
            <select
              value={newAllocation.category}
              onChange={e =>
                setNewAllocation(prev => ({
                  ...prev,
                  category: e.target.value as any,
                }))
              }
              className={`w-full px-3 py-2 rounded-md border ${
                darkMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              }`}
            >
              {assetCategories.map(category => (
                <option key={category.value} value={category.value}>
                  {category.label}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className='block text-sm font-medium mb-2'>
              Current Value (CHF)
            </label>
            <input
              type='number'
              value={newAllocation.currentValue || ''}
              onChange={e =>
                setNewAllocation(prev => ({
                  ...prev,
                  currentValue: parseFloat(e.target.value) || 0,
                }))
              }
              className={`w-full px-3 py-2 rounded-md border ${
                darkMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              }`}
              placeholder='0'
            />
          </div>

          <div>
            <label className='block text-sm font-medium mb-2'>
              Target Allocation (%)
            </label>
            <input
              type='number'
              min='0'
              max='100'
              step='0.1'
              value={newAllocation.targetPercentage || ''}
              onChange={e =>
                setNewAllocation(prev => ({
                  ...prev,
                  targetPercentage: parseFloat(e.target.value) || 0,
                }))
              }
              className={`w-full px-3 py-2 rounded-md border ${
                darkMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              }`}
              placeholder='0.0'
            />
          </div>
        </div>

        <div className='mt-4 flex justify-end'>
          <button
            onClick={addAllocation}
            className='px-6 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors'
          >
            Add Allocation
          </button>
        </div>
      </div>

      {/* Current Allocations */}
      {allocations.length > 0 && (
        <div
          className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
        >
          <h3 className='text-lg font-semibold mb-4'>
            📋 Current Allocations & Rebalancing
          </h3>

          <div className='overflow-x-auto'>
            <table className='w-full text-sm'>
              <thead>
                <tr
                  className={`border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}
                >
                  <th className='text-left py-2'>Asset</th>
                  <th className='text-right py-2'>Current Value</th>
                  <th className='text-right py-2'>Current %</th>
                  <th className='text-right py-2'>Target %</th>
                  <th className='text-right py-2'>Deviation</th>
                  <th className='text-right py-2'>Action</th>
                  <th className='text-right py-2'>Amount</th>
                  <th className='text-center py-2'>Remove</th>
                </tr>
              </thead>
              <tbody>
                {allocations.map(allocation => (
                  <tr
                    key={allocation.id}
                    className={`border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}
                  >
                    <td className='py-3'>
                      <div className='flex items-center gap-2'>
                        <span className='text-lg'>
                          {getCategoryIcon(allocation.category)}
                        </span>
                        <div>
                          <div className='font-medium'>{allocation.name}</div>
                          <div className='text-xs text-gray-500 capitalize'>
                            {allocation.category}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className='text-right py-3 font-medium'>
                      {formatCurrency(allocation.currentValue)}
                    </td>
                    <td className='text-right py-3'>
                      {formatPercentage(allocation.currentPercentage)}
                    </td>
                    <td className='text-right py-3'>
                      {formatPercentage(allocation.targetPercentage)}
                    </td>
                    <td className='text-right py-3'>
                      <span
                        className={
                          allocation.deviation > 5
                            ? 'text-red-600'
                            : 'text-green-600'
                        }
                      >
                        {formatPercentage(allocation.deviation)}
                      </span>
                    </td>
                    <td
                      className={`text-right py-3 font-medium ${getActionColor(allocation.action)}`}
                    >
                      {allocation.action.toUpperCase()}
                    </td>
                    <td className='text-right py-3'>
                      {allocation.rebalanceAmount > 0
                        ? formatCurrency(allocation.rebalanceAmount)
                        : '-'}
                    </td>
                    <td className='text-center py-3'>
                      <button
                        onClick={() => removeAllocation(allocation.id)}
                        className='text-red-600 hover:text-red-800 transition-colors'
                        title='Remove allocation'
                      >
                        🗑️
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {allocations.length === 0 && (
        <div
          className={`p-8 rounded-lg text-center ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
        >
          <div className='text-4xl mb-4'>⚖️</div>
          <h3 className='text-lg font-semibold mb-2'>
            No asset allocations configured
          </h3>
          <p className='text-gray-600 dark:text-gray-400'>
            Add your first asset allocation to start portfolio rebalancing
            analysis
          </p>
        </div>
      )}
    </div>
  );
};

export default AdvancedPortfolioRebalancing;
