import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

interface OnboardingWizardProps {
  onComplete: (userData: UserData) => void;
  onSkip: () => void;
  darkMode: boolean;
}

interface UserData {
  age: number;
  canton: string;
  employmentStatus: string;
  primaryGoal: string;
  monthlyIncome: number;
  hasExperience: boolean;
}

const OnboardingWizard: React.FC<OnboardingWizardProps> = ({
  onComplete,
  onSkip,
  darkMode,
}) => {
  const { t } = useTranslation();
  const [currentStep, setCurrentStep] = useState(0);
  const [userData, setUserData] = useState<Partial<UserData>>({});

  const steps = [
    {
      id: 'welcome',
      title: 'Welcome to Swiss Budget Pro! 🇨🇭',
      subtitle: 'Your journey to Financial Independence starts here',
      component: WelcomeStep,
    },
    {
      id: 'goals',
      title: "What's your primary financial goal?",
      subtitle: 'This helps us personalize your experience',
      component: GoalsStep,
    },
    {
      id: 'profile',
      title: 'Tell us about yourself',
      subtitle: 'We need some basic info for Swiss-specific calculations',
      component: ProfileStep,
    },
    {
      id: 'income',
      title: "What's your monthly income?",
      subtitle: 'Include your primary salary and any additional income',
      component: IncomeStep,
    },
    {
      id: 'experience',
      title: 'How familiar are you with Swiss financial planning?',
      subtitle: 'This helps us show the right level of detail',
      component: ExperienceStep,
    },
  ];

  const handleNext = (stepData: Partial<UserData>) => {
    const newUserData = { ...userData, ...stepData };
    setUserData(newUserData);

    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      onComplete(newUserData as UserData);
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const CurrentStepComponent = steps[currentStep].component;

  return (
    <div
      className={`min-h-screen flex items-center justify-center p-4 ${
        darkMode
          ? 'bg-gradient-to-br from-gray-900 via-purple-900/25 to-blue-900/25'
          : 'bg-gradient-to-br from-blue-50 via-purple-50/40 to-pink-50'
      }`}
    >
      <div
        className={`max-w-2xl w-full p-8 rounded-2xl ${
          darkMode
            ? 'bg-gray-800/90 border border-gray-600/50'
            : 'bg-white/95 border border-gray-200 shadow-xl'
        } backdrop-blur-sm`}
      >
        {/* Progress Bar */}
        <div className='mb-8'>
          <div className='flex items-center justify-between mb-2'>
            <span
              className={`text-sm font-medium ${
                darkMode ? 'text-gray-300' : 'text-gray-600'
              }`}
            >
              Step {currentStep + 1} of {steps.length}
            </span>
            <button
              onClick={onSkip}
              className={`text-sm ${
                darkMode
                  ? 'text-gray-400 hover:text-gray-300'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              Skip setup
            </button>
          </div>
          <div
            className={`w-full h-2 rounded-full ${
              darkMode ? 'bg-gray-700' : 'bg-gray-200'
            }`}
          >
            <div
              className='h-2 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 transition-all duration-500'
              style={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
            />
          </div>
        </div>

        {/* Step Content */}
        <div className='mb-8'>
          <h1
            className={`text-3xl font-bold mb-2 ${
              darkMode ? 'text-white' : 'text-gray-800'
            }`}
          >
            {steps[currentStep].title}
          </h1>
          <p
            className={`text-lg ${
              darkMode ? 'text-gray-300' : 'text-gray-600'
            }`}
          >
            {steps[currentStep].subtitle}
          </p>
        </div>

        <CurrentStepComponent
          userData={userData}
          onNext={handleNext}
          onBack={currentStep > 0 ? handleBack : undefined}
          darkMode={darkMode}
        />
      </div>
    </div>
  );
};

// Step Components
const WelcomeStep: React.FC<StepProps> = ({ onNext, darkMode }) => (
  <div className='text-center'>
    <div className='text-6xl mb-6'>🚀</div>
    <div
      className={`text-lg mb-8 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}
    >
      Swiss Budget Pro helps you plan your path to Financial Independence and
      Early Retirement (FIRE) with Swiss-specific features like Pillar 3a
      optimization and cantonal tax comparisons.
    </div>
    <div className='grid grid-cols-1 md:grid-cols-3 gap-4 mb-8'>
      <FeatureCard
        icon='🇨🇭'
        title='Swiss-Optimized'
        description='Pillar 3a, BVG, and cantonal tax features'
        darkMode={darkMode}
      />
      <FeatureCard
        icon='📊'
        title='Advanced Analytics'
        description='Monte Carlo simulations and projections'
        darkMode={darkMode}
      />
      <FeatureCard
        icon='🎯'
        title='FIRE Planning'
        description='Track your progress to financial independence'
        darkMode={darkMode}
      />
    </div>
    <button
      onClick={() => onNext({})}
      className='w-full py-3 px-6 bg-gradient-to-r from-blue-500 to-purple-500 text-white font-semibold rounded-lg hover:from-blue-600 hover:to-purple-600 transition-all'
    >
      Let's Get Started! 🎉
    </button>
  </div>
);

const GoalsStep: React.FC<StepProps> = ({
  userData,
  onNext,
  onBack,
  darkMode,
}) => {
  const [selectedGoal, setSelectedGoal] = useState(userData.primaryGoal || '');

  const goals = [
    {
      id: 'fire',
      title: 'Early Retirement (FIRE)',
      description: 'Retire before traditional retirement age',
      icon: '🏖️',
    },
    {
      id: 'wealth',
      title: 'Wealth Building',
      description: 'Build long-term wealth and financial security',
      icon: '💎',
    },
    {
      id: 'tax',
      title: 'Tax Optimization',
      description: 'Minimize taxes and maximize Swiss benefits',
      icon: '🇨🇭',
    },
    {
      id: 'planning',
      title: 'Financial Planning',
      description: 'Better understand and plan my finances',
      icon: '📊',
    },
  ];

  return (
    <div>
      <div className='grid grid-cols-1 md:grid-cols-2 gap-4 mb-8'>
        {goals.map(goal => (
          <button
            key={goal.id}
            onClick={() => setSelectedGoal(goal.id)}
            className={`p-4 rounded-lg border-2 transition-all text-left ${
              selectedGoal === goal.id
                ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/30'
                : darkMode
                  ? 'border-gray-600 bg-gray-700/50 hover:border-gray-500'
                  : 'border-gray-200 bg-gray-50 hover:border-gray-300'
            }`}
          >
            <div className='text-2xl mb-2'>{goal.icon}</div>
            <h3
              className={`font-semibold mb-1 ${
                darkMode ? 'text-white' : 'text-gray-800'
              }`}
            >
              {goal.title}
            </h3>
            <p
              className={`text-sm ${
                darkMode ? 'text-gray-300' : 'text-gray-600'
              }`}
            >
              {goal.description}
            </p>
          </button>
        ))}
      </div>
      <NavigationButtons
        onNext={() => onNext({ primaryGoal: selectedGoal })}
        onBack={onBack}
        nextDisabled={!selectedGoal}
        darkMode={darkMode}
      />
    </div>
  );
};

const ProfileStep: React.FC<StepProps> = ({
  userData,
  onNext,
  onBack,
  darkMode,
}) => {
  const [age, setAge] = useState(userData.age || '');
  const [canton, setCanton] = useState(userData.canton || '');
  const [employment, setEmployment] = useState(userData.employmentStatus || '');

  const cantons = [
    'ZH',
    'BE',
    'LU',
    'UR',
    'SZ',
    'OW',
    'NW',
    'GL',
    'ZG',
    'FR',
    'SO',
    'BS',
    'BL',
    'SH',
    'AR',
    'AI',
    'SG',
    'GR',
    'AG',
    'TG',
    'TI',
    'VD',
    'VS',
    'NE',
    'GE',
    'JU',
  ];

  return (
    <div>
      <div className='space-y-6 mb-8'>
        <div>
          <label
            className={`block text-sm font-medium mb-2 ${
              darkMode ? 'text-gray-300' : 'text-gray-700'
            }`}
          >
            Your age
          </label>
          <input
            type='number'
            value={age}
            onChange={e => setAge(e.target.value)}
            placeholder='e.g., 30'
            className={`w-full p-3 rounded-lg border ${
              darkMode
                ? 'bg-gray-700 border-gray-600 text-white'
                : 'bg-white border-gray-300'
            } focus:ring-2 focus:ring-blue-500`}
          />
        </div>

        <div>
          <label
            className={`block text-sm font-medium mb-2 ${
              darkMode ? 'text-gray-300' : 'text-gray-700'
            }`}
          >
            Canton (for tax calculations)
          </label>
          <select
            value={canton}
            onChange={e => setCanton(e.target.value)}
            className={`w-full p-3 rounded-lg border ${
              darkMode
                ? 'bg-gray-700 border-gray-600 text-white'
                : 'bg-white border-gray-300'
            } focus:ring-2 focus:ring-blue-500`}
          >
            <option value=''>Select your canton</option>
            {cantons.map(c => (
              <option key={c} value={c}>
                {c}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label
            className={`block text-sm font-medium mb-2 ${
              darkMode ? 'text-gray-300' : 'text-gray-700'
            }`}
          >
            Employment status
          </label>
          <select
            value={employment}
            onChange={e => setEmployment(e.target.value)}
            className={`w-full p-3 rounded-lg border ${
              darkMode
                ? 'bg-gray-700 border-gray-600 text-white'
                : 'bg-white border-gray-300'
            } focus:ring-2 focus:ring-blue-500`}
          >
            <option value=''>Select employment status</option>
            <option value='employee'>Employee</option>
            <option value='self-employed'>Self-employed</option>
            <option value='mixed'>Mixed (Employee + Self-employed)</option>
          </select>
        </div>
      </div>

      <NavigationButtons
        onNext={() =>
          onNext({
            age: parseInt(age),
            canton,
            employmentStatus: employment,
          })
        }
        onBack={onBack}
        nextDisabled={!age || !canton || !employment}
        darkMode={darkMode}
      />
    </div>
  );
};

// Helper Components
interface StepProps {
  userData: Partial<UserData>;
  onNext: (data: Partial<UserData>) => void;
  onBack?: () => void;
  darkMode: boolean;
}

const FeatureCard: React.FC<{
  icon: string;
  title: string;
  description: string;
  darkMode: boolean;
}> = ({ icon, title, description, darkMode }) => (
  <div
    className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700/50' : 'bg-gray-100'}`}
  >
    <div className='text-2xl mb-2'>{icon}</div>
    <h3
      className={`font-semibold mb-1 ${
        darkMode ? 'text-white' : 'text-gray-800'
      }`}
    >
      {title}
    </h3>
    <p className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
      {description}
    </p>
  </div>
);

const NavigationButtons: React.FC<{
  onNext: () => void;
  onBack?: () => void;
  nextDisabled?: boolean;
  darkMode: boolean;
}> = ({ onNext, onBack, nextDisabled, darkMode }) => (
  <div className='flex justify-between'>
    {onBack ? (
      <button
        onClick={onBack}
        className={`px-6 py-2 rounded-lg font-medium ${
          darkMode
            ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
            : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
        }`}
      >
        Back
      </button>
    ) : (
      <div />
    )}

    <button
      onClick={onNext}
      disabled={nextDisabled}
      className={`px-6 py-2 rounded-lg font-medium ${
        nextDisabled
          ? 'bg-gray-400 text-gray-600 cursor-not-allowed'
          : 'bg-gradient-to-r from-blue-500 to-purple-500 text-white hover:from-blue-600 hover:to-purple-600'
      }`}
    >
      Continue
    </button>
  </div>
);

const IncomeStep: React.FC<StepProps> = ({
  userData,
  onNext,
  onBack,
  darkMode,
}) => {
  const [income, setIncome] = useState(userData.monthlyIncome || '');
  const [hasAdditionalIncome, setHasAdditionalIncome] = useState(false);
  const [additionalIncome, setAdditionalIncome] = useState('');

  return (
    <div>
      <div className='space-y-6 mb-8'>
        <div>
          <label
            className={`block text-sm font-medium mb-2 ${
              darkMode ? 'text-gray-300' : 'text-gray-700'
            }`}
          >
            Primary monthly income (CHF)
          </label>
          <input
            type='number'
            value={income}
            onChange={e => setIncome(e.target.value)}
            placeholder='e.g., 8000'
            className={`w-full p-3 rounded-lg border ${
              darkMode
                ? 'bg-gray-700 border-gray-600 text-white'
                : 'bg-white border-gray-300'
            } focus:ring-2 focus:ring-blue-500`}
          />
        </div>

        <div>
          <label className='flex items-center space-x-2'>
            <input
              type='checkbox'
              checked={hasAdditionalIncome}
              onChange={e => setHasAdditionalIncome(e.target.checked)}
              className='rounded'
            />
            <span
              className={`text-sm ${
                darkMode ? 'text-gray-300' : 'text-gray-700'
              }`}
            >
              I have additional income sources
            </span>
          </label>
        </div>

        {hasAdditionalIncome && (
          <div>
            <label
              className={`block text-sm font-medium mb-2 ${
                darkMode ? 'text-gray-300' : 'text-gray-700'
              }`}
            >
              Additional monthly income (CHF)
            </label>
            <input
              type='number'
              value={additionalIncome}
              onChange={e => setAdditionalIncome(e.target.value)}
              placeholder='e.g., 1000'
              className={`w-full p-3 rounded-lg border ${
                darkMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-white border-gray-300'
              } focus:ring-2 focus:ring-blue-500`}
            />
          </div>
        )}
      </div>

      <NavigationButtons
        onNext={() =>
          onNext({
            monthlyIncome: parseFloat(income) || 0,
            additionalIncome: hasAdditionalIncome
              ? parseFloat(additionalIncome) || 0
              : 0,
          })
        }
        onBack={onBack}
        nextDisabled={!income}
        darkMode={darkMode}
      />
    </div>
  );
};

const ExperienceStep: React.FC<StepProps> = ({
  userData,
  onNext,
  onBack,
  darkMode,
}) => {
  const [experience, setExperience] = useState(userData.experienceLevel || '');

  const levels = [
    {
      id: 'beginner',
      title: 'Beginner',
      description: 'New to financial planning',
      icon: '🌱',
    },
    {
      id: 'intermediate',
      title: 'Intermediate',
      description: 'Some experience with investments',
      icon: '📈',
    },
    {
      id: 'advanced',
      title: 'Advanced',
      description: 'Experienced with Swiss financial system',
      icon: '🎯',
    },
  ];

  return (
    <div>
      <div className='space-y-4 mb-8'>
        {levels.map(level => (
          <button
            key={level.id}
            onClick={() => setExperience(level.id)}
            className={`w-full p-4 rounded-lg border-2 transition-all text-left ${
              experience === level.id
                ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/30'
                : darkMode
                  ? 'border-gray-600 bg-gray-700/50 hover:border-gray-500'
                  : 'border-gray-200 bg-gray-50 hover:border-gray-300'
            }`}
          >
            <div className='flex items-center space-x-3'>
              <div className='text-2xl'>{level.icon}</div>
              <div>
                <h3
                  className={`font-semibold ${
                    darkMode ? 'text-white' : 'text-gray-800'
                  }`}
                >
                  {level.title}
                </h3>
                <p
                  className={`text-sm ${
                    darkMode ? 'text-gray-300' : 'text-gray-600'
                  }`}
                >
                  {level.description}
                </p>
              </div>
            </div>
          </button>
        ))}
      </div>

      <NavigationButtons
        onNext={() => onNext({ experienceLevel: experience })}
        onBack={onBack}
        nextDisabled={!experience}
        darkMode={darkMode}
      />
    </div>
  );
};

export default OnboardingWizard;
