import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

interface Language {
  code: string;
  name: string;
  nativeName: string;
  flag: string;
}

const SUPPORTED_LANGUAGES: Language[] = [
  {
    code: 'de-CH',
    name: 'Swiss German',
    nativeName: 'Schweizerdeutsch',
    flag: '🇨🇭',
  },
  {
    code: 'en-CH',
    name: 'English (Switzerland)',
    nativeName: 'English',
    flag: '🇬🇧',
  },
  // Future languages
  // {
  //   code: 'fr-CH',
  //   name: 'French (Switzerland)',
  //   nativeName: 'Français',
  //   flag: '🇫🇷'
  // },
  // {
  //   code: 'it-CH',
  //   name: 'Italian (Switzerland)',
  //   nativeName: 'Italiano',
  //   flag: '🇮🇹'
  // },
];

interface LanguageSwitcherProps {
  darkMode?: boolean;
  className?: string;
}

export const LanguageSwitcher: React.FC<LanguageSwitcherProps> = ({
  darkMode = false,
  className = '',
}) => {
  const { i18n, t } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);

  const currentLanguage =
    SUPPORTED_LANGUAGES.find(lang => lang.code === i18n.language) ||
    SUPPORTED_LANGUAGES[0];

  const handleLanguageChange = async (languageCode: string) => {
    try {
      await i18n.changeLanguage(languageCode);
      setIsOpen(false);

      // Optional: Analytics tracking
      if (typeof window !== 'undefined' && (window as any).gtag) {
        (window as any).gtag('event', 'language_change', {
          language: languageCode,
          previous_language: currentLanguage.code,
        });
      }
    } catch (error) {
      console.error('Failed to change language:', error);
    }
  };

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  const handleKeyDown = (event: React.KeyboardEvent, languageCode?: string) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      if (languageCode) {
        handleLanguageChange(languageCode);
      } else {
        toggleDropdown();
      }
    } else if (event.key === 'Escape') {
      setIsOpen(false);
    }
  };

  return (
    <div className={`relative ${className}`}>
      {/* Language Switcher Button */}
      <button
        onClick={toggleDropdown}
        onKeyDown={e => handleKeyDown(e)}
        className={`
          flex items-center space-x-2 px-3 py-2 rounded-lg transition-all duration-200
          ${
            darkMode
              ? 'bg-gray-700 hover:bg-gray-600 text-white border border-gray-600'
              : 'bg-white hover:bg-gray-50 text-gray-700 border border-gray-300 shadow-sm'
          }
          focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1
          ${isOpen ? 'ring-2 ring-blue-500' : ''}
        `}
        aria-label={t('language.switch')}
        aria-expanded={isOpen}
        aria-haspopup='listbox'
      >
        <span className='text-lg' role='img' aria-label={currentLanguage.name}>
          {currentLanguage.flag}
        </span>
        <span className='text-sm font-medium hidden sm:inline'>
          {currentLanguage.nativeName}
        </span>
        <svg
          className={`w-4 h-4 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
          fill='none'
          stroke='currentColor'
          viewBox='0 0 24 24'
        >
          <path
            strokeLinecap='round'
            strokeLinejoin='round'
            strokeWidth={2}
            d='M19 9l-7 7-7-7'
          />
        </svg>
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <>
          {/* Backdrop for mobile */}
          <div
            className='fixed inset-0 z-10 sm:hidden'
            onClick={() => setIsOpen(false)}
            aria-hidden='true'
          />

          {/* Dropdown Content */}
          <div
            className={`
              absolute right-0 mt-2 w-48 rounded-lg shadow-lg z-20
              ${
                darkMode
                  ? 'bg-gray-800 border border-gray-600'
                  : 'bg-white border border-gray-200'
              }
              focus:outline-none
            `}
            role='listbox'
            aria-label={t('language.switch')}
          >
            <div className='py-1'>
              {SUPPORTED_LANGUAGES.map(language => (
                <button
                  key={language.code}
                  onClick={() => handleLanguageChange(language.code)}
                  onKeyDown={e => handleKeyDown(e, language.code)}
                  className={`
                    w-full flex items-center space-x-3 px-4 py-2 text-left transition-colors duration-150
                    ${
                      darkMode
                        ? 'hover:bg-gray-700 text-white'
                        : 'hover:bg-gray-50 text-gray-700'
                    }
                    ${
                      currentLanguage.code === language.code
                        ? darkMode
                          ? 'bg-gray-700 text-blue-400'
                          : 'bg-blue-50 text-blue-600'
                        : ''
                    }
                    focus:outline-none focus:bg-blue-50 focus:text-blue-600
                  `}
                  role='option'
                  aria-selected={currentLanguage.code === language.code}
                >
                  <span
                    className='text-lg'
                    role='img'
                    aria-label={language.name}
                  >
                    {language.flag}
                  </span>
                  <div className='flex-1'>
                    <div className='text-sm font-medium'>
                      {language.nativeName}
                    </div>
                    <div
                      className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}
                    >
                      {language.name}
                    </div>
                  </div>
                  {currentLanguage.code === language.code && (
                    <svg
                      className='w-4 h-4 text-blue-500'
                      fill='currentColor'
                      viewBox='0 0 20 20'
                    >
                      <path
                        fillRule='evenodd'
                        d='M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z'
                        clipRule='evenodd'
                      />
                    </svg>
                  )}
                </button>
              ))}
            </div>

            {/* Footer with current language info */}
            <div
              className={`
              px-4 py-2 border-t text-xs
              ${
                darkMode
                  ? 'border-gray-600 text-gray-400 bg-gray-750'
                  : 'border-gray-200 text-gray-500 bg-gray-50'
              }
            `}
            >
              {t('language.current')}: {currentLanguage.nativeName}
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default LanguageSwitcher;
