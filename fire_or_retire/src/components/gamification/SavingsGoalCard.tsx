import React, { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { SavingsGoal, GoalMilestone } from '../../types/gamification';
import { useGamification } from '../../hooks/useGamification';

interface SavingsGoalCardProps {
  goal: SavingsGoal;
  darkMode: boolean;
  onEdit?: (goal: SavingsGoal) => void;
  onDelete?: (goalId: string) => void;
  onContribute?: (goalId: string, amount: number) => void;
  className?: string;
}

const SavingsGoalCard: React.FC<SavingsGoalCardProps> = ({
  goal,
  darkMode,
  onEdit,
  onDelete,
  onContribute,
  className = '',
}) => {
  const { t } = useTranslation();
  const [contributionAmount, setContributionAmount] = useState<string>('');
  const [showContributeForm, setShowContributeForm] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);

  const { awardSavingsXP, awardGoalAchievementXP } = useGamification(goal.userId);

  // Calculate progress percentage
  const progressPercentage = goal.targetAmount > 0 
    ? Math.min(100, (goal.currentAmount / goal.targetAmount) * 100)
    : 0;

  // Calculate days remaining
  const daysRemaining = Math.ceil(
    (new Date(goal.targetDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
  );

  // Get goal category emoji and color
  const getCategoryInfo = useCallback((category: string) => {
    const categoryMap = {
      emergency_fund: { emoji: '🚨', color: 'red', name: 'Emergency Fund' },
      vacation: { emoji: '✈️', color: 'blue', name: 'Vacation' },
      home_purchase: { emoji: '🏠', color: 'green', name: 'Home Purchase' },
      retirement: { emoji: '🏖️', color: 'purple', name: 'Retirement' },
      education: { emoji: '🎓', color: 'indigo', name: 'Education' },
      vehicle: { emoji: '🚗', color: 'gray', name: 'Vehicle' },
      investment: { emoji: '📈', color: 'yellow', name: 'Investment' },
      debt_payoff: { emoji: '💳', color: 'orange', name: 'Debt Payoff' },
      pillar3a: { emoji: '🏛️', color: 'purple', name: 'Pillar 3a' },
      custom: { emoji: '🎯', color: 'pink', name: 'Custom Goal' },
    };
    return categoryMap[category as keyof typeof categoryMap] || categoryMap.custom;
  }, []);

  // Get priority color
  const getPriorityColor = useCallback((priority: string) => {
    const priorityColors = {
      critical: 'text-red-600',
      high: 'text-orange-600',
      medium: 'text-yellow-600',
      low: 'text-green-600',
    };
    return priorityColors[priority as keyof typeof priorityColors] || priorityColors.medium;
  }, []);

  // Handle contribution
  const handleContribute = useCallback(async () => {
    const amount = parseFloat(contributionAmount);
    if (isNaN(amount) || amount <= 0) return;

    setIsAnimating(true);
    
    try {
      // Award XP for savings contribution
      const isPillar3a = goal.category === 'pillar3a';
      await awardSavingsXP(amount, isPillar3a);

      // Check if goal is completed with this contribution
      const newAmount = goal.currentAmount + amount;
      if (newAmount >= goal.targetAmount && goal.currentAmount < goal.targetAmount) {
        // Award goal achievement XP
        await awardGoalAchievementXP(
          goal.targetAmount,
          new Date(goal.targetDate),
          goal.swissSpecific
        );
      }

      // Call parent handler
      onContribute?.(goal.id, amount);
      
      setContributionAmount('');
      setShowContributeForm(false);
    } catch (error) {
      console.error('Failed to process contribution:', error);
    } finally {
      setTimeout(() => setIsAnimating(false), 1000);
    }
  }, [contributionAmount, goal, awardSavingsXP, awardGoalAchievementXP, onContribute]);

  // Get next milestone
  const getNextMilestone = useCallback((): GoalMilestone | null => {
    return goal.milestones.find(m => !m.isReached) || null;
  }, [goal.milestones]);

  const categoryInfo = getCategoryInfo(goal.category);
  const nextMilestone = getNextMilestone();
  const isCompleted = goal.status === 'completed' || progressPercentage >= 100;
  const isOverdue = daysRemaining < 0 && !isCompleted;

  return (
    <div className={`savings-goal-card ${className} ${isAnimating ? 'animate-pulse' : ''}`}>
      <div className={`p-6 rounded-lg border-2 transition-all duration-300 ${
        isCompleted 
          ? 'border-green-500 bg-green-50 dark:bg-green-900/20'
          : isOverdue
          ? 'border-red-500 bg-red-50 dark:bg-red-900/20'
          : darkMode 
          ? 'border-gray-600 bg-gray-800 hover:border-gray-500'
          : 'border-gray-300 bg-white hover:border-gray-400 shadow-sm'
      }`}>
        {/* Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className="text-2xl">{categoryInfo.emoji}</div>
            <div>
              <h3 className="text-lg font-semibold">{goal.name}</h3>
              <div className="flex items-center space-x-2 text-sm">
                <span className={`font-medium ${getPriorityColor(goal.priority)}`}>
                  {t(`priority.${goal.priority}`, goal.priority)}
                </span>
                <span className={`px-2 py-1 rounded-full text-xs ${
                  darkMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-100 text-gray-600'
                }`}>
                  {t(`category.${goal.category}`, categoryInfo.name)}
                </span>
                {goal.swissSpecific && (
                  <span className="px-2 py-1 rounded-full text-xs bg-red-100 text-red-600 dark:bg-red-900/20 dark:text-red-400">
                    🇨🇭 Swiss
                  </span>
                )}
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            {onEdit && (
              <button
                onClick={() => onEdit(goal)}
                className={`p-2 rounded-lg transition-colors ${
                  darkMode
                    ? 'text-gray-400 hover:text-white hover:bg-gray-700'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                }`}
                title={t('goal.edit', 'Edit Goal')}
              >
                ✏️
              </button>
            )}
            {onDelete && (
              <button
                onClick={() => onDelete(goal.id)}
                className={`p-2 rounded-lg transition-colors ${
                  darkMode
                    ? 'text-gray-400 hover:text-red-400 hover:bg-gray-700'
                    : 'text-gray-600 hover:text-red-600 hover:bg-gray-100'
                }`}
                title={t('goal.delete', 'Delete Goal')}
              >
                🗑️
              </button>
            )}
          </div>
        </div>

        {/* Description */}
        {goal.description && (
          <p className={`text-sm mb-4 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            {goal.description}
          </p>
        )}

        {/* Progress Section */}
        <div className="mb-4">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium">
              {t('goal.progress', 'Progress')}
            </span>
            <span className="text-sm font-semibold">
              {progressPercentage.toFixed(1)}%
            </span>
          </div>
          
          {/* Progress Bar */}
          <div className={`w-full bg-gray-300 rounded-full h-3 ${darkMode ? 'bg-gray-600' : ''}`}>
            <div
              className={`h-3 rounded-full transition-all duration-1000 ease-out ${
                isCompleted 
                  ? 'bg-green-500'
                  : isOverdue
                  ? 'bg-red-500'
                  : 'bg-gradient-to-r from-blue-500 to-purple-600'
              }`}
              style={{ width: `${Math.min(100, progressPercentage)}%` }}
            >
              {isCompleted && (
                <div className="h-full bg-yellow-400 opacity-50 animate-pulse rounded-full"></div>
              )}
            </div>
          </div>

          {/* Amount Display */}
          <div className="flex justify-between text-sm mt-2">
            <span>CHF {goal.currentAmount.toLocaleString()}</span>
            <span>CHF {goal.targetAmount.toLocaleString()}</span>
          </div>
        </div>

        {/* Milestones */}
        {goal.milestones.length > 0 && (
          <div className="mb-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">
                {t('goal.milestones', 'Milestones')}
              </span>
              {nextMilestone && (
                <span className="text-xs text-blue-600 dark:text-blue-400">
                  {t('goal.next', 'Next')}: {nextMilestone.percentage}%
                </span>
              )}
            </div>
            <div className="flex space-x-1">
              {goal.milestones.map((milestone) => (
                <div
                  key={milestone.id}
                  className={`flex-1 h-2 rounded-full ${
                    milestone.isReached
                      ? 'bg-green-500'
                      : darkMode
                      ? 'bg-gray-600'
                      : 'bg-gray-300'
                  }`}
                  title={`${milestone.percentage}% - CHF ${milestone.amount.toLocaleString()}`}
                />
              ))}
            </div>
          </div>
        )}

        {/* Timeline */}
        <div className="mb-4">
          <div className="flex justify-between text-sm">
            <span className={darkMode ? 'text-gray-400' : 'text-gray-600'}>
              {t('goal.targetDate', 'Target Date')}
            </span>
            <span className={`font-medium ${
              isOverdue 
                ? 'text-red-600' 
                : daysRemaining <= 30 
                ? 'text-orange-600' 
                : 'text-green-600'
            }`}>
              {isOverdue 
                ? t('goal.overdue', 'Overdue by {{days}} days', { days: Math.abs(daysRemaining) })
                : daysRemaining === 0
                ? t('goal.dueToday', 'Due Today')
                : daysRemaining === 1
                ? t('goal.dueTomorrow', 'Due Tomorrow')
                : t('goal.daysRemaining', '{{days}} days remaining', { days: daysRemaining })
              }
            </span>
          </div>
          <div className="text-sm text-gray-500">
            {new Date(goal.targetDate).toLocaleDateString()}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex space-x-2">
          {!isCompleted && (
            <>
              {!showContributeForm ? (
                <button
                  onClick={() => setShowContributeForm(true)}
                  className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
                >
                  💰 {t('goal.contribute', 'Contribute')}
                </button>
              ) : (
                <div className="flex-1 flex space-x-2">
                  <input
                    type="number"
                    value={contributionAmount}
                    onChange={(e) => setContributionAmount(e.target.value)}
                    placeholder="Amount (CHF)"
                    className={`flex-1 px-3 py-2 rounded-lg border ${
                      darkMode
                        ? 'bg-gray-700 border-gray-600 text-white'
                        : 'bg-white border-gray-300 text-gray-900'
                    }`}
                    min="0"
                    step="0.01"
                  />
                  <button
                    onClick={handleContribute}
                    disabled={!contributionAmount || parseFloat(contributionAmount) <= 0}
                    className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    ✓
                  </button>
                  <button
                    onClick={() => {
                      setShowContributeForm(false);
                      setContributionAmount('');
                    }}
                    className={`px-4 py-2 rounded-lg transition-colors ${
                      darkMode
                        ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                        : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                    }`}
                  >
                    ✕
                  </button>
                </div>
              )}
            </>
          )}

          {isCompleted && (
            <div className="flex-1 px-4 py-2 bg-green-600 text-white rounded-lg text-center font-medium">
              🎉 {t('goal.completed', 'Completed!')}
            </div>
          )}
        </div>

        {/* Auto-contribute info */}
        {goal.autoContribute && goal.monthlyContribution > 0 && (
          <div className={`mt-3 p-2 rounded-lg text-xs ${
            darkMode ? 'bg-gray-700 text-gray-400' : 'bg-gray-100 text-gray-600'
          }`}>
            🔄 {t('goal.autoContribute', 'Auto-contributing CHF {{amount}} monthly', {
              amount: goal.monthlyContribution.toLocaleString()
            })}
          </div>
        )}
      </div>
    </div>
  );
};

export default SavingsGoalCard;
