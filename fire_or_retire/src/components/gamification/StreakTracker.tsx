import React, { useState, useCallback, useMemo, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocalStorage } from '../../hooks/useLocalStorage';
import { StreakData, StreakMilestone } from '../../types/gamification';
import StreakTrackingService, { StreakActivity } from '../../services/StreakTrackingService';

interface StreakTrackerProps {
  userId: string;
  darkMode: boolean;
  onXPAwarded?: (xp: number, description: string) => void;
  className?: string;
}

const StreakTracker: React.FC<StreakTrackerProps> = ({
  userId,
  darkMode,
  onXPAwarded,
  className = '',
}) => {
  const { t } = useTranslation();
  const [selectedStreakType, setSelectedStreakType] = useState<string>('savings');
  const [showProtectionModal, setShowProtectionModal] = useState<string | null>(null);
  const [showCalendar, setShowCalendar] = useState(false);

  const streakService = useMemo(() => new StreakTrackingService(), []);

  // Initialize user streaks
  useEffect(() => {
    streakService.initializeUserStreaks(userId);
  }, [userId, streakService]);

  // Get all user streaks
  const userStreaks = useMemo(() => {
    return streakService.getAllUserStreaks(userId);
  }, [userId, streakService]);

  // Get streak statistics
  const streakStats = useMemo(() => {
    return streakService.getStreakStatistics(userId);
  }, [userId, streakService]);

  // Get streak milestones
  const milestones = useMemo(() => {
    return streakService.getStreakMilestones();
  }, [streakService]);

  // Get achieved milestones
  const achievedMilestones = useMemo(() => {
    return streakService.getAchievedMilestones(userId);
  }, [userId, streakService]);

  // Get recent activities
  const recentActivities = useMemo(() => {
    return streakService.getRecentActivities(userId, 5);
  }, [userId, streakService]);

  // Record activity
  const recordActivity = useCallback(async (type: string, amount?: number) => {
    const activity = streakService.recordActivity({
      userId,
      type: type as any,
      date: new Date(),
      amount,
    });

    // Calculate XP for streak
    const xpResult = streakService.calculateStreakXP(userId, type);
    
    if (onXPAwarded && xpResult.totalXP > 0) {
      onXPAwarded(xpResult.totalXP, xpResult.description);
    }

    // Force re-render by updating a state
    setSelectedStreakType(prev => prev);
  }, [userId, streakService, onXPAwarded]);

  // Use streak protection
  const useStreakProtection = useCallback((streakType: string) => {
    const success = streakService.useStreakProtection(userId, streakType, 'User requested protection');
    if (success) {
      setShowProtectionModal(null);
      // Force re-render
      setSelectedStreakType(prev => prev);
    }
    return success;
  }, [userId, streakService]);

  // Get streak type info
  const getStreakTypeInfo = useCallback((type: string) => {
    const typeMap = {
      savings: { name: 'Savings', emoji: '💰', color: 'text-green-600', bg: 'bg-green-100 dark:bg-green-900/20' },
      budget_adherence: { name: 'Budget', emoji: '📊', color: 'text-blue-600', bg: 'bg-blue-100 dark:bg-blue-900/20' },
      goal_progress: { name: 'Goals', emoji: '🎯', color: 'text-purple-600', bg: 'bg-purple-100 dark:bg-purple-900/20' },
      app_usage: { name: 'Activity', emoji: '📱', color: 'text-orange-600', bg: 'bg-orange-100 dark:bg-orange-900/20' },
      swiss_optimization: { name: 'Swiss', emoji: '🇨🇭', color: 'text-red-600', bg: 'bg-red-100 dark:bg-red-900/20' },
    };
    return typeMap[type as keyof typeof typeMap] || typeMap.savings;
  }, []);

  // Get calendar data for selected streak
  const calendarData = useMemo(() => {
    const now = new Date();
    return streakService.getStreakCalendar(userId, selectedStreakType, now.getFullYear(), now.getMonth());
  }, [userId, selectedStreakType, streakService]);

  return (
    <div className={`streak-tracker ${className}`}>
      {/* Protection Modal */}
      {showProtectionModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className={`max-w-md w-full rounded-lg p-6 ${darkMode ? 'bg-gray-800' : 'bg-white'}`}>
            <h3 className="text-lg font-bold mb-4">
              🛡️ {t('streak.protection.title', 'Streak Protection')}
            </h3>
            <p className="mb-4">
              {t('streak.protection.description', 'Use a streak protection to save your {{type}} streak?', {
                type: getStreakTypeInfo(showProtectionModal).name
              })}
            </p>
            <div className="flex space-x-3">
              <button
                onClick={() => useStreakProtection(showProtectionModal)}
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                {t('streak.protection.use', 'Use Protection')}
              </button>
              <button
                onClick={() => setShowProtectionModal(null)}
                className={`flex-1 px-4 py-2 rounded-lg transition-colors ${
                  darkMode
                    ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                {t('streak.protection.cancel', 'Cancel')}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Header */}
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-4">
          🔥 {t('streak.title', 'Streak Tracker')}
        </h2>

        {/* Overall Statistics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}>
            <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              {t('streak.stats.active', 'Active Streaks')}
            </div>
            <div className="text-2xl font-bold text-orange-600">
              {streakStats.totalActiveStreaks}
            </div>
          </div>

          <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}>
            <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              {t('streak.stats.longest', 'Longest Current')}
            </div>
            <div className="text-2xl font-bold text-green-600">
              {streakStats.longestCurrentStreak}
            </div>
            <div className={`text-xs ${darkMode ? 'text-gray-500' : 'text-gray-400'}`}>
              {t('streak.days', 'days')}
            </div>
          </div>

          <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}>
            <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              {t('streak.stats.record', 'Personal Record')}
            </div>
            <div className="text-2xl font-bold text-purple-600">
              {streakStats.longestEverStreak}
            </div>
            <div className={`text-xs ${darkMode ? 'text-gray-500' : 'text-gray-400'}`}>
              {t('streak.days', 'days')}
            </div>
          </div>

          <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}>
            <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              {t('streak.stats.protections', 'Protections')}
            </div>
            <div className="text-2xl font-bold text-blue-600">
              {streakStats.protectionsAvailable}
            </div>
            <div className={`text-xs ${darkMode ? 'text-gray-500' : 'text-gray-400'}`}>
              {t('streak.available', 'available')}
            </div>
          </div>
        </div>
      </div>

      {/* Streak Types */}
      <div className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'} mb-6`}>
        <h3 className="text-lg font-semibold mb-4">
          📈 {t('streak.types.title', 'Your Streaks')}
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {Array.from(userStreaks.entries()).map(([type, streak]) => {
            const typeInfo = getStreakTypeInfo(type);
            const canProtect = streakService.canUseStreakProtection(userId, type);
            
            return (
              <div
                key={type}
                className={`p-4 rounded-lg border-2 transition-all cursor-pointer ${
                  selectedStreakType === type
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                    : darkMode
                    ? 'border-gray-600 bg-gray-700 hover:border-gray-500'
                    : 'border-gray-300 bg-gray-50 hover:border-gray-400'
                }`}
                onClick={() => setSelectedStreakType(type)}
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <span className="text-xl">{typeInfo.emoji}</span>
                    <span className="font-medium">{typeInfo.name}</span>
                  </div>
                  {streak.isActive && (
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  )}
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      {t('streak.current', 'Current')}
                    </span>
                    <span className={`font-bold ${typeInfo.color}`}>
                      {streak.currentStreak} {t('streak.days', 'days')}
                    </span>
                  </div>

                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      {t('streak.multiplier', 'Multiplier')}
                    </span>
                    <span className="font-bold text-yellow-600">
                      {streak.multiplier.toFixed(1)}x
                    </span>
                  </div>

                  {streak.currentStreak > 0 && (
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        {t('streak.protections', 'Protections')}
                      </span>
                      <span className="text-sm">
                        {streak.maxProtections - streak.protectionUsed}/{streak.maxProtections}
                        {canProtect && (
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              setShowProtectionModal(type);
                            }}
                            className="ml-1 text-blue-600 hover:text-blue-800"
                          >
                            🛡️
                          </button>
                        )}
                      </span>
                    </div>
                  )}
                </div>

                {/* Quick Action Button */}
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    recordActivity(type);
                  }}
                  className={`w-full mt-3 px-3 py-1 rounded-lg text-sm font-medium transition-colors ${
                    streak.isActive
                      ? 'bg-green-600 text-white hover:bg-green-700'
                      : 'bg-blue-600 text-white hover:bg-blue-700'
                  }`}
                >
                  {streak.isActive 
                    ? t('streak.continue', 'Continue Streak')
                    : t('streak.start', 'Start Streak')
                  }
                </button>
              </div>
            );
          })}
        </div>
      </div>

      {/* Next Milestone */}
      {streakStats.nextMilestone && (
        <div className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'} mb-6`}>
          <h3 className="text-lg font-semibold mb-4">
            🎯 {t('streak.nextMilestone', 'Next Milestone')}
          </h3>
          
          <div className="flex items-center space-x-4">
            <div className="text-4xl">{streakStats.nextMilestone.badge}</div>
            <div className="flex-1">
              <div className="font-bold text-lg">{streakStats.nextMilestone.title}</div>
              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                {streakStats.nextMilestone.description}
              </div>
              <div className="mt-2">
                <div className="flex justify-between text-sm mb-1">
                  <span>{streakStats.longestCurrentStreak} days</span>
                  <span>{streakStats.nextMilestone.days} days</span>
                </div>
                <div className={`w-full bg-gray-300 rounded-full h-2 ${darkMode ? 'bg-gray-600' : ''}`}>
                  <div
                    className="bg-gradient-to-r from-orange-500 to-red-600 h-2 rounded-full transition-all duration-1000"
                    style={{ 
                      width: `${Math.min(100, (streakStats.longestCurrentStreak / streakStats.nextMilestone.days) * 100)}%` 
                    }}
                  />
                </div>
              </div>
            </div>
            <div className="text-right">
              <div className="text-sm text-gray-600 dark:text-gray-400">Reward</div>
              <div className="font-bold text-yellow-600">+{streakStats.nextMilestone.xpReward} XP</div>
            </div>
          </div>
        </div>
      )}

      {/* Calendar View */}
      {showCalendar && (
        <div className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'} mb-6`}>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">
              📅 {t('streak.calendar', 'Activity Calendar')} - {getStreakTypeInfo(selectedStreakType).name}
            </h3>
            <button
              onClick={() => setShowCalendar(false)}
              className={`text-sm ${darkMode ? 'text-gray-400 hover:text-white' : 'text-gray-600 hover:text-gray-900'}`}
            >
              ✕
            </button>
          </div>
          
          <div className="grid grid-cols-7 gap-1">
            {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map(day => (
              <div key={day} className={`text-center text-xs font-medium p-2 ${
                darkMode ? 'text-gray-400' : 'text-gray-600'
              }`}>
                {day}
              </div>
            ))}
            {calendarData.map((hasActivity, index) => (
              <div
                key={index}
                className={`aspect-square rounded-lg flex items-center justify-center text-sm ${
                  hasActivity
                    ? 'bg-green-500 text-white'
                    : darkMode
                    ? 'bg-gray-700 text-gray-400'
                    : 'bg-gray-200 text-gray-600'
                }`}
              >
                {index + 1}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Recent Activities */}
      <div className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}>
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">
            📋 {t('streak.recentActivities', 'Recent Activities')}
          </h3>
          <button
            onClick={() => setShowCalendar(!showCalendar)}
            className={`text-sm px-3 py-1 rounded-lg transition-colors ${
              darkMode
                ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            📅 {t('streak.showCalendar', 'Calendar')}
          </button>
        </div>

        {recentActivities.length > 0 ? (
          <div className="space-y-3">
            {recentActivities.map((activity) => {
              const typeInfo = getStreakTypeInfo(activity.type);
              return (
                <div key={activity.id} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${typeInfo.bg}`}>
                      <span className="text-sm">{typeInfo.emoji}</span>
                    </div>
                    <div>
                      <div className="font-medium">{typeInfo.name} Activity</div>
                      <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                        {new Date(activity.date).toLocaleDateString()}
                      </div>
                    </div>
                  </div>
                  {activity.amount && (
                    <div className="text-right">
                      <div className="font-medium">CHF {activity.amount.toLocaleString()}</div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        ) : (
          <div className={`text-center py-8 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            <div className="text-4xl mb-2">🔥</div>
            <p>{t('streak.noActivities', 'No recent activities')}</p>
            <p className="text-sm mt-1">
              {t('streak.startActivity', 'Start your first streak today!')}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default StreakTracker;
