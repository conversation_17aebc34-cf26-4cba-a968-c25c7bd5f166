import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useGamification } from '../../hooks/useGamification';

interface GamificationDashboardProps {
  userId: string;
  darkMode: boolean;
  className?: string;
  showDetailedStats?: boolean;
  showQuickActions?: boolean;
}

const GamificationDashboard: React.FC<GamificationDashboardProps> = ({
  userId,
  darkMode,
  className = '',
  showDetailedStats = true,
  showQuickActions = true,
}) => {
  const { t } = useTranslation();
  const [showLevelDetails, setShowLevelDetails] = useState(false);

  const {
    progress,
    isLoading,
    error,
    notifications,
    recentLevelUp,
    recentXPGain,
    awardSavingsXP,
    awardSwissOptimizationXP,
    clearNotifications,
    dismissLevelUp,
    hasFeatureAccess,
  } = useGamification(userId);

  // Auto-dismiss XP gain animation after 3 seconds
  useEffect(() => {
    if (recentXPGain > 0) {
      const timeout = setTimeout(() => {
        // XP gain animation would be handled by CSS
      }, 3000);
      return () => clearTimeout(timeout);
    }
  }, [recentXPGain]);

  if (isLoading) {
    return (
      <div className={`gamification-dashboard ${className}`}>
        <div className={`animate-pulse p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}>
          <div className="h-4 bg-gray-300 rounded w-1/4 mb-4"></div>
          <div className="h-8 bg-gray-300 rounded w-1/2 mb-2"></div>
          <div className="h-2 bg-gray-300 rounded w-full"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`gamification-dashboard ${className}`}>
        <div className={`p-4 rounded-lg border-l-4 border-red-500 ${darkMode ? 'bg-red-900/20' : 'bg-red-50'}`}>
          <p className="text-red-600 dark:text-red-400">
            {t('gamification.error', 'Error loading gamification data')}: {error}
          </p>
        </div>
      </div>
    );
  }

  if (!progress) {
    return null;
  }

  const levelProgress = progress.progressToNextLevel;
  const currentLevelTitle = `Level ${progress.currentLevel}`;

  return (
    <div className={`gamification-dashboard ${className}`}>
      {/* Level Up Celebration Modal */}
      {recentLevelUp && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className={`p-8 rounded-lg max-w-md w-full mx-4 ${darkMode ? 'bg-gray-800' : 'bg-white'}`}>
            <div className="text-center">
              <div className="text-6xl mb-4">🎉</div>
              <h2 className="text-2xl font-bold mb-2 text-yellow-500">
                {t('gamification.levelUp', 'Level Up!')}
              </h2>
              <p className="text-lg mb-4">
                {t('gamification.reachedLevel', 'You\'ve reached level')} {recentLevelUp.newLevel}!
              </p>
              
              {recentLevelUp.newUnlocks.length > 0 && (
                <div className="mb-4">
                  <h3 className="font-semibold mb-2">
                    {t('gamification.newUnlocks', 'New Features Unlocked:')}
                  </h3>
                  <ul className="text-sm space-y-1">
                    {recentLevelUp.newUnlocks.map((unlock, index) => (
                      <li key={index} className="text-green-600 dark:text-green-400">
                        ✨ {t(`feature.${unlock}`, unlock.replace('_', ' '))}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              <button
                onClick={dismissLevelUp}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                {t('gamification.awesome', 'Awesome!')}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Main Dashboard */}
      <div className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}>
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold">
            🎮 {t('gamification.dashboard', 'Your Progress')}
          </h2>
          {notifications.length > 0 && (
            <button
              onClick={clearNotifications}
              className={`text-sm px-3 py-1 rounded-lg transition-colors ${
                darkMode
                  ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              {t('gamification.clearNotifications', 'Clear')} ({notifications.length})
            </button>
          )}
        </div>

        {/* Level and XP Display */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center space-x-3">
              <div className="text-2xl font-bold text-blue-600">
                {currentLevelTitle}
              </div>
              <button
                onClick={() => setShowLevelDetails(!showLevelDetails)}
                className={`text-sm px-2 py-1 rounded transition-colors ${
                  darkMode
                    ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                {showLevelDetails ? '▼' : '▶'} {t('gamification.details', 'Details')}
              </button>
            </div>
            <div className="text-right">
              <div className="text-lg font-semibold">
                {progress.totalXP.toLocaleString()} XP
              </div>
              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                {t('gamification.totalEarned', 'Total Earned')}
              </div>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="relative">
            <div className={`w-full bg-gray-300 rounded-full h-3 ${darkMode ? 'bg-gray-600' : ''}`}>
              <div
                className="bg-gradient-to-r from-blue-500 to-purple-600 h-3 rounded-full transition-all duration-1000 ease-out relative overflow-hidden"
                style={{ width: `${levelProgress}%` }}
              >
                {recentXPGain > 0 && (
                  <div className="absolute inset-0 bg-yellow-400 opacity-50 animate-pulse"></div>
                )}
              </div>
            </div>
            <div className="flex justify-between text-xs mt-1">
              <span>{progress.currentLevelXP.toLocaleString()} XP</span>
              <span>{levelProgress.toFixed(1)}%</span>
              <span>{progress.nextLevelXP.toLocaleString()} XP</span>
            </div>
          </div>

          {/* Recent XP Gain Animation */}
          {recentXPGain > 0 && (
            <div className="text-center mt-2">
              <span className="inline-block px-3 py-1 bg-yellow-500 text-white rounded-full text-sm font-semibold animate-bounce">
                +{recentXPGain} XP
              </span>
            </div>
          )}
        </div>

        {/* Level Details */}
        {showLevelDetails && (
          <div className={`mb-6 p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
            <h3 className="font-semibold mb-3">
              {t('gamification.levelBenefits', 'Level Benefits')}
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="text-sm font-medium mb-2">
                  {t('gamification.achievements', 'Achievements')}
                </h4>
                <div className="text-2xl font-bold text-green-600">
                  {progress.totalAchievements}
                </div>
              </div>
              <div>
                <h4 className="text-sm font-medium mb-2">
                  {t('gamification.goalsCompleted', 'Goals Completed')}
                </h4>
                <div className="text-2xl font-bold text-purple-600">
                  {progress.completedGoals}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Quick Stats */}
        {showDetailedStats && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <div className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                {t('gamification.currentStreak', 'Current Streak')}
              </div>
              <div className="text-xl font-bold text-orange-600">
                {progress.streakDays} {t('gamification.days', 'days')}
              </div>
            </div>
            <div className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                {t('gamification.longestStreak', 'Longest Streak')}
              </div>
              <div className="text-xl font-bold text-red-600">
                {progress.longestStreak} {t('gamification.days', 'days')}
              </div>
            </div>
            <div className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                {t('gamification.level', 'Level')}
              </div>
              <div className="text-xl font-bold text-blue-600">
                {progress.currentLevel}
              </div>
            </div>
            <div className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                {t('gamification.rank', 'Rank')}
              </div>
              <div className="text-xl font-bold text-green-600">
                #1
              </div>
            </div>
          </div>
        )}

        {/* Quick Actions */}
        {showQuickActions && (
          <div>
            <h3 className="font-semibold mb-3">
              {t('gamification.quickActions', 'Quick Actions')}
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
              <button
                onClick={() => awardSavingsXP(100)}
                className="p-3 rounded-lg bg-green-600 text-white hover:bg-green-700 transition-colors text-sm"
              >
                💰 {t('gamification.testSavings', 'Test Savings')}
              </button>
              <button
                onClick={() => awardSwissOptimizationXP('pillar3a', 1000)}
                className="p-3 rounded-lg bg-purple-600 text-white hover:bg-purple-700 transition-colors text-sm"
              >
                🏛️ {t('gamification.testPillar3a', 'Test Pillar 3a')}
              </button>
              <button
                onClick={() => awardSwissOptimizationXP('tax')}
                className="p-3 rounded-lg bg-blue-600 text-white hover:bg-blue-700 transition-colors text-sm"
              >
                📊 {t('gamification.testTax', 'Test Tax Opt')}
              </button>
              <button
                onClick={() => awardSwissOptimizationXP('canton_comparison')}
                className="p-3 rounded-lg bg-red-600 text-white hover:bg-red-700 transition-colors text-sm"
              >
                🗺️ {t('gamification.testCanton', 'Test Canton')}
              </button>
            </div>
          </div>
        )}

        {/* Notifications */}
        {notifications.length > 0 && (
          <div className="mt-6">
            <h3 className="font-semibold mb-3">
              {t('gamification.notifications', 'Recent Activity')}
            </h3>
            <div className="space-y-2">
              {notifications.slice(0, 3).map((notification) => (
                <div
                  key={notification.id}
                  className={`p-3 rounded-lg border-l-4 ${
                    notification.priority === 'high'
                      ? 'border-yellow-500 bg-yellow-50 dark:bg-yellow-900/20'
                      : notification.priority === 'medium'
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                      : 'border-gray-500 bg-gray-50 dark:bg-gray-900/20'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">{notification.title}</div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">
                        {notification.message}
                      </div>
                    </div>
                    <div className="text-xs text-gray-500">
                      {notification.createdAt.toLocaleTimeString()}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default GamificationDashboard;
