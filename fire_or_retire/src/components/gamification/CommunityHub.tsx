import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useCommunity } from '../../hooks/useCommunity';
import { useGamification } from '../../hooks/useGamification';

interface CommunityHubProps {
  userId: string;
  darkMode: boolean;
  className?: string;
}

type CommunityView = 'leaderboards' | 'challenges' | 'achievements' | 'discussions' | 'mentorship';

interface LeaderboardEntry {
  userId: string;
  username: string;
  avatar: string;
  level: number;
  totalXP: number;
  rank: number;
  canton: string;
  badges: string[];
  isCurrentUser: boolean;
}

interface CommunityChallenge {
  id: string;
  title: string;
  description: string;
  type: 'savings' | 'streak' | 'swiss' | 'goal';
  startDate: Date;
  endDate: Date;
  participants: number;
  maxParticipants?: number;
  reward: {
    xp: number;
    badge?: string;
    title?: string;
  };
  progress?: {
    current: number;
    target: number;
    percentage: number;
  };
  isParticipating: boolean;
  difficulty: 'easy' | 'medium' | 'hard' | 'expert';
}

const CommunityHub: React.FC<CommunityHubProps> = ({
  userId,
  darkMode,
  className = '',
}) => {
  const { t } = useTranslation();
  const [activeView, setActiveView] = useState<CommunityView>('leaderboards');
  const [selectedLeaderboard, setSelectedLeaderboard] = useState<'global' | 'canton' | 'friends'>('global');
  const [selectedTimeframe, setSelectedTimeframe] = useState<'weekly' | 'monthly' | 'alltime'>('monthly');

  const { progress } = useGamification(userId);
  const {
    leaderboards,
    challenges,
    communityAchievements,
    discussions,
    mentorshipProgram,
    joinChallenge,
    leaveChallenge,
    shareAchievement,
    createDiscussion,
    isLoading,
  } = useCommunity(userId);

  const handleJoinChallenge = useCallback(async (challengeId: string) => {
    try {
      await joinChallenge(challengeId);
      // Show success message
    } catch (error) {
      console.error('Failed to join challenge:', error);
    }
  }, [joinChallenge]);

  const handleLeaveChallenge = useCallback(async (challengeId: string) => {
    try {
      await leaveChallenge(challengeId);
      // Show success message
    } catch (error) {
      console.error('Failed to leave challenge:', error);
    }
  }, [leaveChallenge]);

  const renderLeaderboards = () => (
    <div className="community-leaderboards">
      <div className="leaderboard-controls">
        <div className="leaderboard-selector">
          {[
            { key: 'global', label: 'Global', icon: '🌍' },
            { key: 'canton', label: 'Canton', icon: '🏛️' },
            { key: 'friends', label: 'Friends', icon: '👥' },
          ].map(board => (
            <button
              key={board.key}
              className={`selector-btn ${selectedLeaderboard === board.key ? 'active' : ''}`}
              onClick={() => setSelectedLeaderboard(board.key as any)}
            >
              <span className="selector-icon">{board.icon}</span>
              <span className="selector-label">{board.label}</span>
            </button>
          ))}
        </div>

        <div className="timeframe-selector">
          {[
            { key: 'weekly', label: 'This Week' },
            { key: 'monthly', label: 'This Month' },
            { key: 'alltime', label: 'All Time' },
          ].map(timeframe => (
            <button
              key={timeframe.key}
              className={`timeframe-btn ${selectedTimeframe === timeframe.key ? 'active' : ''}`}
              onClick={() => setSelectedTimeframe(timeframe.key as any)}
            >
              {timeframe.label}
            </button>
          ))}
        </div>
      </div>

      <div className="leaderboard-content">
        <div className="leaderboard-header">
          <h3>
            {selectedLeaderboard === 'global' && t('community.globalLeaderboard', 'Global Leaderboard')}
            {selectedLeaderboard === 'canton' && t('community.cantonLeaderboard', 'Canton Leaderboard')}
            {selectedLeaderboard === 'friends' && t('community.friendsLeaderboard', 'Friends Leaderboard')}
          </h3>
          <div className="leaderboard-stats">
            <span className="total-participants">
              {leaderboards?.[selectedLeaderboard]?.totalParticipants || 0} {t('community.participants', 'participants')}
            </span>
          </div>
        </div>

        <div className="leaderboard-list">
          {leaderboards?.[selectedLeaderboard]?.entries.map((entry: LeaderboardEntry, index: number) => (
            <div 
              key={entry.userId} 
              className={`leaderboard-entry ${entry.isCurrentUser ? 'current-user' : ''} ${index < 3 ? 'top-three' : ''}`}
            >
              <div className="entry-rank">
                {index < 3 ? (
                  <span className="rank-medal">
                    {index === 0 ? '🥇' : index === 1 ? '🥈' : '🥉'}
                  </span>
                ) : (
                  <span className="rank-number">#{entry.rank}</span>
                )}
              </div>

              <div className="entry-avatar">
                <img src={entry.avatar} alt={entry.username} />
                <div className="level-badge">{entry.level}</div>
              </div>

              <div className="entry-info">
                <div className="entry-username">
                  {entry.username}
                  {entry.isCurrentUser && <span className="you-indicator">(You)</span>}
                </div>
                <div className="entry-details">
                  <span className="entry-xp">{entry.totalXP.toLocaleString()} XP</span>
                  {selectedLeaderboard === 'canton' && (
                    <span className="entry-canton">{entry.canton}</span>
                  )}
                </div>
              </div>

              <div className="entry-badges">
                {entry.badges.slice(0, 3).map((badge, badgeIndex) => (
                  <span key={badgeIndex} className="badge-icon" title={badge}>
                    {badge}
                  </span>
                ))}
                {entry.badges.length > 3 && (
                  <span className="badge-more">+{entry.badges.length - 3}</span>
                )}
              </div>
            </div>
          ))}
        </div>

        {progress && (
          <div className="user-position">
            <div className="position-card">
              <h4>{t('community.yourPosition', 'Your Position')}</h4>
              <div className="position-stats">
                <div className="stat-item">
                  <span className="stat-label">{t('community.currentRank', 'Current Rank')}</span>
                  <span className="stat-value">#{leaderboards?.[selectedLeaderboard]?.userRank || 'N/A'}</span>
                </div>
                <div className="stat-item">
                  <span className="stat-label">{t('community.xpToNext', 'XP to Next Rank')}</span>
                  <span className="stat-value">{leaderboards?.[selectedLeaderboard]?.xpToNextRank || 'N/A'}</span>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );

  const renderChallenges = () => (
    <div className="community-challenges">
      <div className="challenges-header">
        <h3>{t('community.activeChallenges', 'Active Challenges')}</h3>
        <div className="challenge-filters">
          <select className="difficulty-filter">
            <option value="all">{t('community.allDifficulties', 'All Difficulties')}</option>
            <option value="easy">{t('community.easy', 'Easy')}</option>
            <option value="medium">{t('community.medium', 'Medium')}</option>
            <option value="hard">{t('community.hard', 'Hard')}</option>
            <option value="expert">{t('community.expert', 'Expert')}</option>
          </select>
          <select className="type-filter">
            <option value="all">{t('community.allTypes', 'All Types')}</option>
            <option value="savings">{t('community.savings', 'Savings')}</option>
            <option value="streak">{t('community.streak', 'Streak')}</option>
            <option value="swiss">{t('community.swiss', 'Swiss')}</option>
            <option value="goal">{t('community.goal', 'Goal')}</option>
          </select>
        </div>
      </div>

      <div className="challenges-grid">
        {challenges?.active.map((challenge: CommunityChallenge) => (
          <div key={challenge.id} className={`challenge-card ${challenge.difficulty}`}>
            <div className="challenge-header">
              <div className="challenge-type">
                {challenge.type === 'savings' && '💰'}
                {challenge.type === 'streak' && '🔥'}
                {challenge.type === 'swiss' && '🇨🇭'}
                {challenge.type === 'goal' && '🎯'}
              </div>
              <div className="challenge-difficulty">
                <span className={`difficulty-badge ${challenge.difficulty}`}>
                  {challenge.difficulty.toUpperCase()}
                </span>
              </div>
            </div>

            <div className="challenge-content">
              <h4 className="challenge-title">{challenge.title}</h4>
              <p className="challenge-description">{challenge.description}</p>

              <div className="challenge-timeline">
                <div className="timeline-item">
                  <span className="timeline-label">{t('community.starts', 'Starts')}</span>
                  <span className="timeline-value">{challenge.startDate.toLocaleDateString()}</span>
                </div>
                <div className="timeline-item">
                  <span className="timeline-label">{t('community.ends', 'Ends')}</span>
                  <span className="timeline-value">{challenge.endDate.toLocaleDateString()}</span>
                </div>
              </div>

              {challenge.progress && (
                <div className="challenge-progress">
                  <div className="progress-header">
                    <span className="progress-label">{t('community.progress', 'Progress')}</span>
                    <span className="progress-percentage">{challenge.progress.percentage}%</span>
                  </div>
                  <div className="progress-bar">
                    <div 
                      className="progress-fill"
                      style={{ width: `${challenge.progress.percentage}%` }}
                    />
                  </div>
                  <div className="progress-details">
                    {challenge.progress.current} / {challenge.progress.target}
                  </div>
                </div>
              )}

              <div className="challenge-participants">
                <span className="participants-count">
                  {challenge.participants} {t('community.participants', 'participants')}
                </span>
                {challenge.maxParticipants && (
                  <span className="participants-max">
                    / {challenge.maxParticipants} {t('community.max', 'max')}
                  </span>
                )}
              </div>

              <div className="challenge-reward">
                <h5>{t('community.rewards', 'Rewards')}</h5>
                <div className="reward-items">
                  <span className="reward-xp">+{challenge.reward.xp} XP</span>
                  {challenge.reward.badge && (
                    <span className="reward-badge">{challenge.reward.badge} Badge</span>
                  )}
                  {challenge.reward.title && (
                    <span className="reward-title">"{challenge.reward.title}" Title</span>
                  )}
                </div>
              </div>
            </div>

            <div className="challenge-actions">
              {challenge.isParticipating ? (
                <div className="participating-actions">
                  <span className="participating-indicator">
                    ✅ {t('community.participating', 'Participating')}
                  </span>
                  <button 
                    className="leave-challenge-btn"
                    onClick={() => handleLeaveChallenge(challenge.id)}
                  >
                    {t('community.leave', 'Leave')}
                  </button>
                </div>
              ) : (
                <button 
                  className="join-challenge-btn"
                  onClick={() => handleJoinChallenge(challenge.id)}
                  disabled={challenge.maxParticipants && challenge.participants >= challenge.maxParticipants}
                >
                  {t('community.joinChallenge', 'Join Challenge')}
                </button>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderAchievements = () => (
    <div className="community-achievements">
      <div className="achievements-header">
        <h3>{t('community.communityAchievements', 'Community Achievements')}</h3>
        <button className="share-achievement-btn">
          {t('community.shareYourAchievements', 'Share Your Achievements')}
        </button>
      </div>

      <div className="recent-achievements">
        <h4>{t('community.recentAchievements', 'Recent Community Achievements')}</h4>
        <div className="achievements-feed">
          {communityAchievements?.recent.map((achievement, index) => (
            <div key={index} className="achievement-feed-item">
              <div className="achievement-avatar">
                <img src={achievement.userAvatar} alt={achievement.username} />
              </div>
              <div className="achievement-content">
                <div className="achievement-text">
                  <strong>{achievement.username}</strong> {t('community.unlocked', 'unlocked')} 
                  <span className="achievement-name">{achievement.achievementName}</span>
                </div>
                <div className="achievement-meta">
                  <span className="achievement-time">{achievement.timeAgo}</span>
                  <span className="achievement-xp">+{achievement.xpReward} XP</span>
                </div>
              </div>
              <div className="achievement-icon">
                {achievement.achievementIcon}
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="achievement-leaderboard">
        <h4>{t('community.topAchievers', 'Top Achievers This Month')}</h4>
        <div className="achievers-list">
          {communityAchievements?.topAchievers.map((achiever, index) => (
            <div key={index} className="achiever-item">
              <div className="achiever-rank">#{index + 1}</div>
              <div className="achiever-avatar">
                <img src={achiever.avatar} alt={achiever.username} />
              </div>
              <div className="achiever-info">
                <div className="achiever-name">{achiever.username}</div>
                <div className="achiever-stats">
                  {achiever.achievementsThisMonth} {t('community.achievements', 'achievements')} • 
                  {achiever.totalXPFromAchievements.toLocaleString()} XP
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderDiscussions = () => (
    <div className="community-discussions">
      <div className="discussions-header">
        <h3>{t('community.discussions', 'Community Discussions')}</h3>
        <button className="new-discussion-btn">
          {t('community.startDiscussion', 'Start New Discussion')}
        </button>
      </div>

      <div className="discussion-categories">
        {[
          { key: 'general', label: 'General', icon: '💬' },
          { key: 'swiss-tips', label: 'Swiss Tips', icon: '🇨🇭' },
          { key: 'goal-strategies', label: 'Goal Strategies', icon: '🎯' },
          { key: 'success-stories', label: 'Success Stories', icon: '🎉' },
        ].map(category => (
          <button key={category.key} className="category-btn">
            <span className="category-icon">{category.icon}</span>
            <span className="category-label">{category.label}</span>
          </button>
        ))}
      </div>

      <div className="discussions-list">
        {discussions?.recent.map((discussion, index) => (
          <div key={index} className="discussion-item">
            <div className="discussion-avatar">
              <img src={discussion.authorAvatar} alt={discussion.author} />
            </div>
            <div className="discussion-content">
              <h4 className="discussion-title">{discussion.title}</h4>
              <div className="discussion-meta">
                <span className="discussion-author">{discussion.author}</span>
                <span className="discussion-time">{discussion.timeAgo}</span>
                <span className="discussion-category">{discussion.category}</span>
              </div>
              <p className="discussion-preview">{discussion.preview}</p>
              <div className="discussion-stats">
                <span className="replies-count">{discussion.replies} replies</span>
                <span className="likes-count">{discussion.likes} likes</span>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderMentorship = () => (
    <div className="community-mentorship">
      <div className="mentorship-header">
        <h3>{t('community.mentorshipProgram', 'Mentorship Program')}</h3>
        <div className="mentorship-actions">
          <button className="find-mentor-btn">
            {t('community.findMentor', 'Find a Mentor')}
          </button>
          <button className="become-mentor-btn">
            {t('community.becomeMentor', 'Become a Mentor')}
          </button>
        </div>
      </div>

      <div className="mentorship-benefits">
        <div className="benefit-card">
          <div className="benefit-icon">👨‍🏫</div>
          <h4>{t('community.expertGuidance', 'Expert Guidance')}</h4>
          <p>{t('community.expertGuidanceDesc', 'Get personalized advice from experienced Swiss financial planners')}</p>
        </div>
        <div className="benefit-card">
          <div className="benefit-icon">🎯</div>
          <h4>{t('community.goalAcceleration', 'Goal Acceleration')}</h4>
          <p>{t('community.goalAccelerationDesc', 'Achieve your financial goals 40% faster with mentor support')}</p>
        </div>
        <div className="benefit-card">
          <div className="benefit-icon">🇨🇭</div>
          <h4>{t('community.swissExpertise', 'Swiss Expertise')}</h4>
          <p>{t('community.swissExpertiseDesc', 'Learn Swiss-specific optimization strategies from locals')}</p>
        </div>
      </div>

      <div className="featured-mentors">
        <h4>{t('community.featuredMentors', 'Featured Mentors')}</h4>
        <div className="mentors-grid">
          {mentorshipProgram?.featuredMentors.map((mentor, index) => (
            <div key={index} className="mentor-card">
              <div className="mentor-avatar">
                <img src={mentor.avatar} alt={mentor.name} />
                <div className="mentor-level">{mentor.level}</div>
              </div>
              <div className="mentor-info">
                <h5 className="mentor-name">{mentor.name}</h5>
                <div className="mentor-specialties">
                  {mentor.specialties.map((specialty, specIndex) => (
                    <span key={specIndex} className="specialty-tag">{specialty}</span>
                  ))}
                </div>
                <div className="mentor-stats">
                  <span className="mentees-count">{mentor.mentees} mentees</span>
                  <span className="success-rate">{mentor.successRate}% success rate</span>
                </div>
                <div className="mentor-rating">
                  {'⭐'.repeat(Math.floor(mentor.rating))} {mentor.rating}/5
                </div>
              </div>
              <button className="connect-mentor-btn">
                {t('community.connect', 'Connect')}
              </button>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  if (isLoading) {
    return (
      <div className={`community-hub loading ${className}`}>
        <div className="loading-spinner" />
        <p>{t('community.loadingCommunity', 'Loading community features...')}</p>
      </div>
    );
  }

  return (
    <div className={`community-hub ${className}`}>
      <div className="community-header">
        <h2 className="community-title">
          👥 {t('community.title', 'Community Hub')}
        </h2>
        <div className="community-navigation">
          {[
            { key: 'leaderboards', label: 'Leaderboards', icon: '🏆' },
            { key: 'challenges', label: 'Challenges', icon: '⚔️' },
            { key: 'achievements', label: 'Achievements', icon: '🎖️' },
            { key: 'discussions', label: 'Discussions', icon: '💬' },
            { key: 'mentorship', label: 'Mentorship', icon: '👨‍🏫' },
          ].map(view => (
            <button
              key={view.key}
              className={`nav-btn ${activeView === view.key ? 'active' : ''}`}
              onClick={() => setActiveView(view.key as CommunityView)}
            >
              <span className="nav-icon">{view.icon}</span>
              <span className="nav-label">{view.label}</span>
            </button>
          ))}
        </div>
      </div>

      <div className="community-content">
        {activeView === 'leaderboards' && renderLeaderboards()}
        {activeView === 'challenges' && renderChallenges()}
        {activeView === 'achievements' && renderAchievements()}
        {activeView === 'discussions' && renderDiscussions()}
        {activeView === 'mentorship' && renderMentorship()}
      </div>
    </div>
  );
};

export default CommunityHub;
