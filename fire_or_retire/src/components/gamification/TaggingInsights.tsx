import React, { useState, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocalStorage } from '../../hooks/useLocalStorage';
import { TaggedTransaction, TransactionTag, TagCategory } from '../../types/gamification';
import SmartTaggingService, { Transaction } from '../../services/SmartTaggingService';

interface TaggingInsightsProps {
  userId: string;
  darkMode: boolean;
  className?: string;
}

interface TaggingStats {
  totalTransactions: number;
  taggedTransactions: number;
  autoTaggedTransactions: number;
  totalXPFromTags: number;
  categoryBreakdown: Record<TagCategory, number>;
  topTags: { tag: TransactionTag; count: number; totalXP: number }[];
  goalAlignmentAverage: number;
  swissOptimizationCount: number;
}

const TaggingInsights: React.FC<TaggingInsightsProps> = ({
  userId,
  darkMode,
  className = '',
}) => {
  const { t } = useTranslation();
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'quarter' | 'year'>('month');
  const [selectedCategory, setSelectedCategory] = useState<TagCategory | 'all'>('all');

  // Mock data - in real implementation, this would come from API
  const [transactions] = useLocalStorage<Transaction[]>(`transactions_${userId}`, []);
  const [taggedTransactions] = useLocalStorage<TaggedTransaction[]>(`tagged_transactions_${userId}`, []);

  const taggingService = useMemo(() => new SmartTaggingService(), []);
  const allTags = useMemo(() => taggingService.getAllTags(), [taggingService]);

  // Calculate statistics
  const stats = useMemo((): TaggingStats => {
    const now = new Date();
    const periodStart = new Date();
    
    switch (selectedPeriod) {
      case 'week':
        periodStart.setDate(now.getDate() - 7);
        break;
      case 'month':
        periodStart.setMonth(now.getMonth() - 1);
        break;
      case 'quarter':
        periodStart.setMonth(now.getMonth() - 3);
        break;
      case 'year':
        periodStart.setFullYear(now.getFullYear() - 1);
        break;
    }

    const periodTransactions = transactions.filter(t => new Date(t.date) >= periodStart);
    const periodTaggedTransactions = taggedTransactions.filter(tt => {
      const transaction = transactions.find(t => t.id === tt.transactionId);
      return transaction && new Date(transaction.date) >= periodStart;
    });

    const categoryBreakdown: Record<TagCategory, number> = {
      goal_booster: 0,
      goal_blocker: 0,
      smart_choice: 0,
      swiss_saver: 0,
      investment: 0,
      reward: 0,
      neutral: 0,
    };

    const tagCounts = new Map<string, { count: number; totalXP: number }>();
    let totalXP = 0;
    let totalGoalAlignment = 0;
    let swissOptimizationCount = 0;

    periodTaggedTransactions.forEach(tt => {
      totalXP += tt.xpAwarded;
      totalGoalAlignment += tt.goalAlignment;

      tt.tags.forEach(tagId => {
        const tag = allTags.find(t => t.id === tagId);
        if (tag) {
          categoryBreakdown[tag.category]++;
          
          if (tag.isSwissSpecific) {
            swissOptimizationCount++;
          }

          const current = tagCounts.get(tagId) || { count: 0, totalXP: 0 };
          tagCounts.set(tagId, {
            count: current.count + 1,
            totalXP: current.totalXP + tag.xpModifier,
          });
        }
      });
    });

    const topTags = Array.from(tagCounts.entries())
      .map(([tagId, data]) => ({
        tag: allTags.find(t => t.id === tagId)!,
        count: data.count,
        totalXP: data.totalXP,
      }))
      .filter(item => item.tag)
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    return {
      totalTransactions: periodTransactions.length,
      taggedTransactions: periodTaggedTransactions.length,
      autoTaggedTransactions: periodTaggedTransactions.filter(tt => tt.autoTagged).length,
      totalXPFromTags: totalXP,
      categoryBreakdown,
      topTags,
      goalAlignmentAverage: periodTaggedTransactions.length > 0 
        ? totalGoalAlignment / periodTaggedTransactions.length 
        : 0,
      swissOptimizationCount,
    };
  }, [transactions, taggedTransactions, selectedPeriod, allTags]);

  // Get category info
  const getCategoryInfo = useCallback((category: TagCategory) => {
    const categoryMap = {
      goal_booster: { name: 'Goal Booster', emoji: '🎯', color: 'text-green-600', bg: 'bg-green-100 dark:bg-green-900/20' },
      goal_blocker: { name: 'Goal Blocker', emoji: '⚠️', color: 'text-red-600', bg: 'bg-red-100 dark:bg-red-900/20' },
      smart_choice: { name: 'Smart Choice', emoji: '💡', color: 'text-blue-600', bg: 'bg-blue-100 dark:bg-blue-900/20' },
      swiss_saver: { name: 'Swiss Saver', emoji: '🇨🇭', color: 'text-red-600', bg: 'bg-red-100 dark:bg-red-900/20' },
      investment: { name: 'Investment', emoji: '📈', color: 'text-purple-600', bg: 'bg-purple-100 dark:bg-purple-900/20' },
      reward: { name: 'Reward', emoji: '🎉', color: 'text-yellow-600', bg: 'bg-yellow-100 dark:bg-yellow-900/20' },
      neutral: { name: 'Neutral', emoji: '⚪', color: 'text-gray-600', bg: 'bg-gray-100 dark:bg-gray-900/20' },
    };
    return categoryMap[category];
  }, []);

  // Get alignment color
  const getAlignmentColor = useCallback((score: number) => {
    if (score > 20) return 'text-green-600';
    if (score > 0) return 'text-blue-600';
    if (score > -20) return 'text-yellow-600';
    return 'text-red-600';
  }, []);

  return (
    <div className={`tagging-insights ${className}`}>
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold">
            🏷️ {t('tagging.insights.title', 'Tagging Insights')}
          </h2>
          
          {/* Period Selector */}
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value as any)}
            className={`px-3 py-2 rounded-lg border ${
              darkMode
                ? 'bg-gray-700 border-gray-600 text-white'
                : 'bg-white border-gray-300 text-gray-900'
            }`}
          >
            <option value="week">{t('period.week', 'Last Week')}</option>
            <option value="month">{t('period.month', 'Last Month')}</option>
            <option value="quarter">{t('period.quarter', 'Last Quarter')}</option>
            <option value="year">{t('period.year', 'Last Year')}</option>
          </select>
        </div>

        {/* Key Statistics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}>
            <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              {t('tagging.stats.tagged', 'Tagged Transactions')}
            </div>
            <div className="text-2xl font-bold text-blue-600">
              {stats.taggedTransactions}
            </div>
            <div className={`text-xs ${darkMode ? 'text-gray-500' : 'text-gray-400'}`}>
              of {stats.totalTransactions} total
            </div>
          </div>

          <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}>
            <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              {t('tagging.stats.xpEarned', 'XP from Tags')}
            </div>
            <div className="text-2xl font-bold text-green-600">
              {stats.totalXPFromTags}
            </div>
          </div>

          <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}>
            <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              {t('tagging.stats.goalAlignment', 'Goal Alignment')}
            </div>
            <div className={`text-2xl font-bold ${getAlignmentColor(stats.goalAlignmentAverage)}`}>
              {stats.goalAlignmentAverage > 0 ? '+' : ''}{stats.goalAlignmentAverage.toFixed(0)}
            </div>
          </div>

          <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}>
            <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              {t('tagging.stats.swissOptimizations', 'Swiss Optimizations')}
            </div>
            <div className="text-2xl font-bold text-red-600">
              {stats.swissOptimizationCount}
            </div>
          </div>
        </div>
      </div>

      {/* Category Breakdown */}
      <div className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'} mb-6`}>
        <h3 className="text-lg font-semibold mb-4">
          📊 {t('tagging.categoryBreakdown', 'Category Breakdown')}
        </h3>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {Object.entries(stats.categoryBreakdown).map(([category, count]) => {
            const categoryInfo = getCategoryInfo(category as TagCategory);
            const percentage = stats.taggedTransactions > 0 
              ? (count / stats.taggedTransactions) * 100 
              : 0;
            
            return (
              <div key={category} className={`p-3 rounded-lg ${categoryInfo.bg}`}>
                <div className="flex items-center justify-between mb-2">
                  <span className={`text-sm font-medium ${categoryInfo.color}`}>
                    {categoryInfo.emoji} {t(`tagging.category.${category}`, categoryInfo.name)}
                  </span>
                  <span className="text-lg font-bold">{count}</span>
                </div>
                <div className={`w-full bg-gray-300 rounded-full h-2 ${darkMode ? 'bg-gray-600' : ''}`}>
                  <div
                    className={`h-2 rounded-full ${
                      category === 'goal_booster' ? 'bg-green-500' :
                      category === 'goal_blocker' ? 'bg-red-500' :
                      category === 'smart_choice' ? 'bg-blue-500' :
                      category === 'swiss_saver' ? 'bg-red-500' :
                      category === 'investment' ? 'bg-purple-500' :
                      category === 'reward' ? 'bg-yellow-500' :
                      'bg-gray-500'
                    }`}
                    style={{ width: `${percentage}%` }}
                  />
                </div>
                <div className={`text-xs mt-1 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  {percentage.toFixed(1)}%
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Top Tags */}
      <div className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'} mb-6`}>
        <h3 className="text-lg font-semibold mb-4">
          🏆 {t('tagging.topTags', 'Most Used Tags')}
        </h3>
        
        {stats.topTags.length > 0 ? (
          <div className="space-y-3">
            {stats.topTags.map((item, index) => (
              <div key={item.tag.id} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                    index === 0 ? 'bg-yellow-500 text-white' :
                    index === 1 ? 'bg-gray-400 text-white' :
                    index === 2 ? 'bg-orange-500 text-white' :
                    darkMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-200 text-gray-700'
                  }`}>
                    {index + 1}
                  </div>
                  <div>
                    <div className="flex items-center space-x-2">
                      <span>{item.tag.icon}</span>
                      <span className="font-medium">{item.tag.name}</span>
                      {item.tag.isSwissSpecific && <span>🇨🇭</span>}
                    </div>
                    <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      {t(`tagging.category.${item.tag.category}`, item.tag.category)}
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-bold">{item.count} uses</div>
                  <div className={`text-sm ${
                    item.totalXP > 0 ? 'text-green-600' : 
                    item.totalXP < 0 ? 'text-red-600' : 
                    'text-gray-600'
                  }`}>
                    {item.totalXP > 0 ? '+' : ''}{item.totalXP} XP
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className={`text-center py-8 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            <div className="text-4xl mb-2">🏷️</div>
            <p>{t('tagging.noTags', 'No tags used in this period')}</p>
            <p className="text-sm mt-1">
              {t('tagging.startTagging', 'Start tagging your transactions to see insights')}
            </p>
          </div>
        )}
      </div>

      {/* Recommendations */}
      <div className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}>
        <h3 className="text-lg font-semibold mb-4">
          💡 {t('tagging.recommendations', 'Recommendations')}
        </h3>
        
        <div className="space-y-3">
          {stats.goalAlignmentAverage < -10 && (
            <div className={`p-3 rounded-lg ${darkMode ? 'bg-red-900/20' : 'bg-red-50'} border-l-4 border-red-500`}>
              <div className="font-medium text-red-600">
                {t('tagging.recommendation.goalAlignment', 'Improve Goal Alignment')}
              </div>
              <div className={`text-sm ${darkMode ? 'text-red-400' : 'text-red-700'}`}>
                {t('tagging.recommendation.goalAlignmentDesc', 'Your spending is working against your goals. Consider reviewing your expenses.')}
              </div>
            </div>
          )}
          
          {stats.swissOptimizationCount < 5 && (
            <div className={`p-3 rounded-lg ${darkMode ? 'bg-blue-900/20' : 'bg-blue-50'} border-l-4 border-blue-500`}>
              <div className="font-medium text-blue-600">
                {t('tagging.recommendation.swissOptimization', 'Explore Swiss Optimizations')}
              </div>
              <div className={`text-sm ${darkMode ? 'text-blue-400' : 'text-blue-700'}`}>
                {t('tagging.recommendation.swissOptimizationDesc', 'Take advantage of Swiss-specific financial optimizations like Pillar 3a.')}
              </div>
            </div>
          )}
          
          {stats.taggedTransactions / stats.totalTransactions < 0.5 && (
            <div className={`p-3 rounded-lg ${darkMode ? 'bg-yellow-900/20' : 'bg-yellow-50'} border-l-4 border-yellow-500`}>
              <div className="font-medium text-yellow-600">
                {t('tagging.recommendation.moreTagging', 'Tag More Transactions')}
              </div>
              <div className={`text-sm ${darkMode ? 'text-yellow-400' : 'text-yellow-700'}`}>
                {t('tagging.recommendation.moreTaggingDesc', 'Tag more transactions to get better insights and earn more XP.')}
              </div>
            </div>
          )}
          
          {stats.categoryBreakdown.smart_choice > stats.categoryBreakdown.goal_blocker && (
            <div className={`p-3 rounded-lg ${darkMode ? 'bg-green-900/20' : 'bg-green-50'} border-l-4 border-green-500`}>
              <div className="font-medium text-green-600">
                {t('tagging.recommendation.goodChoices', 'Great Financial Choices!')}
              </div>
              <div className={`text-sm ${darkMode ? 'text-green-400' : 'text-green-700'}`}>
                {t('tagging.recommendation.goodChoicesDesc', 'You\'re making more smart choices than goal-blocking decisions. Keep it up!')}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TaggingInsights;
