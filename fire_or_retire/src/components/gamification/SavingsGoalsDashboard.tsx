import React, { useState, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocalStorage } from '../../hooks/useLocalStorage';
import { SavingsGoal, SavingsGoalCategory, SavingsGoalPriority } from '../../types/gamification';
import SavingsGoalCard from './SavingsGoalCard';
import SavingsGoalWizard from './SavingsGoalWizard';

interface SavingsGoalsDashboardProps {
  userId: string;
  darkMode: boolean;
  className?: string;
}

type SortOption = 'priority' | 'progress' | 'deadline' | 'amount' | 'created';
type FilterOption = 'all' | 'active' | 'completed' | 'overdue' | 'swiss';

const SavingsGoalsDashboard: React.FC<SavingsGoalsDashboardProps> = ({
  userId,
  darkMode,
  className = '',
}) => {
  const { t } = useTranslation();
  const [goals, setGoals] = useLocalStorage<SavingsGoal[]>(`savings_goals_${userId}`, []);
  const [showWizard, setShowWizard] = useState(false);
  const [editingGoal, setEditingGoal] = useState<SavingsGoal | undefined>();
  const [sortBy, setSortBy] = useState<SortOption>('priority');
  const [filterBy, setFilterBy] = useState<FilterOption>('all');
  const [searchQuery, setSearchQuery] = useState('');

  // Generate unique ID for new goals
  const generateGoalId = useCallback(() => {
    return `goal_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }, []);

  // Filter and sort goals
  const filteredAndSortedGoals = useMemo(() => {
    let filtered = goals.filter(goal => {
      // Search filter
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        if (!goal.name.toLowerCase().includes(query) && 
            !goal.description.toLowerCase().includes(query)) {
          return false;
        }
      }

      // Category filter
      switch (filterBy) {
        case 'active':
          return goal.status === 'active';
        case 'completed':
          return goal.status === 'completed';
        case 'overdue':
          return goal.status === 'active' && new Date(goal.targetDate) < new Date();
        case 'swiss':
          return goal.swissSpecific;
        default:
          return true;
      }
    });

    // Sort goals
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'priority':
          const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
          return priorityOrder[b.priority] - priorityOrder[a.priority];
        
        case 'progress':
          const progressA = a.targetAmount > 0 ? (a.currentAmount / a.targetAmount) * 100 : 0;
          const progressB = b.targetAmount > 0 ? (b.currentAmount / b.targetAmount) * 100 : 0;
          return progressB - progressA;
        
        case 'deadline':
          return new Date(a.targetDate).getTime() - new Date(b.targetDate).getTime();
        
        case 'amount':
          return b.targetAmount - a.targetAmount;
        
        case 'created':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        
        default:
          return 0;
      }
    });

    return filtered;
  }, [goals, sortBy, filterBy, searchQuery]);

  // Calculate dashboard statistics
  const dashboardStats = useMemo(() => {
    const activeGoals = goals.filter(g => g.status === 'active');
    const completedGoals = goals.filter(g => g.status === 'completed');
    const totalTargetAmount = activeGoals.reduce((sum, g) => sum + g.targetAmount, 0);
    const totalCurrentAmount = activeGoals.reduce((sum, g) => sum + g.currentAmount, 0);
    const overallProgress = totalTargetAmount > 0 ? (totalCurrentAmount / totalTargetAmount) * 100 : 0;
    const overdueGoals = activeGoals.filter(g => new Date(g.targetDate) < new Date());

    return {
      totalGoals: goals.length,
      activeGoals: activeGoals.length,
      completedGoals: completedGoals.length,
      overdueGoals: overdueGoals.length,
      totalTargetAmount,
      totalCurrentAmount,
      overallProgress,
    };
  }, [goals]);

  // Handle goal creation/update
  const handleSaveGoal = useCallback((goalData: Omit<SavingsGoal, 'id' | 'createdAt' | 'updatedAt'>) => {
    if (editingGoal) {
      // Update existing goal
      setGoals(prev => prev.map(goal => 
        goal.id === editingGoal.id 
          ? { ...goalData, id: editingGoal.id, createdAt: editingGoal.createdAt, updatedAt: new Date() }
          : goal
      ));
    } else {
      // Create new goal
      const newGoal: SavingsGoal = {
        ...goalData,
        id: generateGoalId(),
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      setGoals(prev => [...prev, newGoal]);
    }
    
    setShowWizard(false);
    setEditingGoal(undefined);
  }, [editingGoal, setGoals, generateGoalId]);

  // Handle goal deletion
  const handleDeleteGoal = useCallback((goalId: string) => {
    if (window.confirm(t('goals.confirmDelete', 'Are you sure you want to delete this goal?'))) {
      setGoals(prev => prev.filter(goal => goal.id !== goalId));
    }
  }, [setGoals, t]);

  // Handle goal contribution
  const handleContribute = useCallback((goalId: string, amount: number) => {
    setGoals(prev => prev.map(goal => {
      if (goal.id === goalId) {
        const newAmount = goal.currentAmount + amount;
        const isCompleted = newAmount >= goal.targetAmount;
        
        // Update milestones
        const updatedMilestones = goal.milestones.map(milestone => ({
          ...milestone,
          isReached: newAmount >= milestone.amount,
          reachedAt: newAmount >= milestone.amount && !milestone.isReached ? new Date() : milestone.reachedAt,
        }));

        return {
          ...goal,
          currentAmount: newAmount,
          status: isCompleted ? 'completed' : goal.status,
          completedAt: isCompleted && !goal.completedAt ? new Date() : goal.completedAt,
          milestones: updatedMilestones,
          updatedAt: new Date(),
        };
      }
      return goal;
    }));
  }, [setGoals]);

  // Handle edit goal
  const handleEditGoal = useCallback((goal: SavingsGoal) => {
    setEditingGoal(goal);
    setShowWizard(true);
  }, []);

  return (
    <div className={`savings-goals-dashboard ${className}`}>
      {/* Wizard Modal */}
      {showWizard && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <SavingsGoalWizard
              userId={userId}
              darkMode={darkMode}
              onSave={handleSaveGoal}
              onCancel={() => {
                setShowWizard(false);
                setEditingGoal(undefined);
              }}
              existingGoal={editingGoal}
            />
          </div>
        </div>
      )}

      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold">
            🎯 {t('goals.dashboard.title', 'Savings Goals')}
          </h2>
          <button
            onClick={() => setShowWizard(true)}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
          >
            ➕ {t('goals.createNew', 'Create Goal')}
          </button>
        </div>

        {/* Dashboard Statistics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}>
            <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              {t('goals.stats.total', 'Total Goals')}
            </div>
            <div className="text-2xl font-bold text-blue-600">
              {dashboardStats.totalGoals}
            </div>
          </div>
          
          <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}>
            <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              {t('goals.stats.active', 'Active Goals')}
            </div>
            <div className="text-2xl font-bold text-green-600">
              {dashboardStats.activeGoals}
            </div>
          </div>
          
          <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}>
            <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              {t('goals.stats.completed', 'Completed')}
            </div>
            <div className="text-2xl font-bold text-purple-600">
              {dashboardStats.completedGoals}
            </div>
          </div>
          
          <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}>
            <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              {t('goals.stats.progress', 'Overall Progress')}
            </div>
            <div className="text-2xl font-bold text-orange-600">
              {dashboardStats.overallProgress.toFixed(1)}%
            </div>
          </div>
        </div>

        {/* Overall Progress Bar */}
        {dashboardStats.activeGoals > 0 && (
          <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'} mb-6`}>
            <div className="flex justify-between items-center mb-2">
              <span className="font-medium">{t('goals.overallProgress', 'Overall Progress')}</span>
              <span className="text-sm">
                CHF {dashboardStats.totalCurrentAmount.toLocaleString()} / CHF {dashboardStats.totalTargetAmount.toLocaleString()}
              </span>
            </div>
            <div className={`w-full bg-gray-300 rounded-full h-3 ${darkMode ? 'bg-gray-600' : ''}`}>
              <div
                className="bg-gradient-to-r from-green-500 to-blue-600 h-3 rounded-full transition-all duration-1000"
                style={{ width: `${Math.min(100, dashboardStats.overallProgress)}%` }}
              />
            </div>
          </div>
        )}
      </div>

      {/* Filters and Search */}
      <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'} mb-6`}>
        <div className="flex flex-col md:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder={t('goals.search', 'Search goals...')}
              className={`w-full px-3 py-2 rounded-lg border ${
                darkMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              }`}
            />
          </div>

          {/* Filter */}
          <div>
            <select
              value={filterBy}
              onChange={(e) => setFilterBy(e.target.value as FilterOption)}
              className={`px-3 py-2 rounded-lg border ${
                darkMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              }`}
            >
              <option value="all">{t('goals.filter.all', 'All Goals')}</option>
              <option value="active">{t('goals.filter.active', 'Active')}</option>
              <option value="completed">{t('goals.filter.completed', 'Completed')}</option>
              <option value="overdue">{t('goals.filter.overdue', 'Overdue')}</option>
              <option value="swiss">{t('goals.filter.swiss', 'Swiss Specific')}</option>
            </select>
          </div>

          {/* Sort */}
          <div>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as SortOption)}
              className={`px-3 py-2 rounded-lg border ${
                darkMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              }`}
            >
              <option value="priority">{t('goals.sort.priority', 'Priority')}</option>
              <option value="progress">{t('goals.sort.progress', 'Progress')}</option>
              <option value="deadline">{t('goals.sort.deadline', 'Deadline')}</option>
              <option value="amount">{t('goals.sort.amount', 'Amount')}</option>
              <option value="created">{t('goals.sort.created', 'Created')}</option>
            </select>
          </div>
        </div>
      </div>

      {/* Goals Grid */}
      {filteredAndSortedGoals.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredAndSortedGoals.map((goal) => (
            <SavingsGoalCard
              key={goal.id}
              goal={goal}
              darkMode={darkMode}
              onEdit={handleEditGoal}
              onDelete={handleDeleteGoal}
              onContribute={handleContribute}
            />
          ))}
        </div>
      ) : (
        <div className={`text-center py-12 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
          <div className="text-6xl mb-4">🎯</div>
          <h3 className="text-xl font-semibold mb-2">
            {searchQuery || filterBy !== 'all' 
              ? t('goals.noResults', 'No goals found')
              : t('goals.empty.title', 'No Goals Yet')
            }
          </h3>
          <p className="mb-4">
            {searchQuery || filterBy !== 'all'
              ? t('goals.noResults.description', 'Try adjusting your search or filters')
              : t('goals.empty.description', 'Create your first savings goal to start your financial journey')
            }
          </p>
          {!searchQuery && filterBy === 'all' && (
            <button
              onClick={() => setShowWizard(true)}
              className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
            >
              🎯 {t('goals.createFirst', 'Create Your First Goal')}
            </button>
          )}
        </div>
      )}
    </div>
  );
};

export default SavingsGoalsDashboard;
