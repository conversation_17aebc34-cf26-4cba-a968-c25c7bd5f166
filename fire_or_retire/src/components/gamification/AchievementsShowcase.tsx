import React, { useState, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Achievement, UserAchievement, AchievementCategory, AchievementRarity } from '../../types/gamification';
import AchievementCard from './AchievementCard';
import AchievementService from '../../services/AchievementService';

interface AchievementsShowcaseProps {
  userId: string;
  darkMode: boolean;
  className?: string;
}

type FilterType = 'all' | 'unlocked' | 'locked' | 'swiss' | AchievementCategory | AchievementRarity;

const AchievementsShowcase: React.FC<AchievementsShowcaseProps> = ({
  userId,
  darkMode,
  className = '',
}) => {
  const { t } = useTranslation();
  const [selectedFilter, setSelectedFilter] = useState<FilterType>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedAchievement, setSelectedAchievement] = useState<Achievement | null>(null);

  const achievementService = useMemo(() => new AchievementService(), []);

  // Initialize user achievements
  React.useEffect(() => {
    achievementService.initializeUserAchievements(userId);
  }, [userId, achievementService]);

  // Get all achievements and user progress
  const allAchievements = useMemo(() => achievementService.getAllAchievements(), [achievementService]);
  const userAchievements = useMemo(() => achievementService.getUserAchievements(userId), [achievementService, userId]);
  const achievementStats = useMemo(() => achievementService.getAchievementStatistics(userId), [achievementService, userId]);

  // Create achievement map for quick lookup
  const userAchievementMap = useMemo(() => {
    const map = new Map<string, UserAchievement>();
    userAchievements.forEach(ua => map.set(ua.achievementId, ua));
    return map;
  }, [userAchievements]);

  // Filter and search achievements
  const filteredAchievements = useMemo(() => {
    let filtered = allAchievements;

    // Apply category/type filter
    switch (selectedFilter) {
      case 'unlocked':
        filtered = filtered.filter(a => userAchievementMap.get(a.id)?.isCompleted);
        break;
      case 'locked':
        filtered = filtered.filter(a => !userAchievementMap.get(a.id)?.isCompleted);
        break;
      case 'swiss':
        filtered = filtered.filter(a => a.isSwissSpecific);
        break;
      case 'common':
      case 'rare':
      case 'epic':
      case 'legendary':
        filtered = filtered.filter(a => a.rarity === selectedFilter);
        break;
      case 'saver':
      case 'streak':
      case 'goal':
      case 'swiss':
      case 'efficiency':
      case 'learning':
      case 'social':
      case 'discovery':
        filtered = filtered.filter(a => a.category === selectedFilter);
        break;
      default:
        // 'all' - no additional filtering
        break;
    }

    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(a => 
        !a.isSecret && (
          a.name.toLowerCase().includes(query) ||
          a.description.toLowerCase().includes(query)
        )
      );
    }

    return filtered;
  }, [allAchievements, selectedFilter, searchQuery, userAchievementMap]);

  // Handle achievement click
  const handleAchievementClick = useCallback((achievement: Achievement) => {
    setSelectedAchievement(achievement);
  }, []);

  // Close achievement modal
  const closeAchievementModal = useCallback(() => {
    setSelectedAchievement(null);
  }, []);

  // Get filter options
  const filterOptions = useMemo(() => [
    { value: 'all', label: t('achievements.filter.all', 'All Achievements'), count: allAchievements.length },
    { value: 'unlocked', label: t('achievements.filter.unlocked', 'Unlocked'), count: achievementStats.unlockedAchievements },
    { value: 'locked', label: t('achievements.filter.locked', 'Locked'), count: allAchievements.length - achievementStats.unlockedAchievements },
    { value: 'swiss', label: t('achievements.filter.swiss', 'Swiss Specific'), count: achievementStats.swissAchievements },
    
    // Categories
    { value: 'saver', label: t('achievements.category.saver', 'Saver'), count: achievementStats.categoryBreakdown.saver },
    { value: 'streak', label: t('achievements.category.streak', 'Streak'), count: achievementStats.categoryBreakdown.streak },
    { value: 'goal', label: t('achievements.category.goal', 'Goal'), count: achievementStats.categoryBreakdown.goal },
    { value: 'learning', label: t('achievements.category.learning', 'Learning'), count: achievementStats.categoryBreakdown.learning },
    { value: 'social', label: t('achievements.category.social', 'Social'), count: achievementStats.categoryBreakdown.social },
    { value: 'discovery', label: t('achievements.category.discovery', 'Discovery'), count: achievementStats.categoryBreakdown.discovery },
    
    // Rarities
    { value: 'common', label: t('achievements.rarity.common', 'Common'), count: achievementStats.rarityBreakdown.common },
    { value: 'rare', label: t('achievements.rarity.rare', 'Rare'), count: achievementStats.rarityBreakdown.rare },
    { value: 'epic', label: t('achievements.rarity.epic', 'Epic'), count: achievementStats.rarityBreakdown.epic },
    { value: 'legendary', label: t('achievements.rarity.legendary', 'Legendary'), count: achievementStats.rarityBreakdown.legendary },
  ], [t, allAchievements.length, achievementStats]);

  return (
    <div className={`achievements-showcase ${className}`}>
      {/* Achievement Detail Modal */}
      {selectedAchievement && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className={`max-w-md w-full rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white'}`}>
            <div className="p-6">
              <div className="flex justify-between items-start mb-4">
                <h2 className="text-xl font-bold">
                  {t('achievements.details', 'Achievement Details')}
                </h2>
                <button
                  onClick={closeAchievementModal}
                  className={`text-2xl ${darkMode ? 'text-gray-400 hover:text-white' : 'text-gray-600 hover:text-gray-900'}`}
                >
                  ×
                </button>
              </div>
              
              <AchievementCard
                achievement={selectedAchievement}
                userAchievement={userAchievementMap.get(selectedAchievement.id)}
                darkMode={darkMode}
                size="large"
                showProgress={true}
              />
              
              {/* Additional details */}
              <div className="mt-4 space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>{t('achievements.category', 'Category')}:</span>
                  <span className="capitalize">{selectedAchievement.category}</span>
                </div>
                <div className="flex justify-between">
                  <span>{t('achievements.rarity', 'Rarity')}:</span>
                  <span className="capitalize">{selectedAchievement.rarity}</span>
                </div>
                <div className="flex justify-between">
                  <span>{t('achievements.xpReward', 'XP Reward')}:</span>
                  <span>+{selectedAchievement.xpReward} XP</span>
                </div>
                {selectedAchievement.isSwissSpecific && (
                  <div className="flex justify-between">
                    <span>{t('achievements.swissSpecific', 'Swiss Specific')}:</span>
                    <span>🇨🇭 Yes</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Header */}
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-4">
          🏆 {t('achievements.title', 'Achievements')}
        </h2>

        {/* Statistics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}>
            <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              {t('achievements.stats.unlocked', 'Unlocked')}
            </div>
            <div className="text-2xl font-bold text-green-600">
              {achievementStats.unlockedAchievements}
            </div>
            <div className={`text-xs ${darkMode ? 'text-gray-500' : 'text-gray-400'}`}>
              of {achievementStats.totalAchievements}
            </div>
          </div>

          <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}>
            <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              {t('achievements.stats.completion', 'Completion')}
            </div>
            <div className="text-2xl font-bold text-blue-600">
              {achievementStats.completionPercentage.toFixed(1)}%
            </div>
          </div>

          <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}>
            <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              {t('achievements.stats.swiss', 'Swiss Achievements')}
            </div>
            <div className="text-2xl font-bold text-red-600">
              {achievementStats.swissAchievements}
            </div>
          </div>

          <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}>
            <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              {t('achievements.stats.legendary', 'Legendary')}
            </div>
            <div className="text-2xl font-bold text-yellow-600">
              {achievementStats.rarityBreakdown.legendary}
            </div>
          </div>
        </div>

        {/* Progress Bar */}
        <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'} mb-6`}>
          <div className="flex justify-between items-center mb-2">
            <span className="font-medium">{t('achievements.overallProgress', 'Overall Progress')}</span>
            <span className="text-sm">
              {achievementStats.unlockedAchievements} / {achievementStats.totalAchievements}
            </span>
          </div>
          <div className={`w-full bg-gray-300 rounded-full h-3 ${darkMode ? 'bg-gray-600' : ''}`}>
            <div
              className="bg-gradient-to-r from-green-500 to-blue-600 h-3 rounded-full transition-all duration-1000"
              style={{ width: `${achievementStats.completionPercentage}%` }}
            />
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'} mb-6`}>
        <div className="flex flex-col md:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder={t('achievements.search', 'Search achievements...')}
              className={`w-full px-3 py-2 rounded-lg border ${
                darkMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              }`}
            />
          </div>

          {/* Filter */}
          <div>
            <select
              value={selectedFilter}
              onChange={(e) => setSelectedFilter(e.target.value as FilterType)}
              className={`px-3 py-2 rounded-lg border ${
                darkMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              }`}
            >
              {filterOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label} ({option.count})
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Achievements Grid */}
      {filteredAchievements.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {filteredAchievements.map((achievement) => (
            <AchievementCard
              key={achievement.id}
              achievement={achievement}
              userAchievement={userAchievementMap.get(achievement.id)}
              darkMode={darkMode}
              size="medium"
              showProgress={true}
              onClick={handleAchievementClick}
            />
          ))}
        </div>
      ) : (
        <div className={`text-center py-12 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
          <div className="text-6xl mb-4">🏆</div>
          <h3 className="text-xl font-semibold mb-2">
            {searchQuery 
              ? t('achievements.noResults', 'No achievements found')
              : t('achievements.empty.title', 'No achievements in this category')
            }
          </h3>
          <p className="mb-4">
            {searchQuery
              ? t('achievements.noResults.description', 'Try adjusting your search terms')
              : t('achievements.empty.description', 'Try selecting a different filter')
            }
          </p>
        </div>
      )}
    </div>
  );
};

export default AchievementsShowcase;
