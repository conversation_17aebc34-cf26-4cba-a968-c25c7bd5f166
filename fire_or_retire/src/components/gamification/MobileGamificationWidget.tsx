import React, { useState, useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useGamification } from '../../hooks/useGamification';
import { useSwipeable } from 'react-swipeable';

interface MobileGamificationWidgetProps {
  userId: string;
  darkMode: boolean;
  onQuickAction?: (action: string, amount?: number) => void;
  className?: string;
}

type WidgetView = 'overview' | 'quick-actions' | 'progress' | 'achievements';

const MobileGamificationWidget: React.FC<MobileGamificationWidgetProps> = ({
  userId,
  darkMode,
  onQuickAction,
  className = '',
}) => {
  const { t } = useTranslation();
  const [currentView, setCurrentView] = useState<WidgetView>('overview');
  const [isExpanded, setIsExpanded] = useState(false);
  const [quickAmount, setQuickAmount] = useState('50');

  const {
    progress,
    isLoading,
    recentXPGain,
    awardSavingsXP,
    awardSwissOptimizationXP,
  } = useGamification(userId);

  // Swipe handlers for mobile navigation
  const swipeHandlers = useSwipeable({
    onSwipedLeft: () => {
      const views: WidgetView[] = ['overview', 'quick-actions', 'progress', 'achievements'];
      const currentIndex = views.indexOf(currentView);
      const nextIndex = (currentIndex + 1) % views.length;
      setCurrentView(views[nextIndex]);
    },
    onSwipedRight: () => {
      const views: WidgetView[] = ['overview', 'quick-actions', 'progress', 'achievements'];
      const currentIndex = views.indexOf(currentView);
      const prevIndex = currentIndex === 0 ? views.length - 1 : currentIndex - 1;
      setCurrentView(views[prevIndex]);
    },
    trackMouse: false,
    trackTouch: true,
  });

  // Quick action handlers
  const handleQuickSavings = useCallback(async () => {
    const amount = parseFloat(quickAmount);
    if (amount >= 10) {
      await awardSavingsXP(amount, false);
      onQuickAction?.('savings', amount);
      
      // Haptic feedback on mobile
      if ('vibrate' in navigator) {
        navigator.vibrate(50);
      }
    }
  }, [quickAmount, awardSavingsXP, onQuickAction]);

  const handleQuickPillar3a = useCallback(async () => {
    const amount = 588; // Monthly maximum
    await awardSavingsXP(amount, true);
    onQuickAction?.('pillar3a', amount);
    
    if ('vibrate' in navigator) {
      navigator.vibrate([50, 50, 50]);
    }
  }, [awardSavingsXP, onQuickAction]);

  const handleSwissOptimization = useCallback(async () => {
    await awardSwissOptimizationXP('tax', 100);
    onQuickAction?.('swiss_optimization');
    
    if ('vibrate' in navigator) {
      navigator.vibrate(100);
    }
  }, [awardSwissOptimizationXP, onQuickAction]);

  // Auto-collapse after inactivity
  useEffect(() => {
    if (isExpanded) {
      const timer = setTimeout(() => {
        setIsExpanded(false);
      }, 30000); // 30 seconds

      return () => clearTimeout(timer);
    }
  }, [isExpanded]);

  if (isLoading || !progress) {
    return (
      <div className={`mobile-gamification-widget loading ${className}`}>
        <div className="loading-spinner" />
      </div>
    );
  }

  const renderOverview = () => (
    <div className="widget-overview">
      <div className="level-display">
        <div className="level-badge">
          <span className="level-number">{progress.currentLevel}</span>
          <span className="level-label">{t('level', 'Level')}</span>
        </div>
        <div className="xp-info">
          <div className="total-xp">{progress.totalXP.toLocaleString()} XP</div>
          <div className="progress-bar">
            <div 
              className="progress-fill"
              style={{ width: `${progress.progressToNextLevel}%` }}
            />
          </div>
          <div className="next-level">
            {t('nextLevel', 'Next: {{xp}} XP', { 
              xp: (progress.nextLevelXP - progress.currentLevelXP).toLocaleString() 
            })}
          </div>
        </div>
      </div>

      {recentXPGain > 0 && (
        <div className="recent-xp-gain animate-bounce">
          +{recentXPGain} XP
        </div>
      )}

      <div className="quick-stats">
        <div className="stat-item">
          <span className="stat-value">{progress.streakDays}</span>
          <span className="stat-label">{t('streakDays', 'Day Streak')}</span>
        </div>
        <div className="stat-item">
          <span className="stat-value">{progress.totalAchievements}</span>
          <span className="stat-label">{t('achievements', 'Achievements')}</span>
        </div>
        <div className="stat-item">
          <span className="stat-value">{progress.completedGoals}</span>
          <span className="stat-label">{t('goalsCompleted', 'Goals')}</span>
        </div>
      </div>
    </div>
  );

  const renderQuickActions = () => (
    <div className="widget-quick-actions">
      <h4 className="section-title">{t('quickActions', 'Quick Actions')}</h4>
      
      <div className="quick-savings">
        <div className="amount-selector">
          <button 
            className={`amount-btn ${quickAmount === '25' ? 'active' : ''}`}
            onClick={() => setQuickAmount('25')}
          >
            CHF 25
          </button>
          <button 
            className={`amount-btn ${quickAmount === '50' ? 'active' : ''}`}
            onClick={() => setQuickAmount('50')}
          >
            CHF 50
          </button>
          <button 
            className={`amount-btn ${quickAmount === '100' ? 'active' : ''}`}
            onClick={() => setQuickAmount('100')}
          >
            CHF 100
          </button>
          <input
            type="number"
            value={quickAmount}
            onChange={(e) => setQuickAmount(e.target.value)}
            className="custom-amount"
            placeholder="Custom"
            min="10"
          />
        </div>
        
        <button 
          className="action-btn savings"
          onClick={handleQuickSavings}
          disabled={parseFloat(quickAmount) < 10}
        >
          💰 {t('quickSave', 'Save CHF {{amount}}', { amount: quickAmount })}
          <span className="xp-preview">+{Math.floor(parseFloat(quickAmount) / 10)} XP</span>
        </button>
      </div>

      <div className="swiss-actions">
        <button 
          className="action-btn pillar3a"
          onClick={handleQuickPillar3a}
        >
          🏛️ {t('pillar3aContribution', 'Pillar 3a (CHF 588)')}
          <span className="xp-preview">+70 XP</span>
        </button>
        
        <button 
          className="action-btn swiss-optimization"
          onClick={handleSwissOptimization}
        >
          🇨🇭 {t('swissOptimization', 'Swiss Optimization')}
          <span className="xp-preview">+100 XP</span>
        </button>
      </div>
    </div>
  );

  const renderProgress = () => (
    <div className="widget-progress">
      <h4 className="section-title">{t('progressTracking', 'Progress Tracking')}</h4>
      
      <div className="progress-rings">
        <div className="progress-ring">
          <svg className="ring-svg" viewBox="0 0 120 120">
            <circle
              cx="60"
              cy="60"
              r="50"
              fill="none"
              stroke={darkMode ? '#374151' : '#e5e7eb'}
              strokeWidth="8"
            />
            <circle
              cx="60"
              cy="60"
              r="50"
              fill="none"
              stroke="#3b82f6"
              strokeWidth="8"
              strokeLinecap="round"
              strokeDasharray={`${progress.progressToNextLevel * 3.14} 314`}
              transform="rotate(-90 60 60)"
            />
          </svg>
          <div className="ring-content">
            <span className="ring-value">{Math.round(progress.progressToNextLevel)}%</span>
            <span className="ring-label">{t('levelProgress', 'Level')}</span>
          </div>
        </div>
      </div>

      <div className="progress-details">
        <div className="detail-item">
          <span className="detail-label">{t('currentXP', 'Current XP')}</span>
          <span className="detail-value">{progress.currentLevelXP.toLocaleString()}</span>
        </div>
        <div className="detail-item">
          <span className="detail-label">{t('nextLevelXP', 'Next Level')}</span>
          <span className="detail-value">{progress.nextLevelXP.toLocaleString()}</span>
        </div>
        <div className="detail-item">
          <span className="detail-label">{t('totalXP', 'Total XP')}</span>
          <span className="detail-value">{progress.totalXP.toLocaleString()}</span>
        </div>
      </div>
    </div>
  );

  const renderAchievements = () => (
    <div className="widget-achievements">
      <h4 className="section-title">{t('recentAchievements', 'Recent Achievements')}</h4>
      
      <div className="achievement-preview">
        <div className="achievement-item">
          <div className="achievement-icon">🏆</div>
          <div className="achievement-info">
            <div className="achievement-name">{t('weekWarrior', 'Week Warrior')}</div>
            <div className="achievement-desc">{t('weekWarriorDesc', '7-day streak')}</div>
          </div>
          <div className="achievement-xp">+75 XP</div>
        </div>
        
        <div className="achievement-item">
          <div className="achievement-icon">🇨🇭</div>
          <div className="achievement-info">
            <div className="achievement-name">{t('swissSaver', 'Swiss Saver')}</div>
            <div className="achievement-desc">{t('swissSaverDesc', 'Pillar 3a contribution')}</div>
          </div>
          <div className="achievement-xp">+100 XP</div>
        </div>
      </div>

      <div className="achievement-progress">
        <div className="progress-item">
          <span className="progress-label">{t('nextAchievement', 'Next Achievement')}</span>
          <div className="progress-bar-small">
            <div className="progress-fill-small" style={{ width: '60%' }} />
          </div>
          <span className="progress-text">60% to Monthly Master</span>
        </div>
      </div>
    </div>
  );

  return (
    <div 
      className={`mobile-gamification-widget ${isExpanded ? 'expanded' : 'collapsed'} ${className}`}
      {...swipeHandlers}
    >
      {/* Collapsed Header */}
      <div 
        className="widget-header"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="header-content">
          <div className="level-indicator">
            <span className="level-badge-small">{progress.currentLevel}</span>
            <div className="xp-bar-small">
              <div 
                className="xp-fill-small"
                style={{ width: `${progress.progressToNextLevel}%` }}
              />
            </div>
          </div>
          
          {recentXPGain > 0 && (
            <div className="xp-notification">+{recentXPGain}</div>
          )}
          
          <div className="expand-indicator">
            {isExpanded ? '▼' : '▲'}
          </div>
        </div>
      </div>

      {/* Expanded Content */}
      {isExpanded && (
        <div className="widget-content">
          {/* Navigation Dots */}
          <div className="widget-navigation">
            {(['overview', 'quick-actions', 'progress', 'achievements'] as WidgetView[]).map((view, index) => (
              <button
                key={view}
                className={`nav-dot ${currentView === view ? 'active' : ''}`}
                onClick={() => setCurrentView(view)}
                aria-label={t(`navigate${view}`, `Navigate to ${view}`)}
              />
            ))}
          </div>

          {/* Content Views */}
          <div className="widget-views">
            {currentView === 'overview' && renderOverview()}
            {currentView === 'quick-actions' && renderQuickActions()}
            {currentView === 'progress' && renderProgress()}
            {currentView === 'achievements' && renderAchievements()}
          </div>

          {/* Swipe Indicator */}
          <div className="swipe-indicator">
            <span>← {t('swipeToNavigate', 'Swipe to navigate')} →</span>
          </div>
        </div>
      )}

      {/* Floating Action Button */}
      {!isExpanded && (
        <button 
          className="fab"
          onClick={() => setCurrentView('quick-actions')}
          onTouchStart={() => setIsExpanded(true)}
        >
          ⚡
        </button>
      )}
    </div>
  );
};

export default MobileGamificationWidget;
