import React, { useState, useMemo, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { LineChart, Line, AreaChart, Area, BarChart, Bar, PieChart, Pie, Cell, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { useGamification } from '../../hooks/useGamification';
import { useAnalytics } from '../../hooks/useAnalytics';

interface AdvancedAnalyticsDashboardProps {
  userId: string;
  darkMode: boolean;
  timeRange: '7d' | '30d' | '90d' | '1y' | 'all';
  className?: string;
}

type AnalyticsView = 'overview' | 'xp-trends' | 'behavioral' | 'swiss-optimization' | 'predictions';

const AdvancedAnalyticsDashboard: React.FC<AdvancedAnalyticsDashboardProps> = ({
  userId,
  darkMode,
  timeRange = '30d',
  className = '',
}) => {
  const { t } = useTranslation();
  const [activeView, setActiveView] = useState<AnalyticsView>('overview');
  const [selectedMetric, setSelectedMetric] = useState('xp');

  const { progress } = useGamification(userId);
  const {
    xpTrends,
    behavioralInsights,
    swissOptimizationMetrics,
    predictiveAnalytics,
    goalProgressAnalytics,
    streakAnalytics,
    achievementAnalytics,
    isLoading,
  } = useAnalytics(userId, timeRange);

  // Color schemes for charts
  const colors = {
    primary: darkMode ? '#60a5fa' : '#3b82f6',
    secondary: darkMode ? '#34d399' : '#10b981',
    accent: darkMode ? '#fbbf24' : '#f59e0b',
    warning: darkMode ? '#fb7185' : '#ef4444',
    swiss: darkMode ? '#dc2626' : '#b91c1c',
    success: darkMode ? '#22c55e' : '#16a34a',
  };

  // XP Trend Analysis
  const xpTrendData = useMemo(() => {
    if (!xpTrends) return [];
    
    return xpTrends.map((point, index) => ({
      date: point.date,
      dailyXP: point.dailyXP,
      cumulativeXP: point.cumulativeXP,
      savingsXP: point.savingsXP,
      achievementXP: point.achievementXP,
      streakXP: point.streakXP,
      swissXP: point.swissXP,
      movingAverage: index >= 6 ? 
        xpTrends.slice(index - 6, index + 1).reduce((sum, p) => sum + p.dailyXP, 0) / 7 : 
        point.dailyXP,
    }));
  }, [xpTrends]);

  // Swiss Optimization Breakdown
  const swissOptimizationData = useMemo(() => {
    if (!swissOptimizationMetrics) return [];
    
    return [
      { name: 'Pillar 3a', value: swissOptimizationMetrics.pillar3aUsage, color: colors.swiss },
      { name: 'Canton Comparison', value: swissOptimizationMetrics.cantonUsage, color: colors.primary },
      { name: 'Tax Optimization', value: swissOptimizationMetrics.taxOptimization, color: colors.secondary },
      { name: 'Healthcare', value: swissOptimizationMetrics.healthcareOptimization, color: colors.accent },
    ];
  }, [swissOptimizationMetrics, colors]);

  // Behavioral Pattern Analysis
  const behavioralPatternData = useMemo(() => {
    if (!behavioralInsights) return [];
    
    return behavioralInsights.weeklyPatterns.map((pattern, index) => ({
      day: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'][index],
      activity: pattern.activityScore,
      savings: pattern.savingsAmount,
      xpEarned: pattern.xpEarned,
      engagement: pattern.engagementScore,
    }));
  }, [behavioralInsights]);

  // Goal Progress Efficiency
  const goalEfficiencyData = useMemo(() => {
    if (!goalProgressAnalytics) return [];
    
    return goalProgressAnalytics.goals.map(goal => ({
      name: goal.name.substring(0, 15) + (goal.name.length > 15 ? '...' : ''),
      progress: goal.progressPercentage,
      efficiency: goal.efficiencyScore,
      onTrack: goal.onTrackPercentage,
      projected: goal.projectedCompletion,
    }));
  }, [goalProgressAnalytics]);

  const renderOverview = () => (
    <div className="analytics-overview">
      <div className="metrics-grid">
        <div className="metric-card">
          <h4>{t('analytics.totalXP', 'Total XP Earned')}</h4>
          <div className="metric-value">{progress?.totalXP.toLocaleString()}</div>
          <div className="metric-change positive">
            +{xpTrends?.[xpTrends.length - 1]?.dailyXP || 0} {t('analytics.today', 'today')}
          </div>
        </div>

        <div className="metric-card">
          <h4>{t('analytics.avgDailyXP', 'Avg Daily XP')}</h4>
          <div className="metric-value">
            {xpTrends ? Math.round(xpTrends.reduce((sum, p) => sum + p.dailyXP, 0) / xpTrends.length) : 0}
          </div>
          <div className="metric-change">
            {t('analytics.last30Days', 'Last 30 days')}
          </div>
        </div>

        <div className="metric-card">
          <h4>{t('analytics.streakMultiplier', 'Current Streak Multiplier')}</h4>
          <div className="metric-value">
            {streakAnalytics?.currentMultiplier.toFixed(1)}x
          </div>
          <div className="metric-change">
            {streakAnalytics?.longestStreak} {t('analytics.dayRecord', 'day record')}
          </div>
        </div>

        <div className="metric-card">
          <h4>{t('analytics.swissOptimization', 'Swiss Optimization Score')}</h4>
          <div className="metric-value">
            {swissOptimizationMetrics?.overallScore || 0}/100
          </div>
          <div className="metric-change">
            {swissOptimizationMetrics?.monthlyImprovement > 0 ? 'positive' : 'neutral'}
            {swissOptimizationMetrics?.monthlyImprovement > 0 && '+'}
            {swissOptimizationMetrics?.monthlyImprovement || 0} {t('analytics.thisMonth', 'this month')}
          </div>
        </div>
      </div>

      <div className="charts-row">
        <div className="chart-container">
          <h4>{t('analytics.xpTrend', 'XP Trend (30 Days)')}</h4>
          <ResponsiveContainer width="100%" height={200}>
            <AreaChart data={xpTrendData.slice(-30)}>
              <CartesianGrid strokeDasharray="3 3" stroke={darkMode ? '#374151' : '#e5e7eb'} />
              <XAxis 
                dataKey="date" 
                stroke={darkMode ? '#9ca3af' : '#6b7280'}
                fontSize={12}
              />
              <YAxis stroke={darkMode ? '#9ca3af' : '#6b7280'} fontSize={12} />
              <Tooltip 
                contentStyle={{
                  backgroundColor: darkMode ? '#1f2937' : '#ffffff',
                  border: `1px solid ${darkMode ? '#374151' : '#e5e7eb'}`,
                  borderRadius: '8px',
                }}
              />
              <Area 
                type="monotone" 
                dataKey="dailyXP" 
                stroke={colors.primary} 
                fill={colors.primary}
                fillOpacity={0.3}
              />
              <Line 
                type="monotone" 
                dataKey="movingAverage" 
                stroke={colors.accent} 
                strokeWidth={2}
                dot={false}
              />
            </AreaChart>
          </ResponsiveContainer>
        </div>

        <div className="chart-container">
          <h4>{t('analytics.swissFeatureUsage', 'Swiss Feature Usage')}</h4>
          <ResponsiveContainer width="100%" height={200}>
            <PieChart>
              <Pie
                data={swissOptimizationData}
                cx="50%"
                cy="50%"
                innerRadius={40}
                outerRadius={80}
                paddingAngle={5}
                dataKey="value"
              >
                {swissOptimizationData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );

  const renderXPTrends = () => (
    <div className="xp-trends-analysis">
      <div className="trend-controls">
        <div className="metric-selector">
          {[
            { key: 'dailyXP', label: 'Daily XP' },
            { key: 'cumulativeXP', label: 'Cumulative XP' },
            { key: 'savingsXP', label: 'Savings XP' },
            { key: 'swissXP', label: 'Swiss XP' },
          ].map(metric => (
            <button
              key={metric.key}
              className={`metric-btn ${selectedMetric === metric.key ? 'active' : ''}`}
              onClick={() => setSelectedMetric(metric.key)}
            >
              {metric.label}
            </button>
          ))}
        </div>
      </div>

      <div className="trend-chart">
        <ResponsiveContainer width="100%" height={400}>
          <LineChart data={xpTrendData}>
            <CartesianGrid strokeDasharray="3 3" stroke={darkMode ? '#374151' : '#e5e7eb'} />
            <XAxis 
              dataKey="date" 
              stroke={darkMode ? '#9ca3af' : '#6b7280'}
            />
            <YAxis stroke={darkMode ? '#9ca3af' : '#6b7280'} />
            <Tooltip 
              contentStyle={{
                backgroundColor: darkMode ? '#1f2937' : '#ffffff',
                border: `1px solid ${darkMode ? '#374151' : '#e5e7eb'}`,
                borderRadius: '8px',
              }}
            />
            <Legend />
            <Line 
              type="monotone" 
              dataKey={selectedMetric}
              stroke={colors.primary} 
              strokeWidth={3}
              dot={{ fill: colors.primary, strokeWidth: 2, r: 4 }}
            />
            {selectedMetric === 'dailyXP' && (
              <Line 
                type="monotone" 
                dataKey="movingAverage"
                stroke={colors.accent} 
                strokeWidth={2}
                strokeDasharray="5 5"
                dot={false}
                name="7-day Average"
              />
            )}
          </LineChart>
        </ResponsiveContainer>
      </div>

      <div className="trend-insights">
        <div className="insight-card">
          <h5>{t('analytics.bestDay', 'Best Day')}</h5>
          <div className="insight-value">
            {xpTrends && xpTrends.length > 0 ? 
              new Date(xpTrends.reduce((max, p) => p.dailyXP > max.dailyXP ? p : max).date).toLocaleDateString() : 
              'N/A'
            }
          </div>
          <div className="insight-detail">
            {xpTrends && xpTrends.length > 0 ? 
              `${xpTrends.reduce((max, p) => p.dailyXP > max.dailyXP ? p : max).dailyXP} XP` : 
              ''
            }
          </div>
        </div>

        <div className="insight-card">
          <h5>{t('analytics.consistency', 'Consistency Score')}</h5>
          <div className="insight-value">
            {behavioralInsights?.consistencyScore || 0}/100
          </div>
          <div className="insight-detail">
            {t('analytics.basedOnRegularity', 'Based on activity regularity')}
          </div>
        </div>

        <div className="insight-card">
          <h5>{t('analytics.growthRate', 'Growth Rate')}</h5>
          <div className="insight-value">
            +{behavioralInsights?.growthRate || 0}%
          </div>
          <div className="insight-detail">
            {t('analytics.monthOverMonth', 'Month over month')}
          </div>
        </div>
      </div>
    </div>
  );

  const renderBehavioralAnalysis = () => (
    <div className="behavioral-analysis">
      <div className="behavior-charts">
        <div className="chart-container">
          <h4>{t('analytics.weeklyPatterns', 'Weekly Activity Patterns')}</h4>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={behavioralPatternData}>
              <CartesianGrid strokeDasharray="3 3" stroke={darkMode ? '#374151' : '#e5e7eb'} />
              <XAxis dataKey="day" stroke={darkMode ? '#9ca3af' : '#6b7280'} />
              <YAxis stroke={darkMode ? '#9ca3af' : '#6b7280'} />
              <Tooltip 
                contentStyle={{
                  backgroundColor: darkMode ? '#1f2937' : '#ffffff',
                  border: `1px solid ${darkMode ? '#374151' : '#e5e7eb'}`,
                  borderRadius: '8px',
                }}
              />
              <Legend />
              <Bar dataKey="activity" fill={colors.primary} name="Activity Score" />
              <Bar dataKey="engagement" fill={colors.secondary} name="Engagement Score" />
            </BarChart>
          </ResponsiveContainer>
        </div>

        <div className="chart-container">
          <h4>{t('analytics.goalEfficiency', 'Goal Progress Efficiency')}</h4>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={goalEfficiencyData} layout="horizontal">
              <CartesianGrid strokeDasharray="3 3" stroke={darkMode ? '#374151' : '#e5e7eb'} />
              <XAxis type="number" stroke={darkMode ? '#9ca3af' : '#6b7280'} />
              <YAxis dataKey="name" type="category" stroke={darkMode ? '#9ca3af' : '#6b7280'} width={100} />
              <Tooltip 
                contentStyle={{
                  backgroundColor: darkMode ? '#1f2937' : '#ffffff',
                  border: `1px solid ${darkMode ? '#374151' : '#e5e7eb'}`,
                  borderRadius: '8px',
                }}
              />
              <Legend />
              <Bar dataKey="progress" fill={colors.primary} name="Progress %" />
              <Bar dataKey="efficiency" fill={colors.accent} name="Efficiency Score" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>

      <div className="behavioral-insights">
        <div className="insight-section">
          <h5>{t('analytics.optimalTimes', 'Optimal Activity Times')}</h5>
          <div className="time-heatmap">
            {behavioralInsights?.optimalTimes.map((time, index) => (
              <div key={index} className="time-slot">
                <span className="time-label">{time.hour}:00</span>
                <div 
                  className="activity-bar"
                  style={{ 
                    width: `${time.activityLevel}%`,
                    backgroundColor: colors.primary,
                  }}
                />
                <span className="activity-score">{time.activityLevel}%</span>
              </div>
            ))}
          </div>
        </div>

        <div className="insight-section">
          <h5>{t('analytics.motivationFactors', 'Top Motivation Factors')}</h5>
          <div className="motivation-list">
            {behavioralInsights?.motivationFactors.map((factor, index) => (
              <div key={index} className="motivation-item">
                <span className="factor-name">{factor.name}</span>
                <div className="factor-impact">
                  <div 
                    className="impact-bar"
                    style={{ 
                      width: `${factor.impact}%`,
                      backgroundColor: colors.success,
                    }}
                  />
                  <span className="impact-score">{factor.impact}%</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );

  const renderSwissOptimization = () => (
    <div className="swiss-optimization-analysis">
      <div className="optimization-score-card">
        <h4>{t('analytics.swissOptimizationScore', 'Swiss Optimization Score')}</h4>
        <div className="score-display">
          <div className="score-circle">
            <svg viewBox="0 0 120 120" className="score-svg">
              <circle
                cx="60"
                cy="60"
                r="50"
                fill="none"
                stroke={darkMode ? '#374151' : '#e5e7eb'}
                strokeWidth="8"
              />
              <circle
                cx="60"
                cy="60"
                r="50"
                fill="none"
                stroke={colors.swiss}
                strokeWidth="8"
                strokeLinecap="round"
                strokeDasharray={`${(swissOptimizationMetrics?.overallScore || 0) * 3.14} 314`}
                transform="rotate(-90 60 60)"
              />
            </svg>
            <div className="score-text">
              <span className="score-number">{swissOptimizationMetrics?.overallScore || 0}</span>
              <span className="score-label">/100</span>
            </div>
          </div>
          <div className="score-breakdown">
            <div className="breakdown-item">
              <span className="breakdown-label">Pillar 3a Optimization</span>
              <span className="breakdown-value">{swissOptimizationMetrics?.pillar3aScore || 0}/100</span>
            </div>
            <div className="breakdown-item">
              <span className="breakdown-label">Tax Efficiency</span>
              <span className="breakdown-value">{swissOptimizationMetrics?.taxEfficiencyScore || 0}/100</span>
            </div>
            <div className="breakdown-item">
              <span className="breakdown-label">Canton Optimization</span>
              <span className="breakdown-value">{swissOptimizationMetrics?.cantonScore || 0}/100</span>
            </div>
            <div className="breakdown-item">
              <span className="breakdown-label">Healthcare Efficiency</span>
              <span className="breakdown-value">{swissOptimizationMetrics?.healthcareScore || 0}/100</span>
            </div>
          </div>
        </div>
      </div>

      <div className="optimization-recommendations">
        <h5>{t('analytics.recommendations', 'Optimization Recommendations')}</h5>
        <div className="recommendations-list">
          {swissOptimizationMetrics?.recommendations.map((rec, index) => (
            <div key={index} className="recommendation-item">
              <div className="rec-priority" data-priority={rec.priority}>
                {rec.priority === 'high' ? '🔴' : rec.priority === 'medium' ? '🟡' : '🟢'}
              </div>
              <div className="rec-content">
                <h6 className="rec-title">{rec.title}</h6>
                <p className="rec-description">{rec.description}</p>
                <div className="rec-impact">
                  <span className="impact-label">Potential Savings:</span>
                  <span className="impact-value">CHF {rec.potentialSavings.toLocaleString()}/year</span>
                </div>
              </div>
              <button className="rec-action-btn">
                {t('analytics.implement', 'Implement')}
              </button>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderPredictions = () => (
    <div className="predictive-analysis">
      <div className="prediction-cards">
        <div className="prediction-card">
          <h5>{t('analytics.goalCompletion', 'Goal Completion Forecast')}</h5>
          <div className="prediction-chart">
            <ResponsiveContainer width="100%" height={200}>
              <LineChart data={predictiveAnalytics?.goalCompletionForecast || []}>
                <CartesianGrid strokeDasharray="3 3" stroke={darkMode ? '#374151' : '#e5e7eb'} />
                <XAxis dataKey="month" stroke={darkMode ? '#9ca3af' : '#6b7280'} />
                <YAxis stroke={darkMode ? '#9ca3af' : '#6b7280'} />
                <Tooltip />
                <Line 
                  type="monotone" 
                  dataKey="predicted" 
                  stroke={colors.primary} 
                  strokeDasharray="5 5"
                  name="Predicted"
                />
                <Line 
                  type="monotone" 
                  dataKey="optimistic" 
                  stroke={colors.success} 
                  strokeWidth={1}
                  name="Optimistic"
                />
                <Line 
                  type="monotone" 
                  dataKey="conservative" 
                  stroke={colors.warning} 
                  strokeWidth={1}
                  name="Conservative"
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </div>

        <div className="prediction-card">
          <h5>{t('analytics.xpProjection', 'XP Growth Projection')}</h5>
          <div className="projection-stats">
            <div className="stat-item">
              <span className="stat-label">{t('analytics.nextLevel', 'Next Level In')}</span>
              <span className="stat-value">
                {predictiveAnalytics?.nextLevelDays || 0} {t('analytics.days', 'days')}
              </span>
            </div>
            <div className="stat-item">
              <span className="stat-label">{t('analytics.yearEndLevel', 'Year-End Level')}</span>
              <span className="stat-value">
                {predictiveAnalytics?.yearEndLevel || 0}
              </span>
            </div>
            <div className="stat-item">
              <span className="stat-label">{t('analytics.yearEndXP', 'Year-End XP')}</span>
              <span className="stat-value">
                {(predictiveAnalytics?.yearEndXP || 0).toLocaleString()}
              </span>
            </div>
          </div>
        </div>
      </div>

      <div className="ai-insights">
        <h5>{t('analytics.aiInsights', 'AI-Powered Insights')}</h5>
        <div className="insights-list">
          {predictiveAnalytics?.aiInsights.map((insight, index) => (
            <div key={index} className="insight-item">
              <div className="insight-icon">{insight.type === 'opportunity' ? '💡' : insight.type === 'warning' ? '⚠️' : 'ℹ️'}</div>
              <div className="insight-content">
                <h6 className="insight-title">{insight.title}</h6>
                <p className="insight-description">{insight.description}</p>
                <div className="insight-confidence">
                  <span className="confidence-label">Confidence:</span>
                  <div className="confidence-bar">
                    <div 
                      className="confidence-fill"
                      style={{ 
                        width: `${insight.confidence}%`,
                        backgroundColor: insight.confidence > 80 ? colors.success : insight.confidence > 60 ? colors.accent : colors.warning,
                      }}
                    />
                  </div>
                  <span className="confidence-value">{insight.confidence}%</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  if (isLoading) {
    return (
      <div className={`advanced-analytics-dashboard loading ${className}`}>
        <div className="loading-spinner" />
        <p>{t('analytics.loadingAnalytics', 'Loading advanced analytics...')}</p>
      </div>
    );
  }

  return (
    <div className={`advanced-analytics-dashboard ${className}`}>
      <div className="dashboard-header">
        <h2 className="dashboard-title">
          📊 {t('analytics.title', 'Advanced Analytics Dashboard')}
        </h2>
        <div className="view-selector">
          {[
            { key: 'overview', label: 'Overview', icon: '📈' },
            { key: 'xp-trends', label: 'XP Trends', icon: '⚡' },
            { key: 'behavioral', label: 'Behavioral', icon: '🧠' },
            { key: 'swiss-optimization', label: 'Swiss Optimization', icon: '🇨🇭' },
            { key: 'predictions', label: 'Predictions', icon: '🔮' },
          ].map(view => (
            <button
              key={view.key}
              className={`view-btn ${activeView === view.key ? 'active' : ''}`}
              onClick={() => setActiveView(view.key as AnalyticsView)}
            >
              <span className="view-icon">{view.icon}</span>
              <span className="view-label">{view.label}</span>
            </button>
          ))}
        </div>
      </div>

      <div className="dashboard-content">
        {activeView === 'overview' && renderOverview()}
        {activeView === 'xp-trends' && renderXPTrends()}
        {activeView === 'behavioral' && renderBehavioralAnalysis()}
        {activeView === 'swiss-optimization' && renderSwissOptimization()}
        {activeView === 'predictions' && renderPredictions()}
      </div>
    </div>
  );
};

export default AdvancedAnalyticsDashboard;
