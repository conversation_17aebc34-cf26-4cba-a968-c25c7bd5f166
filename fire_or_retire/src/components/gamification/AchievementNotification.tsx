import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Achievement, UserAchievement } from '../../types/gamification';

interface AchievementNotificationProps {
  achievement: Achievement;
  userAchievement: UserAchievement;
  darkMode: boolean;
  onDismiss: () => void;
  autoHide?: boolean;
  autoHideDelay?: number;
  showConfetti?: boolean;
  className?: string;
}

const AchievementNotification: React.FC<AchievementNotificationProps> = ({
  achievement,
  userAchievement,
  darkMode,
  onDismiss,
  autoHide = true,
  autoHideDelay = 5000,
  showConfetti = true,
  className = '',
}) => {
  const { t } = useTranslation();
  const [isVisible, setIsVisible] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);

  // Show notification with entrance animation
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
      setIsAnimating(true);
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  // Auto-hide functionality
  useEffect(() => {
    if (autoHide && isVisible) {
      const timer = setTimeout(() => {
        handleDismiss();
      }, autoHideDelay);

      return () => clearTimeout(timer);
    }
  }, [autoHide, autoHideDelay, isVisible]);

  // Handle dismiss with exit animation
  const handleDismiss = useCallback(() => {
    setIsAnimating(false);
    setTimeout(() => {
      setIsVisible(false);
      onDismiss();
    }, 300);
  }, [onDismiss]);

  // Get rarity-specific styles
  const getRarityStyles = () => {
    switch (achievement.rarity) {
      case 'legendary':
        return {
          bg: 'bg-gradient-to-r from-yellow-400 to-orange-500',
          border: 'border-yellow-400',
          glow: 'shadow-2xl shadow-yellow-400/50',
          text: 'text-yellow-900',
          confetti: '🎆',
        };
      case 'epic':
        return {
          bg: 'bg-gradient-to-r from-purple-400 to-pink-500',
          border: 'border-purple-400',
          glow: 'shadow-xl shadow-purple-400/40',
          text: 'text-purple-900',
          confetti: '✨',
        };
      case 'rare':
        return {
          bg: 'bg-gradient-to-r from-blue-400 to-cyan-500',
          border: 'border-blue-400',
          glow: 'shadow-lg shadow-blue-400/30',
          text: 'text-blue-900',
          confetti: '🌟',
        };
      default:
        return {
          bg: 'bg-gradient-to-r from-gray-400 to-gray-500',
          border: 'border-gray-400',
          glow: 'shadow-md shadow-gray-400/20',
          text: 'text-gray-900',
          confetti: '⭐',
        };
    }
  };

  const styles = getRarityStyles();

  if (!isVisible) return null;

  return (
    <div className={`achievement-notification ${className}`}>
      {/* Backdrop */}
      <div 
        className={`fixed inset-0 bg-black transition-opacity duration-300 z-40 ${
          isAnimating ? 'bg-opacity-30' : 'bg-opacity-0'
        }`}
        onClick={handleDismiss}
      />

      {/* Notification */}
      <div className={`
        fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2
        max-w-md w-full mx-4 z-50
        transition-all duration-300 ease-out
        ${isAnimating ? 'scale-100 opacity-100' : 'scale-75 opacity-0'}
      `}>
        <div className={`
          relative rounded-xl border-2 p-6 text-center
          ${styles.bg} ${styles.border} ${styles.glow}
          ${isAnimating ? 'animate-pulse' : ''}
        `}>
          {/* Confetti Background */}
          {showConfetti && (
            <div className="absolute inset-0 overflow-hidden rounded-xl">
              {[...Array(20)].map((_, i) => (
                <div
                  key={i}
                  className={`absolute text-2xl animate-bounce`}
                  style={{
                    left: `${Math.random() * 100}%`,
                    top: `${Math.random() * 100}%`,
                    animationDelay: `${Math.random() * 2}s`,
                    animationDuration: `${2 + Math.random() * 2}s`,
                  }}
                >
                  {styles.confetti}
                </div>
              ))}
            </div>
          )}

          {/* Close Button */}
          <button
            onClick={handleDismiss}
            className={`absolute top-2 right-2 w-8 h-8 rounded-full bg-white bg-opacity-20 
              hover:bg-opacity-30 transition-colors flex items-center justify-center ${styles.text}`}
          >
            ×
          </button>

          {/* Content */}
          <div className="relative z-10">
            {/* Header */}
            <div className="mb-4">
              <h2 className={`text-2xl font-bold ${styles.text} mb-1`}>
                🏆 {t('achievement.unlocked', 'Achievement Unlocked!')}
              </h2>
              <div className={`inline-block px-3 py-1 rounded-full bg-white bg-opacity-20 text-sm font-medium ${styles.text}`}>
                {t(`rarity.${achievement.rarity}`, achievement.rarity.toUpperCase())}
              </div>
            </div>

            {/* Achievement Icon */}
            <div className="text-6xl mb-4 animate-bounce">
              {achievement.icon}
            </div>

            {/* Achievement Details */}
            <div className="mb-4">
              <h3 className={`text-xl font-bold ${styles.text} mb-2`}>
                {achievement.name}
              </h3>
              <p className={`text-sm ${styles.text} opacity-90`}>
                {achievement.description}
              </p>
            </div>

            {/* XP Reward */}
            <div className={`inline-flex items-center space-x-2 px-4 py-2 rounded-full bg-white bg-opacity-20 ${styles.text}`}>
              <span className="text-lg">⚡</span>
              <span className="font-bold">+{achievement.xpReward} XP</span>
            </div>

            {/* Swiss Badge */}
            {achievement.isSwissSpecific && (
              <div className="mt-3">
                <div className={`inline-block px-3 py-1 rounded-full bg-red-500 text-white text-sm font-medium`}>
                  🇨🇭 {t('achievement.swissSpecific', 'Swiss Specific')}
                </div>
              </div>
            )}

            {/* Unlock Date */}
            <div className={`mt-4 text-xs ${styles.text} opacity-75`}>
              {t('achievement.unlockedOn', 'Unlocked on')} {
                new Date(userAchievement.unlockedAt).toLocaleString()
              }
            </div>
          </div>

          {/* Legendary Extra Effects */}
          {achievement.rarity === 'legendary' && (
            <>
              {/* Rotating glow */}
              <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-yellow-400/20 to-orange-400/20 animate-spin" 
                   style={{ animationDuration: '3s' }} />
              
              {/* Pulsing border */}
              <div className="absolute inset-0 rounded-xl border-4 border-yellow-400 animate-pulse" />
            </>
          )}

          {/* Epic Sparkle Effect */}
          {achievement.rarity === 'epic' && (
            <div className="absolute inset-0 rounded-xl">
              {[...Array(10)].map((_, i) => (
                <div
                  key={i}
                  className="absolute w-2 h-2 bg-white rounded-full animate-ping"
                  style={{
                    left: `${Math.random() * 100}%`,
                    top: `${Math.random() * 100}%`,
                    animationDelay: `${Math.random() * 2}s`,
                  }}
                />
              ))}
            </div>
          )}
        </div>

        {/* Sound Effect Indicator */}
        {achievement.rarity === 'legendary' && (
          <div className="text-center mt-2">
            <span className="text-yellow-400 text-sm animate-pulse">
              🔊 {t('achievement.legendarySound', 'Legendary Achievement!')}
            </span>
          </div>
        )}
      </div>
    </div>
  );
};

export default AchievementNotification;
