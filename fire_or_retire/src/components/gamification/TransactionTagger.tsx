import React, { useState, useCallback, useMemo, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { TransactionTag, TaggedTransaction, TagCategory } from '../../types/gamification';
import SmartTaggingService, { Transaction, GoalAlignmentAnalysis } from '../../services/SmartTaggingService';

interface TransactionTaggerProps {
  transaction: Transaction;
  existingTags?: TaggedTransaction;
  darkMode: boolean;
  onTagsUpdated: (taggedTransaction: TaggedTransaction) => void;
  onXPAwarded?: (xp: number, tags: string[]) => void;
  className?: string;
}

const TransactionTagger: React.FC<TransactionTaggerProps> = ({
  transaction,
  existingTags,
  darkMode,
  onTagsUpdated,
  onXPAwarded,
  className = '',
}) => {
  const { t } = useTranslation();
  const [selectedTags, setSelectedTags] = useState<string[]>(existingTags?.tags || []);
  const [isAutoTagging, setIsAutoTagging] = useState(false);
  const [goalAlignment, setGoalAlignment] = useState<GoalAlignmentAnalysis | null>(null);
  const [showAllTags, setShowAllTags] = useState(false);

  const taggingService = useMemo(() => new SmartTaggingService(), []);
  const allTags = useMemo(() => taggingService.getAllTags(), [taggingService]);

  // Group tags by category
  const tagsByCategory = useMemo(() => {
    const grouped: Record<TagCategory, TransactionTag[]> = {
      goal_booster: [],
      goal_blocker: [],
      smart_choice: [],
      swiss_saver: [],
      investment: [],
      reward: [],
      neutral: [],
    };

    allTags.forEach(tag => {
      grouped[tag.category].push(tag);
    });

    return grouped;
  }, [allTags]);

  // Calculate total XP from selected tags
  const totalXP = useMemo(() => {
    return selectedTags.reduce((sum, tagId) => {
      const tag = allTags.find(t => t.id === tagId);
      return sum + (tag?.xpModifier || 0);
    }, 0);
  }, [selectedTags, allTags]);

  // Auto-tag transaction
  const handleAutoTag = useCallback(async () => {
    setIsAutoTagging(true);
    try {
      const taggedTransaction = await taggingService.autoTagTransaction(transaction);
      setSelectedTags(taggedTransaction.tags);
      
      // Analyze goal alignment
      const alignment = taggingService.analyzeGoalAlignment(transaction, []);
      setGoalAlignment(alignment);
      
      onTagsUpdated(taggedTransaction);
      
      if (onXPAwarded && taggedTransaction.xpAwarded > 0) {
        onXPAwarded(taggedTransaction.xpAwarded, taggedTransaction.tags);
      }
    } catch (error) {
      console.error('Auto-tagging failed:', error);
    } finally {
      setIsAutoTagging(false);
    }
  }, [transaction, taggingService, onTagsUpdated, onXPAwarded]);

  // Handle manual tag selection
  const handleTagToggle = useCallback((tagId: string) => {
    setSelectedTags(prev => {
      const newTags = prev.includes(tagId)
        ? prev.filter(id => id !== tagId)
        : [...prev, tagId];
      
      // Update tagged transaction
      const updatedTransaction = taggingService.updateTransactionTags(
        transaction.id,
        newTags,
        transaction.userId
      );
      
      onTagsUpdated(updatedTransaction);
      
      if (onXPAwarded) {
        const xpDiff = updatedTransaction.xpAwarded - (existingTags?.xpAwarded || 0);
        if (xpDiff !== 0) {
          onXPAwarded(xpDiff, newTags);
        }
      }
      
      return newTags;
    });
  }, [transaction, taggingService, onTagsUpdated, onXPAwarded, existingTags]);

  // Get category display info
  const getCategoryInfo = useCallback((category: TagCategory) => {
    const categoryMap = {
      goal_booster: { name: 'Goal Booster', emoji: '🎯', color: 'text-green-600' },
      goal_blocker: { name: 'Goal Blocker', emoji: '⚠️', color: 'text-red-600' },
      smart_choice: { name: 'Smart Choice', emoji: '💡', color: 'text-blue-600' },
      swiss_saver: { name: 'Swiss Saver', emoji: '🇨🇭', color: 'text-red-600' },
      investment: { name: 'Investment', emoji: '📈', color: 'text-purple-600' },
      reward: { name: 'Reward', emoji: '🎉', color: 'text-yellow-600' },
      neutral: { name: 'Neutral', emoji: '⚪', color: 'text-gray-600' },
    };
    return categoryMap[category];
  }, []);

  // Get alignment color
  const getAlignmentColor = useCallback((score: number) => {
    if (score > 20) return 'text-green-600';
    if (score > 0) return 'text-blue-600';
    if (score > -20) return 'text-yellow-600';
    return 'text-red-600';
  }, []);

  return (
    <div className={`transaction-tagger ${className}`}>
      <div className={`p-4 rounded-lg border ${
        darkMode 
          ? 'bg-gray-800 border-gray-600' 
          : 'bg-white border-gray-300 shadow-sm'
      }`}>
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">
            🏷️ {t('tagging.title', 'Transaction Tags')}
          </h3>
          <div className="flex items-center space-x-2">
            {/* XP Display */}
            {totalXP !== 0 && (
              <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                totalXP > 0 
                  ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                  : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
              }`}>
                {totalXP > 0 ? '+' : ''}{totalXP} XP
              </div>
            )}
            
            {/* Auto-tag Button */}
            <button
              onClick={handleAutoTag}
              disabled={isAutoTagging}
              className={`px-3 py-1 rounded-lg text-sm font-medium transition-colors ${
                isAutoTagging
                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  : 'bg-blue-600 text-white hover:bg-blue-700'
              }`}
            >
              {isAutoTagging ? '🔄' : '🤖'} {t('tagging.autoTag', 'Auto Tag')}
            </button>
          </div>
        </div>

        {/* Transaction Info */}
        <div className={`p-3 rounded-lg mb-4 ${
          darkMode ? 'bg-gray-700' : 'bg-gray-50'
        }`}>
          <div className="flex justify-between items-start">
            <div>
              <div className="font-medium">{transaction.description}</div>
              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                {transaction.merchant && `${transaction.merchant} • `}
                {new Date(transaction.date).toLocaleDateString()}
              </div>
            </div>
            <div className={`text-lg font-bold ${
              transaction.type === 'expense' ? 'text-red-600' : 'text-green-600'
            }`}>
              {transaction.type === 'expense' ? '-' : '+'}CHF {Math.abs(transaction.amount).toLocaleString()}
            </div>
          </div>
        </div>

        {/* Goal Alignment */}
        {goalAlignment && (
          <div className={`p-3 rounded-lg mb-4 ${
            darkMode ? 'bg-gray-700' : 'bg-gray-50'
          }`}>
            <div className="flex items-center justify-between mb-2">
              <span className="font-medium">
                🎯 {t('tagging.goalAlignment', 'Goal Alignment')}
              </span>
              <span className={`font-bold ${getAlignmentColor(goalAlignment.alignmentScore)}`}>
                {goalAlignment.alignmentScore > 0 ? '+' : ''}{goalAlignment.alignmentScore.toFixed(0)}
              </span>
            </div>
            {goalAlignment.recommendations.length > 0 && (
              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                {goalAlignment.recommendations[0]}
              </div>
            )}
          </div>
        )}

        {/* Tag Categories */}
        <div className="space-y-4">
          {Object.entries(tagsByCategory).map(([category, tags]) => {
            if (tags.length === 0) return null;
            
            const categoryInfo = getCategoryInfo(category as TagCategory);
            const visibleTags = showAllTags ? tags : tags.slice(0, 3);
            
            return (
              <div key={category}>
                <div className="flex items-center justify-between mb-2">
                  <h4 className={`text-sm font-medium ${categoryInfo.color}`}>
                    {categoryInfo.emoji} {t(`tagging.category.${category}`, categoryInfo.name)}
                  </h4>
                  {tags.length > 3 && !showAllTags && (
                    <button
                      onClick={() => setShowAllTags(true)}
                      className={`text-xs ${darkMode ? 'text-gray-400 hover:text-white' : 'text-gray-600 hover:text-gray-900'}`}
                    >
                      +{tags.length - 3} more
                    </button>
                  )}
                </div>
                
                <div className="flex flex-wrap gap-2">
                  {visibleTags.map(tag => (
                    <button
                      key={tag.id}
                      onClick={() => handleTagToggle(tag.id)}
                      className={`px-3 py-1 rounded-full text-sm font-medium transition-all duration-200 ${
                        selectedTags.includes(tag.id)
                          ? `bg-opacity-100 text-white shadow-md`
                          : `bg-opacity-20 hover:bg-opacity-30 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`
                      }`}
                      style={{
                        backgroundColor: selectedTags.includes(tag.id) ? tag.color : `${tag.color}33`,
                        borderColor: tag.color,
                        borderWidth: '1px',
                      }}
                    >
                      <span className="mr-1">{tag.icon}</span>
                      {tag.name}
                      {tag.xpModifier !== 0 && (
                        <span className="ml-1 text-xs">
                          ({tag.xpModifier > 0 ? '+' : ''}{tag.xpModifier})
                        </span>
                      )}
                      {tag.isSwissSpecific && (
                        <span className="ml-1">🇨🇭</span>
                      )}
                    </button>
                  ))}
                </div>
              </div>
            );
          })}
        </div>

        {/* Show All Tags Toggle */}
        {!showAllTags && Object.values(tagsByCategory).some(tags => tags.length > 3) && (
          <div className="mt-4 text-center">
            <button
              onClick={() => setShowAllTags(true)}
              className={`text-sm ${darkMode ? 'text-gray-400 hover:text-white' : 'text-gray-600 hover:text-gray-900'}`}
            >
              {t('tagging.showAllTags', 'Show All Tags')}
            </button>
          </div>
        )}

        {/* Selected Tags Summary */}
        {selectedTags.length > 0 && (
          <div className={`mt-4 p-3 rounded-lg ${
            darkMode ? 'bg-gray-700' : 'bg-gray-50'
          }`}>
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">
                {t('tagging.selectedTags', 'Selected Tags')} ({selectedTags.length})
              </span>
              <button
                onClick={() => setSelectedTags([])}
                className={`text-xs ${darkMode ? 'text-gray-400 hover:text-white' : 'text-gray-600 hover:text-gray-900'}`}
              >
                {t('tagging.clearAll', 'Clear All')}
              </button>
            </div>
            <div className="flex flex-wrap gap-1">
              {selectedTags.map(tagId => {
                const tag = allTags.find(t => t.id === tagId);
                if (!tag) return null;
                
                return (
                  <span
                    key={tagId}
                    className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400"
                  >
                    {tag.icon} {tag.name}
                    <button
                      onClick={() => handleTagToggle(tagId)}
                      className="ml-1 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200"
                    >
                      ×
                    </button>
                  </span>
                );
              })}
            </div>
          </div>
        )}

        {/* Auto-tagging Status */}
        {existingTags?.autoTagged && (
          <div className={`mt-2 text-xs ${darkMode ? 'text-gray-500' : 'text-gray-400'}`}>
            🤖 {t('tagging.autoTagged', 'Auto-tagged')} • 
            {t('tagging.confirmTags', 'Review and confirm tags for accuracy')}
          </div>
        )}
      </div>
    </div>
  );
};

export default TransactionTagger;
