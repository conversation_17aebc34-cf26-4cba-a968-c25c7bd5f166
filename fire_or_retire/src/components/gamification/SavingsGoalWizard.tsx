import React, { useState, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { SavingsGoal, SavingsGoalCategory, SavingsGoalPriority, GoalMilestone } from '../../types/gamification';

interface SavingsGoalWizardProps {
  userId: string;
  darkMode: boolean;
  onSave: (goal: Omit<SavingsGoal, 'id' | 'createdAt' | 'updatedAt'>) => void;
  onCancel: () => void;
  existingGoal?: SavingsGoal;
  className?: string;
}

interface WizardStep {
  id: string;
  title: string;
  description: string;
}

const SavingsGoalWizard: React.FC<SavingsGoalWizardProps> = ({
  userId,
  darkMode,
  onSave,
  onCancel,
  existingGoal,
  className = '',
}) => {
  const { t } = useTranslation();
  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState({
    name: existingGoal?.name || '',
    description: existingGoal?.description || '',
    category: existingGoal?.category || 'custom' as SavingsGoalCategory,
    targetAmount: existingGoal?.targetAmount || 0,
    currentAmount: existingGoal?.currentAmount || 0,
    monthlyContribution: existingGoal?.monthlyContribution || 0,
    targetDate: existingGoal?.targetDate ? new Date(existingGoal.targetDate).toISOString().split('T')[0] : '',
    priority: existingGoal?.priority || 'medium' as SavingsGoalPriority,
    autoContribute: existingGoal?.autoContribute || false,
    swissSpecific: existingGoal?.swissSpecific || false,
    tags: existingGoal?.tags || [],
    sharingSettings: existingGoal?.sharingSettings || {
      isPublic: false,
      shareProgress: false,
      shareAchievements: false,
      allowComments: false,
    },
  });

  const wizardSteps: WizardStep[] = useMemo(() => [
    {
      id: 'type',
      title: t('wizard.step.type.title', 'Goal Type'),
      description: t('wizard.step.type.description', 'What are you saving for?'),
    },
    {
      id: 'details',
      title: t('wizard.step.details.title', 'Goal Details'),
      description: t('wizard.step.details.description', 'Set your target and timeline'),
    },
    {
      id: 'strategy',
      title: t('wizard.step.strategy.title', 'Savings Strategy'),
      description: t('wizard.step.strategy.description', 'How will you reach your goal?'),
    },
    {
      id: 'settings',
      title: t('wizard.step.settings.title', 'Settings'),
      description: t('wizard.step.settings.description', 'Customize your goal preferences'),
    },
  ], [t]);

  // Goal categories with Swiss-specific options
  const goalCategories = useMemo(() => [
    { value: 'emergency_fund', label: t('category.emergency_fund', 'Emergency Fund'), emoji: '🚨', swissSpecific: false },
    { value: 'vacation', label: t('category.vacation', 'Vacation'), emoji: '✈️', swissSpecific: false },
    { value: 'home_purchase', label: t('category.home_purchase', 'Home Purchase'), emoji: '🏠', swissSpecific: true },
    { value: 'retirement', label: t('category.retirement', 'Retirement'), emoji: '🏖️', swissSpecific: true },
    { value: 'pillar3a', label: t('category.pillar3a', 'Pillar 3a'), emoji: '🏛️', swissSpecific: true },
    { value: 'education', label: t('category.education', 'Education'), emoji: '🎓', swissSpecific: false },
    { value: 'vehicle', label: t('category.vehicle', 'Vehicle'), emoji: '🚗', swissSpecific: false },
    { value: 'investment', label: t('category.investment', 'Investment'), emoji: '📈', swissSpecific: false },
    { value: 'debt_payoff', label: t('category.debt_payoff', 'Debt Payoff'), emoji: '💳', swissSpecific: false },
    { value: 'custom', label: t('category.custom', 'Custom Goal'), emoji: '🎯', swissSpecific: false },
  ], [t]);

  // Smart suggestions based on category
  const getSmartSuggestions = useCallback((category: SavingsGoalCategory) => {
    const suggestions = {
      emergency_fund: {
        targetAmount: 15000, // 3-6 months expenses for Swiss cost of living
        timelineMonths: 12,
        monthlyContribution: 1250,
        description: t('suggestion.emergency_fund', '3-6 months of living expenses for financial security'),
      },
      vacation: {
        targetAmount: 5000,
        timelineMonths: 8,
        monthlyContribution: 625,
        description: t('suggestion.vacation', 'Dream vacation fund for travel and experiences'),
      },
      home_purchase: {
        targetAmount: 200000, // Swiss down payment (20% of CHF 1M property)
        timelineMonths: 60,
        monthlyContribution: 3333,
        description: t('suggestion.home_purchase', 'Down payment for Swiss property purchase (20% minimum)'),
      },
      retirement: {
        targetAmount: 1000000,
        timelineMonths: 360, // 30 years
        monthlyContribution: 2778,
        description: t('suggestion.retirement', 'Long-term retirement savings beyond Pillar 1 & 2'),
      },
      pillar3a: {
        targetAmount: 7056, // 2024 annual limit
        timelineMonths: 12,
        monthlyContribution: 588,
        description: t('suggestion.pillar3a', 'Tax-advantaged retirement savings (annual limit CHF 7,056)'),
      },
      education: {
        targetAmount: 50000,
        timelineMonths: 48,
        monthlyContribution: 1042,
        description: t('suggestion.education', 'Education fund for personal or family learning'),
      },
      vehicle: {
        targetAmount: 25000,
        timelineMonths: 24,
        monthlyContribution: 1042,
        description: t('suggestion.vehicle', 'Vehicle purchase or replacement fund'),
      },
      investment: {
        targetAmount: 10000,
        timelineMonths: 18,
        monthlyContribution: 556,
        description: t('suggestion.investment', 'Investment capital for stocks, ETFs, or other assets'),
      },
      debt_payoff: {
        targetAmount: 15000,
        timelineMonths: 24,
        monthlyContribution: 625,
        description: t('suggestion.debt_payoff', 'Debt elimination for financial freedom'),
      },
      custom: {
        targetAmount: 10000,
        timelineMonths: 12,
        monthlyContribution: 833,
        description: t('suggestion.custom', 'Custom savings goal tailored to your needs'),
      },
    };
    return suggestions[category] || suggestions.custom;
  }, [t]);

  // Apply smart suggestions
  const applySuggestions = useCallback((category: SavingsGoalCategory) => {
    const suggestions = getSmartSuggestions(category);
    const targetDate = new Date();
    targetDate.setMonth(targetDate.getMonth() + suggestions.timelineMonths);

    setFormData(prev => ({
      ...prev,
      category,
      targetAmount: suggestions.targetAmount,
      monthlyContribution: suggestions.monthlyContribution,
      targetDate: targetDate.toISOString().split('T')[0],
      description: suggestions.description,
      swissSpecific: goalCategories.find(c => c.value === category)?.swissSpecific || false,
    }));
  }, [getSmartSuggestions, goalCategories]);

  // Generate milestones
  const generateMilestones = useCallback((): GoalMilestone[] => {
    const milestones: GoalMilestone[] = [];
    const percentages = [25, 50, 75, 100];
    
    percentages.forEach((percentage, index) => {
      const amount = (formData.targetAmount * percentage) / 100;
      const xpReward = Math.floor(amount / 100) + (percentage === 100 ? 500 : 0); // Bonus for completion
      
      milestones.push({
        id: `milestone_${percentage}`,
        percentage,
        amount,
        isReached: formData.currentAmount >= amount,
        reachedAt: formData.currentAmount >= amount ? new Date() : undefined,
        xpReward,
        celebrationShown: false,
      });
    });

    return milestones;
  }, [formData.targetAmount, formData.currentAmount]);

  // Validate current step
  const validateStep = useCallback((step: number): boolean => {
    switch (step) {
      case 0: // Type
        return formData.category !== '';
      case 1: // Details
        return formData.name.trim() !== '' && formData.targetAmount > 0 && formData.targetDate !== '';
      case 2: // Strategy
        return formData.monthlyContribution >= 0;
      case 3: // Settings
        return true;
      default:
        return false;
    }
  }, [formData]);

  // Handle next step
  const handleNext = useCallback(() => {
    if (validateStep(currentStep) && currentStep < wizardSteps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  }, [currentStep, validateStep, wizardSteps.length]);

  // Handle previous step
  const handlePrevious = useCallback(() => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  }, [currentStep]);

  // Handle save
  const handleSave = useCallback(() => {
    if (!validateStep(currentStep)) return;

    const goalData: Omit<SavingsGoal, 'id' | 'createdAt' | 'updatedAt'> = {
      userId,
      name: formData.name.trim(),
      description: formData.description.trim(),
      category: formData.category,
      targetAmount: formData.targetAmount,
      currentAmount: formData.currentAmount,
      monthlyContribution: formData.monthlyContribution,
      targetDate: new Date(formData.targetDate),
      priority: formData.priority,
      status: 'active',
      autoContribute: formData.autoContribute,
      swissSpecific: formData.swissSpecific,
      tags: formData.tags,
      milestones: generateMilestones(),
      linkedAccounts: [],
      sharingSettings: formData.sharingSettings,
      completedAt: undefined,
    };

    onSave(goalData);
  }, [currentStep, validateStep, userId, formData, generateMilestones, onSave]);

  // Render step content
  const renderStepContent = () => {
    switch (currentStep) {
      case 0: // Goal Type
        return (
          <div className="space-y-4">
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {goalCategories.map((category) => (
                <button
                  key={category.value}
                  onClick={() => applySuggestions(category.value as SavingsGoalCategory)}
                  className={`p-4 rounded-lg border-2 transition-all duration-200 ${
                    formData.category === category.value
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                      : darkMode
                      ? 'border-gray-600 bg-gray-800 hover:border-gray-500'
                      : 'border-gray-300 bg-white hover:border-gray-400'
                  }`}
                >
                  <div className="text-2xl mb-2">{category.emoji}</div>
                  <div className="text-sm font-medium">{category.label}</div>
                  {category.swissSpecific && (
                    <div className="text-xs text-red-600 dark:text-red-400 mt-1">🇨🇭 Swiss</div>
                  )}
                </button>
              ))}
            </div>
          </div>
        );

      case 1: // Goal Details
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">
                {t('wizard.goalName', 'Goal Name')} *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                className={`w-full px-3 py-2 rounded-lg border ${
                  darkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
                placeholder={t('wizard.goalNamePlaceholder', 'Enter your goal name')}
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">
                {t('wizard.description', 'Description')}
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                className={`w-full px-3 py-2 rounded-lg border ${
                  darkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
                rows={3}
                placeholder={t('wizard.descriptionPlaceholder', 'Describe your goal (optional)')}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">
                  {t('wizard.targetAmount', 'Target Amount (CHF)')} *
                </label>
                <input
                  type="number"
                  value={formData.targetAmount}
                  onChange={(e) => setFormData(prev => ({ ...prev, targetAmount: parseFloat(e.target.value) || 0 }))}
                  className={`w-full px-3 py-2 rounded-lg border ${
                    darkMode
                      ? 'bg-gray-700 border-gray-600 text-white'
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                  min="0"
                  step="0.01"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  {t('wizard.targetDate', 'Target Date')} *
                </label>
                <input
                  type="date"
                  value={formData.targetDate}
                  onChange={(e) => setFormData(prev => ({ ...prev, targetDate: e.target.value }))}
                  className={`w-full px-3 py-2 rounded-lg border ${
                    darkMode
                      ? 'bg-gray-700 border-gray-600 text-white'
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                  min={new Date().toISOString().split('T')[0]}
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">
                {t('wizard.priority', 'Priority')}
              </label>
              <select
                value={formData.priority}
                onChange={(e) => setFormData(prev => ({ ...prev, priority: e.target.value as SavingsGoalPriority }))}
                className={`w-full px-3 py-2 rounded-lg border ${
                  darkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              >
                <option value="low">{t('priority.low', 'Low')}</option>
                <option value="medium">{t('priority.medium', 'Medium')}</option>
                <option value="high">{t('priority.high', 'High')}</option>
                <option value="critical">{t('priority.critical', 'Critical')}</option>
              </select>
            </div>
          </div>
        );

      case 2: // Savings Strategy
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">
                {t('wizard.monthlyContribution', 'Monthly Contribution (CHF)')}
              </label>
              <input
                type="number"
                value={formData.monthlyContribution}
                onChange={(e) => setFormData(prev => ({ ...prev, monthlyContribution: parseFloat(e.target.value) || 0 }))}
                className={`w-full px-3 py-2 rounded-lg border ${
                  darkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
                min="0"
                step="0.01"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">
                {t('wizard.currentAmount', 'Current Amount (CHF)')}
              </label>
              <input
                type="number"
                value={formData.currentAmount}
                onChange={(e) => setFormData(prev => ({ ...prev, currentAmount: parseFloat(e.target.value) || 0 }))}
                className={`w-full px-3 py-2 rounded-lg border ${
                  darkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
                min="0"
                step="0.01"
              />
            </div>

            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="autoContribute"
                checked={formData.autoContribute}
                onChange={(e) => setFormData(prev => ({ ...prev, autoContribute: e.target.checked }))}
                className="rounded"
              />
              <label htmlFor="autoContribute" className="text-sm">
                {t('wizard.autoContribute', 'Enable automatic monthly contributions')}
              </label>
            </div>
          </div>
        );

      case 3: // Settings
        return (
          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-3">{t('wizard.sharingSettings', 'Sharing Settings')}</h4>
              <div className="space-y-2">
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={formData.sharingSettings.shareProgress}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      sharingSettings: { ...prev.sharingSettings, shareProgress: e.target.checked }
                    }))}
                    className="rounded"
                  />
                  <span className="text-sm">{t('wizard.shareProgress', 'Share progress with community')}</span>
                </label>
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={formData.sharingSettings.shareAchievements}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      sharingSettings: { ...prev.sharingSettings, shareAchievements: e.target.checked }
                    }))}
                    className="rounded"
                  />
                  <span className="text-sm">{t('wizard.shareAchievements', 'Share achievements')}</span>
                </label>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className={`savings-goal-wizard ${className}`}>
      <div className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-lg'}`}>
        {/* Header */}
        <div className="mb-6">
          <h2 className="text-xl font-bold mb-2">
            {existingGoal ? t('wizard.editGoal', 'Edit Goal') : t('wizard.createGoal', 'Create New Goal')}
          </h2>
          
          {/* Progress Steps */}
          <div className="flex items-center space-x-2 mb-4">
            {wizardSteps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  index <= currentStep
                    ? 'bg-blue-600 text-white'
                    : darkMode
                    ? 'bg-gray-600 text-gray-400'
                    : 'bg-gray-300 text-gray-600'
                }`}>
                  {index + 1}
                </div>
                {index < wizardSteps.length - 1 && (
                  <div className={`w-8 h-1 mx-2 ${
                    index < currentStep
                      ? 'bg-blue-600'
                      : darkMode
                      ? 'bg-gray-600'
                      : 'bg-gray-300'
                  }`} />
                )}
              </div>
            ))}
          </div>

          <div>
            <h3 className="font-semibold">{wizardSteps[currentStep].title}</h3>
            <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              {wizardSteps[currentStep].description}
            </p>
          </div>
        </div>

        {/* Step Content */}
        <div className="mb-6">
          {renderStepContent()}
        </div>

        {/* Navigation */}
        <div className="flex justify-between">
          <div>
            {currentStep > 0 && (
              <button
                onClick={handlePrevious}
                className={`px-4 py-2 rounded-lg transition-colors ${
                  darkMode
                    ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                {t('wizard.previous', 'Previous')}
              </button>
            )}
          </div>

          <div className="flex space-x-2">
            <button
              onClick={onCancel}
              className={`px-4 py-2 rounded-lg transition-colors ${
                darkMode
                  ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              {t('wizard.cancel', 'Cancel')}
            </button>

            {currentStep < wizardSteps.length - 1 ? (
              <button
                onClick={handleNext}
                disabled={!validateStep(currentStep)}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {t('wizard.next', 'Next')}
              </button>
            ) : (
              <button
                onClick={handleSave}
                disabled={!validateStep(currentStep)}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {existingGoal ? t('wizard.update', 'Update Goal') : t('wizard.create', 'Create Goal')}
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SavingsGoalWizard;
