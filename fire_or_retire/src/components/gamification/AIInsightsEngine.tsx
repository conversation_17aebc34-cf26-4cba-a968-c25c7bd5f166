import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useAIInsights } from '../../hooks/useAIInsights';
import { useGamification } from '../../hooks/useGamification';

interface AIInsightsEngineProps {
  userId: string;
  darkMode: boolean;
  className?: string;
}

type InsightType = 'optimization' | 'prediction' | 'recommendation' | 'warning' | 'opportunity';
type InsightCategory = 'xp' | 'goals' | 'streaks' | 'swiss' | 'behavioral' | 'financial';

interface AIInsight {
  id: string;
  type: InsightType;
  category: InsightCategory;
  title: string;
  description: string;
  confidence: number;
  impact: 'low' | 'medium' | 'high' | 'critical';
  actionable: boolean;
  actions?: {
    primary: string;
    secondary?: string;
  };
  data?: {
    currentValue: number;
    projectedValue: number;
    timeframe: string;
    improvement: number;
  };
  swissSpecific?: boolean;
  timestamp: Date;
}

const AIInsightsEngine: React.FC<AIInsightsEngineProps> = ({
  userId,
  darkMode,
  className = '',
}) => {
  const { t } = useTranslation();
  const [selectedCategory, setSelectedCategory] = useState<InsightCategory | 'all'>('all');
  const [insightHistory, setInsightHistory] = useState<AIInsight[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);

  const { progress } = useGamification(userId);
  const {
    insights,
    generateNewInsights,
    dismissInsight,
    implementRecommendation,
    getInsightHistory,
    isLoading,
  } = useAIInsights(userId);

  // Load insight history on component mount
  useEffect(() => {
    const loadHistory = async () => {
      const history = await getInsightHistory();
      setInsightHistory(history);
    };
    loadHistory();
  }, [getInsightHistory]);

  // Generate new insights periodically
  const handleGenerateInsights = useCallback(async () => {
    setIsGenerating(true);
    try {
      await generateNewInsights();
    } catch (error) {
      console.error('Failed to generate insights:', error);
    } finally {
      setIsGenerating(false);
    }
  }, [generateNewInsights]);

  const handleImplementAction = useCallback(async (insightId: string, action: string) => {
    try {
      await implementRecommendation(insightId, action);
      // Show success feedback
    } catch (error) {
      console.error('Failed to implement action:', error);
    }
  }, [implementRecommendation]);

  const getInsightIcon = (type: InsightType): string => {
    switch (type) {
      case 'optimization': return '⚡';
      case 'prediction': return '🔮';
      case 'recommendation': return '💡';
      case 'warning': return '⚠️';
      case 'opportunity': return '🎯';
      default: return 'ℹ️';
    }
  };

  const getCategoryIcon = (category: InsightCategory): string => {
    switch (category) {
      case 'xp': return '⚡';
      case 'goals': return '🎯';
      case 'streaks': return '🔥';
      case 'swiss': return '🇨🇭';
      case 'behavioral': return '🧠';
      case 'financial': return '💰';
      default: return '📊';
    }
  };

  const getImpactColor = (impact: string): string => {
    switch (impact) {
      case 'critical': return darkMode ? '#ef4444' : '#dc2626';
      case 'high': return darkMode ? '#f59e0b' : '#d97706';
      case 'medium': return darkMode ? '#3b82f6' : '#2563eb';
      case 'low': return darkMode ? '#6b7280' : '#4b5563';
      default: return darkMode ? '#6b7280' : '#4b5563';
    }
  };

  const filteredInsights = insights?.filter(insight => 
    selectedCategory === 'all' || insight.category === selectedCategory
  ) || [];

  const renderInsightCard = (insight: AIInsight) => (
    <div key={insight.id} className={`insight-card ${insight.type} ${insight.impact}`}>
      <div className="insight-header">
        <div className="insight-type">
          <span className="type-icon">{getInsightIcon(insight.type)}</span>
          <span className="category-icon">{getCategoryIcon(insight.category)}</span>
          {insight.swissSpecific && <span className="swiss-flag">🇨🇭</span>}
        </div>
        <div className="insight-meta">
          <div className="confidence-indicator">
            <div 
              className="confidence-bar"
              style={{ 
                width: `${insight.confidence}%`,
                backgroundColor: insight.confidence > 80 ? '#10b981' : insight.confidence > 60 ? '#f59e0b' : '#ef4444'
              }}
            />
            <span className="confidence-text">{insight.confidence}%</span>
          </div>
          <div 
            className="impact-badge"
            style={{ backgroundColor: getImpactColor(insight.impact) }}
          >
            {insight.impact.toUpperCase()}
          </div>
        </div>
      </div>

      <div className="insight-content">
        <h4 className="insight-title">{insight.title}</h4>
        <p className="insight-description">{insight.description}</p>

        {insight.data && (
          <div className="insight-data">
            <div className="data-visualization">
              <div className="data-item">
                <span className="data-label">{t('ai.current', 'Current')}</span>
                <span className="data-value">{insight.data.currentValue.toLocaleString()}</span>
              </div>
              <div className="data-arrow">→</div>
              <div className="data-item">
                <span className="data-label">{t('ai.projected', 'Projected')}</span>
                <span className="data-value projected">{insight.data.projectedValue.toLocaleString()}</span>
              </div>
              <div className="data-improvement">
                <span className="improvement-label">{t('ai.improvement', 'Improvement')}</span>
                <span className="improvement-value">
                  +{insight.data.improvement}% {t('ai.in', 'in')} {insight.data.timeframe}
                </span>
              </div>
            </div>
          </div>
        )}

        {insight.actionable && insight.actions && (
          <div className="insight-actions">
            <button 
              className="action-btn primary"
              onClick={() => handleImplementAction(insight.id, insight.actions!.primary)}
            >
              {insight.actions.primary}
            </button>
            {insight.actions.secondary && (
              <button 
                className="action-btn secondary"
                onClick={() => handleImplementAction(insight.id, insight.actions!.secondary)}
              >
                {insight.actions.secondary}
              </button>
            )}
          </div>
        )}
      </div>

      <div className="insight-footer">
        <div className="insight-timestamp">
          {insight.timestamp.toLocaleDateString()} {insight.timestamp.toLocaleTimeString()}
        </div>
        <button 
          className="dismiss-btn"
          onClick={() => dismissInsight(insight.id)}
          title={t('ai.dismiss', 'Dismiss insight')}
        >
          ×
        </button>
      </div>
    </div>
  );

  const renderInsightSummary = () => {
    const criticalInsights = filteredInsights.filter(i => i.impact === 'critical').length;
    const highImpactInsights = filteredInsights.filter(i => i.impact === 'high').length;
    const opportunities = filteredInsights.filter(i => i.type === 'opportunity').length;
    const swissSpecific = filteredInsights.filter(i => i.swissSpecific).length;

    return (
      <div className="insights-summary">
        <div className="summary-card critical">
          <div className="summary-icon">⚠️</div>
          <div className="summary-content">
            <div className="summary-number">{criticalInsights}</div>
            <div className="summary-label">{t('ai.criticalInsights', 'Critical Insights')}</div>
          </div>
        </div>

        <div className="summary-card high">
          <div className="summary-icon">🔥</div>
          <div className="summary-content">
            <div className="summary-number">{highImpactInsights}</div>
            <div className="summary-label">{t('ai.highImpact', 'High Impact')}</div>
          </div>
        </div>

        <div className="summary-card opportunity">
          <div className="summary-icon">🎯</div>
          <div className="summary-content">
            <div className="summary-number">{opportunities}</div>
            <div className="summary-label">{t('ai.opportunities', 'Opportunities')}</div>
          </div>
        </div>

        <div className="summary-card swiss">
          <div className="summary-icon">🇨🇭</div>
          <div className="summary-content">
            <div className="summary-number">{swissSpecific}</div>
            <div className="summary-label">{t('ai.swissSpecific', 'Swiss Specific')}</div>
          </div>
        </div>
      </div>
    );
  };

  const renderAICoach = () => (
    <div className="ai-coach-section">
      <div className="coach-header">
        <div className="coach-avatar">🤖</div>
        <div className="coach-info">
          <h3>{t('ai.coachName', 'Swiss Finance AI Coach')}</h3>
          <p>{t('ai.coachDescription', 'Your personal AI assistant for Swiss financial optimization')}</p>
        </div>
      </div>

      <div className="coach-insights">
        <div className="coach-message">
          <div className="message-content">
            <h4>{t('ai.personalizedRecommendation', 'Personalized Recommendation')}</h4>
            <p>
              {t('ai.coachMessage', 
                'Based on your current progress (Level {{level}}, {{xp}} XP), I recommend focusing on Pillar 3a optimization. You could earn an additional {{bonusXP}} XP this month while saving CHF {{savings}} in taxes.',
                { 
                  level: progress?.currentLevel || 0,
                  xp: progress?.totalXP.toLocaleString() || '0',
                  bonusXP: '420',
                  savings: '1,200'
                }
              )}
            </p>
          </div>
          <div className="message-actions">
            <button className="coach-action-btn">
              {t('ai.showMeHow', 'Show Me How')}
            </button>
            <button className="coach-action-btn secondary">
              {t('ai.learnMore', 'Learn More')}
            </button>
          </div>
        </div>
      </div>

      <div className="coach-stats">
        <div className="stat-item">
          <span className="stat-label">{t('ai.insightsGenerated', 'Insights Generated')}</span>
          <span className="stat-value">{insightHistory.length}</span>
        </div>
        <div className="stat-item">
          <span className="stat-label">{t('ai.actionsImplemented', 'Actions Implemented')}</span>
          <span className="stat-value">{insightHistory.filter(i => i.actionable).length}</span>
        </div>
        <div className="stat-item">
          <span className="stat-label">{t('ai.avgConfidence', 'Avg Confidence')}</span>
          <span className="stat-value">
            {insightHistory.length > 0 ? 
              Math.round(insightHistory.reduce((sum, i) => sum + i.confidence, 0) / insightHistory.length) : 0
            }%
          </span>
        </div>
      </div>
    </div>
  );

  if (isLoading) {
    return (
      <div className={`ai-insights-engine loading ${className}`}>
        <div className="loading-spinner" />
        <p>{t('ai.loadingInsights', 'AI is analyzing your financial data...')}</p>
      </div>
    );
  }

  return (
    <div className={`ai-insights-engine ${className}`}>
      <div className="insights-header">
        <div className="header-content">
          <h2 className="insights-title">
            🤖 {t('ai.title', 'AI Financial Insights')}
          </h2>
          <p className="insights-subtitle">
            {t('ai.subtitle', 'Personalized recommendations powered by Swiss financial expertise')}
          </p>
        </div>
        <div className="header-actions">
          <button 
            className="generate-insights-btn"
            onClick={handleGenerateInsights}
            disabled={isGenerating}
          >
            {isGenerating ? (
              <>
                <span className="spinner" />
                {t('ai.generating', 'Generating...')}
              </>
            ) : (
              <>
                ⚡ {t('ai.generateInsights', 'Generate New Insights')}
              </>
            )}
          </button>
        </div>
      </div>

      {renderInsightSummary()}
      {renderAICoach()}

      <div className="insights-content">
        <div className="insights-filters">
          <div className="category-filters">
            {[
              { key: 'all', label: 'All Insights', icon: '📊' },
              { key: 'xp', label: 'XP Optimization', icon: '⚡' },
              { key: 'goals', label: 'Goal Progress', icon: '🎯' },
              { key: 'streaks', label: 'Streak Building', icon: '🔥' },
              { key: 'swiss', label: 'Swiss Features', icon: '🇨🇭' },
              { key: 'behavioral', label: 'Behavioral', icon: '🧠' },
              { key: 'financial', label: 'Financial', icon: '💰' },
            ].map(category => (
              <button
                key={category.key}
                className={`category-filter ${selectedCategory === category.key ? 'active' : ''}`}
                onClick={() => setSelectedCategory(category.key as any)}
              >
                <span className="filter-icon">{category.icon}</span>
                <span className="filter-label">{category.label}</span>
                <span className="filter-count">
                  {category.key === 'all' ? 
                    filteredInsights.length : 
                    insights?.filter(i => i.category === category.key).length || 0
                  }
                </span>
              </button>
            ))}
          </div>
        </div>

        <div className="insights-list">
          {filteredInsights.length > 0 ? (
            filteredInsights
              .sort((a, b) => {
                // Sort by impact (critical first), then by confidence
                const impactOrder = { critical: 4, high: 3, medium: 2, low: 1 };
                const impactDiff = impactOrder[b.impact] - impactOrder[a.impact];
                if (impactDiff !== 0) return impactDiff;
                return b.confidence - a.confidence;
              })
              .map(renderInsightCard)
          ) : (
            <div className="no-insights">
              <div className="no-insights-icon">🤖</div>
              <h3>{t('ai.noInsights', 'No insights available')}</h3>
              <p>
                {t('ai.noInsightsDescription', 
                  'Generate new insights to get personalized recommendations for optimizing your financial journey.'
                )}
              </p>
              <button 
                className="generate-first-insights-btn"
                onClick={handleGenerateInsights}
                disabled={isGenerating}
              >
                {t('ai.generateFirstInsights', 'Generate Your First Insights')}
              </button>
            </div>
          )}
        </div>
      </div>

      <div className="insights-footer">
        <div className="ai-disclaimer">
          <p>
            {t('ai.disclaimer', 
              'AI insights are based on your financial data and Swiss market analysis. Always consult with a qualified financial advisor for major decisions.'
            )}
          </p>
        </div>
      </div>
    </div>
  );
};

export default AIInsightsEngine;
