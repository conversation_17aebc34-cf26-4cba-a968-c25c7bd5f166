import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Achievement, UserAchievement, AchievementRarity } from '../../types/gamification';

interface AchievementCardProps {
  achievement: Achievement;
  userAchievement?: UserAchievement;
  darkMode: boolean;
  showProgress?: boolean;
  size?: 'small' | 'medium' | 'large';
  onClick?: (achievement: Achievement) => void;
  className?: string;
}

const AchievementCard: React.FC<AchievementCardProps> = ({
  achievement,
  userAchievement,
  darkMode,
  showProgress = true,
  size = 'medium',
  onClick,
  className = '',
}) => {
  const { t } = useTranslation();
  const [isAnimating, setIsAnimating] = useState(false);

  const isUnlocked = userAchievement?.isCompleted || false;
  const progress = userAchievement?.progress || 0;
  const isSecret = achievement.isSecret && !isUnlocked;

  // Trigger animation when achievement is unlocked
  useEffect(() => {
    if (isUnlocked && userAchievement?.unlockedAt) {
      const unlockTime = new Date(userAchievement.unlockedAt).getTime();
      const now = new Date().getTime();
      const timeDiff = now - unlockTime;
      
      // If unlocked within the last 5 seconds, show animation
      if (timeDiff < 5000) {
        setIsAnimating(true);
        setTimeout(() => setIsAnimating(false), 2000);
      }
    }
  }, [isUnlocked, userAchievement?.unlockedAt]);

  // Get rarity colors and styles
  const getRarityStyles = (rarity: AchievementRarity) => {
    const styles = {
      common: {
        border: 'border-gray-400',
        bg: 'bg-gray-100 dark:bg-gray-800',
        glow: 'shadow-gray-400/20',
        text: 'text-gray-600 dark:text-gray-400',
      },
      rare: {
        border: 'border-blue-400',
        bg: 'bg-blue-100 dark:bg-blue-900/20',
        glow: 'shadow-blue-400/30',
        text: 'text-blue-600 dark:text-blue-400',
      },
      epic: {
        border: 'border-purple-400',
        bg: 'bg-purple-100 dark:bg-purple-900/20',
        glow: 'shadow-purple-400/30',
        text: 'text-purple-600 dark:text-purple-400',
      },
      legendary: {
        border: 'border-yellow-400',
        bg: 'bg-yellow-100 dark:bg-yellow-900/20',
        glow: 'shadow-yellow-400/40',
        text: 'text-yellow-600 dark:text-yellow-400',
      },
    };
    return styles[rarity];
  };

  // Get size classes
  const getSizeClasses = () => {
    const sizes = {
      small: {
        container: 'p-3',
        icon: 'text-2xl',
        title: 'text-sm font-medium',
        description: 'text-xs',
        xp: 'text-xs',
      },
      medium: {
        container: 'p-4',
        icon: 'text-3xl',
        title: 'text-base font-semibold',
        description: 'text-sm',
        xp: 'text-sm',
      },
      large: {
        container: 'p-6',
        icon: 'text-4xl',
        title: 'text-lg font-bold',
        description: 'text-base',
        xp: 'text-base',
      },
    };
    return sizes[size];
  };

  const rarityStyles = getRarityStyles(achievement.rarity);
  const sizeClasses = getSizeClasses();

  return (
    <div
      className={`achievement-card ${className} ${
        isAnimating ? 'animate-bounce' : ''
      } ${onClick ? 'cursor-pointer' : ''}`}
      onClick={() => onClick?.(achievement)}
    >
      <div
        className={`
          relative rounded-lg border-2 transition-all duration-300
          ${rarityStyles.border}
          ${isUnlocked ? rarityStyles.bg : darkMode ? 'bg-gray-800' : 'bg-gray-100'}
          ${isUnlocked ? `shadow-lg ${rarityStyles.glow}` : 'shadow-sm'}
          ${!isUnlocked ? 'opacity-60' : ''}
          ${onClick ? 'hover:scale-105 hover:shadow-xl' : ''}
          ${sizeClasses.container}
        `}
      >
        {/* Rarity indicator */}
        <div className={`absolute top-2 right-2 px-2 py-1 rounded-full text-xs font-medium ${
          rarityStyles.bg
        } ${rarityStyles.text}`}>
          {t(`rarity.${achievement.rarity}`, achievement.rarity)}
        </div>

        {/* Swiss indicator */}
        {achievement.isSwissSpecific && (
          <div className="absolute top-2 left-2 text-xs">
            🇨🇭
          </div>
        )}

        {/* Unlock animation overlay */}
        {isAnimating && (
          <div className="absolute inset-0 bg-gradient-to-r from-yellow-400/20 to-orange-400/20 rounded-lg animate-pulse" />
        )}

        {/* Content */}
        <div className="relative z-10">
          {/* Icon */}
          <div className="text-center mb-3">
            <div className={`${sizeClasses.icon} ${isSecret ? 'filter blur-sm' : ''}`}>
              {isSecret ? '❓' : achievement.icon}
            </div>
          </div>

          {/* Title and Description */}
          <div className="text-center mb-3">
            <h3 className={`${sizeClasses.title} ${
              isUnlocked ? '' : darkMode ? 'text-gray-400' : 'text-gray-600'
            }`}>
              {isSecret ? t('achievement.secret', 'Secret Achievement') : achievement.name}
            </h3>
            <p className={`${sizeClasses.description} ${
              darkMode ? 'text-gray-400' : 'text-gray-600'
            } mt-1`}>
              {isSecret 
                ? t('achievement.secretDescription', 'Complete certain actions to unlock')
                : achievement.description
              }
            </p>
          </div>

          {/* Progress Bar */}
          {showProgress && !isSecret && userAchievement && progress < 100 && (
            <div className="mb-3">
              <div className="flex justify-between text-xs mb-1">
                <span>{t('achievement.progress', 'Progress')}</span>
                <span>{progress.toFixed(0)}%</span>
              </div>
              <div className={`w-full bg-gray-300 rounded-full h-2 ${
                darkMode ? 'bg-gray-600' : ''
              }`}>
                <div
                  className={`h-2 rounded-full transition-all duration-500 ${
                    achievement.rarity === 'legendary' ? 'bg-gradient-to-r from-yellow-400 to-orange-500' :
                    achievement.rarity === 'epic' ? 'bg-gradient-to-r from-purple-400 to-pink-500' :
                    achievement.rarity === 'rare' ? 'bg-gradient-to-r from-blue-400 to-cyan-500' :
                    'bg-gradient-to-r from-gray-400 to-gray-500'
                  }`}
                  style={{ width: `${progress}%` }}
                />
              </div>
            </div>
          )}

          {/* XP Reward */}
          {!isSecret && (
            <div className="flex items-center justify-between">
              <div className={`${sizeClasses.xp} ${rarityStyles.text} font-medium`}>
                +{achievement.xpReward} XP
              </div>
              
              {/* Unlock status */}
              <div className="flex items-center space-x-1">
                {isUnlocked ? (
                  <>
                    <span className="text-green-500">✓</span>
                    <span className={`${sizeClasses.xp} text-green-600 dark:text-green-400`}>
                      {t('achievement.unlocked', 'Unlocked')}
                    </span>
                  </>
                ) : (
                  <span className={`${sizeClasses.xp} ${
                    darkMode ? 'text-gray-500' : 'text-gray-400'
                  }`}>
                    {t('achievement.locked', 'Locked')}
                  </span>
                )}
              </div>
            </div>
          )}

          {/* Unlock date */}
          {isUnlocked && userAchievement?.unlockedAt && size !== 'small' && (
            <div className={`mt-2 text-xs ${
              darkMode ? 'text-gray-500' : 'text-gray-400'
            } text-center`}>
              {t('achievement.unlockedOn', 'Unlocked on')} {
                new Date(userAchievement.unlockedAt).toLocaleDateString()
              }
            </div>
          )}
        </div>

        {/* Legendary glow effect */}
        {isUnlocked && achievement.rarity === 'legendary' && (
          <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-yellow-400/10 to-orange-400/10 animate-pulse" />
        )}

        {/* Epic sparkle effect */}
        {isUnlocked && achievement.rarity === 'epic' && (
          <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-purple-400/10 to-pink-400/10" />
        )}
      </div>
    </div>
  );
};

export default AchievementCard;
