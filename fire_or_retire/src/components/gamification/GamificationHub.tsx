import React, { useState, useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useGamification } from '../../hooks/useGamification';
import GamificationDashboard from './GamificationDashboard';
import SavingsGoalsDashboard from './SavingsGoalsDashboard';
import AchievementsShowcase from './AchievementsShowcase';
import StreakTracker from './StreakTracker';
import TaggingInsights from './TaggingInsights';
import AchievementNotification from './AchievementNotification';

interface GamificationHubProps {
  userId: string;
  darkMode: boolean;
  className?: string;
}

type ActiveTab = 'dashboard' | 'goals' | 'achievements' | 'streaks' | 'insights';

const GamificationHub: React.FC<GamificationHubProps> = ({
  userId,
  darkMode,
  className = '',
}) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<ActiveTab>('dashboard');
  const [showWelcome, setShowWelcome] = useState(false);

  const {
    progress,
    isLoading,
    error,
    recentLevelUp,
    recentAchievements,
    recentXPGain,
    awardSavingsXP,
    awardGoalAchievementXP,
    awardStreakXP,
    awardSwissOptimizationXP,
    dismissLevelUp,
    dismissAchievement,
    refreshProgress,
  } = useGamification(userId);

  // Check if user is new and show welcome
  useEffect(() => {
    if (progress && progress.totalXP === 0 && progress.currentLevel === 1) {
      setShowWelcome(true);
    }
  }, [progress]);

  // Handle XP awards from child components
  const handleXPAwarded = useCallback(async (xp: number, description: string, source?: string) => {
    try {
      switch (source) {
        case 'savings':
          await awardSavingsXP(xp);
          break;
        case 'goal':
          await awardGoalAchievementXP(xp, new Date());
          break;
        case 'streak':
          await awardStreakXP(7); // Default 7-day streak
          break;
        case 'swiss':
          await awardSwissOptimizationXP('pillar3a', xp);
          break;
        default:
          // Generic XP award - would need to extend the hook for this
          console.log(`Awarded ${xp} XP: ${description}`);
      }
    } catch (error) {
      console.error('Failed to award XP:', error);
    }
  }, [awardSavingsXP, awardGoalAchievementXP, awardStreakXP, awardSwissOptimizationXP]);

  // Tab configuration
  const tabs = [
    {
      id: 'dashboard' as ActiveTab,
      name: t('gamification.tabs.dashboard', 'Dashboard'),
      icon: '🎮',
      description: t('gamification.tabs.dashboard.desc', 'Overview and progress'),
    },
    {
      id: 'goals' as ActiveTab,
      name: t('gamification.tabs.goals', 'Goals'),
      icon: '🎯',
      description: t('gamification.tabs.goals.desc', 'Savings goals and milestones'),
    },
    {
      id: 'achievements' as ActiveTab,
      name: t('gamification.tabs.achievements', 'Achievements'),
      icon: '🏆',
      description: t('gamification.tabs.achievements.desc', 'Badges and accomplishments'),
    },
    {
      id: 'streaks' as ActiveTab,
      name: t('gamification.tabs.streaks', 'Streaks'),
      icon: '🔥',
      description: t('gamification.tabs.streaks.desc', 'Activity streaks and bonuses'),
    },
    {
      id: 'insights' as ActiveTab,
      name: t('gamification.tabs.insights', 'Insights'),
      icon: '📊',
      description: t('gamification.tabs.insights.desc', 'Smart tagging and analytics'),
    },
  ];

  // Render active tab content
  const renderTabContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return (
          <GamificationDashboard
            userId={userId}
            darkMode={darkMode}
            showDetailedStats={true}
            showQuickActions={true}
          />
        );
      case 'goals':
        return (
          <SavingsGoalsDashboard
            userId={userId}
            darkMode={darkMode}
          />
        );
      case 'achievements':
        return (
          <AchievementsShowcase
            userId={userId}
            darkMode={darkMode}
          />
        );
      case 'streaks':
        return (
          <StreakTracker
            userId={userId}
            darkMode={darkMode}
            onXPAwarded={(xp, description) => handleXPAwarded(xp, description, 'streak')}
          />
        );
      case 'insights':
        return (
          <TaggingInsights
            userId={userId}
            darkMode={darkMode}
          />
        );
      default:
        return null;
    }
  };

  if (isLoading) {
    return (
      <div className={`gamification-hub ${className}`}>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`gamification-hub ${className}`}>
        <div className={`p-6 rounded-lg border-l-4 border-red-500 ${
          darkMode ? 'bg-red-900/20' : 'bg-red-50'
        }`}>
          <h3 className="font-bold text-red-600 mb-2">
            {t('gamification.error.title', 'Gamification Error')}
          </h3>
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={refreshProgress}
            className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            {t('gamification.error.retry', 'Retry')}
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`gamification-hub ${className}`}>
      {/* Welcome Modal for New Users */}
      {showWelcome && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className={`max-w-lg w-full rounded-lg p-6 ${darkMode ? 'bg-gray-800' : 'bg-white'}`}>
            <div className="text-center">
              <div className="text-6xl mb-4">🎮</div>
              <h2 className="text-2xl font-bold mb-4">
                {t('gamification.welcome.title', 'Welcome to Swiss Budget Pro!')}
              </h2>
              <p className="mb-6 text-gray-600 dark:text-gray-400">
                {t('gamification.welcome.description', 
                  'Transform your financial journey into an engaging game! Earn XP, unlock achievements, and reach your Swiss financial goals with our gamified experience.'
                )}
              </p>
              <div className="grid grid-cols-2 gap-4 mb-6 text-sm">
                <div className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                  <div className="text-lg mb-1">💰</div>
                  <div className="font-medium">Earn XP</div>
                  <div className="text-gray-600 dark:text-gray-400">Save money and gain experience</div>
                </div>
                <div className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                  <div className="text-lg mb-1">🏆</div>
                  <div className="font-medium">Unlock Achievements</div>
                  <div className="text-gray-600 dark:text-gray-400">Complete financial milestones</div>
                </div>
                <div className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                  <div className="text-lg mb-1">🔥</div>
                  <div className="font-medium">Build Streaks</div>
                  <div className="text-gray-600 dark:text-gray-400">Maintain consistent habits</div>
                </div>
                <div className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                  <div className="text-lg mb-1">🇨🇭</div>
                  <div className="font-medium">Swiss Optimization</div>
                  <div className="text-gray-600 dark:text-gray-400">Maximize Swiss benefits</div>
                </div>
              </div>
              <button
                onClick={() => setShowWelcome(false)}
                className="w-full px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
              >
                {t('gamification.welcome.start', 'Start Your Journey!')}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Achievement Notifications */}
      {recentAchievements.map((userAchievement) => {
        // In a real implementation, you'd get the achievement details from the service
        const achievement = {
          id: userAchievement.achievementId,
          name: 'Achievement Unlocked',
          description: 'You\'ve earned a new achievement!',
          icon: '🏆',
          rarity: 'common' as const,
          xpReward: 100,
          isSwissSpecific: false,
        };

        return (
          <AchievementNotification
            key={userAchievement.id}
            achievement={achievement}
            userAchievement={userAchievement}
            darkMode={darkMode}
            onDismiss={() => dismissAchievement(userAchievement.achievementId)}
            autoHide={true}
            autoHideDelay={5000}
            showConfetti={true}
          />
        );
      })}

      {/* Header with Progress Summary */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-3xl font-bold">
            🎮 {t('gamification.title', 'Gamification Hub')}
          </h1>
          {progress && (
            <div className="flex items-center space-x-4">
              <div className={`px-4 py-2 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}>
                <div className="text-sm text-gray-600 dark:text-gray-400">Level</div>
                <div className="text-xl font-bold text-blue-600">{progress.currentLevel}</div>
              </div>
              <div className={`px-4 py-2 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}>
                <div className="text-sm text-gray-600 dark:text-gray-400">Total XP</div>
                <div className="text-xl font-bold text-green-600">{progress.totalXP.toLocaleString()}</div>
              </div>
              {recentXPGain > 0 && (
                <div className="px-3 py-1 bg-yellow-500 text-white rounded-full text-sm font-medium animate-bounce">
                  +{recentXPGain} XP
                </div>
              )}
            </div>
          )}
        </div>

        {/* Tab Navigation */}
        <div className="flex space-x-1 overflow-x-auto">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all whitespace-nowrap ${
                activeTab === tab.id
                  ? 'bg-blue-600 text-white shadow-md'
                  : darkMode
                  ? 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                  : 'bg-white text-gray-700 hover:bg-gray-50 shadow-sm'
              }`}
            >
              <span>{tab.icon}</span>
              <span>{tab.name}</span>
            </button>
          ))}
        </div>

        {/* Tab Description */}
        <div className={`mt-2 text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
          {tabs.find(tab => tab.id === activeTab)?.description}
        </div>
      </div>

      {/* Tab Content */}
      <div className="min-h-[600px]">
        {renderTabContent()}
      </div>

      {/* Quick Actions Floating Button */}
      <div className="fixed bottom-6 right-6 z-40">
        <div className="relative">
          <button
            className="w-14 h-14 bg-blue-600 text-white rounded-full shadow-lg hover:bg-blue-700 transition-all hover:scale-110 flex items-center justify-center"
            onClick={() => {
              // Quick action menu - could expand to show common actions
              handleXPAwarded(10, 'Quick action bonus', 'savings');
            }}
          >
            ⚡
          </button>
          {recentXPGain > 0 && (
            <div className="absolute -top-2 -right-2 w-6 h-6 bg-yellow-500 text-white rounded-full text-xs flex items-center justify-center font-bold animate-pulse">
              +
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default GamificationHub;
