import React, { useState, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

interface MonteCarloSimulationProps {
  darkMode: boolean;
  userData: {
    currentAge: number;
    retirementAge: number;
    currentSavings: number;
    monthlyIncome: number;
    monthlyExpenses: number;
    expectedReturn: number;
    inflationRate: number;
    safeWithdrawalRate: number;
  };
}

interface SimulationResult {
  successRate: number;
  percentiles: {
    p10: number;
    p25: number;
    p50: number;
    p75: number;
    p90: number;
  };
  averageBalance: number;
  worstCase: number;
  bestCase: number;
  riskMetrics: {
    probabilityOfRuin: number;
    expectedShortfall: number;
    valueAtRisk: number;
  };
  iterations: number;
}

interface StressTestScenario {
  name: string;
  expectedReturn: number;
  volatility: number;
  inflationRate: number;
  inflationVolatility: number;
  economicShocks?: {
    probability: number;
    severity: number;
  };
}

const MonteCarloSimulation: React.FC<MonteCarloSimulationProps> = ({
  darkMode,
  userData,
}) => {
  const { t } = useTranslation();
  const [isRunning, setIsRunning] = useState(false);
  const [results, setResults] = useState<SimulationResult | null>(null);
  const [selectedScenario, setSelectedScenario] = useState<string>('baseCase');
  const [iterations, setIterations] = useState(1000);
  const [volatility, setVolatility] = useState(15);

  const stressTestScenarios: Record<string, StressTestScenario> = {
    baseCase: {
      name: 'Base Case',
      expectedReturn: userData.expectedReturn,
      volatility: 15.0,
      inflationRate: userData.inflationRate,
      inflationVolatility: 1.0,
    },
    bearMarket: {
      name: 'Bear Market',
      expectedReturn: userData.expectedReturn - 2,
      volatility: 25.0,
      inflationRate: userData.inflationRate - 1,
      inflationVolatility: 1.5,
      economicShocks: { probability: 0.15, severity: -0.3 },
    },
    highInflation: {
      name: 'High Inflation',
      expectedReturn: userData.expectedReturn + 1,
      volatility: 20.0,
      inflationRate: userData.inflationRate + 2,
      inflationVolatility: 2.0,
      economicShocks: { probability: 0.1, severity: -0.2 },
    },
    recession: {
      name: 'Recession Scenario',
      expectedReturn: userData.expectedReturn - 3,
      volatility: 30.0,
      inflationRate: userData.inflationRate - 1.5,
      inflationVolatility: 2.0,
      economicShocks: { probability: 0.25, severity: -0.4 },
    },
    stagflation: {
      name: 'Stagflation',
      expectedReturn: userData.expectedReturn - 4,
      volatility: 25.0,
      inflationRate: userData.inflationRate + 3,
      inflationVolatility: 3.0,
      economicShocks: { probability: 0.2, severity: -0.25 },
    },
  };

  // Generate random returns using Box-Muller transformation
  const generateRandomReturn = (
    meanReturn: number,
    volatility: number
  ): number => {
    const u1 = Math.random();
    const u2 = Math.random();
    const z0 = Math.sqrt(-2 * Math.log(u1)) * Math.cos(2 * Math.PI * u2);
    return meanReturn + (volatility / 100) * z0;
  };

  // Run Monte Carlo simulation
  const runSimulation = async (
    scenario: StressTestScenario
  ): Promise<SimulationResult> => {
    const {
      currentAge,
      retirementAge,
      currentSavings,
      monthlyIncome,
      monthlyExpenses,
    } = userData;

    const yearsToRetirement = retirementAge - currentAge;
    const monthlyContribution = monthlyIncome - monthlyExpenses;
    const targetAmount = monthlyExpenses * 12 * 25; // 4% rule
    const results: any[] = [];

    for (let i = 0; i < iterations; i++) {
      let balance = currentSavings;
      let realBalance = currentSavings;
      let monthlyContrib = monthlyContribution;
      let cumulativeInflation = 1;

      for (let year = 0; year < yearsToRetirement; year++) {
        // Generate random returns and inflation
        const annualReturn = generateRandomReturn(
          scenario.expectedReturn / 100,
          scenario.volatility
        );
        const annualInflation = generateRandomReturn(
          scenario.inflationRate / 100,
          scenario.inflationVolatility
        );

        // Apply economic shocks
        let shockMultiplier = 1;
        if (
          scenario.economicShocks &&
          Math.random() < scenario.economicShocks.probability
        ) {
          shockMultiplier = 1 + scenario.economicShocks.severity;
        }

        const adjustedReturn = annualReturn * shockMultiplier;

        // Calculate year-end balance
        const yearStartBalance = balance;
        const annualContributions = monthlyContrib * 12;

        // Apply returns to average balance throughout year
        const avgBalance = yearStartBalance + annualContributions / 2;
        const investmentGrowth = avgBalance * adjustedReturn;

        balance = yearStartBalance + annualContributions + investmentGrowth;

        // Track real purchasing power
        cumulativeInflation *= 1 + annualInflation;
        realBalance = balance / cumulativeInflation;

        // Adjust future contributions for inflation
        monthlyContrib *= 1 + annualInflation;
      }

      const finalRealBalance = realBalance;
      const success = finalRealBalance >= targetAmount;
      const shortfall = success ? 0 : targetAmount - finalRealBalance;

      results.push({
        finalRealBalance,
        success,
        shortfall,
      });
    }

    return analyzeResults(results, targetAmount);
  };

  // Analyze simulation results
  const analyzeResults = (
    results: any[],
    targetAmount: number
  ): SimulationResult => {
    const successfulRuns = results.filter(r => r.success);
    const successRate = (successfulRuns.length / results.length) * 100;

    const finalBalances = results.map(r => r.finalRealBalance);
    finalBalances.sort((a, b) => a - b);

    const percentiles = {
      p10: finalBalances[Math.floor(results.length * 0.1)],
      p25: finalBalances[Math.floor(results.length * 0.25)],
      p50: finalBalances[Math.floor(results.length * 0.5)],
      p75: finalBalances[Math.floor(results.length * 0.75)],
      p90: finalBalances[Math.floor(results.length * 0.9)],
    };

    const averageBalance =
      finalBalances.reduce((sum, bal) => sum + bal, 0) / finalBalances.length;
    const shortfalls = results.filter(r => !r.success).map(r => r.shortfall);
    const averageShortfall =
      shortfalls.length > 0
        ? shortfalls.reduce((sum, s) => sum + s, 0) / shortfalls.length
        : 0;

    const worstCase = finalBalances[0];
    const bestCase = finalBalances[finalBalances.length - 1];

    return {
      successRate,
      percentiles,
      averageBalance,
      worstCase,
      bestCase,
      riskMetrics: {
        probabilityOfRuin:
          ((results.length - successfulRuns.length) / results.length) * 100,
        expectedShortfall: averageShortfall,
        valueAtRisk: targetAmount - percentiles.p10,
      },
      iterations: results.length,
    };
  };

  const handleRunSimulation = async () => {
    setIsRunning(true);
    try {
      const scenario = stressTestScenarios[selectedScenario];
      const result = await runSimulation(scenario);
      setResults(result);
    } catch (error) {
      console.error('Simulation failed:', error);
    } finally {
      setIsRunning(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('de-CH', {
      style: 'currency',
      currency: 'CHF',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getSuccessRateColor = (rate: number) => {
    if (rate >= 90) return 'text-green-600';
    if (rate >= 75) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div>
        <h2 className='text-2xl font-bold mb-2'>🎲 Monte Carlo Simulation</h2>
        <p className='text-gray-600 dark:text-gray-400'>
          Advanced probabilistic analysis of your FIRE plan using thousands of
          market scenarios
        </p>
      </div>

      {/* Configuration */}
      <div
        className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
      >
        <h3 className='text-lg font-semibold mb-4'>Simulation Parameters</h3>

        <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
          <div>
            <label className='block text-sm font-medium mb-2'>Scenario</label>
            <select
              value={selectedScenario}
              onChange={e => setSelectedScenario(e.target.value)}
              className={`w-full px-3 py-2 rounded-md border ${
                darkMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              }`}
            >
              {Object.entries(stressTestScenarios).map(([key, scenario]) => (
                <option key={key} value={key}>
                  {scenario.name}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className='block text-sm font-medium mb-2'>Iterations</label>
            <select
              value={iterations}
              onChange={e => setIterations(parseInt(e.target.value))}
              className={`w-full px-3 py-2 rounded-md border ${
                darkMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              }`}
            >
              <option value={500}>500 (Fast)</option>
              <option value={1000}>1,000 (Standard)</option>
              <option value={5000}>5,000 (Detailed)</option>
              <option value={10000}>10,000 (Comprehensive)</option>
            </select>
          </div>

          <div className='flex items-end'>
            <button
              onClick={handleRunSimulation}
              disabled={isRunning}
              className={`w-full px-4 py-2 rounded-md font-medium transition-colors ${
                isRunning
                  ? 'bg-gray-400 cursor-not-allowed'
                  : 'bg-blue-600 hover:bg-blue-700'
              } text-white`}
            >
              {isRunning ? '🔄 Running...' : '🚀 Run Simulation'}
            </button>
          </div>
        </div>

        {/* Scenario Details */}
        <div className='mt-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg'>
          <h4 className='font-medium mb-2'>
            Selected Scenario: {stressTestScenarios[selectedScenario].name}
          </h4>
          <div className='grid grid-cols-2 md:grid-cols-4 gap-4 text-sm'>
            <div>
              <span className='text-gray-600 dark:text-gray-400'>
                Expected Return:
              </span>
              <div className='font-semibold'>
                {stressTestScenarios[selectedScenario].expectedReturn.toFixed(
                  1
                )}
                %
              </div>
            </div>
            <div>
              <span className='text-gray-600 dark:text-gray-400'>
                Volatility:
              </span>
              <div className='font-semibold'>
                {stressTestScenarios[selectedScenario].volatility.toFixed(1)}%
              </div>
            </div>
            <div>
              <span className='text-gray-600 dark:text-gray-400'>
                Inflation:
              </span>
              <div className='font-semibold'>
                {stressTestScenarios[selectedScenario].inflationRate.toFixed(1)}
                %
              </div>
            </div>
            <div>
              <span className='text-gray-600 dark:text-gray-400'>
                Economic Shocks:
              </span>
              <div className='font-semibold'>
                {stressTestScenarios[selectedScenario].economicShocks
                  ? `${(stressTestScenarios[selectedScenario].economicShocks!.probability * 100).toFixed(0)}% chance`
                  : 'None'}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Results */}
      {results && (
        <div
          className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
        >
          <h3 className='text-lg font-semibold mb-4'>Simulation Results</h3>

          {/* Success Rate */}
          <div className='mb-6'>
            <div className='flex items-center justify-between mb-2'>
              <span className='text-sm font-medium'>Success Rate</span>
              <span
                className={`text-2xl font-bold ${getSuccessRateColor(results.successRate)}`}
              >
                {results.successRate.toFixed(1)}%
              </span>
            </div>
            <div className='w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3'>
              <div
                className={`h-3 rounded-full transition-all duration-500 ${
                  results.successRate >= 90
                    ? 'bg-green-500'
                    : results.successRate >= 75
                      ? 'bg-yellow-500'
                      : 'bg-red-500'
                }`}
                style={{ width: `${Math.min(results.successRate, 100)}%` }}
              />
            </div>
            <p className='text-xs text-gray-600 dark:text-gray-400 mt-1'>
              Probability of reaching your FIRE goal in{' '}
              {userData.retirementAge - userData.currentAge} years
            </p>
          </div>

          {/* Outcome Distribution */}
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6'>
            <div className='text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg'>
              <div className='text-sm text-gray-600 dark:text-gray-400'>
                Best Case (90th %ile)
              </div>
              <div className='text-xl font-bold text-green-600'>
                {formatCurrency(results.percentiles.p90)}
              </div>
            </div>
            <div className='text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg'>
              <div className='text-sm text-gray-600 dark:text-gray-400'>
                Median (50th %ile)
              </div>
              <div className='text-xl font-bold text-blue-600'>
                {formatCurrency(results.percentiles.p50)}
              </div>
            </div>
            <div className='text-center p-4 bg-red-50 dark:bg-red-900/20 rounded-lg'>
              <div className='text-sm text-gray-600 dark:text-gray-400'>
                Worst Case (10th %ile)
              </div>
              <div className='text-xl font-bold text-red-600'>
                {formatCurrency(results.percentiles.p10)}
              </div>
            </div>
          </div>

          {/* Risk Metrics */}
          <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
            <div className='p-4 bg-gray-50 dark:bg-gray-700 rounded-lg'>
              <div className='text-sm text-gray-600 dark:text-gray-400'>
                Probability of Ruin
              </div>
              <div className='text-lg font-bold text-red-600'>
                {results.riskMetrics.probabilityOfRuin.toFixed(1)}%
              </div>
              <div className='text-xs text-gray-500'>
                Chance of running out of money
              </div>
            </div>
            <div className='p-4 bg-gray-50 dark:bg-gray-700 rounded-lg'>
              <div className='text-sm text-gray-600 dark:text-gray-400'>
                Value at Risk
              </div>
              <div className='text-lg font-bold text-orange-600'>
                {formatCurrency(results.riskMetrics.valueAtRisk)}
              </div>
              <div className='text-xs text-gray-500'>
                10% chance of losing this much
              </div>
            </div>
            <div className='p-4 bg-gray-50 dark:bg-gray-700 rounded-lg'>
              <div className='text-sm text-gray-600 dark:text-gray-400'>
                Expected Shortfall
              </div>
              <div className='text-lg font-bold text-red-600'>
                {formatCurrency(results.riskMetrics.expectedShortfall)}
              </div>
              <div className='text-xs text-gray-500'>
                Average shortfall if unsuccessful
              </div>
            </div>
          </div>

          <div className='mt-4 text-xs text-gray-500'>
            Based on {results.iterations.toLocaleString()} simulations using{' '}
            {stressTestScenarios[selectedScenario].name} scenario
          </div>
        </div>
      )}
    </div>
  );
};

export default MonteCarloSimulation;
