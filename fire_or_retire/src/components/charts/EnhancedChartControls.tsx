import React, { useState, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

export type ChartTypeOption = 'line' | 'area' | 'bar' | 'scatter' | 'donut' | 'heatmap';
export type TimeframeOption = '1M' | '3M' | '6M' | '1Y' | '2Y' | '5Y' | 'ALL';
export type MetricOption = 'netWorth' | 'savings' | 'expenses' | 'income' | 'taxes' | 'pillar3a' | 'healthcare';
export type CantonOption = 'ZH' | 'BE' | 'LU' | 'UR' | 'SZ' | 'OW' | 'NW' | 'GL' | 'ZG' | 'FR' | 'SO' | 'BS' | 'BL' | 'SH' | 'AR' | 'AI' | 'SG' | 'GR' | 'AG' | 'TG' | 'TI' | 'VD' | 'VS' | 'NE' | 'GE' | 'JU';

export interface ChartControlsState {
  chartType: ChartTypeOption;
  timeframe: TimeframeOption;
  selectedMetrics: MetricOption[];
  selectedCantons: CantonOption[];
  showTrendline: boolean;
  showConfidenceInterval: boolean;
  enableAnimations: boolean;
  enableInteractivity: boolean;
  showComparison: boolean;
  darkMode: boolean;
}

export interface EnhancedChartControlsProps {
  state: ChartControlsState;
  onChange: (newState: Partial<ChartControlsState>) => void;
  availableMetrics?: MetricOption[];
  availableCantons?: CantonOption[];
  showCantonSelector?: boolean;
  showAdvancedOptions?: boolean;
  className?: string;
}

const EnhancedChartControls: React.FC<EnhancedChartControlsProps> = ({
  state,
  onChange,
  availableMetrics = ['netWorth', 'savings', 'expenses', 'income'],
  availableCantons = ['ZH', 'BE', 'LU', 'ZG', 'AG', 'VD', 'GE'],
  showCantonSelector = false,
  showAdvancedOptions = false,
  className = '',
}) => {
  const { t } = useTranslation();
  const [showAdvanced, setShowAdvanced] = useState(false);

  // Chart type options with Swiss-specific considerations
  const chartTypeOptions: { value: ChartTypeOption; label: string; icon: string; description: string }[] = useMemo(() => [
    {
      value: 'line',
      label: t('chart.type.line', 'Line Chart'),
      icon: '📈',
      description: t('chart.type.line.description', 'Best for trends over time'),
    },
    {
      value: 'area',
      label: t('chart.type.area', 'Area Chart'),
      icon: '📊',
      description: t('chart.type.area.description', 'Shows cumulative values'),
    },
    {
      value: 'bar',
      label: t('chart.type.bar', 'Bar Chart'),
      icon: '📊',
      description: t('chart.type.bar.description', 'Compare discrete values'),
    },
    {
      value: 'donut',
      label: t('chart.type.donut', 'Donut Chart'),
      icon: '🍩',
      description: t('chart.type.donut.description', 'Show proportions'),
    },
    {
      value: 'heatmap',
      label: t('chart.type.heatmap', 'Heat Map'),
      icon: '🔥',
      description: t('chart.type.heatmap.description', 'Visualize data density'),
    },
  ], [t]);

  // Timeframe options
  const timeframeOptions: { value: TimeframeOption; label: string }[] = useMemo(() => [
    { value: '1M', label: t('timeframe.1M', '1 Month') },
    { value: '3M', label: t('timeframe.3M', '3 Months') },
    { value: '6M', label: t('timeframe.6M', '6 Months') },
    { value: '1Y', label: t('timeframe.1Y', '1 Year') },
    { value: '2Y', label: t('timeframe.2Y', '2 Years') },
    { value: '5Y', label: t('timeframe.5Y', '5 Years') },
    { value: 'ALL', label: t('timeframe.ALL', 'All Time') },
  ], [t]);

  // Metric options with Swiss-specific labels
  const metricOptions: { value: MetricOption; label: string; icon: string; color: string }[] = useMemo(() => [
    {
      value: 'netWorth',
      label: t('metric.netWorth', 'Net Worth'),
      icon: '💰',
      color: '#059669',
    },
    {
      value: 'savings',
      label: t('metric.savings', 'Savings'),
      icon: '🏦',
      color: '#3B82F6',
    },
    {
      value: 'expenses',
      label: t('metric.expenses', 'Expenses'),
      icon: '💸',
      color: '#DC2626',
    },
    {
      value: 'income',
      label: t('metric.income', 'Income'),
      icon: '💵',
      color: '#059669',
    },
    {
      value: 'taxes',
      label: t('metric.taxes', 'Taxes'),
      icon: '🏛️',
      color: '#DC2626',
    },
    {
      value: 'pillar3a',
      label: t('metric.pillar3a', 'Pillar 3a'),
      icon: '🏛️',
      color: '#7C3AED',
    },
    {
      value: 'healthcare',
      label: t('metric.healthcare', 'Healthcare'),
      icon: '🏥',
      color: '#059669',
    },
  ], [t]);

  // Canton options with full names
  const cantonOptions: { value: CantonOption; label: string; flag: string }[] = useMemo(() => [
    { value: 'ZH', label: t('canton.ZH', 'Zurich'), flag: '🏔️' },
    { value: 'BE', label: t('canton.BE', 'Bern'), flag: '🐻' },
    { value: 'LU', label: t('canton.LU', 'Lucerne'), flag: '🏔️' },
    { value: 'ZG', label: t('canton.ZG', 'Zug'), flag: '💰' },
    { value: 'AG', label: t('canton.AG', 'Aargau'), flag: '🌊' },
    { value: 'VD', label: t('canton.VD', 'Vaud'), flag: '🍇' },
    { value: 'GE', label: t('canton.GE', 'Geneva'), flag: '⚡' },
    { value: 'BS', label: t('canton.BS', 'Basel-Stadt'), flag: '🏛️' },
    { value: 'SG', label: t('canton.SG', 'St. Gallen'), flag: '🏔️' },
    { value: 'TI', label: t('canton.TI', 'Ticino'), flag: '🌴' },
  ], [t]);

  // Handle metric selection
  const handleMetricToggle = useCallback((metric: MetricOption) => {
    const newMetrics = state.selectedMetrics.includes(metric)
      ? state.selectedMetrics.filter(m => m !== metric)
      : [...state.selectedMetrics, metric];
    onChange({ selectedMetrics: newMetrics });
  }, [state.selectedMetrics, onChange]);

  // Handle canton selection
  const handleCantonToggle = useCallback((canton: CantonOption) => {
    const newCantons = state.selectedCantons.includes(canton)
      ? state.selectedCantons.filter(c => c !== canton)
      : [...state.selectedCantons, canton];
    onChange({ selectedCantons: newCantons });
  }, [state.selectedCantons, onChange]);

  return (
    <div className={`enhanced-chart-controls space-y-6 ${className}`}>
      {/* Chart Type Selection */}
      <div>
        <label className="block text-sm font-medium mb-3">
          {t('controls.chartType', 'Chart Type')}
        </label>
        <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
          {chartTypeOptions.map((option) => (
            <button
              key={option.value}
              onClick={() => onChange({ chartType: option.value })}
              className={`p-3 rounded-lg border-2 transition-all duration-200 ${
                state.chartType === option.value
                  ? state.darkMode
                    ? 'border-blue-500 bg-blue-900/20 text-blue-300'
                    : 'border-blue-500 bg-blue-50 text-blue-700'
                  : state.darkMode
                    ? 'border-gray-600 bg-gray-800 text-gray-300 hover:border-gray-500'
                    : 'border-gray-300 bg-white text-gray-700 hover:border-gray-400'
              }`}
              title={option.description}
            >
              <div className="text-lg mb-1">{option.icon}</div>
              <div className="text-xs font-medium">{option.label}</div>
            </button>
          ))}
        </div>
      </div>

      {/* Timeframe Selection */}
      <div>
        <label className="block text-sm font-medium mb-3">
          {t('controls.timeframe', 'Timeframe')}
        </label>
        <div className="flex flex-wrap gap-2">
          {timeframeOptions.map((option) => (
            <button
              key={option.value}
              onClick={() => onChange({ timeframe: option.value })}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                state.timeframe === option.value
                  ? state.darkMode
                    ? 'bg-blue-600 text-white'
                    : 'bg-blue-600 text-white'
                  : state.darkMode
                    ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              {option.label}
            </button>
          ))}
        </div>
      </div>

      {/* Metric Selection */}
      <div>
        <label className="block text-sm font-medium mb-3">
          {t('controls.metrics', 'Metrics to Display')}
        </label>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
          {metricOptions
            .filter(option => availableMetrics.includes(option.value))
            .map((option) => (
              <button
                key={option.value}
                onClick={() => handleMetricToggle(option.value)}
                className={`p-3 rounded-lg border-2 transition-all duration-200 ${
                  state.selectedMetrics.includes(option.value)
                    ? 'border-current bg-opacity-20'
                    : state.darkMode
                      ? 'border-gray-600 bg-gray-800 text-gray-300 hover:border-gray-500'
                      : 'border-gray-300 bg-white text-gray-700 hover:border-gray-400'
                }`}
                style={{
                  borderColor: state.selectedMetrics.includes(option.value) ? option.color : undefined,
                  backgroundColor: state.selectedMetrics.includes(option.value) ? `${option.color}20` : undefined,
                  color: state.selectedMetrics.includes(option.value) ? option.color : undefined,
                }}
              >
                <div className="text-lg mb-1">{option.icon}</div>
                <div className="text-xs font-medium">{option.label}</div>
              </button>
            ))}
        </div>
      </div>

      {/* Canton Selection (Swiss-specific) */}
      {showCantonSelector && (
        <div>
          <label className="block text-sm font-medium mb-3">
            {t('controls.cantons', 'Swiss Cantons')}
          </label>
          <div className="grid grid-cols-3 md:grid-cols-5 gap-2">
            {cantonOptions
              .filter(option => availableCantons.includes(option.value))
              .map((option) => (
                <button
                  key={option.value}
                  onClick={() => handleCantonToggle(option.value)}
                  className={`p-2 rounded-lg border-2 transition-all duration-200 ${
                    state.selectedCantons.includes(option.value)
                      ? state.darkMode
                        ? 'border-red-500 bg-red-900/20 text-red-300'
                        : 'border-red-500 bg-red-50 text-red-700'
                      : state.darkMode
                        ? 'border-gray-600 bg-gray-800 text-gray-300 hover:border-gray-500'
                        : 'border-gray-300 bg-white text-gray-700 hover:border-gray-400'
                  }`}
                >
                  <div className="text-sm mb-1">{option.flag}</div>
                  <div className="text-xs font-medium">{option.value}</div>
                </button>
              ))}
          </div>
        </div>
      )}

      {/* Advanced Options Toggle */}
      {showAdvancedOptions && (
        <div>
          <button
            onClick={() => setShowAdvanced(!showAdvanced)}
            className={`flex items-center space-x-2 text-sm font-medium ${
              state.darkMode ? 'text-gray-300 hover:text-white' : 'text-gray-700 hover:text-gray-900'
            }`}
          >
            <span>{showAdvanced ? '▼' : '▶'}</span>
            <span>{t('controls.advancedOptions', 'Advanced Options')}</span>
          </button>

          {showAdvanced && (
            <div className="mt-4 space-y-4 p-4 rounded-lg bg-gray-50 dark:bg-gray-800">
              {/* Chart Features */}
              <div className="grid grid-cols-2 gap-4">
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={state.showTrendline}
                    onChange={(e) => onChange({ showTrendline: e.target.checked })}
                    className="rounded"
                  />
                  <span className="text-sm">{t('controls.showTrendline', 'Show Trendline')}</span>
                </label>

                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={state.showConfidenceInterval}
                    onChange={(e) => onChange({ showConfidenceInterval: e.target.checked })}
                    className="rounded"
                  />
                  <span className="text-sm">{t('controls.showConfidenceInterval', 'Confidence Interval')}</span>
                </label>

                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={state.enableAnimations}
                    onChange={(e) => onChange({ enableAnimations: e.target.checked })}
                    className="rounded"
                  />
                  <span className="text-sm">{t('controls.enableAnimations', 'Enable Animations')}</span>
                </label>

                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={state.enableInteractivity}
                    onChange={(e) => onChange({ enableInteractivity: e.target.checked })}
                    className="rounded"
                  />
                  <span className="text-sm">{t('controls.enableInteractivity', 'Interactive Features')}</span>
                </label>

                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={state.showComparison}
                    onChange={(e) => onChange({ showComparison: e.target.checked })}
                    className="rounded"
                  />
                  <span className="text-sm">{t('controls.showComparison', 'Show Comparison')}</span>
                </label>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default EnhancedChartControls;
