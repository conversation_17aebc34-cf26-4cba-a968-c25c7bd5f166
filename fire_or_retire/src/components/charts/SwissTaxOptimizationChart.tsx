import React, { useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import SwissFinancialChart, { SwissFinancialDataPoint, SwissChartConfig } from './SwissFinancialChart';

export interface TaxOptimizationData {
  canton: string;
  income: number;
  federalTax: number;
  cantonalTax: number;
  municipalTax: number;
  totalTax: number;
  taxRate: number;
  pillar3aContribution: number;
  taxSavings: number;
  netIncome: number;
  effectiveRate: number;
}

export interface SwissTaxOptimizationChartProps {
  income: number;
  pillar3aContribution: number;
  selectedCantons: string[];
  darkMode: boolean;
  showComparison?: boolean;
  showPillar3aImpact?: boolean;
  className?: string;
  onCantonClick?: (canton: string, data: TaxOptimizationData) => void;
}

// Swiss tax rates by canton (simplified for demonstration)
const SWISS_TAX_RATES = {
  ZG: { federal: 8.5, cantonal: 3.0, municipal: 2.5, total: 14.0 },
  SZ: { federal: 8.5, cantonal: 4.2, municipal: 3.1, total: 15.8 },
  NW: { federal: 8.5, cantonal: 4.8, municipal: 3.2, total: 16.5 },
  UR: { federal: 8.5, cantonal: 5.1, municipal: 3.4, total: 17.0 },
  LU: { federal: 8.5, cantonal: 5.5, municipal: 4.0, total: 18.0 },
  ZH: { federal: 8.5, cantonal: 6.8, municipal: 4.2, total: 19.5 },
  AG: { federal: 8.5, cantonal: 6.2, municipal: 4.8, total: 19.5 },
  TG: { federal: 8.5, cantonal: 6.5, municipal: 4.5, total: 19.5 },
  SG: { federal: 8.5, cantonal: 6.8, municipal: 4.7, total: 20.0 },
  BE: { federal: 8.5, cantonal: 7.2, municipal: 5.3, total: 21.0 },
  BL: { federal: 8.5, cantonal: 7.0, municipal: 5.5, total: 21.0 },
  SO: { federal: 8.5, cantonal: 7.5, municipal: 5.0, total: 21.0 },
  SH: { federal: 8.5, cantonal: 7.8, municipal: 4.7, total: 21.0 },
  AR: { federal: 8.5, cantonal: 7.2, municipal: 6.3, total: 22.0 },
  AI: { federal: 8.5, cantonal: 6.8, municipal: 6.7, total: 22.0 },
  GR: { federal: 8.5, cantonal: 8.0, municipal: 5.5, total: 22.0 },
  FR: { federal: 8.5, cantonal: 8.5, municipal: 5.0, total: 22.0 },
  BS: { federal: 8.5, cantonal: 9.2, municipal: 4.3, total: 22.0 },
  GL: { federal: 8.5, cantonal: 8.2, municipal: 5.8, total: 22.5 },
  OW: { federal: 8.5, cantonal: 8.0, municipal: 6.0, total: 22.5 },
  TI: { federal: 8.5, cantonal: 9.8, municipal: 4.7, total: 23.0 },
  VD: { federal: 8.5, cantonal: 10.2, municipal: 4.8, total: 23.5 },
  NE: { federal: 8.5, cantonal: 10.8, municipal: 4.2, total: 23.5 },
  VS: { federal: 8.5, cantonal: 10.5, municipal: 4.5, total: 23.5 },
  GE: { federal: 8.5, cantonal: 11.2, municipal: 4.3, total: 24.0 },
  JU: { federal: 8.5, cantonal: 11.8, municipal: 4.7, total: 25.0 },
};

const SwissTaxOptimizationChart: React.FC<SwissTaxOptimizationChartProps> = ({
  income,
  pillar3aContribution,
  selectedCantons,
  darkMode,
  showComparison = true,
  showPillar3aImpact = true,
  className = '',
  onCantonClick,
}) => {
  const { t } = useTranslation();

  // Calculate tax data for all cantons
  const taxData = useMemo((): TaxOptimizationData[] => {
    const cantons = selectedCantons.length > 0 ? selectedCantons : Object.keys(SWISS_TAX_RATES);
    
    return cantons.map(canton => {
      const rates = SWISS_TAX_RATES[canton as keyof typeof SWISS_TAX_RATES];
      if (!rates) {
        return {
          canton,
          income,
          federalTax: 0,
          cantonalTax: 0,
          municipalTax: 0,
          totalTax: 0,
          taxRate: 0,
          pillar3aContribution,
          taxSavings: 0,
          netIncome: income,
          effectiveRate: 0,
        };
      }

      // Calculate taxes without Pillar 3a
      const taxableIncomeBase = income;
      const federalTaxBase = (taxableIncomeBase * rates.federal) / 100;
      const cantonalTaxBase = (taxableIncomeBase * rates.cantonal) / 100;
      const municipalTaxBase = (taxableIncomeBase * rates.municipal) / 100;
      const totalTaxBase = federalTaxBase + cantonalTaxBase + municipalTaxBase;

      // Calculate taxes with Pillar 3a
      const taxableIncomeOptimized = Math.max(0, income - pillar3aContribution);
      const federalTax = (taxableIncomeOptimized * rates.federal) / 100;
      const cantonalTax = (taxableIncomeOptimized * rates.cantonal) / 100;
      const municipalTax = (taxableIncomeOptimized * rates.municipal) / 100;
      const totalTax = federalTax + cantonalTax + municipalTax;

      const taxSavings = totalTaxBase - totalTax;
      const netIncome = income - totalTax - pillar3aContribution;
      const effectiveRate = income > 0 ? (totalTax / income) * 100 : 0;

      return {
        canton,
        income,
        federalTax,
        cantonalTax,
        municipalTax,
        totalTax,
        taxRate: rates.total,
        pillar3aContribution,
        taxSavings,
        netIncome,
        effectiveRate,
      };
    }).sort((a, b) => a.totalTax - b.totalTax); // Sort by total tax (lowest first)
  }, [income, pillar3aContribution, selectedCantons]);

  // Convert tax data to chart data points
  const chartData = useMemo((): SwissFinancialDataPoint[] => {
    return taxData.map((data, index) => ({
      date: new Date(2024, index, 1), // Use months as x-axis for canton comparison
      value: data.totalTax,
      category: 'taxes',
      canton: data.canton,
      taxRate: data.effectiveRate,
      pillar3a: data.pillar3aContribution,
      label: `${data.canton}: CHF ${data.totalTax.toLocaleString()}`,
    }));
  }, [taxData]);

  // Chart configuration for tax optimization
  const chartConfig: SwissChartConfig = useMemo(() => ({
    type: 'bar',
    metric: 'taxes',
    showTrendline: false,
    showConfidenceInterval: false,
    enableZoom: false,
    enableBrush: false,
    showCantonComparison: true,
    swissFormatting: true,
    interactive: true,
    animated: true,
    responsive: true,
  }), []);

  // Handle canton click
  const handleCantonClick = useCallback((dataPoint: SwissFinancialDataPoint) => {
    const cantonData = taxData.find(d => d.canton === dataPoint.canton);
    if (cantonData && onCantonClick) {
      onCantonClick(cantonData.canton, cantonData);
    }
  }, [taxData, onCantonClick]);

  // Get best and worst cantons
  const bestCanton = taxData[0];
  const worstCanton = taxData[taxData.length - 1];
  const potentialSavings = worstCanton.totalTax - bestCanton.totalTax;

  return (
    <div className={`swiss-tax-optimization-chart ${className}`}>
      {/* Header */}
      <div className="mb-6">
        <h3 className="text-xl font-bold mb-2">
          🏛️ {t('tax.optimization.title', 'Swiss Tax Optimization by Canton')}
        </h3>
        <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
          {t('tax.optimization.description', 'Compare tax burden across Swiss cantons and optimize with Pillar 3a')}
        </p>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}>
          <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            {t('tax.bestCanton', 'Best Canton')}
          </div>
          <div className="text-lg font-bold text-green-600">
            {bestCanton.canton}
          </div>
          <div className="text-sm">
            CHF {bestCanton.totalTax.toLocaleString()}
          </div>
        </div>

        <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}>
          <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            {t('tax.worstCanton', 'Highest Tax')}
          </div>
          <div className="text-lg font-bold text-red-600">
            {worstCanton.canton}
          </div>
          <div className="text-sm">
            CHF {worstCanton.totalTax.toLocaleString()}
          </div>
        </div>

        <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}>
          <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            {t('tax.potentialSavings', 'Potential Savings')}
          </div>
          <div className="text-lg font-bold text-blue-600">
            CHF {potentialSavings.toLocaleString()}
          </div>
          <div className="text-sm">
            {((potentialSavings / worstCanton.totalTax) * 100).toFixed(1)}%
          </div>
        </div>

        <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}>
          <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            {t('tax.pillar3aSavings', 'Pillar 3a Savings')}
          </div>
          <div className="text-lg font-bold text-purple-600">
            CHF {bestCanton.taxSavings.toLocaleString()}
          </div>
          <div className="text-sm">
            {t('tax.perYear', 'per year')}
          </div>
        </div>
      </div>

      {/* Tax Comparison Chart */}
      <div className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'} mb-6`}>
        <SwissFinancialChart
          data={chartData}
          config={chartConfig}
          darkMode={darkMode}
          width={800}
          height={400}
          onDataPointClick={handleCantonClick}
        />
      </div>

      {/* Detailed Tax Breakdown */}
      {showComparison && (
        <div className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}>
          <h4 className="text-lg font-semibold mb-4">
            📊 {t('tax.detailedBreakdown', 'Detailed Tax Breakdown')}
          </h4>
          
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className={`border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
                  <th className="text-left py-2">{t('tax.canton', 'Canton')}</th>
                  <th className="text-right py-2">{t('tax.federal', 'Federal')}</th>
                  <th className="text-right py-2">{t('tax.cantonal', 'Cantonal')}</th>
                  <th className="text-right py-2">{t('tax.municipal', 'Municipal')}</th>
                  <th className="text-right py-2">{t('tax.total', 'Total')}</th>
                  <th className="text-right py-2">{t('tax.effectiveRate', 'Rate %')}</th>
                  <th className="text-right py-2">{t('tax.netIncome', 'Net Income')}</th>
                </tr>
              </thead>
              <tbody>
                {taxData.slice(0, 10).map((data) => (
                  <tr
                    key={data.canton}
                    className={`border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'} hover:bg-opacity-50 ${
                      darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-50'
                    } cursor-pointer`}
                    onClick={() => onCantonClick?.(data.canton, data)}
                  >
                    <td className="py-2 font-medium">{data.canton}</td>
                    <td className="text-right py-2">CHF {data.federalTax.toLocaleString()}</td>
                    <td className="text-right py-2">CHF {data.cantonalTax.toLocaleString()}</td>
                    <td className="text-right py-2">CHF {data.municipalTax.toLocaleString()}</td>
                    <td className="text-right py-2 font-semibold">CHF {data.totalTax.toLocaleString()}</td>
                    <td className="text-right py-2">{data.effectiveRate.toFixed(1)}%</td>
                    <td className="text-right py-2 text-green-600 font-medium">
                      CHF {data.netIncome.toLocaleString()}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {taxData.length > 10 && (
            <div className={`mt-4 text-center text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              {t('tax.showingTop10', 'Showing top 10 cantons by tax efficiency')}
            </div>
          )}
        </div>
      )}

      {/* Pillar 3a Impact */}
      {showPillar3aImpact && pillar3aContribution > 0 && (
        <div className={`mt-6 p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}>
          <h4 className="text-lg font-semibold mb-4">
            🏛️ {t('tax.pillar3aImpact', 'Pillar 3a Tax Impact')}
          </h4>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                {t('tax.pillar3aContribution', 'Annual Contribution')}
              </div>
              <div className="text-xl font-bold text-purple-600">
                CHF {pillar3aContribution.toLocaleString()}
              </div>
            </div>
            
            <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                {t('tax.averageSavings', 'Average Tax Savings')}
              </div>
              <div className="text-xl font-bold text-green-600">
                CHF {Math.round(taxData.reduce((sum, d) => sum + d.taxSavings, 0) / taxData.length).toLocaleString()}
              </div>
            </div>
            
            <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                {t('tax.effectiveReturn', 'Effective Return')}
              </div>
              <div className="text-xl font-bold text-blue-600">
                {pillar3aContribution > 0 
                  ? ((taxData.reduce((sum, d) => sum + d.taxSavings, 0) / taxData.length / pillar3aContribution) * 100).toFixed(1)
                  : 0}%
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SwissTaxOptimizationChart;
