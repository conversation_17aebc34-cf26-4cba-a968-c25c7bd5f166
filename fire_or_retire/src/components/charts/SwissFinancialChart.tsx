import React, { useRef, useEffect, useCallback, useMemo } from 'react';
import * as d3 from 'd3';
import { useTranslation } from 'react-i18next';
import { useD3Chart, ChartDimensions } from '../../hooks/useD3Chart';

export interface SwissFinancialDataPoint {
  date: Date;
  value: number;
  category?: string;
  canton?: string;
  taxRate?: number;
  pillar3a?: number;
  healthcare?: number;
  label?: string;
}

export interface SwissChartConfig {
  type: 'line' | 'area' | 'bar' | 'scatter' | 'donut' | 'heatmap';
  metric: 'netWorth' | 'savings' | 'taxes' | 'pillar3a' | 'healthcare' | 'expenses';
  showTrendline?: boolean;
  showConfidenceInterval?: boolean;
  enableZoom?: boolean;
  enableBrush?: boolean;
  showCantonComparison?: boolean;
  swissFormatting?: boolean;
  interactive?: boolean;
  animated?: boolean;
  responsive?: boolean;
}

export interface SwissFinancialChartProps {
  data: SwissFinancialDataPoint[];
  config: SwissChartConfig;
  darkMode: boolean;
  width?: number;
  height?: number;
  className?: string;
  onDataPointClick?: (data: SwissFinancialDataPoint) => void;
  onDataPointHover?: (data: SwissFinancialDataPoint | null) => void;
  onZoomChange?: (domain: [Date, Date]) => void;
}

const SwissFinancialChart: React.FC<SwissFinancialChartProps> = ({
  data,
  config,
  darkMode,
  width = 1000,
  height = 400,
  className = '',
  onDataPointClick,
  onDataPointHover,
  onZoomChange,
}) => {
  const { t } = useTranslation();
  const brushRef = useRef<d3.BrushBehavior<unknown> | null>(null);
  const zoomRef = useRef<d3.ZoomBehavior<Element, unknown> | null>(null);

  const dimensions: ChartDimensions = useMemo(
    () => ({
      width,
      height,
      margin: { top: 20, right: 60, bottom: 80, left: 80 },
    }),
    [width, height]
  );

  const {
    svgRef,
    containerRef,
    createSVG,
    createTimeScale,
    createLinearScale,
    animatePathDraw,
    createTooltip,
    showTooltip,
    hideTooltip,
    formatCurrency,
    formatDate,
    formatPercentage,
  } = useD3Chart(data, dimensions, {
    responsive: config.responsive,
    animated: config.animated,
    darkMode,
  });

  // Swiss-specific color schemes
  const swissColors = useMemo(() => {
    const baseColors = {
      primary: '#DC2626', // Swiss red
      secondary: '#FFFFFF', // Swiss white
      accent: '#1F2937', // Dark gray
      success: '#059669', // Green for positive values
      warning: '#D97706', // Orange for warnings
      danger: '#DC2626', // Red for negative values
    };

    return darkMode
      ? {
          ...baseColors,
          background: '#1F2937',
          text: '#F9FAFB',
          grid: '#374151',
          axis: '#6B7280',
        }
      : {
          ...baseColors,
          background: '#FFFFFF',
          text: '#111827',
          grid: '#E5E7EB',
          axis: '#6B7280',
        };
  }, [darkMode]);

  // Swiss locale formatting
  const swissLocale = useMemo(() => {
    return d3.formatLocale({
      decimal: '.',
      thousands: "'",
      grouping: [3],
      currency: ['CHF ', ''],
    });
  }, []);

  // Enhanced Swiss currency formatter
  const formatSwissCurrency = useCallback(
    (value: number, compact = false) => {
      if (compact && Math.abs(value) >= 1000000) {
        return `CHF ${(value / 1000000).toFixed(1)}M`;
      } else if (compact && Math.abs(value) >= 1000) {
        return `CHF ${(value / 1000).toFixed(0)}K`;
      }
      return swissLocale.format(',.0f')(value).replace(/^/, 'CHF ');
    },
    [swissLocale]
  );

  // Create enhanced scales with Swiss-specific considerations
  const createSwissScales = useCallback(() => {
    if (!data.length) return null;

    const xExtent = d3.extent(data, d => d.date) as [Date, Date];
    const yExtent = d3.extent(data, d => d.value) as [number, number];

    // Add padding to y-scale for better visualization
    const yPadding = (yExtent[1] - yExtent[0]) * 0.1;
    const yDomain: [number, number] = [
      yExtent[0] - yPadding,
      yExtent[1] + yPadding,
    ];

    const innerWidth = dimensions.width - dimensions.margin.left - dimensions.margin.right;
    const innerHeight = dimensions.height - dimensions.margin.top - dimensions.margin.bottom;

    return {
      xScale: createTimeScale(xExtent, [0, innerWidth]),
      yScale: createLinearScale(yDomain, [innerHeight, 0], true),
      innerWidth,
      innerHeight,
    };
  }, [data, dimensions, createTimeScale, createLinearScale]);

  // Render chart based on type
  const renderChart = useCallback(() => {
    const svgSetup = createSVG();
    if (!svgSetup || !data.length) return;

    const { svg, dimensions: dims } = svgSetup;
    const scales = createSwissScales();
    if (!scales) return;

    const { xScale, yScale, innerWidth, innerHeight } = scales;

    // Create main chart group
    const g = svg
      .append('g')
      .attr('transform', `translate(${dims.margin.left},${dims.margin.top})`);

    // Add background
    g.append('rect')
      .attr('width', innerWidth)
      .attr('height', innerHeight)
      .attr('fill', swissColors.background)
      .attr('opacity', 0.1);

    // Add grid lines
    const xAxis = d3.axisBottom(xScale).tickSize(-innerHeight);
    const yAxis = d3.axisLeft(yScale).tickSize(-innerWidth);

    g.append('g')
      .attr('class', 'grid')
      .attr('transform', `translate(0,${innerHeight})`)
      .call(xAxis)
      .selectAll('line')
      .attr('stroke', swissColors.grid)
      .attr('stroke-opacity', 0.3);

    g.append('g')
      .attr('class', 'grid')
      .call(yAxis)
      .selectAll('line')
      .attr('stroke', swissColors.grid)
      .attr('stroke-opacity', 0.3);

    // Render based on chart type
    switch (config.type) {
      case 'line':
        renderLineChart(g, data, xScale, yScale);
        break;
      case 'area':
        renderAreaChart(g, data, xScale, yScale);
        break;
      case 'bar':
        renderBarChart(g, data, xScale, yScale);
        break;
      case 'donut':
        renderDonutChart(g, data, innerWidth, innerHeight);
        break;
      default:
        renderLineChart(g, data, xScale, yScale);
    }

    // Add axes
    g.append('g')
      .attr('transform', `translate(0,${innerHeight})`)
      .call(d3.axisBottom(xScale).tickFormat(d => formatDate(d as Date)))
      .selectAll('text')
      .attr('fill', swissColors.text)
      .style('font-size', '12px');

    g.append('g')
      .call(d3.axisLeft(yScale).tickFormat(d => formatSwissCurrency(d as number, true)))
      .selectAll('text')
      .attr('fill', swissColors.text)
      .style('font-size', '12px');

    // Add chart title
    svg
      .append('text')
      .attr('x', dims.width / 2)
      .attr('y', 20)
      .attr('text-anchor', 'middle')
      .attr('fill', swissColors.text)
      .style('font-size', '16px')
      .style('font-weight', 'bold')
      .text(t(`chart.${config.metric}.title`, config.metric));

  }, [
    createSVG,
    data,
    createSwissScales,
    swissColors,
    config,
    formatDate,
    formatSwissCurrency,
    t,
  ]);

  // Line chart renderer
  const renderLineChart = useCallback(
    (
      g: d3.Selection<SVGGElement, unknown, null, undefined>,
      chartData: SwissFinancialDataPoint[],
      xScale: d3.ScaleTime<number, number>,
      yScale: d3.ScaleLinear<number, number>
    ) => {
      const line = d3
        .line<SwissFinancialDataPoint>()
        .x(d => xScale(d.date))
        .y(d => yScale(d.value))
        .curve(d3.curveMonotoneX);

      const path = g
        .append('path')
        .datum(chartData)
        .attr('fill', 'none')
        .attr('stroke', swissColors.primary)
        .attr('stroke-width', 2)
        .attr('d', line);

      if (config.animated) {
        animatePathDraw(path, 1500);
      }

      // Add data points
      g.selectAll('.data-point')
        .data(chartData)
        .enter()
        .append('circle')
        .attr('class', 'data-point')
        .attr('cx', d => xScale(d.date))
        .attr('cy', d => yScale(d.value))
        .attr('r', 4)
        .attr('fill', swissColors.primary)
        .style('cursor', 'pointer')
        .on('mouseover', (event, d) => {
          showTooltip(
            event,
            `${formatDate(d.date)}<br/>${formatSwissCurrency(d.value)}`
          );
          onDataPointHover?.(d);
        })
        .on('mouseout', () => {
          hideTooltip();
          onDataPointHover?.(null);
        })
        .on('click', (event, d) => {
          onDataPointClick?.(d);
        });
    },
    [
      swissColors,
      config.animated,
      animatePathDraw,
      showTooltip,
      hideTooltip,
      formatDate,
      formatSwissCurrency,
      onDataPointHover,
      onDataPointClick,
    ]
  );

  // Area chart renderer
  const renderAreaChart = useCallback(
    (
      g: d3.Selection<SVGGElement, unknown, null, undefined>,
      chartData: SwissFinancialDataPoint[],
      xScale: d3.ScaleTime<number, number>,
      yScale: d3.ScaleLinear<number, number>
    ) => {
      const area = d3
        .area<SwissFinancialDataPoint>()
        .x(d => xScale(d.date))
        .y0(yScale(0))
        .y1(d => yScale(d.value))
        .curve(d3.curveMonotoneX);

      g.append('path')
        .datum(chartData)
        .attr('fill', swissColors.primary)
        .attr('fill-opacity', 0.3)
        .attr('d', area);

      // Add line on top
      renderLineChart(g, chartData, xScale, yScale);
    },
    [swissColors, renderLineChart]
  );

  // Bar chart renderer
  const renderBarChart = useCallback(
    (
      g: d3.Selection<SVGGElement, unknown, null, undefined>,
      chartData: SwissFinancialDataPoint[],
      xScale: d3.ScaleTime<number, number>,
      yScale: d3.ScaleLinear<number, number>
    ) => {
      const barWidth = (xScale.range()[1] - xScale.range()[0]) / chartData.length * 0.8;

      g.selectAll('.bar')
        .data(chartData)
        .enter()
        .append('rect')
        .attr('class', 'bar')
        .attr('x', d => xScale(d.date) - barWidth / 2)
        .attr('y', d => yScale(Math.max(0, d.value)))
        .attr('width', barWidth)
        .attr('height', d => Math.abs(yScale(d.value) - yScale(0)))
        .attr('fill', d => d.value >= 0 ? swissColors.success : swissColors.danger)
        .style('cursor', 'pointer')
        .on('mouseover', (event, d) => {
          showTooltip(
            event,
            `${formatDate(d.date)}<br/>${formatSwissCurrency(d.value)}`
          );
          onDataPointHover?.(d);
        })
        .on('mouseout', () => {
          hideTooltip();
          onDataPointHover?.(null);
        })
        .on('click', (event, d) => {
          onDataPointClick?.(d);
        });
    },
    [
      swissColors,
      showTooltip,
      hideTooltip,
      formatDate,
      formatSwissCurrency,
      onDataPointHover,
      onDataPointClick,
    ]
  );

  // Donut chart renderer
  const renderDonutChart = useCallback(
    (
      g: d3.Selection<SVGGElement, unknown, null, undefined>,
      chartData: SwissFinancialDataPoint[],
      innerWidth: number,
      innerHeight: number
    ) => {
      const radius = Math.min(innerWidth, innerHeight) / 2;
      const innerRadius = radius * 0.6;

      const pie = d3.pie<SwissFinancialDataPoint>().value(d => d.value);
      const arc = d3.arc<d3.PieArcDatum<SwissFinancialDataPoint>>()
        .innerRadius(innerRadius)
        .outerRadius(radius);

      const chartGroup = g
        .append('g')
        .attr('transform', `translate(${innerWidth / 2},${innerHeight / 2})`);

      const arcs = chartGroup
        .selectAll('.arc')
        .data(pie(chartData))
        .enter()
        .append('g')
        .attr('class', 'arc');

      arcs
        .append('path')
        .attr('d', arc)
        .attr('fill', (d, i) => d3.schemeCategory10[i % 10])
        .style('cursor', 'pointer')
        .on('mouseover', (event, d) => {
          showTooltip(
            event,
            `${d.data.label || formatDate(d.data.date)}<br/>${formatSwissCurrency(d.data.value)}`
          );
          onDataPointHover?.(d.data);
        })
        .on('mouseout', () => {
          hideTooltip();
          onDataPointHover?.(null);
        })
        .on('click', (event, d) => {
          onDataPointClick?.(d.data);
        });
    },
    [
      showTooltip,
      hideTooltip,
      formatDate,
      formatSwissCurrency,
      onDataPointHover,
      onDataPointClick,
    ]
  );

  // Main effect to render chart
  useEffect(() => {
    renderChart();
  }, [renderChart]);

  return (
    <div ref={containerRef} className={`swiss-financial-chart ${className}`}>
      <svg ref={svgRef} className="w-full h-full" />
    </div>
  );
};

export default SwissFinancialChart;
