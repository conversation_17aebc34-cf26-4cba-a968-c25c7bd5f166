import React, { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import * as d3 from 'd3';

export type ExportFormat = 'png' | 'svg' | 'pdf' | 'csv' | 'json';

export interface ExportOptions {
  format: ExportFormat;
  quality?: number; // For PNG/JPEG (0-1)
  width?: number;
  height?: number;
  backgroundColor?: string;
  includeTitle?: boolean;
  includeWatermark?: boolean;
  filename?: string;
}

export interface ChartExportUtilsProps {
  svgRef: React.RefObject<SVGSVGElement>;
  data: any[];
  title?: string;
  darkMode: boolean;
  className?: string;
  onExportStart?: () => void;
  onExportComplete?: (success: boolean, filename?: string) => void;
}

const ChartExportUtils: React.FC<ChartExportUtilsProps> = ({
  svgRef,
  data,
  title = 'Swiss Financial Chart',
  darkMode,
  className = '',
  onExportStart,
  onExportComplete,
}) => {
  const { t } = useTranslation();
  const [isExporting, setIsExporting] = useState(false);
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'png',
    quality: 0.9,
    width: 1200,
    height: 600,
    backgroundColor: darkMode ? '#1F2937' : '#FFFFFF',
    includeTitle: true,
    includeWatermark: false,
    filename: 'swiss-financial-chart',
  });

  // Export formats configuration
  const exportFormats: { value: ExportFormat; label: string; icon: string; description: string }[] = [
    {
      value: 'png',
      label: t('export.format.png', 'PNG Image'),
      icon: '🖼️',
      description: t('export.format.png.description', 'High-quality raster image'),
    },
    {
      value: 'svg',
      label: t('export.format.svg', 'SVG Vector'),
      icon: '📐',
      description: t('export.format.svg.description', 'Scalable vector graphics'),
    },
    {
      value: 'pdf',
      label: t('export.format.pdf', 'PDF Document'),
      icon: '📄',
      description: t('export.format.pdf.description', 'Portable document format'),
    },
    {
      value: 'csv',
      label: t('export.format.csv', 'CSV Data'),
      icon: '📊',
      description: t('export.format.csv.description', 'Comma-separated values'),
    },
    {
      value: 'json',
      label: t('export.format.json', 'JSON Data'),
      icon: '🔧',
      description: t('export.format.json.description', 'JavaScript object notation'),
    },
  ];

  // Export to PNG
  const exportToPNG = useCallback(async (options: ExportOptions): Promise<boolean> => {
    if (!svgRef.current) return false;

    try {
      const svg = svgRef.current;
      const svgData = new XMLSerializer().serializeToString(svg);
      
      // Create canvas
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      if (!ctx) return false;

      canvas.width = options.width || 1200;
      canvas.height = options.height || 600;

      // Set background
      if (options.backgroundColor) {
        ctx.fillStyle = options.backgroundColor;
        ctx.fillRect(0, 0, canvas.width, canvas.height);
      }

      // Create image from SVG
      const img = new Image();
      const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
      const url = URL.createObjectURL(svgBlob);

      return new Promise((resolve) => {
        img.onload = () => {
          ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
          
          // Add title if requested
          if (options.includeTitle && title) {
            ctx.fillStyle = darkMode ? '#FFFFFF' : '#000000';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(title, canvas.width / 2, 40);
          }

          // Add watermark if requested
          if (options.includeWatermark) {
            ctx.fillStyle = darkMode ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)';
            ctx.font = '12px Arial';
            ctx.textAlign = 'right';
            ctx.fillText('Swiss Budget Pro', canvas.width - 20, canvas.height - 20);
          }

          // Download
          canvas.toBlob((blob) => {
            if (blob) {
              const link = document.createElement('a');
              link.download = `${options.filename || 'chart'}.png`;
              link.href = URL.createObjectURL(blob);
              link.click();
              URL.revokeObjectURL(link.href);
              resolve(true);
            } else {
              resolve(false);
            }
          }, 'image/png', options.quality || 0.9);

          URL.revokeObjectURL(url);
        };

        img.onerror = () => {
          URL.revokeObjectURL(url);
          resolve(false);
        };

        img.src = url;
      });
    } catch (error) {
      console.error('PNG export failed:', error);
      return false;
    }
  }, [svgRef, title, darkMode]);

  // Export to SVG
  const exportToSVG = useCallback(async (options: ExportOptions): Promise<boolean> => {
    if (!svgRef.current) return false;

    try {
      const svg = svgRef.current.cloneNode(true) as SVGSVGElement;
      
      // Set dimensions
      svg.setAttribute('width', String(options.width || 1200));
      svg.setAttribute('height', String(options.height || 600));

      // Add title if requested
      if (options.includeTitle && title) {
        const titleElement = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        titleElement.setAttribute('x', String((options.width || 1200) / 2));
        titleElement.setAttribute('y', '30');
        titleElement.setAttribute('text-anchor', 'middle');
        titleElement.setAttribute('font-size', '24');
        titleElement.setAttribute('font-weight', 'bold');
        titleElement.setAttribute('fill', darkMode ? '#FFFFFF' : '#000000');
        titleElement.textContent = title;
        svg.insertBefore(titleElement, svg.firstChild);
      }

      const svgData = new XMLSerializer().serializeToString(svg);
      const blob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
      
      const link = document.createElement('a');
      link.download = `${options.filename || 'chart'}.svg`;
      link.href = URL.createObjectURL(blob);
      link.click();
      URL.revokeObjectURL(link.href);

      return true;
    } catch (error) {
      console.error('SVG export failed:', error);
      return false;
    }
  }, [svgRef, title, darkMode]);

  // Export to CSV
  const exportToCSV = useCallback(async (options: ExportOptions): Promise<boolean> => {
    try {
      if (!data || data.length === 0) return false;

      // Convert data to CSV format
      const headers = Object.keys(data[0]);
      const csvContent = [
        headers.join(','),
        ...data.map(row => 
          headers.map(header => {
            const value = row[header];
            // Handle dates and special characters
            if (value instanceof Date) {
              return value.toISOString();
            }
            if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
              return `"${value.replace(/"/g, '""')}"`;
            }
            return String(value);
          }).join(',')
        )
      ].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8' });
      const link = document.createElement('a');
      link.download = `${options.filename || 'chart-data'}.csv`;
      link.href = URL.createObjectURL(blob);
      link.click();
      URL.revokeObjectURL(link.href);

      return true;
    } catch (error) {
      console.error('CSV export failed:', error);
      return false;
    }
  }, [data]);

  // Export to JSON
  const exportToJSON = useCallback(async (options: ExportOptions): Promise<boolean> => {
    try {
      const exportData = {
        title,
        exportDate: new Date().toISOString(),
        data,
        metadata: {
          format: 'Swiss Budget Pro Chart Data',
          version: '1.0',
          darkMode,
        },
      };

      const jsonContent = JSON.stringify(exportData, null, 2);
      const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8' });
      
      const link = document.createElement('a');
      link.download = `${options.filename || 'chart-data'}.json`;
      link.href = URL.createObjectURL(blob);
      link.click();
      URL.revokeObjectURL(link.href);

      return true;
    } catch (error) {
      console.error('JSON export failed:', error);
      return false;
    }
  }, [data, title, darkMode]);

  // Main export function
  const handleExport = useCallback(async () => {
    setIsExporting(true);
    onExportStart?.();

    let success = false;

    try {
      switch (exportOptions.format) {
        case 'png':
          success = await exportToPNG(exportOptions);
          break;
        case 'svg':
          success = await exportToSVG(exportOptions);
          break;
        case 'csv':
          success = await exportToCSV(exportOptions);
          break;
        case 'json':
          success = await exportToJSON(exportOptions);
          break;
        default:
          success = false;
      }
    } catch (error) {
      console.error('Export failed:', error);
      success = false;
    }

    setIsExporting(false);
    onExportComplete?.(success, exportOptions.filename);
  }, [exportOptions, exportToPNG, exportToSVG, exportToCSV, exportToJSON, onExportStart, onExportComplete]);

  return (
    <div className={`chart-export-utils ${className}`}>
      {/* Export Format Selection */}
      <div className="mb-4">
        <label className="block text-sm font-medium mb-2">
          {t('export.format', 'Export Format')}
        </label>
        <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
          {exportFormats.map((format) => (
            <button
              key={format.value}
              onClick={() => setExportOptions(prev => ({ ...prev, format: format.value }))}
              className={`p-3 rounded-lg border-2 transition-all duration-200 ${
                exportOptions.format === format.value
                  ? darkMode
                    ? 'border-blue-500 bg-blue-900/20 text-blue-300'
                    : 'border-blue-500 bg-blue-50 text-blue-700'
                  : darkMode
                    ? 'border-gray-600 bg-gray-800 text-gray-300 hover:border-gray-500'
                    : 'border-gray-300 bg-white text-gray-700 hover:border-gray-400'
              }`}
              title={format.description}
            >
              <div className="text-lg mb-1">{format.icon}</div>
              <div className="text-xs font-medium">{format.label}</div>
            </button>
          ))}
        </div>
      </div>

      {/* Export Options */}
      {(exportOptions.format === 'png' || exportOptions.format === 'svg') && (
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium mb-1">
              {t('export.width', 'Width (px)')}
            </label>
            <input
              type="number"
              value={exportOptions.width}
              onChange={(e) => setExportOptions(prev => ({ ...prev, width: parseInt(e.target.value) }))}
              className={`w-full px-3 py-2 rounded-md border ${
                darkMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              }`}
              min="100"
              max="4000"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">
              {t('export.height', 'Height (px)')}
            </label>
            <input
              type="number"
              value={exportOptions.height}
              onChange={(e) => setExportOptions(prev => ({ ...prev, height: parseInt(e.target.value) }))}
              className={`w-full px-3 py-2 rounded-md border ${
                darkMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              }`}
              min="100"
              max="4000"
            />
          </div>
        </div>
      )}

      {/* Filename */}
      <div className="mb-4">
        <label className="block text-sm font-medium mb-1">
          {t('export.filename', 'Filename')}
        </label>
        <input
          type="text"
          value={exportOptions.filename}
          onChange={(e) => setExportOptions(prev => ({ ...prev, filename: e.target.value }))}
          className={`w-full px-3 py-2 rounded-md border ${
            darkMode
              ? 'bg-gray-700 border-gray-600 text-white'
              : 'bg-white border-gray-300 text-gray-900'
          }`}
          placeholder="chart-export"
        />
      </div>

      {/* Export Button */}
      <button
        onClick={handleExport}
        disabled={isExporting}
        className={`w-full px-4 py-3 rounded-lg font-medium transition-colors ${
          isExporting
            ? 'bg-gray-400 cursor-not-allowed'
            : 'bg-blue-600 hover:bg-blue-700'
        } text-white`}
      >
        {isExporting ? (
          <span className="flex items-center justify-center">
            <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2"></div>
            {t('export.exporting', 'Exporting...')}
          </span>
        ) : (
          `📥 ${t('export.download', 'Download')} ${exportOptions.format.toUpperCase()}`
        )}
      </button>
    </div>
  );
};

export default ChartExportUtils;
