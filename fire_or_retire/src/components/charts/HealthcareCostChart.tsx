import React, { useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import SwissFinancialChart, { SwissFinancialDataPoint, SwissChartConfig } from './SwissFinancialChart';

export interface HealthcareCostData {
  year: number;
  age: number;
  basicInsurance: number;
  supplementaryInsurance: number;
  deductible: number;
  outOfPocketCosts: number;
  totalCosts: number;
  inflationAdjusted: number;
  canton: string;
}

export interface HealthcareCostChartProps {
  currentAge: number;
  projectionYears: number;
  basicInsuranceCost: number;
  supplementaryInsuranceCost: number;
  deductible: number;
  canton: string;
  healthcareInflationRate: number;
  darkMode: boolean;
  showProjections?: boolean;
  showCantonComparison?: boolean;
  showOptimization?: boolean;
  className?: string;
  onYearClick?: (year: number, data: HealthcareCostData) => void;
}

// Swiss healthcare cost data by canton (average monthly premiums 2024)
const HEALTHCARE_COSTS_BY_CANTON = {
  ZG: { basic: 420, supplementary: 180, avgDeductible: 1500 },
  NW: { basic: 435, supplementary: 185, avgDeductible: 1500 },
  UR: { basic: 445, supplementary: 190, avgDeductible: 1500 },
  SZ: { basic: 455, supplementary: 195, avgDeductible: 1500 },
  LU: { basic: 465, supplementary: 200, avgDeductible: 1500 },
  ZH: { basic: 485, supplementary: 220, avgDeductible: 2000 },
  AG: { basic: 475, supplementary: 210, avgDeductible: 1500 },
  TG: { basic: 470, supplementary: 205, avgDeductible: 1500 },
  SG: { basic: 480, supplementary: 215, avgDeductible: 1500 },
  BE: { basic: 495, supplementary: 225, avgDeductible: 2000 },
  BL: { basic: 490, supplementary: 220, avgDeductible: 2000 },
  SO: { basic: 485, supplementary: 215, avgDeductible: 1500 },
  SH: { basic: 475, supplementary: 210, avgDeductible: 1500 },
  AR: { basic: 500, supplementary: 230, avgDeductible: 2000 },
  AI: { basic: 495, supplementary: 225, avgDeductible: 2000 },
  GR: { basic: 520, supplementary: 240, avgDeductible: 2000 },
  FR: { basic: 510, supplementary: 235, avgDeductible: 2000 },
  BS: { basic: 525, supplementary: 245, avgDeductible: 2500 },
  GL: { basic: 515, supplementary: 240, avgDeductible: 2000 },
  OW: { basic: 505, supplementary: 230, avgDeductible: 2000 },
  TI: { basic: 535, supplementary: 250, avgDeductible: 2500 },
  VD: { basic: 545, supplementary: 255, avgDeductible: 2500 },
  NE: { basic: 540, supplementary: 250, avgDeductible: 2500 },
  VS: { basic: 530, supplementary: 245, avgDeductible: 2500 },
  GE: { basic: 555, supplementary: 265, avgDeductible: 2500 },
  JU: { basic: 550, supplementary: 260, avgDeductible: 2500 },
};

// Age-based healthcare cost multipliers
const AGE_COST_MULTIPLIERS = {
  '18-25': 0.8,
  '26-35': 0.9,
  '36-45': 1.0,
  '46-55': 1.2,
  '56-65': 1.5,
  '66-75': 2.0,
  '76-85': 2.8,
  '86+': 3.5,
};

const HealthcareCostChart: React.FC<HealthcareCostChartProps> = ({
  currentAge,
  projectionYears,
  basicInsuranceCost,
  supplementaryInsuranceCost,
  deductible,
  canton,
  healthcareInflationRate,
  darkMode,
  showProjections = true,
  showCantonComparison = false,
  showOptimization = true,
  className = '',
  onYearClick,
}) => {
  const { t } = useTranslation();

  // Get age multiplier
  const getAgeMultiplier = useCallback((age: number): number => {
    if (age <= 25) return AGE_COST_MULTIPLIERS['18-25'];
    if (age <= 35) return AGE_COST_MULTIPLIERS['26-35'];
    if (age <= 45) return AGE_COST_MULTIPLIERS['36-45'];
    if (age <= 55) return AGE_COST_MULTIPLIERS['46-55'];
    if (age <= 65) return AGE_COST_MULTIPLIERS['56-65'];
    if (age <= 75) return AGE_COST_MULTIPLIERS['66-75'];
    if (age <= 85) return AGE_COST_MULTIPLIERS['76-85'];
    return AGE_COST_MULTIPLIERS['86+'];
  }, []);

  // Calculate healthcare cost projections
  const healthcareData = useMemo((): HealthcareCostData[] => {
    const data: HealthcareCostData[] = [];
    const currentYear = new Date().getFullYear();

    for (let i = 0; i <= projectionYears; i++) {
      const year = currentYear + i;
      const age = currentAge + i;
      const ageMultiplier = getAgeMultiplier(age);
      const inflationMultiplier = Math.pow(1 + healthcareInflationRate / 100, i);

      // Calculate costs with age and inflation adjustments
      const adjustedBasic = basicInsuranceCost * ageMultiplier * inflationMultiplier;
      const adjustedSupplementary = supplementaryInsuranceCost * ageMultiplier * inflationMultiplier;
      const adjustedDeductible = deductible * inflationMultiplier;

      // Estimate out-of-pocket costs (increases with age)
      const outOfPocketCosts = (adjustedDeductible * 0.6) + (age > 65 ? 2000 : 1000) * inflationMultiplier;

      const totalCosts = (adjustedBasic + adjustedSupplementary) * 12 + outOfPocketCosts;
      const inflationAdjusted = totalCosts / inflationMultiplier; // Real value in today's money

      data.push({
        year,
        age,
        basicInsurance: adjustedBasic * 12,
        supplementaryInsurance: adjustedSupplementary * 12,
        deductible: adjustedDeductible,
        outOfPocketCosts,
        totalCosts,
        inflationAdjusted,
        canton,
      });
    }

    return data;
  }, [currentAge, projectionYears, basicInsuranceCost, supplementaryInsuranceCost, deductible, canton, healthcareInflationRate, getAgeMultiplier]);

  // Canton comparison data
  const cantonComparisonData = useMemo((): HealthcareCostData[] => {
    if (!showCantonComparison) return [];

    return Object.entries(HEALTHCARE_COSTS_BY_CANTON).map(([cantonCode, costs]) => {
      const ageMultiplier = getAgeMultiplier(currentAge);
      const totalCosts = (costs.basic + costs.supplementary) * 12 + costs.avgDeductible * 0.6 + 1500;

      return {
        year: new Date().getFullYear(),
        age: currentAge,
        basicInsurance: costs.basic * 12 * ageMultiplier,
        supplementaryInsurance: costs.supplementary * 12 * ageMultiplier,
        deductible: costs.avgDeductible,
        outOfPocketCosts: costs.avgDeductible * 0.6 + 1500,
        totalCosts: totalCosts * ageMultiplier,
        inflationAdjusted: totalCosts * ageMultiplier,
        canton: cantonCode,
      };
    }).sort((a, b) => a.totalCosts - b.totalCosts);
  }, [showCantonComparison, currentAge, getAgeMultiplier]);

  // Convert to chart data points
  const chartData = useMemo((): SwissFinancialDataPoint[] => {
    return healthcareData.map((data) => ({
      date: new Date(data.year, 0, 1),
      value: data.totalCosts,
      category: 'healthcare',
      healthcare: data.totalCosts,
      label: `${data.year} (Age ${data.age}): CHF ${data.totalCosts.toLocaleString()}`,
    }));
  }, [healthcareData]);

  // Canton comparison chart data
  const cantonChartData = useMemo((): SwissFinancialDataPoint[] => {
    return cantonComparisonData.map((data, index) => ({
      date: new Date(2024, index, 1),
      value: data.totalCosts,
      category: 'healthcare',
      canton: data.canton,
      healthcare: data.totalCosts,
      label: `${data.canton}: CHF ${data.totalCosts.toLocaleString()}`,
    }));
  }, [cantonComparisonData]);

  // Chart configurations
  const projectionChartConfig: SwissChartConfig = useMemo(() => ({
    type: 'line',
    metric: 'healthcare',
    showTrendline: true,
    showConfidenceInterval: false,
    enableZoom: true,
    enableBrush: false,
    showCantonComparison: false,
    swissFormatting: true,
    interactive: true,
    animated: true,
    responsive: true,
  }), []);

  const cantonChartConfig: SwissChartConfig = useMemo(() => ({
    type: 'bar',
    metric: 'healthcare',
    showTrendline: false,
    showConfidenceInterval: false,
    enableZoom: false,
    enableBrush: false,
    showCantonComparison: true,
    swissFormatting: true,
    interactive: true,
    animated: true,
    responsive: true,
  }), []);

  // Handle year click
  const handleYearClick = useCallback((dataPoint: SwissFinancialDataPoint) => {
    const year = dataPoint.date.getFullYear();
    const yearData = healthcareData.find(d => d.year === year);
    if (yearData && onYearClick) {
      onYearClick(year, yearData);
    }
  }, [healthcareData, onYearClick]);

  // Calculate key metrics
  const finalData = healthcareData[healthcareData.length - 1];
  const totalLifetimeCosts = healthcareData.reduce((sum, d) => sum + d.totalCosts, 0);
  const averageAnnualCost = totalLifetimeCosts / healthcareData.length;
  const costIncrease = finalData.totalCosts - healthcareData[0].totalCosts;
  const bestCanton = cantonComparisonData[0];
  const worstCanton = cantonComparisonData[cantonComparisonData.length - 1];

  return (
    <div className={`healthcare-cost-chart ${className}`}>
      {/* Header */}
      <div className="mb-6">
        <h3 className="text-xl font-bold mb-2">
          🏥 {t('healthcare.cost.title', 'Swiss Healthcare Cost Analysis')}
        </h3>
        <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
          {t('healthcare.cost.description', 'Analyze healthcare costs, insurance premiums, and age-related expense projections')}
        </p>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}>
          <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            {t('healthcare.currentAnnualCost', 'Current Annual Cost')}
          </div>
          <div className="text-lg font-bold text-blue-600">
            CHF {healthcareData[0].totalCosts.toLocaleString()}
          </div>
          <div className="text-sm">
            {t('healthcare.age', 'Age')} {currentAge}
          </div>
        </div>

        <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}>
          <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            {t('healthcare.projectedFinalCost', 'Final Year Cost')}
          </div>
          <div className="text-lg font-bold text-red-600">
            CHF {finalData.totalCosts.toLocaleString()}
          </div>
          <div className="text-sm">
            {t('healthcare.age', 'Age')} {finalData.age}
          </div>
        </div>

        <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}>
          <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            {t('healthcare.lifetimeCosts', 'Lifetime Costs')}
          </div>
          <div className="text-lg font-bold text-purple-600">
            CHF {totalLifetimeCosts.toLocaleString()}
          </div>
          <div className="text-sm">
            {projectionYears + 1} {t('healthcare.years', 'years')}
          </div>
        </div>

        <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}>
          <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            {t('healthcare.costIncrease', 'Cost Increase')}
          </div>
          <div className="text-lg font-bold text-orange-600">
            +{((costIncrease / healthcareData[0].totalCosts) * 100).toFixed(0)}%
          </div>
          <div className="text-sm">
            CHF {costIncrease.toLocaleString()}
          </div>
        </div>
      </div>

      {/* Healthcare Cost Projection Chart */}
      <div className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'} mb-6`}>
        <h4 className="text-lg font-semibold mb-4">
          📈 {t('healthcare.costProjection', 'Healthcare Cost Projection')}
        </h4>
        <SwissFinancialChart
          data={chartData}
          config={projectionChartConfig}
          darkMode={darkMode}
          width={800}
          height={400}
          onDataPointClick={handleYearClick}
        />
      </div>

      {/* Canton Comparison */}
      {showCantonComparison && cantonComparisonData.length > 0 && (
        <div className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'} mb-6`}>
          <h4 className="text-lg font-semibold mb-4">
            🇨🇭 {t('healthcare.cantonComparison', 'Healthcare Costs by Canton')}
          </h4>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                {t('healthcare.cheapestCanton', 'Cheapest Canton')}
              </div>
              <div className="text-lg font-bold text-green-600">
                {bestCanton.canton}
              </div>
              <div className="text-sm">
                CHF {bestCanton.totalCosts.toLocaleString()}
              </div>
            </div>

            <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                {t('healthcare.mostExpensive', 'Most Expensive')}
              </div>
              <div className="text-lg font-bold text-red-600">
                {worstCanton.canton}
              </div>
              <div className="text-sm">
                CHF {worstCanton.totalCosts.toLocaleString()}
              </div>
            </div>

            <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                {t('healthcare.potentialSavings', 'Potential Savings')}
              </div>
              <div className="text-lg font-bold text-blue-600">
                CHF {(worstCanton.totalCosts - bestCanton.totalCosts).toLocaleString()}
              </div>
              <div className="text-sm">
                {t('healthcare.perYear', 'per year')}
              </div>
            </div>
          </div>

          <SwissFinancialChart
            data={cantonChartData.slice(0, 15)} // Show top 15 cantons
            config={cantonChartConfig}
            darkMode={darkMode}
            width={800}
            height={300}
          />
        </div>
      )}

      {/* Optimization Recommendations */}
      {showOptimization && (
        <div className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}>
          <h4 className="text-lg font-semibold mb-4">
            💡 {t('healthcare.optimization', 'Cost Optimization Strategies')}
          </h4>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Deductible Optimization */}
            <div>
              <h5 className="font-medium mb-3">
                💰 {t('healthcare.deductibleOptimization', 'Deductible Optimization')}
              </h5>
              
              <div className="space-y-3">
                <div className={`p-4 rounded-lg border-l-4 border-blue-500 ${darkMode ? 'bg-blue-900/20' : 'bg-blue-50'}`}>
                  <p className="text-sm">
                    💡 {t('healthcare.deductibleTip', 'Consider increasing your deductible to')} CHF 2,500 {t('healthcare.toLowerPremiums', 'to lower monthly premiums by approximately')} CHF 100-150.
                  </p>
                </div>

                <div className={`p-4 rounded-lg border-l-4 border-green-500 ${darkMode ? 'bg-green-900/20' : 'bg-green-50'}`}>
                  <p className="text-sm">
                    ✅ {t('healthcare.healthSavings', 'Use a Health Savings Account (HSA) to set aside')} CHF {deductible.toLocaleString()} {t('healthcare.forDeductible', 'for your annual deductible')}.
                  </p>
                </div>
              </div>
            </div>

            {/* Age-Based Planning */}
            <div>
              <h5 className="font-medium mb-3">
                📅 {t('healthcare.agePlanning', 'Age-Based Planning')}
              </h5>
              
              <div className="space-y-3">
                <div className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    {t('healthcare.currentMultiplier', 'Current Age Multiplier')}
                  </div>
                  <div className="text-lg font-semibold">
                    {getAgeMultiplier(currentAge)}x
                  </div>
                </div>

                <div className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    {t('healthcare.retirementMultiplier', 'At Age 65 Multiplier')}
                  </div>
                  <div className="text-lg font-semibold text-orange-600">
                    {getAgeMultiplier(65)}x
                  </div>
                </div>

                <div className={`p-4 rounded-lg border-l-4 border-yellow-500 ${darkMode ? 'bg-yellow-900/20' : 'bg-yellow-50'}`}>
                  <p className="text-sm">
                    ⚠️ {t('healthcare.retirementWarning', 'Healthcare costs typically double after age 65. Plan accordingly for retirement budgeting')}.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default HealthcareCostChart;
