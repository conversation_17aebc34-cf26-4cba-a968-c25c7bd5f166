import React, { useMemo, useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import SwissFinancialChart, { SwissFinancialDataPoint, SwissChartConfig } from './SwissFinancialChart';

export interface CantonalData {
  canton: string;
  cantonName: string;
  taxRate: number;
  healthcareCost: number;
  livingCost: number;
  totalCost: number;
  netIncome: number;
  qualityOfLife: number;
  score: number;
  rank: number;
}

export interface CantonalComparisonChartProps {
  income: number;
  darkMode: boolean;
  selectedCantons?: string[];
  showQualityOfLife?: boolean;
  showLivingCosts?: boolean;
  showHealthcareCosts?: boolean;
  className?: string;
  onCantonClick?: (canton: string, data: CantonalData) => void;
}

// Comprehensive Swiss cantonal data
const CANTONAL_DATA = {
  ZG: { name: 'Zug', taxRate: 14.0, healthcare: 420, livingCost: 130, qualityOfLife: 95 },
  SZ: { name: '<PERSON><PERSON><PERSON><PERSON>', taxRate: 15.8, healthcare: 455, livingCost: 115, qualityOfLife: 88 },
  NW: { name: 'Nidwalden', taxRate: 16.5, healthcare: 435, livingCost: 110, qualityOfLife: 90 },
  UR: { name: 'Uri', taxRate: 17.0, healthcare: 445, livingCost: 105, qualityOfLife: 85 },
  LU: { name: 'Luzern', taxRate: 18.0, healthcare: 465, livingCost: 108, qualityOfLife: 87 },
  ZH: { name: 'Zürich', taxRate: 19.5, healthcare: 485, livingCost: 140, qualityOfLife: 92 },
  AG: { name: 'Aargau', taxRate: 19.5, healthcare: 475, livingCost: 112, qualityOfLife: 86 },
  TG: { name: 'Thurgau', taxRate: 19.5, healthcare: 470, livingCost: 108, qualityOfLife: 84 },
  SG: { name: 'St. Gallen', taxRate: 20.0, healthcare: 480, livingCost: 110, qualityOfLife: 85 },
  BE: { name: 'Bern', taxRate: 21.0, healthcare: 495, livingCost: 118, qualityOfLife: 89 },
  BL: { name: 'Basel-Landschaft', taxRate: 21.0, healthcare: 490, livingCost: 125, qualityOfLife: 88 },
  SO: { name: 'Solothurn', taxRate: 21.0, healthcare: 485, livingCost: 112, qualityOfLife: 83 },
  SH: { name: 'Schaffhausen', taxRate: 21.0, healthcare: 475, livingCost: 115, qualityOfLife: 84 },
  AR: { name: 'Appenzell A.Rh.', taxRate: 22.0, healthcare: 500, livingCost: 110, qualityOfLife: 86 },
  AI: { name: 'Appenzell I.Rh.', taxRate: 22.0, healthcare: 495, livingCost: 108, qualityOfLife: 87 },
  GR: { name: 'Graubünden', taxRate: 22.0, healthcare: 520, livingCost: 115, qualityOfLife: 91 },
  FR: { name: 'Fribourg', taxRate: 22.0, healthcare: 510, livingCost: 110, qualityOfLife: 85 },
  BS: { name: 'Basel-Stadt', taxRate: 22.0, healthcare: 525, livingCost: 135, qualityOfLife: 90 },
  GL: { name: 'Glarus', taxRate: 22.5, healthcare: 515, livingCost: 108, qualityOfLife: 83 },
  OW: { name: 'Obwalden', taxRate: 22.5, healthcare: 505, livingCost: 110, qualityOfLife: 88 },
  TI: { name: 'Ticino', taxRate: 23.0, healthcare: 535, livingCost: 120, qualityOfLife: 93 },
  VD: { name: 'Vaud', taxRate: 23.5, healthcare: 545, livingCost: 125, qualityOfLife: 91 },
  NE: { name: 'Neuchâtel', taxRate: 23.5, healthcare: 540, livingCost: 115, qualityOfLife: 86 },
  VS: { name: 'Valais', taxRate: 23.5, healthcare: 530, livingCost: 110, qualityOfLife: 89 },
  GE: { name: 'Geneva', taxRate: 24.0, healthcare: 555, livingCost: 145, qualityOfLife: 94 },
  JU: { name: 'Jura', taxRate: 25.0, healthcare: 550, livingCost: 105, qualityOfLife: 82 },
};

const CantonalComparisonChart: React.FC<CantonalComparisonChartProps> = ({
  income,
  darkMode,
  selectedCantons = [],
  showQualityOfLife = true,
  showLivingCosts = true,
  showHealthcareCosts = true,
  className = '',
  onCantonClick,
}) => {
  const { t } = useTranslation();
  const [sortBy, setSortBy] = useState<'score' | 'taxRate' | 'totalCost' | 'qualityOfLife'>('score');

  // Calculate cantonal comparison data
  const cantonalData = useMemo((): CantonalData[] => {
    const cantons = selectedCantons.length > 0 
      ? selectedCantons 
      : Object.keys(CANTONAL_DATA);

    return cantons.map((canton, index) => {
      const data = CANTONAL_DATA[canton as keyof typeof CANTONAL_DATA];
      if (!data) {
        return {
          canton,
          cantonName: canton,
          taxRate: 0,
          healthcareCost: 0,
          livingCost: 0,
          totalCost: 0,
          netIncome: income,
          qualityOfLife: 0,
          score: 0,
          rank: index + 1,
        };
      }

      // Calculate costs
      const annualTax = (income * data.taxRate) / 100;
      const annualHealthcare = data.healthcare * 12;
      const annualLivingCost = (income * data.livingCost) / 100; // Living cost as % of income
      const totalCost = annualTax + annualHealthcare + annualLivingCost;
      const netIncome = income - totalCost;

      // Calculate composite score (higher is better)
      // Factors: net income (40%), quality of life (30%), low healthcare costs (15%), low living costs (15%)
      const netIncomeScore = (netIncome / income) * 100;
      const healthcareScore = Math.max(0, 100 - ((data.healthcare - 400) / 200) * 100);
      const livingCostScore = Math.max(0, 100 - ((data.livingCost - 100) / 50) * 100);
      
      const score = (
        netIncomeScore * 0.4 +
        data.qualityOfLife * 0.3 +
        healthcareScore * 0.15 +
        livingCostScore * 0.15
      );

      return {
        canton,
        cantonName: data.name,
        taxRate: data.taxRate,
        healthcareCost: annualHealthcare,
        livingCost: annualLivingCost,
        totalCost,
        netIncome,
        qualityOfLife: data.qualityOfLife,
        score,
        rank: 0, // Will be set after sorting
      };
    }).sort((a, b) => {
      switch (sortBy) {
        case 'taxRate':
          return a.taxRate - b.taxRate;
        case 'totalCost':
          return a.totalCost - b.totalCost;
        case 'qualityOfLife':
          return b.qualityOfLife - a.qualityOfLife;
        case 'score':
        default:
          return b.score - a.score;
      }
    }).map((item, index) => ({ ...item, rank: index + 1 }));
  }, [income, selectedCantons, sortBy]);

  // Convert to chart data points for different metrics
  const getChartData = useCallback((metric: 'score' | 'taxRate' | 'totalCost' | 'netIncome'): SwissFinancialDataPoint[] => {
    return cantonalData.map((data, index) => ({
      date: new Date(2024, index, 1),
      value: data[metric],
      category: metric === 'taxRate' ? 'taxes' : metric === 'netIncome' ? 'income' : 'other',
      canton: data.canton,
      label: `${data.cantonName}: ${
        metric === 'score' ? data.score.toFixed(1) + ' pts' :
        metric === 'taxRate' ? data.taxRate.toFixed(1) + '%' :
        'CHF ' + data[metric].toLocaleString()
      }`,
    }));
  }, [cantonalData]);

  // Chart configurations
  const getChartConfig = useCallback((metric: string): SwissChartConfig => ({
    type: 'bar',
    metric: metric === 'taxRate' ? 'taxes' : metric === 'netIncome' ? 'income' : 'netWorth',
    showTrendline: false,
    showConfidenceInterval: false,
    enableZoom: false,
    enableBrush: false,
    showCantonComparison: true,
    swissFormatting: true,
    interactive: true,
    animated: true,
    responsive: true,
  }), []);

  // Handle canton click
  const handleCantonClick = useCallback((dataPoint: SwissFinancialDataPoint) => {
    const cantonData = cantonalData.find(d => d.canton === dataPoint.canton);
    if (cantonData && onCantonClick) {
      onCantonClick(cantonData.canton, cantonData);
    }
  }, [cantonalData, onCantonClick]);

  // Get top performers
  const topCanton = cantonalData[0];
  const lowestTaxCanton = [...cantonalData].sort((a, b) => a.taxRate - b.taxRate)[0];
  const highestQualityCanton = [...cantonalData].sort((a, b) => b.qualityOfLife - a.qualityOfLife)[0];

  return (
    <div className={`cantonal-comparison-chart ${className}`}>
      {/* Header */}
      <div className="mb-6">
        <h3 className="text-xl font-bold mb-2">
          🇨🇭 {t('cantonal.comparison.title', 'Swiss Cantonal Comparison')}
        </h3>
        <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
          {t('cantonal.comparison.description', 'Compare Swiss cantons by taxes, living costs, healthcare, and quality of life')}
        </p>
      </div>

      {/* Sort Controls */}
      <div className="mb-6">
        <label className="block text-sm font-medium mb-2">
          {t('cantonal.sortBy', 'Sort by')}
        </label>
        <div className="flex flex-wrap gap-2">
          {[
            { value: 'score', label: t('cantonal.overallScore', 'Overall Score') },
            { value: 'taxRate', label: t('cantonal.taxRate', 'Tax Rate') },
            { value: 'totalCost', label: t('cantonal.totalCost', 'Total Cost') },
            { value: 'qualityOfLife', label: t('cantonal.qualityOfLife', 'Quality of Life') },
          ].map((option) => (
            <button
              key={option.value}
              onClick={() => setSortBy(option.value as any)}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                sortBy === option.value
                  ? darkMode
                    ? 'bg-blue-600 text-white'
                    : 'bg-blue-600 text-white'
                  : darkMode
                    ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              {option.label}
            </button>
          ))}
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}>
          <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            {t('cantonal.topOverall', 'Top Overall')}
          </div>
          <div className="text-lg font-bold text-green-600">
            {topCanton.cantonName}
          </div>
          <div className="text-sm">
            {topCanton.score.toFixed(1)} {t('cantonal.points', 'points')}
          </div>
        </div>

        <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}>
          <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            {t('cantonal.lowestTax', 'Lowest Tax')}
          </div>
          <div className="text-lg font-bold text-blue-600">
            {lowestTaxCanton.cantonName}
          </div>
          <div className="text-sm">
            {lowestTaxCanton.taxRate.toFixed(1)}%
          </div>
        </div>

        <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}>
          <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            {t('cantonal.bestQuality', 'Best Quality of Life')}
          </div>
          <div className="text-lg font-bold text-purple-600">
            {highestQualityCanton.cantonName}
          </div>
          <div className="text-sm">
            {highestQualityCanton.qualityOfLife}/100
          </div>
        </div>
      </div>

      {/* Comparison Chart */}
      <div className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'} mb-6`}>
        <h4 className="text-lg font-semibold mb-4">
          📊 {t('cantonal.comparisonChart', 'Cantonal Comparison Chart')}
        </h4>
        <SwissFinancialChart
          data={getChartData(sortBy)}
          config={getChartConfig(sortBy)}
          darkMode={darkMode}
          width={800}
          height={400}
          onDataPointClick={handleCantonClick}
        />
      </div>

      {/* Detailed Comparison Table */}
      <div className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}>
        <h4 className="text-lg font-semibold mb-4">
          📋 {t('cantonal.detailedComparison', 'Detailed Comparison')}
        </h4>
        
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className={`border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
                <th className="text-left py-2">{t('cantonal.rank', 'Rank')}</th>
                <th className="text-left py-2">{t('cantonal.canton', 'Canton')}</th>
                <th className="text-right py-2">{t('cantonal.score', 'Score')}</th>
                <th className="text-right py-2">{t('cantonal.taxRate', 'Tax Rate')}</th>
                {showHealthcareCosts && (
                  <th className="text-right py-2">{t('cantonal.healthcare', 'Healthcare')}</th>
                )}
                {showLivingCosts && (
                  <th className="text-right py-2">{t('cantonal.livingCost', 'Living Cost')}</th>
                )}
                <th className="text-right py-2">{t('cantonal.netIncome', 'Net Income')}</th>
                {showQualityOfLife && (
                  <th className="text-right py-2">{t('cantonal.qualityOfLife', 'Quality')}</th>
                )}
              </tr>
            </thead>
            <tbody>
              {cantonalData.slice(0, 15).map((data) => (
                <tr
                  key={data.canton}
                  className={`border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'} hover:bg-opacity-50 ${
                    darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-50'
                  } cursor-pointer`}
                  onClick={() => onCantonClick?.(data.canton, data)}
                >
                  <td className="py-2 font-medium">#{data.rank}</td>
                  <td className="py-2 font-medium">{data.cantonName}</td>
                  <td className="text-right py-2 font-semibold text-green-600">
                    {data.score.toFixed(1)}
                  </td>
                  <td className="text-right py-2">{data.taxRate.toFixed(1)}%</td>
                  {showHealthcareCosts && (
                    <td className="text-right py-2">CHF {data.healthcareCost.toLocaleString()}</td>
                  )}
                  {showLivingCosts && (
                    <td className="text-right py-2">CHF {data.livingCost.toLocaleString()}</td>
                  )}
                  <td className="text-right py-2 font-semibold text-blue-600">
                    CHF {data.netIncome.toLocaleString()}
                  </td>
                  {showQualityOfLife && (
                    <td className="text-right py-2">{data.qualityOfLife}/100</td>
                  )}
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {cantonalData.length > 15 && (
          <div className={`mt-4 text-center text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            {t('cantonal.showingTop15', 'Showing top 15 cantons')} • {cantonalData.length} {t('cantonal.total', 'total')}
          </div>
        )}
      </div>
    </div>
  );
};

export default CantonalComparisonChart;
