import React, { useCallback, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocalStorage } from '../../hooks/useLocalStorage';
import EnhancedChartControls, {
  ChartControlsState,
  MetricOption,
} from './EnhancedChartControls';
import SwissFinancialChart, {
  SwissChartConfig,
  SwissFinancialDataPoint,
} from './SwissFinancialChart';

interface ChartDashboardProps {
  darkMode: boolean;
  userData: {
    currentAge: number;
    retirementAge: number;
    currentSavings: number;
    monthlyIncome: number;
    monthlyExpenses: number;
    expectedReturn: number;
    inflationRate: number;
    safeWithdrawalRate: number;
  };
  historicalData?: SwissFinancialDataPoint[];
  className?: string;
}

interface DashboardLayout {
  id: string;
  metric: MetricOption;
  position: { x: number; y: number; w: number; h: number };
  visible: boolean;
}

const ChartDashboard: React.FC<ChartDashboardProps> = ({
  darkMode,
  userData,
  historicalData = [],
  className = '',
}) => {
  const { t } = useTranslation();

  // Chart controls state
  const [controlsState, setControlsState] = useLocalStorage<ChartControlsState>(
    'chart_controls_state',
    {
      chartType: 'line',
      timeframe: '1Y',
      selectedMetrics: ['netWorth', 'savings'],
      selectedCantons: [],
      showTrendline: false,
      showConfidenceInterval: false,
      enableAnimations: true,
      enableInteractivity: true,
      showComparison: false,
      darkMode,
    }
  );

  // Dashboard layout state
  const [dashboardLayout, setDashboardLayout] = useLocalStorage<
    DashboardLayout[]
  >('dashboard_layout', [
    {
      id: 'netWorth',
      metric: 'netWorth',
      position: { x: 0, y: 0, w: 6, h: 4 },
      visible: true,
    },
    {
      id: 'savings',
      metric: 'savings',
      position: { x: 6, y: 0, w: 6, h: 4 },
      visible: true,
    },
    {
      id: 'expenses',
      metric: 'expenses',
      position: { x: 0, y: 4, w: 6, h: 4 },
      visible: false,
    },
    {
      id: 'income',
      metric: 'income',
      position: { x: 6, y: 4, w: 6, h: 4 },
      visible: false,
    },
  ]);

  // Update controls state when darkMode changes
  useEffect(() => {
    setControlsState(prev => ({ ...prev, darkMode }));
  }, [darkMode, setControlsState]);

  // Generate sample data for demonstration
  const generateSampleData = useCallback(
    (metric: MetricOption): SwissFinancialDataPoint[] => {
      if (historicalData.length > 0) {
        return historicalData.filter(d => d.category === metric);
      }

      // Generate sample data based on user data
      const data: SwissFinancialDataPoint[] = [];
      const startDate = new Date();
      startDate.setFullYear(startDate.getFullYear() - 2);

      for (let i = 0; i < 24; i++) {
        const date = new Date(startDate);
        date.setMonth(date.getMonth() + i);

        let value = 0;
        switch (metric) {
          case 'netWorth':
            value = userData.currentSavings + i * userData.monthlyIncome * 0.2;
            break;
          case 'savings':
            value =
              (userData.monthlyIncome - userData.monthlyExpenses) * (i + 1);
            break;
          case 'expenses':
            value = userData.monthlyExpenses * (1 + Math.random() * 0.1 - 0.05);
            break;
          case 'income':
            value = userData.monthlyIncome * (1 + Math.random() * 0.05);
            break;
          case 'taxes':
            value =
              userData.monthlyIncome * 0.25 * (1 + Math.random() * 0.1 - 0.05);
            break;
          case 'pillar3a':
            value =
              Math.min(7056, userData.monthlyIncome * 0.1 * 12) * (i / 12);
            break;
          case 'healthcare':
            value =
              userData.monthlyIncome * 0.08 * (1 + Math.random() * 0.1 - 0.05);
            break;
          default:
            value = Math.random() * 10000;
        }

        data.push({
          date,
          value,
          category: metric,
          label: t(`metric.${metric}`, metric),
        });
      }

      return data;
    },
    [userData, historicalData, t]
  );

  // Filter data based on timeframe
  const filterDataByTimeframe = useCallback(
    (data: SwissFinancialDataPoint[]) => {
      const now = new Date();
      const cutoffDate = new Date();

      switch (controlsState.timeframe) {
        case '1M':
          cutoffDate.setMonth(now.getMonth() - 1);
          break;
        case '3M':
          cutoffDate.setMonth(now.getMonth() - 3);
          break;
        case '6M':
          cutoffDate.setMonth(now.getMonth() - 6);
          break;
        case '1Y':
          cutoffDate.setFullYear(now.getFullYear() - 1);
          break;
        case '2Y':
          cutoffDate.setFullYear(now.getFullYear() - 2);
          break;
        case '5Y':
          cutoffDate.setFullYear(now.getFullYear() - 5);
          break;
        case 'ALL':
          return data;
      }

      return data.filter(d => d.date >= cutoffDate);
    },
    [controlsState.timeframe]
  );

  // Get chart configuration for a metric
  const getChartConfig = useCallback(
    (metric: MetricOption): SwissChartConfig => {
      return {
        type: controlsState.chartType,
        metric,
        showTrendline: controlsState.showTrendline,
        showConfidenceInterval: controlsState.showConfidenceInterval,
        enableZoom: controlsState.enableInteractivity,
        enableBrush: controlsState.enableInteractivity,
        showCantonComparison: controlsState.showComparison,
        swissFormatting: true,
        interactive: controlsState.enableInteractivity,
        animated: controlsState.enableAnimations,
        responsive: true,
      };
    },
    [controlsState]
  );

  // Handle chart controls change
  const handleControlsChange = useCallback(
    (newState: Partial<ChartControlsState>) => {
      setControlsState(prev => ({ ...prev, ...newState }));
    },
    [setControlsState]
  );

  // Handle chart layout change
  const handleLayoutChange = useCallback(
    (newLayout: DashboardLayout[]) => {
      setDashboardLayout(newLayout);
    },
    [setDashboardLayout]
  );

  // Toggle chart visibility
  const toggleChartVisibility = useCallback(
    (chartId: string) => {
      setDashboardLayout(prev =>
        prev.map(item =>
          item.id === chartId ? { ...item, visible: !item.visible } : item
        )
      );
    },
    [setDashboardLayout]
  );

  // Get visible charts
  const visibleCharts = useMemo(() => {
    return dashboardLayout.filter(
      item =>
        item.visible && controlsState.selectedMetrics.includes(item.metric)
    );
  }, [dashboardLayout, controlsState.selectedMetrics]);

  return (
    <div className={`chart-dashboard ${className}`}>
      {/* Dashboard Header */}
      <div className='mb-6'>
        <h2 className='text-2xl font-bold mb-2'>
          📊 {t('dashboard.title', 'Financial Data Visualization')}
        </h2>
        <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
          {t(
            'dashboard.description',
            'Interactive charts and analytics for your Swiss financial data'
          )}
        </p>
      </div>

      {/* Chart Controls */}
      <div
        className={`mb-6 p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
      >
        <EnhancedChartControls
          state={controlsState}
          onChange={handleControlsChange}
          availableMetrics={[
            'netWorth',
            'savings',
            'expenses',
            'income',
            'taxes',
            'pillar3a',
            'healthcare',
          ]}
          showCantonSelector={false}
          showAdvancedOptions={true}
        />
      </div>

      {/* Dashboard Statistics */}
      <div className='grid grid-cols-2 md:grid-cols-4 gap-4 mb-6'>
        <div
          className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
        >
          <div
            className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}
          >
            {t('dashboard.stats.activeCharts', 'Active Charts')}
          </div>
          <div className='text-2xl font-bold text-blue-600'>
            {visibleCharts.length}
          </div>
        </div>
        <div
          className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
        >
          <div
            className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}
          >
            {t('dashboard.stats.dataPoints', 'Data Points')}
          </div>
          <div className='text-2xl font-bold text-green-600'>
            {generateSampleData('netWorth').length}
          </div>
        </div>
        <div
          className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
        >
          <div
            className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}
          >
            {t('dashboard.stats.timeframe', 'Timeframe')}
          </div>
          <div className='text-2xl font-bold text-purple-600'>
            {controlsState.timeframe}
          </div>
        </div>
        <div
          className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
        >
          <div
            className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}
          >
            {t('dashboard.stats.chartType', 'Chart Type')}
          </div>
          <div className='text-2xl font-bold text-orange-600'>
            {controlsState.chartType.toUpperCase()}
          </div>
        </div>
      </div>

      {/* Charts Grid */}
      <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
        {visibleCharts.map(chartLayout => {
          const data = filterDataByTimeframe(
            generateSampleData(chartLayout.metric)
          );
          const config = getChartConfig(chartLayout.metric);

          return (
            <div
              key={chartLayout.id}
              className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
            >
              {/* Chart Header */}
              <div className='flex items-center justify-between mb-4'>
                <h3 className='text-lg font-semibold'>
                  {t(`metric.${chartLayout.metric}`, chartLayout.metric)}
                </h3>
                <button
                  onClick={() => toggleChartVisibility(chartLayout.id)}
                  className={`p-2 rounded-lg transition-colors ${
                    darkMode
                      ? 'text-gray-400 hover:text-white hover:bg-gray-700'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  }`}
                  title={t('dashboard.hideChart', 'Hide Chart')}
                >
                  ✕
                </button>
              </div>

              {/* Chart Component */}
              <SwissFinancialChart
                data={data}
                config={config}
                darkMode={darkMode}
                width={500}
                height={300}
                onDataPointClick={dataPoint => {
                  console.log('Data point clicked:', dataPoint);
                }}
                onDataPointHover={dataPoint => {
                  console.log('Data point hovered:', dataPoint);
                }}
              />
            </div>
          );
        })}
      </div>

      {/* Empty State */}
      {visibleCharts.length === 0 && (
        <div
          className={`text-center py-12 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}
        >
          <div className='text-6xl mb-4'>📊</div>
          <h3 className='text-xl font-semibold mb-2'>
            {t('dashboard.empty.title', 'No Charts Selected')}
          </h3>
          <p>
            {t(
              'dashboard.empty.description',
              'Select metrics from the controls above to display charts'
            )}
          </p>
        </div>
      )}
    </div>
  );
};

export default ChartDashboard;
