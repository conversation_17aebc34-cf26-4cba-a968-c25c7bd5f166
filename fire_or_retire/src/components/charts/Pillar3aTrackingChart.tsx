import React, { useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import SwissFinancialChart, { SwissFinancialDataPoint, SwissChartConfig } from './SwissFinancialChart';

export interface Pillar3aData {
  year: number;
  contribution: number;
  cumulativeContribution: number;
  taxSavings: number;
  cumulativeTaxSavings: number;
  investmentReturn: number;
  totalValue: number;
  maxContribution: number;
  utilizationRate: number;
}

export interface Pillar3aTrackingChartProps {
  currentAge: number;
  retirementAge: number;
  annualContribution: number;
  expectedReturn: number;
  taxRate: number;
  darkMode: boolean;
  showProjections?: boolean;
  showTaxSavings?: boolean;
  showOptimization?: boolean;
  className?: string;
  onYearClick?: (year: number, data: Pillar3aData) => void;
}

// Swiss Pillar 3a contribution limits
const PILLAR_3A_LIMITS = {
  2024: { employed: 7056, selfEmployed: 35280 },
  2023: { employed: 6883, selfEmployed: 34416 },
  2022: { employed: 6826, selfEmployed: 34128 },
  2021: { employed: 6826, selfEmployed: 34128 },
  2020: { employed: 6826, selfEmployed: 34128 },
};

const Pillar3aTrackingChart: React.FC<Pillar3aTrackingChartProps> = ({
  currentAge,
  retirementAge,
  annualContribution,
  expectedReturn,
  taxRate,
  darkMode,
  showProjections = true,
  showTaxSavings = true,
  showOptimization = true,
  className = '',
  onYearClick,
}) => {
  const { t } = useTranslation();

  // Calculate Pillar 3a projections
  const pillar3aData = useMemo((): Pillar3aData[] => {
    const data: Pillar3aData[] = [];
    const currentYear = new Date().getFullYear();
    const yearsToRetirement = retirementAge - currentAge;
    
    let cumulativeContribution = 0;
    let cumulativeTaxSavings = 0;
    let totalValue = 0;

    for (let i = 0; i <= yearsToRetirement; i++) {
      const year = currentYear + i;
      const age = currentAge + i;
      
      // Get contribution limit for the year (use 2024 limit for future years)
      const maxContribution = year >= 2024 
        ? PILLAR_3A_LIMITS[2024].employed 
        : PILLAR_3A_LIMITS[year as keyof typeof PILLAR_3A_LIMITS]?.employed || 7056;

      // Actual contribution (limited by max)
      const actualContribution = Math.min(annualContribution, maxContribution);
      cumulativeContribution += actualContribution;

      // Tax savings
      const yearlyTaxSavings = actualContribution * (taxRate / 100);
      cumulativeTaxSavings += yearlyTaxSavings;

      // Investment return on existing balance
      const investmentReturn = totalValue * (expectedReturn / 100);
      totalValue = totalValue + actualContribution + investmentReturn;

      // Utilization rate
      const utilizationRate = maxContribution > 0 ? (actualContribution / maxContribution) * 100 : 0;

      data.push({
        year,
        contribution: actualContribution,
        cumulativeContribution,
        taxSavings: yearlyTaxSavings,
        cumulativeTaxSavings,
        investmentReturn,
        totalValue,
        maxContribution,
        utilizationRate,
      });
    }

    return data;
  }, [currentAge, retirementAge, annualContribution, expectedReturn, taxRate]);

  // Convert to chart data points
  const chartData = useMemo((): SwissFinancialDataPoint[] => {
    return pillar3aData.map((data) => ({
      date: new Date(data.year, 0, 1),
      value: data.totalValue,
      category: 'pillar3a',
      pillar3a: data.contribution,
      taxRate: data.taxSavings,
      label: `${data.year}: CHF ${data.totalValue.toLocaleString()}`,
    }));
  }, [pillar3aData]);

  // Tax savings chart data
  const taxSavingsData = useMemo((): SwissFinancialDataPoint[] => {
    return pillar3aData.map((data) => ({
      date: new Date(data.year, 0, 1),
      value: data.cumulativeTaxSavings,
      category: 'taxes',
      pillar3a: data.contribution,
      taxRate: data.taxSavings,
      label: `${data.year}: CHF ${data.cumulativeTaxSavings.toLocaleString()}`,
    }));
  }, [pillar3aData]);

  // Chart configurations
  const valueChartConfig: SwissChartConfig = useMemo(() => ({
    type: 'area',
    metric: 'pillar3a',
    showTrendline: true,
    showConfidenceInterval: false,
    enableZoom: true,
    enableBrush: false,
    showCantonComparison: false,
    swissFormatting: true,
    interactive: true,
    animated: true,
    responsive: true,
  }), []);

  const taxSavingsChartConfig: SwissChartConfig = useMemo(() => ({
    type: 'line',
    metric: 'taxes',
    showTrendline: false,
    showConfidenceInterval: false,
    enableZoom: true,
    enableBrush: false,
    showCantonComparison: false,
    swissFormatting: true,
    interactive: true,
    animated: true,
    responsive: true,
  }), []);

  // Handle year click
  const handleYearClick = useCallback((dataPoint: SwissFinancialDataPoint) => {
    const year = dataPoint.date.getFullYear();
    const yearData = pillar3aData.find(d => d.year === year);
    if (yearData && onYearClick) {
      onYearClick(year, yearData);
    }
  }, [pillar3aData, onYearClick]);

  // Calculate key metrics
  const finalData = pillar3aData[pillar3aData.length - 1];
  const averageUtilization = pillar3aData.reduce((sum, d) => sum + d.utilizationRate, 0) / pillar3aData.length;
  const totalReturn = finalData.totalValue - finalData.cumulativeContribution;
  const effectiveReturnRate = finalData.cumulativeContribution > 0 
    ? ((finalData.totalValue / finalData.cumulativeContribution) ** (1 / pillar3aData.length) - 1) * 100 
    : 0;

  return (
    <div className={`pillar3a-tracking-chart ${className}`}>
      {/* Header */}
      <div className="mb-6">
        <h3 className="text-xl font-bold mb-2">
          🏛️ {t('pillar3a.tracking.title', 'Pillar 3a Retirement Savings Tracking')}
        </h3>
        <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
          {t('pillar3a.tracking.description', 'Track your Swiss Pillar 3a contributions, tax savings, and investment growth')}
        </p>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}>
          <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            {t('pillar3a.finalValue', 'Final Value')}
          </div>
          <div className="text-lg font-bold text-green-600">
            CHF {finalData.totalValue.toLocaleString()}
          </div>
          <div className="text-sm">
            {t('pillar3a.atRetirement', 'at retirement')}
          </div>
        </div>

        <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}>
          <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            {t('pillar3a.totalContributions', 'Total Contributions')}
          </div>
          <div className="text-lg font-bold text-blue-600">
            CHF {finalData.cumulativeContribution.toLocaleString()}
          </div>
          <div className="text-sm">
            {pillar3aData.length} {t('pillar3a.years', 'years')}
          </div>
        </div>

        <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}>
          <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            {t('pillar3a.totalTaxSavings', 'Total Tax Savings')}
          </div>
          <div className="text-lg font-bold text-purple-600">
            CHF {finalData.cumulativeTaxSavings.toLocaleString()}
          </div>
          <div className="text-sm">
            {((finalData.cumulativeTaxSavings / finalData.cumulativeContribution) * 100).toFixed(1)}% {t('pillar3a.ofContributions', 'of contributions')}
          </div>
        </div>

        <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}>
          <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            {t('pillar3a.investmentReturn', 'Investment Return')}
          </div>
          <div className="text-lg font-bold text-orange-600">
            CHF {totalReturn.toLocaleString()}
          </div>
          <div className="text-sm">
            {effectiveReturnRate.toFixed(1)}% {t('pillar3a.annualized', 'annualized')}
          </div>
        </div>
      </div>

      {/* Pillar 3a Value Growth Chart */}
      <div className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'} mb-6`}>
        <h4 className="text-lg font-semibold mb-4">
          📈 {t('pillar3a.valueGrowth', 'Pillar 3a Value Growth')}
        </h4>
        <SwissFinancialChart
          data={chartData}
          config={valueChartConfig}
          darkMode={darkMode}
          width={800}
          height={400}
          onDataPointClick={handleYearClick}
        />
      </div>

      {/* Tax Savings Chart */}
      {showTaxSavings && (
        <div className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'} mb-6`}>
          <h4 className="text-lg font-semibold mb-4">
            💰 {t('pillar3a.cumulativeTaxSavings', 'Cumulative Tax Savings')}
          </h4>
          <SwissFinancialChart
            data={taxSavingsData}
            config={taxSavingsChartConfig}
            darkMode={darkMode}
            width={800}
            height={300}
            onDataPointClick={handleYearClick}
          />
        </div>
      )}

      {/* Optimization Recommendations */}
      {showOptimization && (
        <div className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'} mb-6`}>
          <h4 className="text-lg font-semibold mb-4">
            🎯 {t('pillar3a.optimization', 'Optimization Recommendations')}
          </h4>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Utilization Analysis */}
            <div>
              <h5 className="font-medium mb-3">
                📊 {t('pillar3a.utilizationAnalysis', 'Contribution Utilization')}
              </h5>
              
              <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'} mb-4`}>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm">{t('pillar3a.averageUtilization', 'Average Utilization')}</span>
                  <span className="font-semibold">{averageUtilization.toFixed(1)}%</span>
                </div>
                <div className={`w-full bg-gray-300 rounded-full h-2 ${darkMode ? 'bg-gray-600' : ''}`}>
                  <div 
                    className="bg-blue-600 h-2 rounded-full" 
                    style={{ width: `${Math.min(averageUtilization, 100)}%` }}
                  ></div>
                </div>
              </div>

              {averageUtilization < 100 && (
                <div className={`p-4 rounded-lg border-l-4 border-yellow-500 ${darkMode ? 'bg-yellow-900/20' : 'bg-yellow-50'}`}>
                  <p className="text-sm">
                    💡 {t('pillar3a.underUtilized', 'You could increase your contributions by')} CHF {(PILLAR_3A_LIMITS[2024].employed - annualContribution).toLocaleString()} {t('pillar3a.perYear', 'per year')} {t('pillar3a.toMaximize', 'to maximize tax benefits')}.
                  </p>
                </div>
              )}
            </div>

            {/* Tax Efficiency */}
            <div>
              <h5 className="font-medium mb-3">
                🏛️ {t('pillar3a.taxEfficiency', 'Tax Efficiency')}
              </h5>
              
              <div className="space-y-3">
                <div className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    {t('pillar3a.effectiveTaxReturn', 'Effective Tax Return')}
                  </div>
                  <div className="text-lg font-semibold text-green-600">
                    {taxRate.toFixed(1)}%
                  </div>
                </div>

                <div className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    {t('pillar3a.annualTaxSavings', 'Annual Tax Savings')}
                  </div>
                  <div className="text-lg font-semibold text-purple-600">
                    CHF {(annualContribution * (taxRate / 100)).toLocaleString()}
                  </div>
                </div>

                <div className={`p-4 rounded-lg border-l-4 border-green-500 ${darkMode ? 'bg-green-900/20' : 'bg-green-50'}`}>
                  <p className="text-sm">
                    ✅ {t('pillar3a.taxBenefit', 'Your Pillar 3a contributions provide an immediate')} {taxRate.toFixed(1)}% {t('pillar3a.returnOnInvestment', 'return through tax savings')}.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Yearly Breakdown Table */}
      {showProjections && (
        <div className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}>
          <h4 className="text-lg font-semibold mb-4">
            📅 {t('pillar3a.yearlyBreakdown', 'Yearly Breakdown')}
          </h4>
          
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className={`border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
                  <th className="text-left py-2">{t('pillar3a.year', 'Year')}</th>
                  <th className="text-right py-2">{t('pillar3a.contribution', 'Contribution')}</th>
                  <th className="text-right py-2">{t('pillar3a.taxSavings', 'Tax Savings')}</th>
                  <th className="text-right py-2">{t('pillar3a.investmentReturn', 'Investment Return')}</th>
                  <th className="text-right py-2">{t('pillar3a.totalValue', 'Total Value')}</th>
                  <th className="text-right py-2">{t('pillar3a.utilization', 'Utilization')}</th>
                </tr>
              </thead>
              <tbody>
                {pillar3aData.slice(0, 10).map((data) => (
                  <tr
                    key={data.year}
                    className={`border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'} hover:bg-opacity-50 ${
                      darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-50'
                    } cursor-pointer`}
                    onClick={() => onYearClick?.(data.year, data)}
                  >
                    <td className="py-2 font-medium">{data.year}</td>
                    <td className="text-right py-2">CHF {data.contribution.toLocaleString()}</td>
                    <td className="text-right py-2 text-purple-600">CHF {data.taxSavings.toLocaleString()}</td>
                    <td className="text-right py-2 text-orange-600">CHF {data.investmentReturn.toLocaleString()}</td>
                    <td className="text-right py-2 font-semibold text-green-600">CHF {data.totalValue.toLocaleString()}</td>
                    <td className="text-right py-2">{data.utilizationRate.toFixed(1)}%</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {pillar3aData.length > 10 && (
            <div className={`mt-4 text-center text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              {t('pillar3a.showingFirst10', 'Showing first 10 years')} • {pillar3aData.length} {t('pillar3a.totalYears', 'total years')}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default Pillar3aTrackingChart;
