/**
 * Swiss Budget Pro - Security Dashboard Component
 *
 * Provides a comprehensive security overview with real-time monitoring,
 * privacy controls, and audit trail management.
 */

import React, { useState, useEffect } from 'react';
import {
  Shield,
  Lock,
  Eye,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Settings,
  Download,
  Trash2,
  Activity,
  BarChart3,
  Clock,
} from 'lucide-react';

import {
  securityMonitor,
  SecurityEventType,
  SecuritySeverity,
} from '../../security/security-monitor';
import { privacyControls, DataCategory } from '../../security/privacy-controls';
import { encryption, SecurityUtils } from '../../security/encryption';

interface SecurityDashboardProps {
  className?: string;
}

export const SecurityDashboard: React.FC<SecurityDashboardProps> = ({
  className = '',
}) => {
  const [activeTab, setActiveTab] = useState<
    'overview' | 'events' | 'privacy' | 'encryption'
  >('overview');
  const [securityStatus, setSecurityStatus] = useState(
    securityMonitor.getSecurityStatus()
  );
  const [metrics, setMetrics] = useState(securityMonitor.getMetrics());
  const [events, setEvents] = useState(
    securityMonitor.getEvents().slice(0, 10)
  );
  const [privacySettings, setPrivacySettings] = useState(
    privacyControls.getPrivacySettings()
  );

  // Refresh data every 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setSecurityStatus(securityMonitor.getSecurityStatus());
      setMetrics(securityMonitor.getMetrics());
      setEvents(securityMonitor.getEvents().slice(0, 10));
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'secure':
        return 'text-green-600 bg-green-100';
      case 'warning':
        return 'text-yellow-600 bg-yellow-100';
      case 'critical':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getSeverityColor = (severity: SecuritySeverity) => {
    switch (severity) {
      case SecuritySeverity.LOW:
        return 'text-blue-600 bg-blue-100';
      case SecuritySeverity.MEDIUM:
        return 'text-yellow-600 bg-yellow-100';
      case SecuritySeverity.HIGH:
        return 'text-orange-600 bg-orange-100';
      case SecuritySeverity.CRITICAL:
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const handlePrivacySettingChange = (
    category: DataCategory,
    setting: string,
    value: any
  ) => {
    privacyControls.updatePrivacySettings(category, { [setting]: value });
    setPrivacySettings(privacyControls.getPrivacySettings());

    securityMonitor.logEvent(
      SecurityEventType.PRIVACY_SETTINGS_CHANGED,
      SecuritySeverity.LOW,
      `Privacy setting changed for ${category}`,
      { category, setting, value }
    );
  };

  const handleDataExport = () => {
    const dataInventory = privacyControls.getDataInventory();
    const exportData = {
      timestamp: new Date().toISOString(),
      securityEvents: events,
      privacySettings,
      dataInventory,
      metrics,
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json',
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `swiss-budget-security-export-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);

    securityMonitor.logEvent(
      SecurityEventType.DATA_EXPORT,
      SecuritySeverity.LOW,
      'Security data exported',
      { exportType: 'security_dashboard' }
    );
  };

  const renderOverviewTab = () => (
    <div className='space-y-6'>
      {/* Security Status */}
      <div className='bg-white rounded-lg shadow-sm border p-6'>
        <div className='flex items-center justify-between mb-4'>
          <h3 className='text-lg font-semibold text-gray-900'>
            Security Status
          </h3>
          <div
            className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(securityStatus.overall)}`}
          >
            {securityStatus.overall.toUpperCase()}
          </div>
        </div>

        <div className='grid grid-cols-1 md:grid-cols-3 gap-4 mb-4'>
          <div className='text-center'>
            <div className='text-2xl font-bold text-gray-900'>
              {securityStatus.threats}
            </div>
            <div className='text-sm text-gray-600'>Active Threats</div>
          </div>
          <div className='text-center'>
            <div className='text-2xl font-bold text-gray-900'>
              {securityStatus.complianceScore}%
            </div>
            <div className='text-sm text-gray-600'>Compliance Score</div>
          </div>
          <div className='text-center'>
            <div className='text-2xl font-bold text-gray-900'>
              {metrics.totalEvents}
            </div>
            <div className='text-sm text-gray-600'>Total Events</div>
          </div>
        </div>

        {securityStatus.recommendations.length > 0 && (
          <div className='bg-yellow-50 border border-yellow-200 rounded-lg p-4'>
            <h4 className='text-sm font-medium text-yellow-800 mb-2'>
              Recommendations
            </h4>
            <ul className='text-sm text-yellow-700 space-y-1'>
              {securityStatus.recommendations.map((rec, index) => (
                <li key={index} className='flex items-start'>
                  <AlertTriangle className='w-4 h-4 mr-2 mt-0.5 flex-shrink-0' />
                  {rec}
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>

      {/* Quick Stats */}
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4'>
        <div className='bg-white rounded-lg shadow-sm border p-4'>
          <div className='flex items-center'>
            <Shield className='w-8 h-8 text-blue-600 mr-3' />
            <div>
              <div className='text-lg font-semibold text-gray-900'>
                {metrics.threatsResolved}
              </div>
              <div className='text-sm text-gray-600'>Threats Resolved</div>
            </div>
          </div>
        </div>

        <div className='bg-white rounded-lg shadow-sm border p-4'>
          <div className='flex items-center'>
            <Lock className='w-8 h-8 text-green-600 mr-3' />
            <div>
              <div className='text-lg font-semibold text-gray-900'>
                {encryption.getMetrics().length}
              </div>
              <div className='text-sm text-gray-600'>Encryption Operations</div>
            </div>
          </div>
        </div>

        <div className='bg-white rounded-lg shadow-sm border p-4'>
          <div className='flex items-center'>
            <Eye className='w-8 h-8 text-purple-600 mr-3' />
            <div>
              <div className='text-lg font-semibold text-gray-900'>
                {privacyControls.getDataInventory().length}
              </div>
              <div className='text-sm text-gray-600'>Data Items Tracked</div>
            </div>
          </div>
        </div>

        <div className='bg-white rounded-lg shadow-sm border p-4'>
          <div className='flex items-center'>
            <Clock className='w-8 h-8 text-orange-600 mr-3' />
            <div>
              <div className='text-lg font-semibold text-gray-900'>
                {Math.round(metrics.averageResponseTime)}s
              </div>
              <div className='text-sm text-gray-600'>Avg Response Time</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderEventsTab = () => (
    <div className='space-y-6'>
      <div className='bg-white rounded-lg shadow-sm border'>
        <div className='p-6 border-b border-gray-200'>
          <div className='flex items-center justify-between'>
            <h3 className='text-lg font-semibold text-gray-900'>
              Recent Security Events
            </h3>
            <button
              onClick={handleDataExport}
              className='flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200'
            >
              <Download className='w-4 h-4 mr-2' />
              Export
            </button>
          </div>
        </div>

        <div className='overflow-x-auto'>
          <table className='w-full'>
            <thead className='bg-gray-50'>
              <tr>
                <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                  Timestamp
                </th>
                <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                  Type
                </th>
                <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                  Severity
                </th>
                <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                  Description
                </th>
                <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                  Status
                </th>
              </tr>
            </thead>
            <tbody className='bg-white divide-y divide-gray-200'>
              {events.map(event => (
                <tr key={event.id} className='hover:bg-gray-50'>
                  <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-900'>
                    {event.timestamp.toLocaleString()}
                  </td>
                  <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-900'>
                    {event.type.replace(/_/g, ' ').toLowerCase()}
                  </td>
                  <td className='px-6 py-4 whitespace-nowrap'>
                    <span
                      className={`px-2 py-1 text-xs font-medium rounded-full ${getSeverityColor(event.severity)}`}
                    >
                      {event.severity}
                    </span>
                  </td>
                  <td className='px-6 py-4 text-sm text-gray-900'>
                    {event.description}
                  </td>
                  <td className='px-6 py-4 whitespace-nowrap'>
                    {event.resolved ? (
                      <CheckCircle className='w-5 h-5 text-green-600' />
                    ) : (
                      <XCircle className='w-5 h-5 text-red-600' />
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );

  const renderPrivacyTab = () => (
    <div className='space-y-6'>
      <div className='bg-white rounded-lg shadow-sm border p-6'>
        <h3 className='text-lg font-semibold text-gray-900 mb-4'>
          Privacy Controls
        </h3>

        <div className='space-y-6'>
          {Object.entries(privacySettings).map(([category, settings]) => (
            <div
              key={category}
              className='border border-gray-200 rounded-lg p-4'
            >
              <h4 className='text-md font-medium text-gray-900 mb-3'>
                {category
                  .replace(/_/g, ' ')
                  .replace(/\b\w/g, l => l.toUpperCase())}
              </h4>

              <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4'>
                <div className='flex items-center'>
                  <input
                    type='checkbox'
                    id={`${category}-enabled`}
                    checked={settings.enabled}
                    onChange={e =>
                      handlePrivacySettingChange(
                        category as DataCategory,
                        'enabled',
                        e.target.checked
                      )
                    }
                    className='h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded'
                  />
                  <label
                    htmlFor={`${category}-enabled`}
                    className='ml-2 text-sm text-gray-700'
                  >
                    Enabled
                  </label>
                </div>

                <div>
                  <label className='block text-sm text-gray-700 mb-1'>
                    Retention (days)
                  </label>
                  <input
                    type='number'
                    value={settings.retentionDays}
                    onChange={e =>
                      handlePrivacySettingChange(
                        category as DataCategory,
                        'retentionDays',
                        parseInt(e.target.value)
                      )
                    }
                    className='w-full px-3 py-1 border border-gray-300 rounded-md text-sm'
                    min='1'
                    max='3650'
                  />
                </div>

                {'encryptionRequired' in settings && (
                  <div className='flex items-center'>
                    <input
                      type='checkbox'
                      id={`${category}-encryption`}
                      checked={(settings as any).encryptionRequired}
                      onChange={e =>
                        handlePrivacySettingChange(
                          category as DataCategory,
                          'encryptionRequired',
                          e.target.checked
                        )
                      }
                      className='h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded'
                    />
                    <label
                      htmlFor={`${category}-encryption`}
                      className='ml-2 text-sm text-gray-700'
                    >
                      Encryption Required
                    </label>
                  </div>
                )}

                <div className='flex items-center'>
                  <input
                    type='checkbox'
                    id={`${category}-export`}
                    checked={settings.exportAllowed}
                    onChange={e =>
                      handlePrivacySettingChange(
                        category as DataCategory,
                        'exportAllowed',
                        e.target.checked
                      )
                    }
                    className='h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded'
                  />
                  <label
                    htmlFor={`${category}-export`}
                    className='ml-2 text-sm text-gray-700'
                  >
                    Export Allowed
                  </label>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderEncryptionTab = () => {
    const encryptionMetrics = encryption.getMetrics();
    const avgEncryptionTime =
      encryptionMetrics.length > 0
        ? encryptionMetrics.reduce((sum, m) => sum + m.encryptionTime, 0) /
          encryptionMetrics.length
        : 0;
    const avgDecryptionTime =
      encryptionMetrics.length > 0
        ? encryptionMetrics.reduce((sum, m) => sum + m.decryptionTime, 0) /
          encryptionMetrics.length
        : 0;

    return (
      <div className='space-y-6'>
        <div className='bg-white rounded-lg shadow-sm border p-6'>
          <h3 className='text-lg font-semibold text-gray-900 mb-4'>
            Encryption Status
          </h3>

          <div className='grid grid-cols-1 md:grid-cols-3 gap-4 mb-6'>
            <div className='text-center'>
              <div className='text-2xl font-bold text-gray-900'>
                {encryptionMetrics.length}
              </div>
              <div className='text-sm text-gray-600'>Total Operations</div>
            </div>
            <div className='text-center'>
              <div className='text-2xl font-bold text-gray-900'>
                {Math.round(avgEncryptionTime)}ms
              </div>
              <div className='text-sm text-gray-600'>Avg Encryption Time</div>
            </div>
            <div className='text-center'>
              <div className='text-2xl font-bold text-gray-900'>
                {Math.round(avgDecryptionTime)}ms
              </div>
              <div className='text-sm text-gray-600'>Avg Decryption Time</div>
            </div>
          </div>

          <div className='bg-green-50 border border-green-200 rounded-lg p-4'>
            <div className='flex items-center'>
              <CheckCircle className='w-5 h-5 text-green-600 mr-2' />
              <div>
                <h4 className='text-sm font-medium text-green-800'>
                  AES-256-GCM Encryption Active
                </h4>
                <p className='text-sm text-green-700'>
                  All sensitive data is encrypted with bank-level security using
                  AES-256-GCM encryption.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className={`max-w-7xl mx-auto ${className}`}>
      {/* Header */}
      <div className='mb-6'>
        <h1 className='text-2xl font-bold text-gray-900 mb-2'>
          Security Dashboard
        </h1>
        <p className='text-gray-600'>
          Monitor security status, manage privacy settings, and review audit
          trails.
        </p>
      </div>

      {/* Tabs */}
      <div className='border-b border-gray-200 mb-6'>
        <nav className='-mb-px flex space-x-8'>
          {[
            { id: 'overview', label: 'Overview', icon: BarChart3 },
            { id: 'events', label: 'Security Events', icon: Activity },
            { id: 'privacy', label: 'Privacy Controls', icon: Eye },
            { id: 'encryption', label: 'Encryption', icon: Lock },
          ].map(({ id, label, icon: Icon }) => (
            <button
              key={id}
              onClick={() => setActiveTab(id as any)}
              className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <Icon className='w-4 h-4 mr-2' />
              {label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && renderOverviewTab()}
      {activeTab === 'events' && renderEventsTab()}
      {activeTab === 'privacy' && renderPrivacyTab()}
      {activeTab === 'encryption' && renderEncryptionTab()}
    </div>
  );
};
