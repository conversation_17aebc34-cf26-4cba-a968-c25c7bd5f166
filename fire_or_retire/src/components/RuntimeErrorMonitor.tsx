import React, { useEffect, useState } from 'react';
import { debugLogger } from '../utils/debug-logger';
import DebugPanel from './DebugPanel';
import { useDebugPanel } from '../hooks/useDebugPanel';

interface RuntimeError {
  id: string;
  timestamp: string;
  type: 'javascript' | 'promise' | 'resource' | 'network';
  message: string;
  source?: string;
  line?: number;
  column?: number;
  stack?: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

interface RuntimeErrorMonitorProps {
  children: React.ReactNode;
  enableAutoCapture?: boolean;
  showErrorToast?: boolean;
  onError?: (error: RuntimeError) => void;
}

const RuntimeErrorMonitor: React.FC<RuntimeErrorMonitorProps> = ({
  children,
  enableAutoCapture = true,
  showErrorToast = true,
  onError,
}) => {
  const [errors, setErrors] = useState<RuntimeError[]>([]);
  const [showToast, setShowToast] = useState(false);
  const [latestError, setLatestError] = useState<RuntimeError | null>(null);
  const { isOpen, togglePanel, openPanel, closePanel } = useDebugPanel();

  useEffect(() => {
    if (!enableAutoCapture) return;

    // JavaScript runtime errors
    const handleError = (event: ErrorEvent) => {
      const error: RuntimeError = {
        id: `js_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: new Date().toISOString(),
        type: 'javascript',
        message: event.message,
        source: event.filename,
        line: event.lineno,
        column: event.colno,
        stack: event.error?.stack,
        severity: determineSeverity(event.message, event.error),
      };

      addError(error);

      debugLogger.log(
        'error',
        'runtime-monitor',
        'JavaScript runtime error captured',
        {
          error,
          userAgent: navigator.userAgent,
          url: window.location.href,
        }
      );
    };

    // Unhandled promise rejections
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      const error: RuntimeError = {
        id: `promise_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: new Date().toISOString(),
        type: 'promise',
        message: event.reason?.message || String(event.reason),
        stack: event.reason?.stack,
        severity: determineSeverity(
          event.reason?.message || String(event.reason),
          event.reason
        ),
      };

      addError(error);

      debugLogger.log(
        'error',
        'runtime-monitor',
        'Unhandled promise rejection captured',
        {
          error,
          reason: event.reason,
        }
      );
    };

    // Resource loading errors
    const handleResourceError = (event: Event) => {
      const target = event.target as HTMLElement;
      if (
        target &&
        (target.tagName === 'IMG' ||
          target.tagName === 'SCRIPT' ||
          target.tagName === 'LINK')
      ) {
        const error: RuntimeError = {
          id: `resource_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          timestamp: new Date().toISOString(),
          type: 'resource',
          message: `Failed to load resource: ${(target as any).src || (target as any).href}`,
          source: (target as any).src || (target as any).href,
          severity: 'medium',
        };

        addError(error);

        debugLogger.log(
          'warn',
          'runtime-monitor',
          'Resource loading error captured',
          {
            error,
            element: target.tagName,
            src: (target as any).src || (target as any).href,
          }
        );
      }
    };

    // Network errors (fetch failures)
    const originalFetch = window.fetch;
    window.fetch = async (...args) => {
      try {
        const response = await originalFetch(...args);
        if (!response.ok) {
          const error: RuntimeError = {
            id: `network_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            timestamp: new Date().toISOString(),
            type: 'network',
            message: `Network request failed: ${response.status} ${response.statusText}`,
            source: args[0]?.toString(),
            severity: response.status >= 500 ? 'high' : 'medium',
          };

          addError(error);

          debugLogger.log('warn', 'runtime-monitor', 'Network error captured', {
            error,
            status: response.status,
            statusText: response.statusText,
            url: args[0],
          });
        }
        return response;
      } catch (fetchError) {
        const error: RuntimeError = {
          id: `network_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          timestamp: new Date().toISOString(),
          type: 'network',
          message: `Network request failed: ${(fetchError as Error).message}`,
          source: args[0]?.toString(),
          severity: 'high',
        };

        addError(error);

        debugLogger.log(
          'error',
          'runtime-monitor',
          'Network fetch error captured',
          {
            error,
            fetchError,
            url: args[0],
          }
        );

        throw fetchError;
      }
    };

    // Add event listeners
    window.addEventListener('error', handleError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);
    window.addEventListener('error', handleResourceError, true); // Capture phase for resource errors

    // Cleanup
    return () => {
      window.removeEventListener('error', handleError);
      window.removeEventListener(
        'unhandledrejection',
        handleUnhandledRejection
      );
      window.removeEventListener('error', handleResourceError, true);
      window.fetch = originalFetch;
    };
  }, [enableAutoCapture]);

  const determineSeverity = (
    message: string,
    error?: any
  ): RuntimeError['severity'] => {
    const lowerMessage = message?.toLowerCase() || '';

    // Critical errors that break the app
    if (
      lowerMessage.includes('referenceerror') ||
      lowerMessage.includes('typeerror') ||
      lowerMessage.includes('syntaxerror') ||
      lowerMessage.includes('is not defined')
    ) {
      return 'critical';
    }

    // High severity errors
    if (
      lowerMessage.includes('network') ||
      lowerMessage.includes('fetch') ||
      lowerMessage.includes('cors') ||
      lowerMessage.includes('unauthorized')
    ) {
      return 'high';
    }

    // Medium severity warnings
    if (
      lowerMessage.includes('warning') ||
      lowerMessage.includes('deprecated') ||
      lowerMessage.includes('fallback')
    ) {
      return 'medium';
    }

    return 'low';
  };

  const addError = (error: RuntimeError) => {
    setErrors(prev => [...prev.slice(-49), error]); // Keep last 50 errors
    setLatestError(error);

    if (showErrorToast && error.severity !== 'low') {
      setShowToast(true);
      setTimeout(() => setShowToast(false), 5000);
    }

    if (onError) {
      onError(error);
    }
  };

  const getSeverityColor = (severity: RuntimeError['severity']) => {
    switch (severity) {
      case 'critical':
        return 'bg-red-600';
      case 'high':
        return 'bg-orange-600';
      case 'medium':
        return 'bg-yellow-600';
      case 'low':
        return 'bg-blue-600';
      default:
        return 'bg-gray-600';
    }
  };

  const getSeverityIcon = (severity: RuntimeError['severity']) => {
    switch (severity) {
      case 'critical':
        return '🚨';
      case 'high':
        return '⚠️';
      case 'medium':
        return '⚡';
      case 'low':
        return 'ℹ️';
      default:
        return '❓';
    }
  };

  const criticalErrors = errors.filter(e => e.severity === 'critical').length;
  const highErrors = errors.filter(e => e.severity === 'high').length;

  return (
    <>
      {children}

      {/* Error Toast */}
      {showToast && latestError && (
        <div className='fixed top-4 right-4 z-50 max-w-md'>
          <div
            className={`${getSeverityColor(latestError.severity)} text-white p-4 rounded-lg shadow-lg border border-white/20`}
          >
            <div className='flex items-start justify-between'>
              <div className='flex items-start space-x-2'>
                <span className='text-lg'>
                  {getSeverityIcon(latestError.severity)}
                </span>
                <div>
                  <div className='font-medium'>Runtime Error Detected</div>
                  <div className='text-sm opacity-90 mt-1'>
                    {latestError.message.length > 100
                      ? `${latestError.message.substring(0, 100)}...`
                      : latestError.message}
                  </div>
                  <button
                    onClick={openPanel}
                    className='text-sm underline opacity-75 hover:opacity-100 mt-2'
                  >
                    View Details
                  </button>
                </div>
              </div>
              <button
                onClick={() => setShowToast(false)}
                className='text-white/70 hover:text-white'
              >
                ✕
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Error Counter Badge (Development only) */}
      {process.env.NODE_ENV === 'development' && errors.length > 0 && (
        <div className='fixed bottom-4 right-4 z-40'>
          <button
            onClick={togglePanel}
            className='bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded-full shadow-lg border border-white/20 transition-colors'
            title='Click to open debug panel'
          >
            <div className='flex items-center space-x-2'>
              <span>🐛</span>
              <span className='text-sm font-medium'>
                {criticalErrors > 0 && (
                  <span className='text-red-200'>{criticalErrors}🚨</span>
                )}
                {highErrors > 0 && (
                  <span className='text-orange-200'>{highErrors}⚠️</span>
                )}
                {errors.length}
              </span>
            </div>
          </button>
        </div>
      )}

      {/* Debug Panel */}
      <DebugPanel isOpen={isOpen} onClose={closePanel} />

      {/* Global keyboard shortcuts info */}
      {process.env.NODE_ENV === 'development' && (
        <div className='fixed bottom-4 left-4 z-40 text-xs text-gray-400 bg-gray-900/80 px-2 py-1 rounded'>
          Ctrl+Shift+D: Debug Panel | Ctrl+Shift+L: Export Logs | Ctrl+Shift+C:
          Clear Logs
        </div>
      )}
    </>
  );
};

export default RuntimeErrorMonitor;
