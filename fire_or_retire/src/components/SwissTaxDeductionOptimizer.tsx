import React, { useState, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

interface SwissTaxDeductionOptimizerProps {
  darkMode: boolean;
  userData: {
    annualIncome: number;
    canton: string;
    maritalStatus: 'single' | 'married';
    children: number;
    currentPillar3a: number;
    professionalExpenses: number;
    insurancePremiums: number;
    medicalExpenses: number;
    donations: number;
    interestExpenses: number;
  };
  onDeductionChange: (deductions: any) => void;
}

interface DeductionCategory {
  id: string;
  name: string;
  description: string;
  maxAmount: number;
  currentAmount: number;
  potentialSavings: number;
  requirements: string[];
  tips: string[];
  riskLevel: 'Low' | 'Medium' | 'High';
  documentation: string[];
}

const SwissTaxDeductionOptimizer: React.FC<SwissTaxDeductionOptimizerProps> = ({
  darkMode,
  userData,
  onDeductionChange,
}) => {
  const { t } = useTranslation();
  const [selectedCategory, setSelectedCategory] = useState<string>('pillar3a');
  const [optimizedDeductions, setOptimizedDeductions] = useState<any>({});

  // Calculate effective tax rate (simplified)
  const effectiveTaxRate = useMemo(() => {
    // Simplified calculation based on income and canton
    const baseRate =
      userData.annualIncome > 100000
        ? 0.25
        : userData.annualIncome > 50000
          ? 0.2
          : 0.15;

    // Canton adjustments (simplified)
    const cantonMultiplier =
      userData.canton === 'ZG'
        ? 0.7
        : userData.canton === 'SZ'
          ? 0.75
          : userData.canton === 'GE'
            ? 1.3
            : userData.canton === 'VD'
              ? 1.25
              : 1.0;

    return baseRate * cantonMultiplier;
  }, [userData.annualIncome, userData.canton]);

  // Define deduction categories with Swiss-specific rules
  const deductionCategories: DeductionCategory[] = useMemo(() => {
    const categories: DeductionCategory[] = [
      {
        id: 'pillar3a',
        name: 'Pillar 3a Contributions',
        description:
          'Tax-privileged retirement savings with immediate tax deductions',
        maxAmount: 7056, // 2024 limit for employees
        currentAmount: userData.currentPillar3a,
        potentialSavings: (7056 - userData.currentPillar3a) * effectiveTaxRate,
        requirements: [
          'Must be employed or self-employed in Switzerland',
          'Cannot exceed annual contribution limit',
          'Funds locked until age 60 (with exceptions)',
        ],
        tips: [
          'Contribute early in the year for maximum compound growth',
          'Choose investment-based Pillar 3a for higher returns',
          'Consider multiple Pillar 3a accounts for withdrawal flexibility',
          'Time withdrawals strategically to minimize taxes',
        ],
        riskLevel: 'Low',
        documentation: [
          'Pillar 3a contribution certificates',
          'Bank or insurance statements',
          'Investment performance reports',
        ],
      },
      {
        id: 'professional',
        name: 'Professional Expenses',
        description:
          'Work-related expenses including transport, meals, and equipment',
        maxAmount: Math.min(userData.annualIncome * 0.2, 4000),
        currentAmount: userData.professionalExpenses,
        potentialSavings:
          (Math.min(userData.annualIncome * 0.2, 4000) -
            userData.professionalExpenses) *
          effectiveTaxRate,
        requirements: [
          'Expenses must be work-related and necessary',
          'Cannot exceed 20% of income or CHF 4,000',
          'Must maintain detailed records and receipts',
        ],
        tips: [
          'Track daily commuting costs and work meals',
          'Include home office expenses if working from home',
          'Deduct professional development and training costs',
          'Keep digital copies of all receipts',
        ],
        riskLevel: 'Low',
        documentation: [
          'Transport receipts and season tickets',
          'Meal receipts during work hours',
          'Equipment and software purchases',
          'Training and conference expenses',
        ],
      },
      {
        id: 'insurance',
        name: 'Insurance Premiums',
        description: 'Health, accident, and life insurance premiums',
        maxAmount: userData.annualIncome * 0.1, // Typically up to 10% of income
        currentAmount: userData.insurancePremiums,
        potentialSavings:
          (userData.annualIncome * 0.1 - userData.insurancePremiums) *
          effectiveTaxRate,
        requirements: [
          'Must be mandatory or voluntary insurance in Switzerland',
          'Premiums must be paid during the tax year',
          'Cannot exceed reasonable limits based on income',
        ],
        tips: [
          'Include health insurance premiums (basic and supplementary)',
          'Deduct life and disability insurance premiums',
          'Consider increasing deductible to lower premiums',
          'Review insurance needs annually',
        ],
        riskLevel: 'Low',
        documentation: [
          'Health insurance premium statements',
          'Life insurance premium receipts',
          'Accident insurance confirmations',
        ],
      },
      {
        id: 'medical',
        name: 'Medical Expenses',
        description:
          'Out-of-pocket medical and dental expenses above insurance coverage',
        maxAmount: userData.annualIncome * 0.05, // Typically 5% threshold
        currentAmount: userData.medicalExpenses,
        potentialSavings:
          Math.max(0, userData.medicalExpenses - userData.annualIncome * 0.05) *
          effectiveTaxRate,
        requirements: [
          'Expenses must exceed 5% of net income threshold',
          'Must be medically necessary treatments',
          'Cannot be reimbursed by insurance',
        ],
        tips: [
          'Keep all medical and dental receipts',
          'Include prescription medications',
          'Deduct medical equipment and aids',
          'Consider timing elective procedures',
        ],
        riskLevel: 'Medium',
        documentation: [
          'Medical bills and receipts',
          'Prescription receipts',
          'Dental treatment invoices',
          'Medical equipment purchases',
        ],
      },
      {
        id: 'donations',
        name: 'Charitable Donations',
        description: 'Donations to recognized Swiss charitable organizations',
        maxAmount: userData.annualIncome * 0.2, // Up to 20% of income
        currentAmount: userData.donations,
        potentialSavings:
          (Math.min(userData.annualIncome * 0.2, userData.donations + 1000) -
            userData.donations) *
          effectiveTaxRate,
        requirements: [
          'Must be to recognized tax-exempt organizations',
          'Cannot exceed 20% of net income',
          'Must have official donation receipts',
        ],
        tips: [
          "Verify organization's tax-exempt status",
          'Consider regular monthly donations',
          'Donate appreciated securities for additional benefits',
          'Time large donations strategically',
        ],
        riskLevel: 'Low',
        documentation: [
          'Official donation receipts',
          'Organization tax-exempt certificates',
          'Bank transfer confirmations',
        ],
      },
      {
        id: 'interest',
        name: 'Interest Expenses',
        description: 'Interest on loans for income-producing investments',
        maxAmount: userData.annualIncome * 0.1, // Reasonable limit
        currentAmount: userData.interestExpenses,
        potentialSavings:
          (Math.min(
            userData.annualIncome * 0.1,
            userData.interestExpenses + 2000
          ) -
            userData.interestExpenses) *
          effectiveTaxRate,
        requirements: [
          'Interest must be for income-producing investments',
          'Cannot exceed investment income in most cases',
          'Must maintain clear documentation',
        ],
        tips: [
          'Only deduct interest on investment loans',
          'Separate investment and personal loan interest',
          'Consider debt structuring for tax efficiency',
          'Maintain detailed loan documentation',
        ],
        riskLevel: 'High',
        documentation: [
          'Loan agreements and statements',
          'Interest payment receipts',
          'Investment income statements',
          'Bank loan confirmations',
        ],
      },
    ];

    return categories.filter(cat => cat.potentialSavings > 0);
  }, [userData, effectiveTaxRate]);

  // Calculate total optimization potential
  const totalOptimizationPotential = useMemo(() => {
    return deductionCategories.reduce(
      (total, category) => total + category.potentialSavings,
      0
    );
  }, [deductionCategories]);

  // Handle deduction optimization
  const optimizeDeduction = (categoryId: string, amount: number) => {
    const category = deductionCategories.find(cat => cat.id === categoryId);
    if (!category) return;

    const optimizedAmount = Math.min(amount, category.maxAmount);
    const newDeductions = {
      ...optimizedDeductions,
      [categoryId]: optimizedAmount,
    };

    setOptimizedDeductions(newDeductions);
    onDeductionChange(newDeductions);
  };

  const selectedCategoryData = deductionCategories.find(
    cat => cat.id === selectedCategory
  );

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div
        className={`p-6 rounded-lg border ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}
      >
        <h3
          className={`text-xl font-bold mb-2 ${darkMode ? 'text-white' : 'text-gray-900'}`}
        >
          📋 Swiss Tax Deduction Optimizer
        </h3>
        <p className={`${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
          Maximize your tax deductions with Swiss-compliant strategies and
          documentation guidance
        </p>
      </div>

      {/* Optimization Summary */}
      <div
        className={`p-6 rounded-lg border ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}
      >
        <h4
          className={`text-lg font-semibold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}
        >
          💰 Optimization Potential
        </h4>
        <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
          <div
            className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}
          >
            <div
              className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}
            >
              Current Deductions
            </div>
            <div
              className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}
            >
              CHF{' '}
              {(
                userData.currentPillar3a +
                userData.professionalExpenses +
                userData.insurancePremiums +
                userData.medicalExpenses +
                userData.donations +
                userData.interestExpenses
              ).toLocaleString()}
            </div>
          </div>
          <div
            className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}
          >
            <div
              className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}
            >
              Potential Additional Deductions
            </div>
            <div
              className={`text-xl font-bold ${darkMode ? 'text-blue-400' : 'text-blue-600'}`}
            >
              CHF{' '}
              {deductionCategories
                .reduce(
                  (total, cat) => total + (cat.maxAmount - cat.currentAmount),
                  0
                )
                .toLocaleString()}
            </div>
          </div>
          <div
            className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}
          >
            <div
              className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}
            >
              Annual Tax Savings
            </div>
            <div
              className={`text-xl font-bold ${darkMode ? 'text-green-400' : 'text-green-600'}`}
            >
              CHF {totalOptimizationPotential.toLocaleString()}
            </div>
          </div>
        </div>
      </div>

      {/* Deduction Categories */}
      <div
        className={`p-6 rounded-lg border ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}
      >
        <h4
          className={`text-lg font-semibold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}
        >
          📊 Deduction Categories
        </h4>

        {/* Category Tabs */}
        <div className='flex flex-wrap gap-2 mb-6'>
          {deductionCategories.map(category => (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                selectedCategory === category.id
                  ? darkMode
                    ? 'bg-blue-600 text-white'
                    : 'bg-blue-600 text-white'
                  : darkMode
                    ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {category.name}
              {category.potentialSavings > 0 && (
                <span
                  className={`ml-2 text-xs px-2 py-1 rounded ${
                    selectedCategory === category.id
                      ? 'bg-white/20'
                      : 'bg-green-100 text-green-800'
                  }`}
                >
                  +CHF {Math.round(category.potentialSavings)}
                </span>
              )}
            </button>
          ))}
        </div>

        {/* Selected Category Details */}
        {selectedCategoryData && (
          <div
            className={`p-4 rounded-lg border ${darkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'}`}
          >
            <div className='flex justify-between items-start mb-4'>
              <div>
                <h5
                  className={`text-lg font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}
                >
                  {selectedCategoryData.name}
                </h5>
                <p
                  className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}
                >
                  {selectedCategoryData.description}
                </p>
              </div>
              <span
                className={`px-3 py-1 rounded text-sm font-medium ${
                  selectedCategoryData.riskLevel === 'Low'
                    ? 'bg-green-100 text-green-800'
                    : selectedCategoryData.riskLevel === 'Medium'
                      ? 'bg-yellow-100 text-yellow-800'
                      : 'bg-red-100 text-red-800'
                }`}
              >
                {selectedCategoryData.riskLevel} Risk
              </span>
            </div>

            {/* Current vs Maximum */}
            <div className='grid grid-cols-1 md:grid-cols-3 gap-4 mb-4'>
              <div>
                <div
                  className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}
                >
                  Current Amount
                </div>
                <div
                  className={`text-lg font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}
                >
                  CHF {selectedCategoryData.currentAmount.toLocaleString()}
                </div>
              </div>
              <div>
                <div
                  className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}
                >
                  Maximum Allowed
                </div>
                <div
                  className={`text-lg font-bold ${darkMode ? 'text-blue-400' : 'text-blue-600'}`}
                >
                  CHF {selectedCategoryData.maxAmount.toLocaleString()}
                </div>
              </div>
              <div>
                <div
                  className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}
                >
                  Potential Savings
                </div>
                <div
                  className={`text-lg font-bold ${darkMode ? 'text-green-400' : 'text-green-600'}`}
                >
                  CHF {selectedCategoryData.potentialSavings.toLocaleString()}
                </div>
              </div>
            </div>

            {/* Requirements */}
            <div className='mb-4'>
              <h6
                className={`text-sm font-medium mb-2 ${darkMode ? 'text-white' : 'text-gray-900'}`}
              >
                Requirements:
              </h6>
              <ul
                className={`text-sm space-y-1 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}
              >
                {selectedCategoryData.requirements.map((req, index) => (
                  <li key={index} className='flex items-start'>
                    <span className='mr-2'>•</span>
                    <span>{req}</span>
                  </li>
                ))}
              </ul>
            </div>

            {/* Tips */}
            <div className='mb-4'>
              <h6
                className={`text-sm font-medium mb-2 ${darkMode ? 'text-white' : 'text-gray-900'}`}
              >
                Optimization Tips:
              </h6>
              <ul
                className={`text-sm space-y-1 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}
              >
                {selectedCategoryData.tips.map((tip, index) => (
                  <li key={index} className='flex items-start'>
                    <span className='mr-2'>💡</span>
                    <span>{tip}</span>
                  </li>
                ))}
              </ul>
            </div>

            {/* Documentation */}
            <div>
              <h6
                className={`text-sm font-medium mb-2 ${darkMode ? 'text-white' : 'text-gray-900'}`}
              >
                Required Documentation:
              </h6>
              <ul
                className={`text-sm space-y-1 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}
              >
                {selectedCategoryData.documentation.map((doc, index) => (
                  <li key={index} className='flex items-start'>
                    <span className='mr-2'>📄</span>
                    <span>{doc}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SwissTaxDeductionOptimizer;
