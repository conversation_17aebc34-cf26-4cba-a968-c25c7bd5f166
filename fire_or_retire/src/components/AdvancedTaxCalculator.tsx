import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';

interface AdvancedTaxCalculatorProps {
  darkMode: boolean;
  userData: {
    monthlyIncome: number;
    currentSavings: number;
    canton?: string;
    civilStatus?: string;
  };
}

interface TaxBracket {
  min: number;
  max: number;
  rate?: number;
  baseRate?: number;
  progressionFactor?: number;
}

interface CantonData {
  name: string;
  federalMultiplier: number;
  cantonalRate: number;
  municipalMultiplier: number;
  wealthTaxRate: number;
  wealthTaxExemption: number;
}

interface TaxResult {
  federalTax: number;
  cantonalTax: number;
  wealthTax: number;
  totalIncomeTax: number;
  totalTax: number;
  effectiveRate: number;
  marginalRate: number;
}

const AdvancedTaxCalculator: React.FC<AdvancedTaxCalculatorProps> = ({
  darkMode,
  userData,
}) => {
  const { t } = useTranslation();
  const [selectedCanton, setSelectedCanton] = useState(userData.canton || 'ZH');
  const [civilStatus, setCivilStatus] = useState(
    userData.civilStatus || 'single'
  );
  const [annualIncome, setAnnualIncome] = useState(userData.monthlyIncome * 12);
  const [netWorth, setNetWorth] = useState(userData.currentSavings);
  const [taxResult, setTaxResult] = useState<TaxResult | null>(null);
  const [cantonComparison, setCantonComparison] = useState<any[]>([]);

  // Swiss Canton Data (simplified version from retire.tsx)
  const cantons: Record<string, CantonData> = {
    ZH: {
      name: 'Zürich',
      federalMultiplier: 1.0,
      cantonalRate: 3.0,
      municipalMultiplier: 1.19,
      wealthTaxRate: 0.0015,
      wealthTaxExemption: 50000,
    },
    ZG: {
      name: 'Zug',
      federalMultiplier: 1.0,
      cantonalRate: 1.5,
      municipalMultiplier: 0.8,
      wealthTaxRate: 0.001,
      wealthTaxExemption: 75000,
    },
    SZ: {
      name: 'Schwyz',
      federalMultiplier: 1.0,
      cantonalRate: 2.2,
      municipalMultiplier: 1.0,
      wealthTaxRate: 0.0012,
      wealthTaxExemption: 60000,
    },
    GE: {
      name: 'Geneva',
      federalMultiplier: 1.0,
      cantonalRate: 4.5,
      municipalMultiplier: 1.45,
      wealthTaxRate: 0.0055,
      wealthTaxExemption: 0,
    },
    BS: {
      name: 'Basel-Stadt',
      federalMultiplier: 1.0,
      cantonalRate: 3.5,
      municipalMultiplier: 1.3,
      wealthTaxRate: 0.002,
      wealthTaxExemption: 30000,
    },
    VD: {
      name: 'Vaud',
      federalMultiplier: 1.0,
      cantonalRate: 4.2,
      municipalMultiplier: 1.6,
      wealthTaxRate: 0.0035,
      wealthTaxExemption: 25000,
    },
    BE: {
      name: 'Bern',
      federalMultiplier: 1.0,
      cantonalRate: 3.8,
      municipalMultiplier: 1.54,
      wealthTaxRate: 0.0025,
      wealthTaxExemption: 40000,
    },
    AG: {
      name: 'Aargau',
      federalMultiplier: 1.0,
      cantonalRate: 3.2,
      municipalMultiplier: 1.3,
      wealthTaxRate: 0.0018,
      wealthTaxExemption: 50000,
    },
  };

  // Federal Tax Brackets (2024)
  const federalTaxBrackets: TaxBracket[] = [
    { min: 0, max: 14500, rate: 0 },
    { min: 14500, max: 31600, baseRate: 0.0077, progressionFactor: 0.000165 },
    { min: 31600, max: 41400, baseRate: 0.0264, progressionFactor: 0.000275 },
    { min: 41400, max: 55200, baseRate: 0.0376, progressionFactor: 0.000275 },
    { min: 55200, max: 72500, baseRate: 0.066, progressionFactor: 0.000275 },
    { min: 72500, max: 78100, baseRate: 0.088, progressionFactor: 0.000275 },
    { min: 78100, max: Infinity, rate: 0.115 },
  ];

  // Calculate Federal Tax
  const calculateFederalTax = (income: number): number => {
    let tax = 0;

    for (const bracket of federalTaxBrackets) {
      if (income <= bracket.min) break;

      const taxableInBracket = Math.min(income, bracket.max) - bracket.min;

      if (bracket.rate !== undefined) {
        tax += taxableInBracket * bracket.rate;
      } else if (bracket.baseRate && bracket.progressionFactor) {
        const midPoint = (bracket.min + Math.min(income, bracket.max)) / 2;
        const effectiveRate =
          bracket.baseRate +
          (midPoint - bracket.min) * bracket.progressionFactor;
        tax += taxableInBracket * effectiveRate;
      }
    }

    return tax;
  };

  // Calculate Cantonal and Municipal Tax
  const calculateCantonalTax = (income: number, canton: string): number => {
    const cantonData = cantons[canton];
    if (!cantonData) return 0;

    const federalTax = calculateFederalTax(income);
    const cantonalTax = federalTax * cantonData.cantonalRate;
    const municipalTax = federalTax * cantonData.municipalMultiplier;

    return cantonalTax + municipalTax;
  };

  // Calculate Wealth Tax
  const calculateWealthTax = (netWorth: number, canton: string): number => {
    const cantonData = cantons[canton];
    if (!cantonData) return 0;

    const taxableWealth = Math.max(0, netWorth - cantonData.wealthTaxExemption);
    return taxableWealth * cantonData.wealthTaxRate;
  };

  // Calculate Total Tax Burden
  const calculateTotalTax = (
    income: number,
    netWorth: number,
    canton: string,
    civilStatus: string
  ): TaxResult => {
    const federalTax = calculateFederalTax(income);
    const cantonalTax = calculateCantonalTax(income, canton);
    const wealthTax = calculateWealthTax(netWorth, canton);

    // Apply civil status adjustments (married couples get reductions)
    const civilStatusMultiplier = civilStatus === 'married' ? 0.85 : 1.0;
    const totalIncomeTax = (federalTax + cantonalTax) * civilStatusMultiplier;

    // Calculate marginal rate
    const higherIncomeTax =
      calculateFederalTax(income + 1000) +
      calculateCantonalTax(income + 1000, canton);
    const marginalRate =
      ((higherIncomeTax * civilStatusMultiplier - totalIncomeTax) / 1000) * 100;

    return {
      federalTax: federalTax * civilStatusMultiplier,
      cantonalTax: cantonalTax * civilStatusMultiplier,
      wealthTax,
      totalIncomeTax,
      totalTax: totalIncomeTax + wealthTax,
      effectiveRate: income > 0 ? (totalIncomeTax / income) * 100 : 0,
      marginalRate,
    };
  };

  // Generate Canton Comparison
  const generateCantonComparison = () => {
    const comparison = Object.keys(cantons).map(cantonCode => {
      const result = calculateTotalTax(
        annualIncome,
        netWorth,
        cantonCode,
        civilStatus
      );
      return {
        canton: cantonCode,
        name: cantons[cantonCode].name,
        ...result,
      };
    });

    return comparison.sort((a, b) => a.totalTax - b.totalTax);
  };

  useEffect(() => {
    const result = calculateTotalTax(
      annualIncome,
      netWorth,
      selectedCanton,
      civilStatus
    );
    setTaxResult(result);
    setCantonComparison(generateCantonComparison());
  }, [annualIncome, netWorth, selectedCanton, civilStatus]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('de-CH', {
      style: 'currency',
      currency: 'CHF',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (rate: number) => {
    return `${rate.toFixed(2)}%`;
  };

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div>
        <h2 className='text-2xl font-bold mb-2'>🏛️ Advanced Tax Calculator</h2>
        <p className='text-gray-600 dark:text-gray-400'>
          Comprehensive Swiss tax analysis with federal, cantonal, and wealth
          tax calculations
        </p>
      </div>

      {/* Input Parameters */}
      <div
        className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
      >
        <h3 className='text-lg font-semibold mb-4'>
          Tax Calculation Parameters
        </h3>

        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4'>
          <div>
            <label className='block text-sm font-medium mb-2'>
              Annual Income (CHF)
            </label>
            <input
              type='number'
              step='1000'
              value={annualIncome}
              onChange={e => setAnnualIncome(parseFloat(e.target.value) || 0)}
              className={`w-full px-3 py-2 rounded-md border ${
                darkMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              }`}
            />
          </div>

          <div>
            <label className='block text-sm font-medium mb-2'>
              Net Worth (CHF)
            </label>
            <input
              type='number'
              step='10000'
              value={netWorth}
              onChange={e => setNetWorth(parseFloat(e.target.value) || 0)}
              className={`w-full px-3 py-2 rounded-md border ${
                darkMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              }`}
            />
          </div>

          <div>
            <label className='block text-sm font-medium mb-2'>Canton</label>
            <select
              value={selectedCanton}
              onChange={e => setSelectedCanton(e.target.value)}
              className={`w-full px-3 py-2 rounded-md border ${
                darkMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              }`}
            >
              {Object.entries(cantons).map(([code, data]) => (
                <option key={code} value={code}>
                  {data.name}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className='block text-sm font-medium mb-2'>
              Civil Status
            </label>
            <select
              value={civilStatus}
              onChange={e => setCivilStatus(e.target.value)}
              className={`w-full px-3 py-2 rounded-md border ${
                darkMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              }`}
            >
              <option value='single'>Single</option>
              <option value='married'>Married</option>
              <option value='divorced'>Divorced</option>
              <option value='widowed'>Widowed</option>
            </select>
          </div>
        </div>
      </div>

      {/* Tax Results */}
      {taxResult && (
        <div
          className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
        >
          <h3 className='text-lg font-semibold mb-4'>
            Tax Calculation Results
          </h3>

          {/* Summary Cards */}
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6'>
            <div className='text-center p-4 bg-red-50 dark:bg-red-900/20 rounded-lg'>
              <div className='text-sm text-gray-600 dark:text-gray-400'>
                Total Tax
              </div>
              <div className='text-2xl font-bold text-red-600'>
                {formatCurrency(taxResult.totalTax)}
              </div>
              <div className='text-xs text-gray-500'>
                {formatPercentage(taxResult.effectiveRate)} effective rate
              </div>
            </div>
            <div className='text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg'>
              <div className='text-sm text-gray-600 dark:text-gray-400'>
                Federal Tax
              </div>
              <div className='text-xl font-bold text-blue-600'>
                {formatCurrency(taxResult.federalTax)}
              </div>
            </div>
            <div className='text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg'>
              <div className='text-sm text-gray-600 dark:text-gray-400'>
                Cantonal Tax
              </div>
              <div className='text-xl font-bold text-green-600'>
                {formatCurrency(taxResult.cantonalTax)}
              </div>
            </div>
            <div className='text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg'>
              <div className='text-sm text-gray-600 dark:text-gray-400'>
                Wealth Tax
              </div>
              <div className='text-xl font-bold text-purple-600'>
                {formatCurrency(taxResult.wealthTax)}
              </div>
            </div>
          </div>

          {/* Detailed Breakdown */}
          <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
            <div>
              <h4 className='font-semibold mb-3'>Tax Breakdown</h4>
              <div className='space-y-2 text-sm'>
                <div className='flex justify-between'>
                  <span>Gross Annual Income:</span>
                  <span className='font-medium'>
                    {formatCurrency(annualIncome)}
                  </span>
                </div>
                <div className='flex justify-between'>
                  <span>Federal Income Tax:</span>
                  <span className='font-medium'>
                    {formatCurrency(taxResult.federalTax)}
                  </span>
                </div>
                <div className='flex justify-between'>
                  <span>Cantonal & Municipal Tax:</span>
                  <span className='font-medium'>
                    {formatCurrency(taxResult.cantonalTax)}
                  </span>
                </div>
                <div className='flex justify-between'>
                  <span>Wealth Tax:</span>
                  <span className='font-medium'>
                    {formatCurrency(taxResult.wealthTax)}
                  </span>
                </div>
                <div className='border-t pt-2 flex justify-between font-semibold'>
                  <span>Total Tax Burden:</span>
                  <span>{formatCurrency(taxResult.totalTax)}</span>
                </div>
                <div className='flex justify-between font-semibold'>
                  <span>Net Income:</span>
                  <span className='text-green-600'>
                    {formatCurrency(annualIncome - taxResult.totalTax)}
                  </span>
                </div>
              </div>
            </div>

            <div>
              <h4 className='font-semibold mb-3'>Tax Rates</h4>
              <div className='space-y-2 text-sm'>
                <div className='flex justify-between'>
                  <span>Effective Tax Rate:</span>
                  <span className='font-medium'>
                    {formatPercentage(taxResult.effectiveRate)}
                  </span>
                </div>
                <div className='flex justify-between'>
                  <span>Marginal Tax Rate:</span>
                  <span className='font-medium'>
                    {formatPercentage(taxResult.marginalRate)}
                  </span>
                </div>
                <div className='flex justify-between'>
                  <span>Civil Status Adjustment:</span>
                  <span className='font-medium'>
                    {civilStatus === 'married' ? '15% reduction' : 'None'}
                  </span>
                </div>
                <div className='flex justify-between'>
                  <span>Wealth Tax Rate:</span>
                  <span className='font-medium'>
                    {formatPercentage(
                      cantons[selectedCanton].wealthTaxRate * 100
                    )}
                  </span>
                </div>
                <div className='flex justify-between'>
                  <span>Wealth Tax Exemption:</span>
                  <span className='font-medium'>
                    {formatCurrency(cantons[selectedCanton].wealthTaxExemption)}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Canton Comparison */}
      <div
        className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
      >
        <h3 className='text-lg font-semibold mb-4'>Canton Tax Comparison</h3>
        <p className='text-sm text-gray-600 dark:text-gray-400 mb-4'>
          Compare your tax burden across different Swiss cantons
        </p>

        <div className='overflow-x-auto'>
          <table className='w-full text-sm'>
            <thead>
              <tr className='border-b border-gray-200 dark:border-gray-700'>
                <th className='text-left py-2'>Canton</th>
                <th className='text-right py-2'>Total Tax</th>
                <th className='text-right py-2'>Effective Rate</th>
                <th className='text-right py-2'>Savings vs Current</th>
              </tr>
            </thead>
            <tbody>
              {cantonComparison.slice(0, 8).map((canton, index) => {
                const savings = taxResult
                  ? taxResult.totalTax - canton.totalTax
                  : 0;
                const isCurrentCanton = canton.canton === selectedCanton;

                return (
                  <tr
                    key={canton.canton}
                    className={`border-b border-gray-100 dark:border-gray-700 ${
                      isCurrentCanton ? 'bg-blue-50 dark:bg-blue-900/20' : ''
                    }`}
                  >
                    <td className='py-2'>
                      <div className='flex items-center'>
                        {index === 0 && (
                          <span className='text-green-500 mr-1'>🏆</span>
                        )}
                        {canton.name}
                        {isCurrentCanton && (
                          <span className='ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded'>
                            Current
                          </span>
                        )}
                      </div>
                    </td>
                    <td className='text-right py-2 font-medium'>
                      {formatCurrency(canton.totalTax)}
                    </td>
                    <td className='text-right py-2'>
                      {formatPercentage(canton.effectiveRate)}
                    </td>
                    <td
                      className={`text-right py-2 font-medium ${
                        savings > 0
                          ? 'text-green-600'
                          : savings < 0
                            ? 'text-red-600'
                            : 'text-gray-500'
                      }`}
                    >
                      {savings > 0 ? '+' : ''}
                      {formatCurrency(savings)}
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default AdvancedTaxCalculator;
