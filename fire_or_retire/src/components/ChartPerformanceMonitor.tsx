import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useTranslation } from 'react-i18next';

interface PerformanceMetric {
  name: string;
  value: number;
  unit: string;
  threshold: number;
  status: 'good' | 'warning' | 'critical';
  description: string;
}

interface ChartPerformanceData {
  renderTime: number;
  dataPoints: number;
  memoryUsage: number;
  frameRate: number;
  interactionLatency: number;
  lastUpdate: Date;
}

interface ChartPerformanceMonitorProps {
  darkMode: boolean;
  enabled?: boolean;
  onPerformanceIssue?: (metric: PerformanceMetric) => void;
  className?: string;
}

const ChartPerformanceMonitor: React.FC<ChartPerformanceMonitorProps> = ({
  darkMode,
  enabled = true,
  onPerformanceIssue,
  className = '',
}) => {
  const { t } = useTranslation();
  const [performanceData, setPerformanceData] = useState<ChartPerformanceData>({
    renderTime: 0,
    dataPoints: 0,
    memoryUsage: 0,
    frameRate: 60,
    interactionLatency: 0,
    lastUpdate: new Date(),
  });
  const [isMonitoring, setIsMonitoring] = useState(enabled);
  const [showDetails, setShowDetails] = useState(false);
  const frameCountRef = useRef(0);
  const lastFrameTimeRef = useRef(performance.now());
  const renderTimesRef = useRef<number[]>([]);

  // Performance thresholds
  const thresholds = {
    renderTime: 500, // ms
    memoryUsage: 50, // MB
    frameRate: 30, // fps
    interactionLatency: 100, // ms
  };

  // Calculate performance metrics
  const calculateMetrics = useCallback((): PerformanceMetric[] => {
    const {
      renderTime,
      dataPoints,
      memoryUsage,
      frameRate,
      interactionLatency,
    } = performanceData;

    return [
      {
        name: 'Render Time',
        value: renderTime,
        unit: 'ms',
        threshold: thresholds.renderTime,
        status:
          renderTime > thresholds.renderTime
            ? 'critical'
            : renderTime > thresholds.renderTime * 0.7
              ? 'warning'
              : 'good',
        description: 'Time taken to render chart components',
      },
      {
        name: 'Data Points',
        value: dataPoints,
        unit: 'points',
        threshold: 1000,
        status: dataPoints > 1000 ? 'warning' : 'good',
        description: 'Number of data points being visualized',
      },
      {
        name: 'Memory Usage',
        value: memoryUsage,
        unit: 'MB',
        threshold: thresholds.memoryUsage,
        status:
          memoryUsage > thresholds.memoryUsage
            ? 'critical'
            : memoryUsage > thresholds.memoryUsage * 0.7
              ? 'warning'
              : 'good',
        description: 'Estimated memory consumption',
      },
      {
        name: 'Frame Rate',
        value: frameRate,
        unit: 'fps',
        threshold: thresholds.frameRate,
        status:
          frameRate < thresholds.frameRate
            ? 'critical'
            : frameRate < thresholds.frameRate * 1.5
              ? 'warning'
              : 'good',
        description: 'Animation smoothness indicator',
      },
      {
        name: 'Interaction Latency',
        value: interactionLatency,
        unit: 'ms',
        threshold: thresholds.interactionLatency,
        status:
          interactionLatency > thresholds.interactionLatency
            ? 'critical'
            : interactionLatency > thresholds.interactionLatency * 0.7
              ? 'warning'
              : 'good',
        description: 'Response time for user interactions',
      },
    ];
  }, [performanceData, thresholds]);

  // Monitor frame rate
  const monitorFrameRate = useCallback(() => {
    const now = performance.now();
    const delta = now - lastFrameTimeRef.current;

    if (delta >= 1000) {
      const fps = Math.round((frameCountRef.current * 1000) / delta);
      setPerformanceData(prev => ({ ...prev, frameRate: fps }));
      frameCountRef.current = 0;
      lastFrameTimeRef.current = now;
    } else {
      frameCountRef.current++;
    }

    if (isMonitoring) {
      requestAnimationFrame(monitorFrameRate);
    }
  }, [isMonitoring]);

  // Monitor memory usage (approximate)
  const monitorMemoryUsage = useCallback(() => {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      const usedMB = memory.usedJSHeapSize / 1024 / 1024;
      setPerformanceData(prev => ({
        ...prev,
        memoryUsage: Math.round(usedMB),
      }));
    }
  }, []);

  // Measure render time
  const measureRenderTime = useCallback((startTime: number) => {
    const endTime = performance.now();
    const renderTime = endTime - startTime;

    renderTimesRef.current.push(renderTime);
    if (renderTimesRef.current.length > 10) {
      renderTimesRef.current.shift();
    }

    const avgRenderTime =
      renderTimesRef.current.reduce((sum, time) => sum + time, 0) /
      renderTimesRef.current.length;
    setPerformanceData(prev => ({
      ...prev,
      renderTime: Math.round(avgRenderTime),
      lastUpdate: new Date(),
    }));
  }, []);

  // Measure interaction latency
  const measureInteractionLatency = useCallback((startTime: number) => {
    const latency = performance.now() - startTime;
    setPerformanceData(prev => ({
      ...prev,
      interactionLatency: Math.round(latency),
    }));
  }, []);

  // Start monitoring
  useEffect(() => {
    if (isMonitoring) {
      monitorFrameRate();
      const memoryInterval = setInterval(monitorMemoryUsage, 2000);
      return () => clearInterval(memoryInterval);
    }
  }, [isMonitoring, monitorFrameRate, monitorMemoryUsage]);

  // Check for performance issues
  useEffect(() => {
    const metrics = calculateMetrics();
    const criticalMetrics = metrics.filter(
      metric => metric.status === 'critical'
    );

    criticalMetrics.forEach(metric => {
      onPerformanceIssue?.(metric);
    });
  }, [performanceData, calculateMetrics, onPerformanceIssue]);

  // Update data points count when charts change
  const updateDataPointsCount = useCallback((count: number) => {
    setPerformanceData(prev => ({ ...prev, dataPoints: count }));
  }, []);

  // Expose performance monitoring functions
  useEffect(() => {
    // Add global performance monitoring functions
    (window as any).chartPerformanceMonitor = {
      measureRenderTime,
      measureInteractionLatency,
      updateDataPointsCount,
    };

    return () => {
      delete (window as any).chartPerformanceMonitor;
    };
  }, [measureRenderTime, measureInteractionLatency, updateDataPointsCount]);

  const metrics = calculateMetrics();
  const overallStatus = metrics.some(m => m.status === 'critical')
    ? 'critical'
    : metrics.some(m => m.status === 'warning')
      ? 'warning'
      : 'good';

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'good':
        return 'text-green-600 bg-green-100 border-green-200';
      case 'warning':
        return 'text-yellow-600 bg-yellow-100 border-yellow-200';
      case 'critical':
        return 'text-red-600 bg-red-100 border-red-200';
      default:
        return 'text-gray-600 bg-gray-100 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'good':
        return '✅';
      case 'warning':
        return '⚠️';
      case 'critical':
        return '🚨';
      default:
        return '📊';
    }
  };

  if (!enabled) return null;

  return (
    <div className={`${className}`}>
      {/* Performance Status Indicator */}
      <div
        className={`inline-flex items-center gap-2 px-3 py-1 rounded-lg border text-sm ${
          darkMode
            ? 'bg-gray-800 border-gray-700'
            : getStatusColor(overallStatus)
        }`}
      >
        <span>{getStatusIcon(overallStatus)}</span>
        <span className='font-medium'>
          Performance:{' '}
          {overallStatus.charAt(0).toUpperCase() + overallStatus.slice(1)}
        </span>
        <button
          onClick={() => setShowDetails(!showDetails)}
          className='ml-2 text-xs opacity-70 hover:opacity-100'
        >
          {showDetails ? '▼' : '▶'}
        </button>
        <button
          onClick={() => setIsMonitoring(!isMonitoring)}
          className={`ml-2 px-2 py-1 rounded text-xs ${
            isMonitoring
              ? 'bg-green-600 text-white'
              : darkMode
                ? 'bg-gray-600 text-gray-200'
                : 'bg-gray-300 text-gray-700'
          }`}
        >
          {isMonitoring ? 'ON' : 'OFF'}
        </button>
      </div>

      {/* Detailed Performance Metrics */}
      {showDetails && (
        <div
          className={`mt-2 p-4 rounded-lg border ${
            darkMode
              ? 'bg-gray-800 border-gray-700'
              : 'bg-white border-gray-200'
          }`}
        >
          <h4
            className={`text-sm font-semibold mb-3 ${
              darkMode ? 'text-gray-200' : 'text-gray-800'
            }`}
          >
            📊 Chart Performance Metrics
          </h4>

          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3'>
            {metrics.map(metric => (
              <div
                key={metric.name}
                className={`p-3 rounded border ${
                  darkMode
                    ? 'bg-gray-700 border-gray-600'
                    : getStatusColor(metric.status)
                }`}
              >
                <div className='flex items-center justify-between mb-1'>
                  <span className='text-xs font-medium'>{metric.name}</span>
                  <span className='text-xs'>
                    {getStatusIcon(metric.status)}
                  </span>
                </div>
                <div className='text-lg font-bold'>
                  {metric.value} {metric.unit}
                </div>
                <div className='text-xs opacity-70 mt-1'>
                  {metric.description}
                </div>
                {metric.status !== 'good' && (
                  <div className='text-xs mt-1 font-medium'>
                    Threshold: {metric.threshold} {metric.unit}
                  </div>
                )}
              </div>
            ))}
          </div>

          <div
            className={`mt-3 text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}
          >
            Last updated: {performanceData.lastUpdate.toLocaleTimeString()}
          </div>
        </div>
      )}
    </div>
  );
};

export default ChartPerformanceMonitor;
