import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocalStorage } from '../hooks/useLocalStorage';

interface ProgressTrackerProps {
  darkMode: boolean;
  userData: {
    currentAge: number;
    retirementAge: number;
    currentSavings: number;
    monthlyIncome: number;
    monthlyExpenses: number;
    expenses: any[];
    savingsGoals: any[];
    investments: any[];
    additionalIncomes: any[];
  };
}

interface ProgressMetric {
  id: string;
  title: string;
  current: number;
  target: number;
  unit: string;
  icon: string;
  color: string;
  description: string;
  trend?: 'up' | 'down' | 'stable';
}

interface Milestone {
  id: string;
  title: string;
  description: string;
  target: number;
  current: number;
  completed: boolean;
  icon: string;
  category: 'savings' | 'income' | 'expenses' | 'investments';
}

const ProgressTracker: React.FC<ProgressTrackerProps> = ({
  darkMode,
  userData,
}) => {
  const { t } = useTranslation();
  const [progressHistory, setProgressHistory] = useLocalStorage(
    'progress_history',
    []
  );
  const [selectedTimeframe, setSelectedTimeframe] = useState<
    '1M' | '3M' | '6M' | '1Y'
  >('3M');
  const [milestones, setMilestones] = useState<Milestone[]>([]);

  useEffect(() => {
    calculateProgress();
    generateMilestones();
    updateProgressHistory();
  }, [userData]);

  const calculateProgress = () => {
    // This would typically update progress metrics
    // For now, we'll calculate them in real-time
  };

  const generateMilestones = () => {
    const newMilestones: Milestone[] = [];

    // Emergency Fund Milestone
    const emergencyFundTarget = userData.monthlyExpenses * 6;
    newMilestones.push({
      id: 'emergency-fund',
      title: 'Emergency Fund (6 months)',
      description: 'Build a safety net covering 6 months of expenses',
      target: emergencyFundTarget,
      current: Math.min(userData.currentSavings, emergencyFundTarget),
      completed: userData.currentSavings >= emergencyFundTarget,
      icon: '🛡️',
      category: 'savings',
    });

    // First 100k Milestone
    newMilestones.push({
      id: 'first-100k',
      title: 'First CHF 100,000',
      description: 'Reach your first major savings milestone',
      target: 100000,
      current: Math.min(userData.currentSavings, 100000),
      completed: userData.currentSavings >= 100000,
      icon: '💯',
      category: 'savings',
    });

    // FIRE 25% Milestone
    const fireTarget = userData.monthlyExpenses * 12 * 25;
    newMilestones.push({
      id: 'fire-25',
      title: 'FIRE 25% Complete',
      description: 'Quarter way to financial independence',
      target: fireTarget * 0.25,
      current: Math.min(userData.currentSavings, fireTarget * 0.25),
      completed: userData.currentSavings >= fireTarget * 0.25,
      icon: '🔥',
      category: 'savings',
    });

    // FIRE 50% Milestone
    newMilestones.push({
      id: 'fire-50',
      title: 'FIRE 50% Complete',
      description: 'Halfway to financial independence',
      target: fireTarget * 0.5,
      current: Math.min(userData.currentSavings, fireTarget * 0.5),
      completed: userData.currentSavings >= fireTarget * 0.5,
      icon: '🎯',
      category: 'savings',
    });

    // Investment Portfolio Milestone
    newMilestones.push({
      id: 'investment-50k',
      title: 'CHF 50k Invested',
      description: 'Build a substantial investment portfolio',
      target: 50000,
      current: userData.investments.reduce(
        (sum, inv) => sum + (inv.currentValue || 0),
        0
      ),
      completed:
        userData.investments.reduce(
          (sum, inv) => sum + (inv.currentValue || 0),
          0
        ) >= 50000,
      icon: '📈',
      category: 'investments',
    });

    // Income Optimization Milestone
    const additionalIncomeTotal = userData.additionalIncomes
      .filter(inc => inc.isActive)
      .reduce((sum, inc) => {
        const monthly =
          inc.frequency === 'monthly'
            ? inc.amount
            : inc.frequency === 'annually'
              ? inc.amount / 12
              : inc.amount;
        return sum + monthly;
      }, 0);

    newMilestones.push({
      id: 'additional-income-2k',
      title: 'CHF 2k Additional Income',
      description: 'Generate significant additional monthly income',
      target: 2000,
      current: additionalIncomeTotal,
      completed: additionalIncomeTotal >= 2000,
      icon: '💼',
      category: 'income',
    });

    setMilestones(newMilestones);
  };

  const updateProgressHistory = () => {
    const today = new Date().toISOString().split('T')[0];
    const existingEntry = progressHistory.find(
      (entry: any) => entry.date === today
    );

    const newEntry = {
      date: today,
      savings: userData.currentSavings,
      investments: userData.investments.reduce(
        (sum, inv) => sum + (inv.currentValue || 0),
        0
      ),
      netWorth:
        userData.currentSavings +
        userData.investments.reduce(
          (sum, inv) => sum + (inv.currentValue || 0),
          0
        ),
      savingsRate:
        ((userData.monthlyIncome - userData.monthlyExpenses) /
          userData.monthlyIncome) *
        100,
    };

    if (existingEntry) {
      // Update existing entry
      const updatedHistory = progressHistory.map((entry: any) =>
        entry.date === today ? newEntry : entry
      );
      setProgressHistory(updatedHistory);
    } else {
      // Add new entry and keep only last 365 days
      const updatedHistory = [...progressHistory, newEntry]
        .sort(
          (a: any, b: any) =>
            new Date(b.date).getTime() - new Date(a.date).getTime()
        )
        .slice(0, 365);
      setProgressHistory(updatedHistory);
    }
  };

  const getProgressMetrics = (): ProgressMetric[] => {
    const savingsRate =
      ((userData.monthlyIncome - userData.monthlyExpenses) /
        userData.monthlyIncome) *
      100;
    const fireTarget = userData.monthlyExpenses * 12 * 25;
    const fireProgress = (userData.currentSavings / fireTarget) * 100;
    const emergencyFundMonths =
      userData.currentSavings / userData.monthlyExpenses;
    const investmentAllocation = userData.investments.reduce(
      (sum, inv) => sum + (inv.currentValue || 0),
      0
    );
    const totalNetWorth = userData.currentSavings + investmentAllocation;

    return [
      {
        id: 'fire-progress',
        title: 'FIRE Progress',
        current: fireProgress,
        target: 100,
        unit: '%',
        icon: '🔥',
        color: 'bg-red-500',
        description: `${fireProgress.toFixed(1)}% toward financial independence`,
      },
      {
        id: 'savings-rate',
        title: 'Savings Rate',
        current: savingsRate,
        target: 50,
        unit: '%',
        icon: '💰',
        color: 'bg-green-500',
        description: `${savingsRate.toFixed(1)}% of income saved monthly`,
      },
      {
        id: 'emergency-fund',
        title: 'Emergency Fund',
        current: emergencyFundMonths,
        target: 6,
        unit: 'months',
        icon: '🛡️',
        color: 'bg-blue-500',
        description: `${emergencyFundMonths.toFixed(1)} months of expenses covered`,
      },
      {
        id: 'net-worth',
        title: 'Net Worth',
        current: totalNetWorth,
        target: fireTarget,
        unit: 'CHF',
        icon: '💎',
        color: 'bg-purple-500',
        description: `CHF ${totalNetWorth.toLocaleString()} total net worth`,
      },
    ];
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('de-CH', {
      style: 'currency',
      currency: 'CHF',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getProgressBarColor = (percentage: number) => {
    if (percentage >= 100) return 'bg-green-500';
    if (percentage >= 75) return 'bg-blue-500';
    if (percentage >= 50) return 'bg-yellow-500';
    if (percentage >= 25) return 'bg-orange-500';
    return 'bg-red-500';
  };

  const progressMetrics = getProgressMetrics();
  const completedMilestones = milestones.filter(m => m.completed).length;
  const totalMilestones = milestones.length;

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div className='flex items-center justify-between'>
        <div>
          <h2 className='text-2xl font-bold'>📊 Progress Tracker</h2>
          <p className='text-gray-600'>
            Track your journey to financial independence
          </p>
        </div>

        {/* Timeframe Selector */}
        <div className='flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1'>
          {(['1M', '3M', '6M', '1Y'] as const).map(timeframe => (
            <button
              key={timeframe}
              onClick={() => setSelectedTimeframe(timeframe)}
              className={`px-3 py-1 rounded-md text-sm transition-colors ${
                selectedTimeframe === timeframe
                  ? 'bg-blue-600 text-white'
                  : 'text-gray-600 hover:text-gray-900 dark:text-gray-300'
              }`}
            >
              {timeframe}
            </button>
          ))}
        </div>
      </div>

      {/* Progress Overview */}
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4'>
        {progressMetrics.map(metric => {
          const percentage = Math.min(
            (metric.current / metric.target) * 100,
            100
          );

          return (
            <div
              key={metric.id}
              className={`p-4 rounded-lg ${
                darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'
              }`}
            >
              <div className='flex items-center justify-between mb-2'>
                <span className='text-2xl'>{metric.icon}</span>
                <span className='text-sm text-gray-500'>
                  {metric.unit === 'CHF'
                    ? formatCurrency(metric.current)
                    : `${metric.current.toFixed(1)}${metric.unit}`}
                </span>
              </div>
              <h3 className='font-semibold mb-1'>{metric.title}</h3>
              <div className='w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-2'>
                <div
                  className={`h-2 rounded-full transition-all duration-300 ${getProgressBarColor(percentage)}`}
                  style={{ width: `${Math.min(percentage, 100)}%` }}
                />
              </div>
              <p className='text-xs text-gray-600 dark:text-gray-400'>
                {metric.description}
              </p>
            </div>
          );
        })}
      </div>

      {/* Milestones */}
      <div>
        <div className='flex items-center justify-between mb-4'>
          <h3 className='text-lg font-semibold'>🏆 Milestones</h3>
          <span className='text-sm text-gray-500'>
            {completedMilestones} of {totalMilestones} completed
          </span>
        </div>

        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'>
          {milestones.map(milestone => {
            const percentage = Math.min(
              (milestone.current / milestone.target) * 100,
              100
            );

            return (
              <div
                key={milestone.id}
                className={`p-4 rounded-lg border-2 transition-all ${
                  milestone.completed
                    ? 'border-green-500 bg-green-50 dark:bg-green-900/20'
                    : darkMode
                      ? 'border-gray-700 bg-gray-800'
                      : 'border-gray-200 bg-white'
                }`}
              >
                <div className='flex items-start justify-between mb-3'>
                  <span className='text-2xl'>{milestone.icon}</span>
                  {milestone.completed && (
                    <span className='text-green-600 text-xl'>✅</span>
                  )}
                </div>

                <h4 className='font-semibold mb-1'>{milestone.title}</h4>
                <p className='text-sm text-gray-600 dark:text-gray-400 mb-3'>
                  {milestone.description}
                </p>

                <div className='space-y-2'>
                  <div className='flex justify-between text-sm'>
                    <span>Progress</span>
                    <span>{percentage.toFixed(1)}%</span>
                  </div>
                  <div className='w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2'>
                    <div
                      className={`h-2 rounded-full transition-all duration-300 ${
                        milestone.completed
                          ? 'bg-green-500'
                          : getProgressBarColor(percentage)
                      }`}
                      style={{ width: `${Math.min(percentage, 100)}%` }}
                    />
                  </div>
                  <div className='flex justify-between text-xs text-gray-500'>
                    <span>{formatCurrency(milestone.current)}</span>
                    <span>{formatCurrency(milestone.target)}</span>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Progress Summary */}
      <div
        className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-50'}`}
      >
        <h3 className='text-lg font-semibold mb-4'>📈 Progress Summary</h3>
        <div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
          <div className='text-center'>
            <div className='text-3xl font-bold text-blue-600 mb-1'>
              {progressMetrics[0].current.toFixed(1)}%
            </div>
            <div className='text-sm text-gray-600'>FIRE Progress</div>
          </div>
          <div className='text-center'>
            <div className='text-3xl font-bold text-green-600 mb-1'>
              {completedMilestones}
            </div>
            <div className='text-sm text-gray-600'>Milestones Achieved</div>
          </div>
          <div className='text-center'>
            <div className='text-3xl font-bold text-purple-600 mb-1'>
              {userData.retirementAge - userData.currentAge}
            </div>
            <div className='text-sm text-gray-600'>Years to Retirement</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProgressTracker;
