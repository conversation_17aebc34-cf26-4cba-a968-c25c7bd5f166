import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocalStorage } from '../hooks/useLocalStorage';

interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  icon: string;
  component: React.ReactNode;
}

interface OnboardingWizardProps {
  darkMode: boolean;
  onComplete: (data: OnboardingData) => void;
  onSkip: () => void;
}

interface OnboardingData {
  personalInfo: {
    age: number;
    retirementAge: number;
    canton: string;
    civilStatus: 'single' | 'married' | 'divorced' | 'widowed';
  };
  financialInfo: {
    monthlyIncome: number;
    currentSavings: number;
    monthlyExpenses: number;
    riskTolerance: 'conservative' | 'moderate' | 'aggressive';
  };
  goals: {
    fireGoal: number;
    primaryGoals: string[];
    timeHorizon: number;
  };
  preferences: {
    language: string;
    currency: string;
    notifications: boolean;
  };
}

const SWISS_CANTONS = [
  { code: 'Z<PERSON>', name: '<PERSON>' },
  { code: '<PERSON><PERSON>', name: '<PERSON>' },
  { code: 'L<PERSON>', name: '<PERSON><PERSON><PERSON>' },
  { code: 'U<PERSON>', name: '<PERSON><PERSON>' },
  { code: 'S<PERSON>', name: '<PERSON><PERSON><PERSON><PERSON>' },
  { code: 'OW', name: 'Obwalden' },
  { code: 'NW', name: 'Nidwalden' },
  { code: 'GL', name: 'Glarus' },
  { code: 'ZG', name: 'Zug' },
  { code: 'FR', name: 'Fribourg' },
  { code: 'SO', name: 'Solothurn' },
  { code: 'BS', name: 'Basel-Stadt' },
  { code: 'BL', name: 'Basel-Landschaft' },
  { code: 'SH', name: 'Schaffhausen' },
  { code: 'AR', name: 'Appenzell Ausserrhoden' },
  { code: 'AI', name: 'Appenzell Innerrhoden' },
  { code: 'SG', name: 'St. Gallen' },
  { code: 'GR', name: 'Graubünden' },
  { code: 'AG', name: 'Aargau' },
  { code: 'TG', name: 'Thurgau' },
  { code: 'TI', name: 'Ticino' },
  { code: 'VD', name: 'Vaud' },
  { code: 'VS', name: 'Valais' },
  { code: 'NE', name: 'Neuchâtel' },
  { code: 'GE', name: 'Geneva' },
  { code: 'JU', name: 'Jura' },
];

const OnboardingWizard: React.FC<OnboardingWizardProps> = ({
  darkMode,
  onComplete,
  onSkip,
}) => {
  const { t } = useTranslation();
  const [hasSeenOnboarding, setHasSeenOnboarding] = useLocalStorage(
    'has_seen_onboarding',
    false
  );
  const [currentStep, setCurrentStep] = useState(0);
  const [onboardingData, setOnboardingData] = useState<OnboardingData>({
    personalInfo: {
      age: 30,
      retirementAge: 65,
      canton: 'ZH',
      civilStatus: 'single',
    },
    financialInfo: {
      monthlyIncome: 8000,
      currentSavings: 50000,
      monthlyExpenses: 5000,
      riskTolerance: 'moderate',
    },
    goals: {
      fireGoal: 1500000,
      primaryGoals: ['retirement', 'house'],
      timeHorizon: 35,
    },
    preferences: {
      language: 'en',
      currency: 'CHF',
      notifications: true,
    },
  });

  const updateData = (section: keyof OnboardingData, data: any) => {
    setOnboardingData(prev => ({
      ...prev,
      [section]: { ...prev[section], ...data },
    }));
  };

  const steps: OnboardingStep[] = [
    {
      id: 'welcome',
      title: 'Welcome to Fire or Retire Calculator! 🔥',
      description:
        "Let's set up your personalized Swiss financial plan in just a few steps.",
      icon: '👋',
      component: (
        <div className='text-center space-y-6'>
          <div className='text-6xl mb-4'>🇨🇭</div>
          <h3 className='text-2xl font-bold'>
            Swiss Financial Independence Calculator
          </h3>
          <p className='text-lg text-gray-600 max-w-md mx-auto'>
            This wizard will help you set up your personalized FIRE (Financial
            Independence, Retire Early) plan tailored specifically for Swiss
            residents.
          </p>
          <div className='grid grid-cols-2 gap-4 mt-8'>
            <div className='p-4 rounded-lg bg-blue-50 dark:bg-blue-900/20'>
              <div className='text-2xl mb-2'>🏛️</div>
              <h4 className='font-semibold'>Swiss Tax System</h4>
              <p className='text-sm text-gray-600'>All 26 cantons supported</p>
            </div>
            <div className='p-4 rounded-lg bg-green-50 dark:bg-green-900/20'>
              <div className='text-2xl mb-2'>💰</div>
              <h4 className='font-semibold'>Pillar 3a Integration</h4>
              <p className='text-sm text-gray-600'>2024 limits included</p>
            </div>
          </div>
        </div>
      ),
    },
    {
      id: 'personal',
      title: 'Personal Information',
      description: 'Tell us about yourself to personalize your financial plan.',
      icon: '👤',
      component: (
        <div className='space-y-6'>
          <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
            <div>
              <label className='block text-sm font-medium mb-2'>
                Current Age
              </label>
              <input
                type='number'
                min='18'
                max='100'
                value={onboardingData.personalInfo.age}
                onChange={e =>
                  updateData('personalInfo', {
                    age: parseInt(e.target.value) || 30,
                  })
                }
                className={`w-full px-3 py-2 rounded-md border ${
                  darkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              />
            </div>
            <div>
              <label className='block text-sm font-medium mb-2'>
                Planned Retirement Age
              </label>
              <input
                type='number'
                min='50'
                max='80'
                value={onboardingData.personalInfo.retirementAge}
                onChange={e =>
                  updateData('personalInfo', {
                    retirementAge: parseInt(e.target.value) || 65,
                  })
                }
                className={`w-full px-3 py-2 rounded-md border ${
                  darkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              />
            </div>
            <div>
              <label className='block text-sm font-medium mb-2'>
                Canton of Residence
              </label>
              <select
                value={onboardingData.personalInfo.canton}
                onChange={e =>
                  updateData('personalInfo', { canton: e.target.value })
                }
                className={`w-full px-3 py-2 rounded-md border ${
                  darkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              >
                {SWISS_CANTONS.map(canton => (
                  <option key={canton.code} value={canton.code}>
                    {canton.name}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className='block text-sm font-medium mb-2'>
                Civil Status
              </label>
              <select
                value={onboardingData.personalInfo.civilStatus}
                onChange={e =>
                  updateData('personalInfo', {
                    civilStatus: e.target.value as any,
                  })
                }
                className={`w-full px-3 py-2 rounded-md border ${
                  darkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              >
                <option value='single'>Single</option>
                <option value='married'>Married</option>
                <option value='divorced'>Divorced</option>
                <option value='widowed'>Widowed</option>
              </select>
            </div>
          </div>
          <div className='bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg'>
            <p className='text-sm'>
              💡 <strong>Why we need this:</strong> Your age and canton
              determine tax calculations, while civil status affects tax
              brackets and deductions.
            </p>
          </div>
        </div>
      ),
    },
    {
      id: 'financial',
      title: 'Financial Situation',
      description: 'Help us understand your current financial position.',
      icon: '💰',
      component: (
        <div className='space-y-6'>
          <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
            <div>
              <label className='block text-sm font-medium mb-2'>
                Monthly Gross Income (CHF)
              </label>
              <input
                type='number'
                step='100'
                value={onboardingData.financialInfo.monthlyIncome}
                onChange={e =>
                  updateData('financialInfo', {
                    monthlyIncome: parseFloat(e.target.value) || 0,
                  })
                }
                className={`w-full px-3 py-2 rounded-md border ${
                  darkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              />
            </div>
            <div>
              <label className='block text-sm font-medium mb-2'>
                Current Savings (CHF)
              </label>
              <input
                type='number'
                step='1000'
                value={onboardingData.financialInfo.currentSavings}
                onChange={e =>
                  updateData('financialInfo', {
                    currentSavings: parseFloat(e.target.value) || 0,
                  })
                }
                className={`w-full px-3 py-2 rounded-md border ${
                  darkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              />
            </div>
            <div>
              <label className='block text-sm font-medium mb-2'>
                Monthly Expenses (CHF)
              </label>
              <input
                type='number'
                step='100'
                value={onboardingData.financialInfo.monthlyExpenses}
                onChange={e =>
                  updateData('financialInfo', {
                    monthlyExpenses: parseFloat(e.target.value) || 0,
                  })
                }
                className={`w-full px-3 py-2 rounded-md border ${
                  darkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              />
            </div>
            <div>
              <label className='block text-sm font-medium mb-2'>
                Risk Tolerance
              </label>
              <select
                value={onboardingData.financialInfo.riskTolerance}
                onChange={e =>
                  updateData('financialInfo', {
                    riskTolerance: e.target.value as any,
                  })
                }
                className={`w-full px-3 py-2 rounded-md border ${
                  darkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              >
                <option value='conservative'>
                  Conservative (Low risk, stable returns)
                </option>
                <option value='moderate'>
                  Moderate (Balanced risk/return)
                </option>
                <option value='aggressive'>
                  Aggressive (High risk, high potential returns)
                </option>
              </select>
            </div>
          </div>

          {/* Financial Health Indicator */}
          <div className='bg-gray-50 dark:bg-gray-800 p-4 rounded-lg'>
            <h4 className='font-semibold mb-2'>Quick Financial Health Check</h4>
            <div className='grid grid-cols-3 gap-4 text-sm'>
              <div>
                <span className='text-gray-600'>Savings Rate:</span>
                <div className='font-semibold text-green-600'>
                  {(
                    ((onboardingData.financialInfo.monthlyIncome -
                      onboardingData.financialInfo.monthlyExpenses) /
                      onboardingData.financialInfo.monthlyIncome) *
                    100
                  ).toFixed(1)}
                  %
                </div>
              </div>
              <div>
                <span className='text-gray-600'>Emergency Fund:</span>
                <div className='font-semibold'>
                  {(
                    onboardingData.financialInfo.currentSavings /
                    onboardingData.financialInfo.monthlyExpenses
                  ).toFixed(1)}{' '}
                  months
                </div>
              </div>
              <div>
                <span className='text-gray-600'>Years to FIRE:</span>
                <div className='font-semibold text-blue-600'>
                  ~
                  {Math.ceil(
                    (onboardingData.goals.fireGoal -
                      onboardingData.financialInfo.currentSavings) /
                      ((onboardingData.financialInfo.monthlyIncome -
                        onboardingData.financialInfo.monthlyExpenses) *
                        12)
                  )}{' '}
                  years
                </div>
              </div>
            </div>
          </div>
        </div>
      ),
    },
    {
      id: 'goals',
      title: 'Financial Goals',
      description: 'Define your FIRE target and primary financial objectives.',
      icon: '🎯',
      component: (
        <div className='space-y-6'>
          <div>
            <label className='block text-sm font-medium mb-2'>
              FIRE Target Amount (CHF)
            </label>
            <input
              type='number'
              step='10000'
              value={onboardingData.goals.fireGoal}
              onChange={e =>
                updateData('goals', {
                  fireGoal: parseFloat(e.target.value) || 0,
                })
              }
              className={`w-full px-3 py-2 rounded-md border ${
                darkMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              }`}
            />
            <p className='text-sm text-gray-500 mt-1'>
              Recommended: 25x your annual expenses (CHF{' '}
              {(
                onboardingData.financialInfo.monthlyExpenses *
                12 *
                25
              ).toLocaleString()}
              )
            </p>
          </div>

          <div>
            <label className='block text-sm font-medium mb-2'>
              Primary Financial Goals (Select all that apply)
            </label>
            <div className='grid grid-cols-2 gap-3'>
              {[
                { id: 'retirement', label: 'Early Retirement', icon: '🏖️' },
                { id: 'house', label: 'Buy a House', icon: '🏠' },
                { id: 'education', label: "Children's Education", icon: '🎓' },
                { id: 'travel', label: 'Travel & Experiences', icon: '✈️' },
                { id: 'business', label: 'Start a Business', icon: '🚀' },
                { id: 'security', label: 'Financial Security', icon: '🛡️' },
              ].map(goal => (
                <label
                  key={goal.id}
                  className='flex items-center space-x-2 cursor-pointer'
                >
                  <input
                    type='checkbox'
                    checked={onboardingData.goals.primaryGoals.includes(
                      goal.id
                    )}
                    onChange={e => {
                      const goals = onboardingData.goals.primaryGoals;
                      if (e.target.checked) {
                        updateData('goals', {
                          primaryGoals: [...goals, goal.id],
                        });
                      } else {
                        updateData('goals', {
                          primaryGoals: goals.filter(g => g !== goal.id),
                        });
                      }
                    }}
                    className='rounded'
                  />
                  <span>
                    {goal.icon} {goal.label}
                  </span>
                </label>
              ))}
            </div>
          </div>

          <div className='bg-green-50 dark:bg-green-900/20 p-4 rounded-lg'>
            <h4 className='font-semibold mb-2'>
              🇨🇭 Swiss FIRE Strategy Recommendations
            </h4>
            <ul className='text-sm space-y-1'>
              <li>• Maximize Pillar 3a contributions (CHF 7,056 in 2024)</li>
              <li>• Consider tax-efficient cantons for retirement</li>
              <li>• Build emergency fund (3-6 months expenses)</li>
              <li>• Diversify with Swiss and international investments</li>
            </ul>
          </div>
        </div>
      ),
    },
    {
      id: 'complete',
      title: 'Setup Complete! 🎉',
      description: 'Your personalized Swiss FIRE plan is ready.',
      icon: '✅',
      component: (
        <div className='text-center space-y-6'>
          <div className='text-6xl mb-4'>🎉</div>
          <h3 className='text-2xl font-bold'>Congratulations!</h3>
          <p className='text-lg text-gray-600'>
            Your personalized Swiss FIRE calculator is now set up and ready to
            use.
          </p>

          <div className='grid grid-cols-1 md:grid-cols-2 gap-4 mt-8'>
            <div className='p-4 rounded-lg bg-blue-50 dark:bg-blue-900/20'>
              <h4 className='font-semibold'>Your FIRE Target</h4>
              <p className='text-2xl font-bold text-blue-600'>
                CHF {onboardingData.goals.fireGoal.toLocaleString()}
              </p>
            </div>
            <div className='p-4 rounded-lg bg-green-50 dark:bg-green-900/20'>
              <h4 className='font-semibold'>Estimated Timeline</h4>
              <p className='text-2xl font-bold text-green-600'>
                {Math.ceil(
                  (onboardingData.goals.fireGoal -
                    onboardingData.financialInfo.currentSavings) /
                    ((onboardingData.financialInfo.monthlyIncome -
                      onboardingData.financialInfo.monthlyExpenses) *
                      12)
                )}{' '}
                years
              </p>
            </div>
          </div>

          <div className='bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg'>
            <h4 className='font-semibold mb-2'>🚀 Next Steps</h4>
            <ul className='text-sm text-left space-y-1'>
              <li>1. Review and refine your expense categories</li>
              <li>
                2. Set up specific savings goals (emergency fund, Pillar 3a)
              </li>
              <li>3. Track your investment portfolio</li>
              <li>4. Monitor your progress monthly</li>
            </ul>
          </div>
        </div>
      ),
    },
  ];

  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      handleComplete();
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleComplete = () => {
    setHasSeenOnboarding(true);
    onComplete(onboardingData);
  };

  const handleSkip = () => {
    setHasSeenOnboarding(true);
    onSkip();
  };

  // Don't show if user has already seen onboarding
  if (hasSeenOnboarding) {
    return null;
  }

  const currentStepData = steps[currentStep];
  const progress = ((currentStep + 1) / steps.length) * 100;

  return (
    <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50'>
      <div
        className={`max-w-4xl w-full max-h-[90vh] overflow-y-auto rounded-lg ${
          darkMode ? 'bg-gray-800' : 'bg-white'
        }`}
      >
        <div className='p-6'>
          {/* Header */}
          <div className='flex items-center justify-between mb-6'>
            <div className='flex items-center gap-3'>
              <span className='text-3xl'>{currentStepData.icon}</span>
              <div>
                <h2 className='text-xl font-bold'>{currentStepData.title}</h2>
                <p className='text-gray-600'>{currentStepData.description}</p>
              </div>
            </div>
            <button
              onClick={handleSkip}
              className='text-gray-500 hover:text-gray-700 text-sm'
            >
              Skip Setup
            </button>
          </div>

          {/* Progress Bar */}
          <div className='mb-8'>
            <div className='flex justify-between text-sm text-gray-500 mb-2'>
              <span>
                Step {currentStep + 1} of {steps.length}
              </span>
              <span>{Math.round(progress)}% Complete</span>
            </div>
            <div className='w-full bg-gray-200 rounded-full h-2'>
              <div
                className='bg-blue-600 h-2 rounded-full transition-all duration-300'
                style={{ width: `${progress}%` }}
              />
            </div>
          </div>

          {/* Step Content */}
          <div className='mb-8'>{currentStepData.component}</div>

          {/* Navigation */}
          <div className='flex justify-between'>
            <button
              onClick={prevStep}
              disabled={currentStep === 0}
              className={`px-6 py-2 rounded-md border transition-colors ${
                currentStep === 0
                  ? 'opacity-50 cursor-not-allowed'
                  : darkMode
                    ? 'border-gray-600 text-gray-300 hover:bg-gray-700'
                    : 'border-gray-300 text-gray-700 hover:bg-gray-50'
              }`}
            >
              Previous
            </button>
            <button
              onClick={nextStep}
              className='px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors'
            >
              {currentStep === steps.length - 1 ? 'Get Started!' : 'Next'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OnboardingWizard;
