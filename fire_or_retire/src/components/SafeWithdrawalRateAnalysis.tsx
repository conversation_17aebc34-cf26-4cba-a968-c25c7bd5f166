import React, { useState, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

interface SafeWithdrawalRateAnalysisProps {
  darkMode: boolean;
  userData: {
    currentAge: number;
    retirementAge: number;
    currentSavings: number;
    monthlyIncome: number;
    monthlyExpenses: number;
    expectedReturn: number;
    inflationRate: number;
    safeWithdrawalRate: number;
  };
}

interface WithdrawalScenario {
  rate: number;
  successRate: number;
  medianPortfolioValue: number;
  worstCasePortfolioValue: number;
  yearsToDepletion: number | null;
  description: string;
  recommendation: 'conservative' | 'moderate' | 'aggressive' | 'risky';
}

interface WithdrawalAnalysis {
  scenarios: WithdrawalScenario[];
  recommendedRate: number;
  currentRateAssessment: string;
  factors: {
    retirementLength: number;
    marketVolatility: number;
    inflationImpact: number;
    sequenceRisk: number;
  };
}

const SafeWithdrawalRateAnalysis: React.FC<SafeWithdrawalRateAnalysisProps> = ({
  darkMode,
  userData,
}) => {
  const { t } = useTranslation();
  const [analysis, setAnalysis] = useState<WithdrawalAnalysis | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [selectedTimeframe, setSelectedTimeframe] = useState<25 | 30 | 35 | 40>(
    30
  );
  const [portfolioAllocation, setPortfolioAllocation] = useState({
    stocks: 60,
    bonds: 40,
  });

  // Calculate safe withdrawal rate analysis
  const calculateWithdrawalAnalysis = async () => {
    setIsAnalyzing(true);

    try {
      await new Promise(resolve => setTimeout(resolve, 2000));

      const {
        retirementAge,
        monthlyExpenses,
        expectedReturn,
        inflationRate,
        safeWithdrawalRate,
      } = userData;

      const retirementLength = selectedTimeframe;
      const annualExpenses = monthlyExpenses * 12;

      // Simulate different withdrawal rates
      const withdrawalRates = [2.5, 3.0, 3.5, 4.0, 4.5, 5.0, 5.5, 6.0];

      const scenarios: WithdrawalScenario[] = withdrawalRates.map(rate => {
        // Monte Carlo simulation (simplified)
        const simulations = 1000;
        let successfulRuns = 0;
        const portfolioValues: number[] = [];

        for (let sim = 0; sim < simulations; sim++) {
          let portfolioValue = annualExpenses * (100 / rate); // Required portfolio size
          let successful = true;

          for (let year = 0; year < retirementLength; year++) {
            // Random market return (simplified normal distribution)
            const randomReturn = expectedReturn + (Math.random() - 0.5) * 20; // ±10% volatility

            // Apply market return
            portfolioValue *= 1 + randomReturn / 100;

            // Withdraw for expenses (inflation-adjusted)
            const inflationAdjustedExpenses =
              annualExpenses * Math.pow(1 + inflationRate / 100, year);
            portfolioValue -= inflationAdjustedExpenses;

            // Check if portfolio depleted
            if (portfolioValue <= 0) {
              successful = false;
              break;
            }
          }

          if (successful) {
            successfulRuns++;
            portfolioValues.push(portfolioValue);
          } else {
            portfolioValues.push(0);
          }
        }

        const successRate = (successfulRuns / simulations) * 100;
        const medianPortfolioValue = portfolioValues.sort((a, b) => a - b)[
          Math.floor(portfolioValues.length / 2)
        ];
        const worstCasePortfolioValue = Math.min(...portfolioValues);

        // Estimate years to depletion for failed scenarios
        let yearsToDepletion: number | null = null;
        if (successRate < 100) {
          // Simplified calculation
          yearsToDepletion = Math.max(
            10,
            retirementLength - (100 - successRate) / 5
          );
        }

        let description = '';
        let recommendation: WithdrawalScenario['recommendation'] = 'moderate';

        if (rate <= 3.0) {
          description =
            'Very conservative approach with high probability of leaving a large inheritance';
          recommendation = 'conservative';
        } else if (rate <= 4.0) {
          description =
            'Traditional safe withdrawal rate with good success probability';
          recommendation = 'moderate';
        } else if (rate <= 5.0) {
          description =
            'Moderate risk with potential for higher spending but increased failure risk';
          recommendation = 'aggressive';
        } else {
          description =
            'High risk approach that may require spending adjustments during retirement';
          recommendation = 'risky';
        }

        return {
          rate,
          successRate,
          medianPortfolioValue,
          worstCasePortfolioValue,
          yearsToDepletion,
          description,
          recommendation,
        };
      });

      // Determine recommended rate based on success rates and user profile
      const recommendedRate =
        scenarios.find(s => s.successRate >= 90)?.rate || 3.5;

      // Assess current rate
      const currentScenario = scenarios.find(
        s => Math.abs(s.rate - safeWithdrawalRate) < 0.1
      );
      const currentRateAssessment = currentScenario
        ? `Your current ${safeWithdrawalRate}% withdrawal rate has a ${currentScenario.successRate.toFixed(1)}% success rate`
        : `Your current ${safeWithdrawalRate}% withdrawal rate needs analysis`;

      // Calculate risk factors
      const factors = {
        retirementLength: Math.min(100, (retirementLength / 40) * 100), // Longer = higher risk
        marketVolatility: Math.min(
          100,
          (portfolioAllocation.stocks / 100) * 80 + 20
        ), // More stocks = higher volatility
        inflationImpact: Math.min(100, inflationRate * 20), // Higher inflation = higher risk
        sequenceRisk: Math.min(100, Math.max(0, (40 - retirementLength) * 2.5)), // Closer to retirement = higher sequence risk
      };

      const analysisResult: WithdrawalAnalysis = {
        scenarios,
        recommendedRate,
        currentRateAssessment,
        factors,
      };

      setAnalysis(analysisResult);
    } catch (error) {
      console.error('Withdrawal analysis failed:', error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  useEffect(() => {
    calculateWithdrawalAnalysis();
  }, [userData, selectedTimeframe, portfolioAllocation]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('de-CH', {
      style: 'currency',
      currency: 'CHF',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getRecommendationColor = (recommendation: string) => {
    switch (recommendation) {
      case 'conservative':
        return 'border-green-500 bg-green-50 dark:bg-green-900/20';
      case 'moderate':
        return 'border-blue-500 bg-blue-50 dark:bg-blue-900/20';
      case 'aggressive':
        return 'border-orange-500 bg-orange-50 dark:bg-orange-900/20';
      case 'risky':
        return 'border-red-500 bg-red-50 dark:bg-red-900/20';
      default:
        return 'border-gray-500 bg-gray-50 dark:bg-gray-900/20';
    }
  };

  const getSuccessRateColor = (rate: number) => {
    if (rate >= 95) return 'text-green-600';
    if (rate >= 85) return 'text-blue-600';
    if (rate >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div>
        <h2 className='text-2xl font-bold mb-2'>
          📊 Safe Withdrawal Rate Analysis
        </h2>
        <p className='text-gray-600 dark:text-gray-400'>
          Comprehensive analysis of withdrawal rates and their probability of
          success over your retirement period
        </p>
      </div>

      {/* Configuration */}
      <div
        className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
      >
        <h3 className='text-lg font-semibold mb-4'>⚙️ Analysis Parameters</h3>

        <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
          <div>
            <label className='block text-sm font-medium mb-2'>
              Retirement Length (Years)
            </label>
            <select
              value={selectedTimeframe}
              onChange={e =>
                setSelectedTimeframe(parseInt(e.target.value) as any)
              }
              className={`w-full px-3 py-2 rounded-md border ${
                darkMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              }`}
            >
              <option value={25}>25 years</option>
              <option value={30}>30 years</option>
              <option value={35}>35 years</option>
              <option value={40}>40 years</option>
            </select>
          </div>

          <div>
            <label className='block text-sm font-medium mb-2'>
              Stock Allocation (%)
            </label>
            <input
              type='range'
              min='0'
              max='100'
              step='5'
              value={portfolioAllocation.stocks}
              onChange={e =>
                setPortfolioAllocation({
                  stocks: parseInt(e.target.value),
                  bonds: 100 - parseInt(e.target.value),
                })
              }
              className='w-full'
            />
            <div className='text-sm text-gray-500 mt-1'>
              {portfolioAllocation.stocks}% Stocks, {portfolioAllocation.bonds}%
              Bonds
            </div>
          </div>

          <div className='flex items-end'>
            <button
              onClick={calculateWithdrawalAnalysis}
              disabled={isAnalyzing}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                isAnalyzing
                  ? 'bg-gray-400 cursor-not-allowed'
                  : 'bg-blue-600 hover:bg-blue-700 text-white'
              }`}
            >
              {isAnalyzing ? 'Analyzing...' : '🔄 Refresh Analysis'}
            </button>
          </div>
        </div>
      </div>

      {/* Current Rate Assessment */}
      {analysis && (
        <div
          className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
        >
          <h3 className='text-lg font-semibold mb-4'>
            🎯 Current Rate Assessment
          </h3>

          <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
            <div className='p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg'>
              <div className='text-sm text-gray-600 dark:text-gray-400'>
                Your Current Rate
              </div>
              <div className='text-2xl font-bold text-blue-600'>
                {userData.safeWithdrawalRate.toFixed(1)}%
              </div>
              <div className='text-sm text-gray-600 dark:text-gray-400 mt-1'>
                {analysis.currentRateAssessment}
              </div>
            </div>

            <div className='p-4 bg-green-50 dark:bg-green-900/20 rounded-lg'>
              <div className='text-sm text-gray-600 dark:text-gray-400'>
                Recommended Rate
              </div>
              <div className='text-2xl font-bold text-green-600'>
                {analysis.recommendedRate.toFixed(1)}%
              </div>
              <div className='text-sm text-gray-600 dark:text-gray-400 mt-1'>
                Based on 90%+ success rate
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Risk Factors */}
      {analysis && (
        <div
          className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
        >
          <h3 className='text-lg font-semibold mb-4'>⚠️ Risk Factors</h3>

          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4'>
            {Object.entries(analysis.factors).map(([key, value]) => (
              <div
                key={key}
                className='text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg'
              >
                <div className='text-sm text-gray-600 dark:text-gray-400 capitalize'>
                  {key.replace(/([A-Z])/g, ' $1').trim()}
                </div>
                <div
                  className={`text-lg font-bold ${
                    value < 30
                      ? 'text-green-600'
                      : value < 60
                        ? 'text-yellow-600'
                        : 'text-red-600'
                  }`}
                >
                  {value.toFixed(0)}%
                </div>
                <div className='w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2 mt-2'>
                  <div
                    className={`h-2 rounded-full transition-all duration-300 ${
                      value < 30
                        ? 'bg-green-600'
                        : value < 60
                          ? 'bg-yellow-600'
                          : 'bg-red-600'
                    }`}
                    style={{ width: `${value}%` }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Withdrawal Rate Scenarios */}
      {isAnalyzing ? (
        <div
          className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
        >
          <div className='flex items-center justify-center py-8'>
            <div className='animate-spin w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full mr-3'></div>
            <span>Running Monte Carlo simulations...</span>
          </div>
        </div>
      ) : (
        analysis && (
          <div
            className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
          >
            <h3 className='text-lg font-semibold mb-4'>
              📈 Withdrawal Rate Scenarios
            </h3>

            <div className='space-y-4'>
              {analysis.scenarios.map(scenario => (
                <div
                  key={scenario.rate}
                  className={`p-4 rounded-lg border-l-4 ${getRecommendationColor(scenario.recommendation)}`}
                >
                  <div className='flex items-start justify-between mb-2'>
                    <div>
                      <h4 className='font-semibold text-lg'>
                        {scenario.rate.toFixed(1)}% Withdrawal Rate
                      </h4>
                      <p className='text-sm text-gray-600 dark:text-gray-400'>
                        {scenario.description}
                      </p>
                    </div>
                    <div className='text-right'>
                      <div
                        className={`text-2xl font-bold ${getSuccessRateColor(scenario.successRate)}`}
                      >
                        {scenario.successRate.toFixed(1)}%
                      </div>
                      <div className='text-sm text-gray-500'>Success Rate</div>
                    </div>
                  </div>

                  <div className='grid grid-cols-1 md:grid-cols-3 gap-4 mt-4'>
                    <div className='text-center p-3 bg-white dark:bg-gray-700 rounded-lg'>
                      <div className='text-sm text-gray-600 dark:text-gray-400'>
                        Median Portfolio
                      </div>
                      <div className='text-lg font-bold text-blue-600'>
                        {formatCurrency(scenario.medianPortfolioValue)}
                      </div>
                    </div>

                    <div className='text-center p-3 bg-white dark:bg-gray-700 rounded-lg'>
                      <div className='text-sm text-gray-600 dark:text-gray-400'>
                        Worst Case
                      </div>
                      <div className='text-lg font-bold text-red-600'>
                        {formatCurrency(scenario.worstCasePortfolioValue)}
                      </div>
                    </div>

                    <div className='text-center p-3 bg-white dark:bg-gray-700 rounded-lg'>
                      <div className='text-sm text-gray-600 dark:text-gray-400'>
                        Years to Depletion
                      </div>
                      <div className='text-lg font-bold text-orange-600'>
                        {scenario.yearsToDepletion
                          ? `${scenario.yearsToDepletion.toFixed(0)} years`
                          : 'Never'}
                      </div>
                    </div>
                  </div>

                  <div className='mt-3 pt-3 border-t border-gray-200 dark:border-gray-600'>
                    <span
                      className={`px-3 py-1 rounded-full text-xs font-medium ${
                        scenario.recommendation === 'conservative'
                          ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                          : scenario.recommendation === 'moderate'
                            ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'
                            : scenario.recommendation === 'aggressive'
                              ? 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300'
                              : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                      }`}
                    >
                      {scenario.recommendation.toUpperCase()} APPROACH
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )
      )}

      {/* Key Insights */}
      {analysis && (
        <div
          className={`p-6 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}
        >
          <h3 className='text-lg font-semibold mb-4'>💡 Key Insights</h3>

          <div className='space-y-3'>
            <div className='flex items-start gap-3'>
              <span className='text-blue-600 text-lg'>📌</span>
              <p className='text-sm'>
                The traditional 4% rule has a{' '}
                {analysis.scenarios
                  .find(s => s.rate === 4.0)
                  ?.successRate.toFixed(1)}
                % success rate for your {selectedTimeframe}-year retirement with{' '}
                {portfolioAllocation.stocks}% stock allocation.
              </p>
            </div>
            <div className='flex items-start gap-3'>
              <span className='text-green-600 text-lg'>✅</span>
              <p className='text-sm'>
                A {analysis.recommendedRate}% withdrawal rate provides 90%+
                success probability, balancing security with spending
                flexibility.
              </p>
            </div>
            <div className='flex items-start gap-3'>
              <span className='text-orange-600 text-lg'>⚠️</span>
              <p className='text-sm'>
                Higher stock allocations increase both expected returns and
                volatility. Consider a bond tent strategy as you approach and
                enter retirement.
              </p>
            </div>
            <div className='flex items-start gap-3'>
              <span className='text-purple-600 text-lg'>🔄</span>
              <p className='text-sm'>
                Withdrawal rates can be adjusted during retirement based on
                market performance and portfolio value using dynamic strategies.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SafeWithdrawalRateAnalysis;
