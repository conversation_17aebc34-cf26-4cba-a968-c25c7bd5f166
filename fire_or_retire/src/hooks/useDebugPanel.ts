import { useState, useEffect, useCallback } from 'react';
import { debugLogger } from '../utils/debug-logger';

export const useDebugPanel = () => {
  const [isOpen, setIsOpen] = useState(false);

  const togglePanel = useCallback(() => {
    setIsOpen(prev => !prev);
    debugLogger.log(
      'info',
      'debug-panel',
      `Debug panel ${!isOpen ? 'opened' : 'closed'}`
    );
  }, [isOpen]);

  const openPanel = useCallback(() => {
    setIsOpen(true);
    debugLogger.log('info', 'debug-panel', 'Debug panel opened');
  }, []);

  const closePanel = useCallback(() => {
    setIsOpen(false);
    debugLogger.log('info', 'debug-panel', 'Debug panel closed');
  }, []);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl+Shift+D to toggle debug panel
      if (event.ctrlKey && event.shiftKey && event.key === 'D') {
        event.preventDefault();
        togglePanel();
      }

      // Ctrl+Shift+L to download logs
      if (event.ctrlKey && event.shiftKey && event.key === 'L') {
        event.preventDefault();
        debugLogger.downloadLogs();
        debugLogger.log(
          'info',
          'debug-panel',
          'Logs downloaded via keyboard shortcut'
        );
      }

      // Ctrl+Shift+C to clear logs
      if (event.ctrlKey && event.shiftKey && event.key === 'C') {
        event.preventDefault();
        debugLogger.clearLogs();
        debugLogger.log(
          'info',
          'debug-panel',
          'Logs cleared via keyboard shortcut'
        );
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [togglePanel]);

  // Auto-open debug panel on errors in development
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      const handleError = () => {
        // Auto-open debug panel when errors occur
        setTimeout(() => {
          setIsOpen(true);
        }, 1000);
      };

      window.addEventListener('error', handleError);
      window.addEventListener('unhandledrejection', handleError);

      return () => {
        window.removeEventListener('error', handleError);
        window.removeEventListener('unhandledrejection', handleError);
      };
    }
  }, []);

  return {
    isOpen,
    togglePanel,
    openPanel,
    closePanel,
  };
};
