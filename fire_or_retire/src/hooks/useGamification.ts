import { useCallback, useEffect, useRef, useState } from 'react';
import AchievementService from '../services/AchievementService';
import { UserProgressService } from '../services/UserProgressService';
import {
  Achievement,
  GamificationNotification,
  LevelUpResult,
  UserAchievement,
  UserProgress,
} from '../types/gamification';

export interface GamificationState {
  progress: UserProgress | null;
  isLoading: boolean;
  error: string | null;
  notifications: GamificationNotification[];
  recentLevelUp: LevelUpResult | null;
  recentXPGain: number;
  recentAchievements: UserAchievement[];
  achievements: Achievement[];
  userAchievements: UserAchievement[];
}

export interface GamificationActions {
  awardSavingsXP: (amount: number, isPillar3a?: boolean) => Promise<void>;
  awardGoalAchievementXP: (
    goalAmount: number,
    targetDate: Date,
    isSwissOptimized?: boolean
  ) => Promise<void>;
  awardStreakXP: (streakDays: number, streakType?: string) => Promise<void>;
  awardSwissOptimizationXP: (
    type: 'tax' | 'healthcare' | 'canton_comparison' | 'pillar3a',
    amount?: number
  ) => Promise<void>;
  awardBudgetAdherenceXP: (
    adherencePercentage: number,
    categoryCount?: number
  ) => Promise<void>;
  awardEducationXP: (
    moduleType: 'basic' | 'intermediate' | 'advanced' | 'swiss_specific',
    score?: number
  ) => Promise<void>;
  refreshProgress: () => Promise<void>;
  clearNotifications: () => void;
  dismissLevelUp: () => void;
  hasFeatureAccess: (feature: string) => Promise<boolean>;
  getAvailableFeatures: () => Promise<string[]>;
  getAchievements: () => Achievement[];
  getUserAchievements: () => UserAchievement[];
  checkAchievements: (
    activityType: string,
    activityData: any
  ) => Promise<UserAchievement[]>;
  dismissAchievement: (achievementId: string) => void;
}

export const useGamification = (
  userId: string
): GamificationState & GamificationActions => {
  const [state, setState] = useState<GamificationState>({
    progress: null,
    isLoading: true,
    error: null,
    notifications: [],
    recentLevelUp: null,
    recentXPGain: 0,
    recentAchievements: [],
    achievements: [],
    userAchievements: [],
  });

  const progressService = useRef(new UserProgressService());
  const achievementService = useRef(new AchievementService());
  const notificationTimeouts = useRef<NodeJS.Timeout[]>([]);

  // Initialize user progress and achievements
  useEffect(() => {
    if (userId) {
      loadUserProgress();
      loadAchievements();
    }
  }, [userId]);

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      notificationTimeouts.current.forEach(timeout => clearTimeout(timeout));
    };
  }, []);

  /**
   * Load user progress from service
   */
  const loadUserProgress = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));
      const progress = await progressService.current.getUserProgress(userId);
      setState(prev => ({ ...prev, progress, isLoading: false }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        error:
          error instanceof Error ? error.message : 'Failed to load progress',
        isLoading: false,
      }));
    }
  }, [userId]);

  /**
   * Load achievements data
   */
  const loadAchievements = useCallback(() => {
    try {
      achievementService.current.initializeUserAchievements(userId);
      const achievements = achievementService.current.getAllAchievements();
      const userAchievements =
        achievementService.current.getUserAchievements(userId);
      setState(prev => ({ ...prev, achievements, userAchievements }));
    } catch (error) {
      console.error('Failed to load achievements:', error);
    }
  }, [userId]);

  /**
   * Handle XP award result and show notifications
   */
  const handleXPAwardResult = useCallback(
    (result: {
      progress: UserProgress;
      levelUp?: LevelUpResult;
      xpGained: number;
    }) => {
      setState(prev => ({
        ...prev,
        progress: result.progress,
        recentLevelUp: result.levelUp || null,
        recentXPGain: result.xpGained,
      }));

      // Show XP gain notification
      if (result.xpGained > 0) {
        showNotification({
          id: `xp_${Date.now()}`,
          userId,
          type: 'achievement_unlock',
          title: 'XP Gained!',
          message: `+${result.xpGained} XP`,
          data: { xpGained: result.xpGained },
          isRead: false,
          priority: 'medium',
          createdAt: new Date(),
        });
      }

      // Show level up notification
      if (result.levelUp) {
        showNotification({
          id: `levelup_${Date.now()}`,
          userId,
          type: 'level_up',
          title: 'Level Up!',
          message: `Congratulations! You've reached level ${result.levelUp.newLevel}!`,
          data: result.levelUp,
          isRead: false,
          priority: 'high',
          createdAt: new Date(),
        });
      }
    },
    [userId]
  );

  /**
   * Show notification with auto-dismiss
   */
  const showNotification = useCallback(
    (notification: GamificationNotification) => {
      setState(prev => ({
        ...prev,
        notifications: [...prev.notifications, notification],
      }));

      // Auto-dismiss after 5 seconds for low priority, 10 seconds for others
      const dismissTime = notification.priority === 'low' ? 5000 : 10000;
      const timeout = setTimeout(() => {
        setState(prev => ({
          ...prev,
          notifications: prev.notifications.filter(
            n => n.id !== notification.id
          ),
        }));
      }, dismissTime);

      notificationTimeouts.current.push(timeout);
    },
    []
  );

  /**
   * Award XP for savings contribution
   */
  const awardSavingsXP = useCallback(
    async (amount: number, isPillar3a: boolean = false) => {
      try {
        const result = await progressService.current.awardSavingsXP(
          userId,
          amount,
          isPillar3a
        );
        handleXPAwardResult(result);
      } catch (error) {
        setState(prev => ({
          ...prev,
          error:
            error instanceof Error
              ? error.message
              : 'Failed to award savings XP',
        }));
      }
    },
    [userId, handleXPAwardResult]
  );

  /**
   * Award XP for goal achievement
   */
  const awardGoalAchievementXP = useCallback(
    async (
      goalAmount: number,
      targetDate: Date,
      isSwissOptimized: boolean = false
    ) => {
      try {
        const result = await progressService.current.awardGoalAchievementXP(
          userId,
          goalAmount,
          targetDate,
          new Date(),
          isSwissOptimized
        );
        handleXPAwardResult(result);
      } catch (error) {
        setState(prev => ({
          ...prev,
          error:
            error instanceof Error
              ? error.message
              : 'Failed to award goal achievement XP',
        }));
      }
    },
    [userId, handleXPAwardResult]
  );

  /**
   * Award XP for streak maintenance
   */
  const awardStreakXP = useCallback(
    async (streakDays: number, streakType: string = 'general') => {
      try {
        const result = await progressService.current.awardStreakXP(
          userId,
          streakDays,
          streakType
        );
        handleXPAwardResult(result);
      } catch (error) {
        setState(prev => ({
          ...prev,
          error:
            error instanceof Error
              ? error.message
              : 'Failed to award streak XP',
        }));
      }
    },
    [userId, handleXPAwardResult]
  );

  /**
   * Award XP for Swiss optimization actions
   */
  const awardSwissOptimizationXP = useCallback(
    async (
      type: 'tax' | 'healthcare' | 'canton_comparison' | 'pillar3a',
      amount?: number
    ) => {
      try {
        const result = await progressService.current.awardSwissOptimizationXP(
          userId,
          type,
          amount
        );
        handleXPAwardResult(result);
      } catch (error) {
        setState(prev => ({
          ...prev,
          error:
            error instanceof Error
              ? error.message
              : 'Failed to award Swiss optimization XP',
        }));
      }
    },
    [userId, handleXPAwardResult]
  );

  /**
   * Award XP for budget adherence
   */
  const awardBudgetAdherenceXP = useCallback(
    async (adherencePercentage: number, categoryCount: number = 1) => {
      try {
        const result = await progressService.current.awardBudgetAdherenceXP(
          userId,
          adherencePercentage,
          categoryCount
        );
        handleXPAwardResult(result);
      } catch (error) {
        setState(prev => ({
          ...prev,
          error:
            error instanceof Error
              ? error.message
              : 'Failed to award budget adherence XP',
        }));
      }
    },
    [userId, handleXPAwardResult]
  );

  /**
   * Award XP for financial education
   */
  const awardEducationXP = useCallback(
    async (
      moduleType: 'basic' | 'intermediate' | 'advanced' | 'swiss_specific',
      score?: number
    ) => {
      try {
        const result = await progressService.current.awardEducationXP(
          userId,
          moduleType,
          score
        );
        handleXPAwardResult(result);
      } catch (error) {
        setState(prev => ({
          ...prev,
          error:
            error instanceof Error
              ? error.message
              : 'Failed to award education XP',
        }));
      }
    },
    [userId, handleXPAwardResult]
  );

  /**
   * Refresh user progress
   */
  const refreshProgress = useCallback(async () => {
    await loadUserProgress();
  }, [loadUserProgress]);

  /**
   * Clear all notifications
   */
  const clearNotifications = useCallback(() => {
    setState(prev => ({ ...prev, notifications: [] }));
    notificationTimeouts.current.forEach(timeout => clearTimeout(timeout));
    notificationTimeouts.current = [];
  }, []);

  /**
   * Dismiss level up notification
   */
  const dismissLevelUp = useCallback(() => {
    setState(prev => ({ ...prev, recentLevelUp: null }));
  }, []);

  /**
   * Check if user has access to a feature
   */
  const hasFeatureAccess = useCallback(
    async (feature: string): Promise<boolean> => {
      try {
        return await progressService.current.hasFeatureAccess(userId, feature);
      } catch (error) {
        console.error('Failed to check feature access:', error);
        return false;
      }
    },
    [userId]
  );

  /**
   * Get available features for user
   */
  const getAvailableFeatures = useCallback(async (): Promise<string[]> => {
    try {
      return await progressService.current.getAvailableFeatures(userId);
    } catch (error) {
      console.error('Failed to get available features:', error);
      return [];
    }
  }, [userId]);

  /**
   * Get all achievements
   */
  const getAchievements = useCallback((): Achievement[] => {
    return state.achievements;
  }, [state.achievements]);

  /**
   * Get user achievements
   */
  const getUserAchievements = useCallback((): UserAchievement[] => {
    return state.userAchievements;
  }, [state.userAchievements]);

  /**
   * Check achievements based on activity
   */
  const checkAchievements = useCallback(
    async (
      activityType: string,
      activityData: any
    ): Promise<UserAchievement[]> => {
      try {
        if (!state.progress) return [];

        const newAchievements =
          achievementService.current.checkAndUnlockAchievements(
            userId,
            activityType,
            activityData,
            state.progress,
            [] // TODO: Pass user goals when available
          );

        return newAchievements;
      } catch (error) {
        console.error('Failed to check achievements:', error);
        return [];
      }
    },
    [userId, state.progress]
  );

  /**
   * Dismiss achievement notification
   */
  const dismissAchievement = useCallback((achievementId: string) => {
    setState(prev => ({
      ...prev,
      recentAchievements: prev.recentAchievements.filter(
        ua => ua.achievementId !== achievementId
      ),
    }));
  }, []);

  return {
    // State
    progress: state.progress,
    isLoading: state.isLoading,
    error: state.error,
    notifications: state.notifications,
    recentLevelUp: state.recentLevelUp,
    recentXPGain: state.recentXPGain,
    recentAchievements: state.recentAchievements,
    achievements: state.achievements,
    userAchievements: state.userAchievements,

    // Actions
    awardSavingsXP,
    awardGoalAchievementXP,
    awardStreakXP,
    awardSwissOptimizationXP,
    awardBudgetAdherenceXP,
    awardEducationXP,
    refreshProgress,
    clearNotifications,
    dismissLevelUp,
    hasFeatureAccess,
    getAvailableFeatures,
    getAchievements,
    getUserAchievements,
    checkAchievements,
    dismissAchievement,
  };
};

export default useGamification;
