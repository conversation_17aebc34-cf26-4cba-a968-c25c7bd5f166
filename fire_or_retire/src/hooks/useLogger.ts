import { useCallback, useEffect, useRef } from 'react';
import { logger, LogLevel, PerformanceMetric } from '../utils/logger';

/**
 * React hook for component-level logging and performance tracking
 */
export const useLogger = (componentName: string) => {
  const renderStartTime = useRef<number>(performance.now());
  const mountTime = useRef<number>(0);

  useEffect(() => {
    mountTime.current = performance.now();
    const mountDuration = mountTime.current - renderStartTime.current;

    logger.debug('Component Lifecycle', `${componentName} mounted`, {
      mountDuration,
      timestamp: new Date().toISOString(),
    });

    logger.recordPerformance(
      `${componentName}-mount`,
      mountDuration,
      'render',
      {
        component: componentName,
      }
    );

    return () => {
      const unmountTime = performance.now();
      const lifetimeDuration = unmountTime - mountTime.current;

      logger.debug('Component Lifecycle', `${componentName} unmounted`, {
        lifetimeDuration,
        timestamp: new Date().toISOString(),
      });
    };
  }, [componentName]);

  const logDebug = useCallback(
    (message: string, data?: any) => {
      logger.debug(componentName, message, data);
    },
    [componentName]
  );

  const logInfo = useCallback(
    (message: string, data?: any) => {
      logger.info(componentName, message, data);
    },
    [componentName]
  );

  const logWarn = useCallback(
    (message: string, data?: any) => {
      logger.warn(componentName, message, data);
    },
    [componentName]
  );

  const logError = useCallback(
    (message: string, data?: any, error?: Error) => {
      logger.error(componentName, message, data, error);
    },
    [componentName]
  );

  const logCritical = useCallback(
    (message: string, data?: any, error?: Error) => {
      logger.critical(componentName, message, data, error);
    },
    [componentName]
  );

  const trackUserAction = useCallback(
    (action: string, data?: any) => {
      logger.trackUserAction(`${componentName}:${action}`, data);
    },
    [componentName]
  );

  const trackCalculation = useCallback(
    (calculationType: string, inputs: any, outputs: any, duration: number) => {
      logger.trackCalculation(
        `${componentName}:${calculationType}`,
        inputs,
        outputs,
        duration
      );
    },
    [componentName]
  );

  const trackPerformance = useCallback(
    (
      name: string,
      value: number,
      category: PerformanceMetric['category'],
      metadata?: any
    ) => {
      logger.recordPerformance(`${componentName}:${name}`, value, category, {
        component: componentName,
        ...metadata,
      });
    },
    [componentName]
  );

  // Measure and log render performance
  const measureRender = useCallback(
    (renderName: string = 'render') => {
      const startTime = performance.now();

      return () => {
        const endTime = performance.now();
        const duration = endTime - startTime;

        logger.recordPerformance(
          `${componentName}:${renderName}`,
          duration,
          'render',
          {
            component: componentName,
            renderName,
          }
        );

        if (duration > 16) {
          // More than one frame at 60fps
          logger.warn(
            'Performance',
            `Slow render detected in ${componentName}`,
            {
              renderName,
              duration,
              threshold: 16,
            }
          );
        }
      };
    },
    [componentName]
  );

  // Measure async operations
  const measureAsync = useCallback(
    async <T>(
      operationName: string,
      operation: () => Promise<T>,
      metadata?: any
    ): Promise<T> => {
      const startTime = performance.now();

      try {
        logDebug(`Starting async operation: ${operationName}`, metadata);
        const result = await operation();
        const duration = performance.now() - startTime;

        trackPerformance(operationName, duration, 'api', metadata);
        logDebug(`Completed async operation: ${operationName}`, {
          duration,
          metadata,
        });

        return result;
      } catch (error) {
        const duration = performance.now() - startTime;
        logError(
          `Failed async operation: ${operationName}`,
          { duration, metadata },
          error as Error
        );
        throw error;
      }
    },
    [componentName, logDebug, logError, trackPerformance]
  );

  // Measure synchronous operations
  const measureSync = useCallback(
    <T>(operationName: string, operation: () => T, metadata?: any): T => {
      const startTime = performance.now();

      try {
        const result = operation();
        const duration = performance.now() - startTime;

        trackPerformance(operationName, duration, 'calculation', metadata);

        if (duration > 10) {
          // Log operations taking more than 10ms
          logDebug(`Completed operation: ${operationName}`, {
            duration,
            metadata,
          });
        }

        return result;
      } catch (error) {
        const duration = performance.now() - startTime;
        logError(
          `Failed operation: ${operationName}`,
          { duration, metadata },
          error as Error
        );
        throw error;
      }
    },
    [componentName, logDebug, logError, trackPerformance]
  );

  // Log component state changes
  const logStateChange = useCallback(
    (stateName: string, oldValue: any, newValue: any) => {
      logDebug(`State change: ${stateName}`, {
        oldValue,
        newValue,
        timestamp: new Date().toISOString(),
      });
    },
    [logDebug]
  );

  // Log component props changes
  const logPropsChange = useCallback(
    (changedProps: Record<string, { old: any; new: any }>) => {
      logDebug('Props changed', {
        changedProps,
        timestamp: new Date().toISOString(),
      });
    },
    [logDebug]
  );

  // Error boundary integration
  const logComponentError = useCallback(
    (error: Error, errorInfo: any) => {
      logCritical(
        'Component Error',
        'React component error caught',
        {
          error: error.message,
          stack: error.stack,
          componentStack: errorInfo.componentStack,
        },
        error
      );
    },
    [logCritical]
  );

  return {
    // Basic logging
    logDebug,
    logInfo,
    logWarn,
    logError,
    logCritical,

    // Performance tracking
    trackUserAction,
    trackCalculation,
    trackPerformance,
    measureRender,
    measureAsync,
    measureSync,

    // Component-specific logging
    logStateChange,
    logPropsChange,
    logComponentError,
  };
};

/**
 * Hook for tracking form interactions and validation
 */
export const useFormLogger = (formName: string) => {
  const { trackUserAction, logError, logInfo } = useLogger(`Form:${formName}`);

  const logFieldChange = useCallback(
    (fieldName: string, value: any, isValid?: boolean) => {
      trackUserAction('field-change', {
        fieldName,
        value: typeof value === 'string' ? value.substring(0, 100) : value, // Truncate long strings
        isValid,
      });
    },
    [trackUserAction]
  );

  const logFormSubmit = useCallback(
    (formData: any, isValid: boolean) => {
      trackUserAction('form-submit', {
        isValid,
        fieldCount: Object.keys(formData).length,
      });

      if (isValid) {
        logInfo('Form submitted successfully', {
          fieldCount: Object.keys(formData).length,
        });
      }
    },
    [trackUserAction, logInfo]
  );

  const logValidationError = useCallback(
    (fieldName: string, error: string) => {
      logError('Validation error', `Field ${fieldName}: ${error}`, {
        fieldName,
        error,
      });
    },
    [logError]
  );

  const logFormError = useCallback(
    (error: string, formData?: any) => {
      logError('Form error', error, { formData });
    },
    [logError]
  );

  return {
    logFieldChange,
    logFormSubmit,
    logValidationError,
    logFormError,
  };
};

/**
 * Hook for tracking financial calculations
 */
export const useFinancialLogger = (calculatorName: string) => {
  const { trackCalculation, logError, logWarn, measureSync } = useLogger(
    `Calculator:${calculatorName}`
  );

  const logCalculation = useCallback(
    (
      calculationType: string,
      inputs: any,
      outputs: any,
      warnings?: string[]
    ) => {
      const startTime = performance.now();
      const duration = performance.now() - startTime;

      trackCalculation(calculationType, inputs, outputs, duration);

      if (warnings && warnings.length > 0) {
        warnings.forEach(warning => {
          logWarn('Calculation warning', warning, { calculationType, inputs });
        });
      }
    },
    [trackCalculation, logWarn]
  );

  const logCalculationError = useCallback(
    (calculationType: string, inputs: any, error: Error) => {
      logError(
        'Calculation failed',
        `${calculationType} calculation failed`,
        {
          calculationType,
          inputs,
          error: error.message,
        },
        error
      );
    },
    [logError]
  );

  const measureCalculation = useCallback(
    <T>(calculationType: string, calculation: () => T, inputs: any): T => {
      return measureSync(calculationType, calculation, { inputs });
    },
    [measureSync]
  );

  return {
    logCalculation,
    logCalculationError,
    measureCalculation,
  };
};
