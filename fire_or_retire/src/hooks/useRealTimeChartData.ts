import { useState, useEffect, useCallback, useRef } from 'react';
import { SwissFinancialDataPoint } from '../components/charts/SwissFinancialChart';
import { MetricOption } from '../components/charts/EnhancedChartControls';

export interface RealTimeDataConfig {
  updateInterval: number; // milliseconds
  maxDataPoints: number;
  enableAutoRefresh: boolean;
  enableLiveCalculations: boolean;
  bufferSize: number;
}

export interface DataUpdateEvent {
  metric: MetricOption;
  dataPoint: SwissFinancialDataPoint;
  timestamp: Date;
  source: 'user_input' | 'calculation' | 'external' | 'simulation';
}

export interface RealTimeDataState {
  data: Map<MetricOption, SwissFinancialDataPoint[]>;
  lastUpdate: Date | null;
  isUpdating: boolean;
  updateCount: number;
  errors: string[];
}

export const useRealTimeChartData = (
  initialData: Map<MetricOption, SwissFinancialDataPoint[]> = new Map(),
  config: RealTimeDataConfig = {
    updateInterval: 5000, // 5 seconds
    maxDataPoints: 100,
    enableAutoRefresh: true,
    enableLiveCalculations: true,
    bufferSize: 10,
  }
) => {
  const [state, setState] = useState<RealTimeDataState>({
    data: initialData,
    lastUpdate: null,
    isUpdating: false,
    updateCount: 0,
    errors: [],
  });

  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const updateQueueRef = useRef<DataUpdateEvent[]>([]);
  const subscribersRef = useRef<Set<(event: DataUpdateEvent) => void>>(new Set());

  // Financial calculation functions
  const calculateNetWorth = useCallback((
    savings: number,
    investments: number,
    debts: number = 0
  ): number => {
    return savings + investments - debts;
  }, []);

  const calculateSavingsRate = useCallback((
    income: number,
    expenses: number
  ): number => {
    if (income <= 0) return 0;
    return ((income - expenses) / income) * 100;
  }, []);

  const calculateFIREProgress = useCallback((
    currentSavings: number,
    annualExpenses: number,
    safeWithdrawalRate: number = 4
  ): number => {
    const targetAmount = annualExpenses * (100 / safeWithdrawalRate);
    return targetAmount > 0 ? (currentSavings / targetAmount) * 100 : 0;
  }, []);

  const calculateTaxOptimization = useCallback((
    income: number,
    canton: string = 'ZH',
    pillar3aContribution: number = 0
  ): number => {
    // Simplified Swiss tax calculation
    const baseTaxRate = canton === 'ZG' ? 0.15 : canton === 'ZH' ? 0.25 : 0.22;
    const taxableIncome = Math.max(0, income - pillar3aContribution);
    return taxableIncome * baseTaxRate;
  }, []);

  // Generate live data point
  const generateLiveDataPoint = useCallback((
    metric: MetricOption,
    baseValue: number,
    userData?: any
  ): SwissFinancialDataPoint => {
    const now = new Date();
    let value = baseValue;

    // Add realistic variations based on metric type
    switch (metric) {
      case 'netWorth':
        // Market volatility simulation
        const marketChange = (Math.random() - 0.5) * 0.02; // ±1% daily change
        value = baseValue * (1 + marketChange);
        break;

      case 'savings':
        // Gradual increase with monthly contributions
        const monthlyContribution = userData?.monthlyIncome - userData?.monthlyExpenses || 1000;
        const dailyContribution = monthlyContribution / 30;
        value = baseValue + dailyContribution + (Math.random() - 0.5) * 100;
        break;

      case 'expenses':
        // Seasonal variations
        const seasonalFactor = 1 + 0.1 * Math.sin((now.getMonth() / 12) * 2 * Math.PI);
        value = baseValue * seasonalFactor * (0.95 + Math.random() * 0.1);
        break;

      case 'income':
        // Stable with small variations
        value = baseValue * (0.98 + Math.random() * 0.04);
        break;

      case 'taxes':
        if (userData) {
          value = calculateTaxOptimization(userData.monthlyIncome * 12, 'ZH', userData.pillar3a || 0) / 12;
        }
        break;

      case 'pillar3a':
        // Gradual accumulation
        const maxAnnual = 7056;
        const monthlyMax = maxAnnual / 12;
        value = Math.min(baseValue + monthlyMax / 30, maxAnnual);
        break;

      case 'healthcare':
        // Relatively stable healthcare costs
        value = baseValue * (0.99 + Math.random() * 0.02);
        break;

      default:
        value = baseValue * (0.95 + Math.random() * 0.1);
    }

    return {
      date: now,
      value: Math.max(0, value), // Ensure non-negative values
      category: metric,
      label: `Live ${metric}`,
    };
  }, [calculateTaxOptimization]);

  // Add data point to metric
  const addDataPoint = useCallback((metric: MetricOption, dataPoint: SwissFinancialDataPoint) => {
    setState(prevState => {
      const newData = new Map(prevState.data);
      const existingData = newData.get(metric) || [];
      
      // Add new point and maintain max data points limit
      const updatedData = [...existingData, dataPoint]
        .sort((a, b) => a.date.getTime() - b.date.getTime())
        .slice(-config.maxDataPoints);
      
      newData.set(metric, updatedData);

      return {
        ...prevState,
        data: newData,
        lastUpdate: new Date(),
        updateCount: prevState.updateCount + 1,
      };
    });
  }, [config.maxDataPoints]);

  // Process update queue
  const processUpdateQueue = useCallback(() => {
    if (updateQueueRef.current.length === 0) return;

    setState(prevState => ({ ...prevState, isUpdating: true }));

    const updates = updateQueueRef.current.splice(0, config.bufferSize);
    
    updates.forEach(event => {
      addDataPoint(event.metric, event.dataPoint);
      
      // Notify subscribers
      subscribersRef.current.forEach(callback => {
        try {
          callback(event);
        } catch (error) {
          console.error('Subscriber callback error:', error);
        }
      });
    });

    setState(prevState => ({ ...prevState, isUpdating: false }));
  }, [config.bufferSize, addDataPoint]);

  // Queue data update
  const queueDataUpdate = useCallback((event: DataUpdateEvent) => {
    updateQueueRef.current.push(event);
    
    // Process immediately if queue is getting full
    if (updateQueueRef.current.length >= config.bufferSize) {
      processUpdateQueue();
    }
  }, [config.bufferSize, processUpdateQueue]);

  // Update data for metric
  const updateMetricData = useCallback((
    metric: MetricOption,
    value: number,
    source: DataUpdateEvent['source'] = 'user_input',
    userData?: any
  ) => {
    const dataPoint = generateLiveDataPoint(metric, value, userData);
    const event: DataUpdateEvent = {
      metric,
      dataPoint,
      timestamp: new Date(),
      source,
    };
    
    queueDataUpdate(event);
  }, [generateLiveDataPoint, queueDataUpdate]);

  // Simulate live data updates
  const simulateLiveUpdates = useCallback((userData?: any) => {
    if (!config.enableLiveCalculations) return;

    const metrics: MetricOption[] = ['netWorth', 'savings', 'expenses', 'income'];
    
    metrics.forEach(metric => {
      const existingData = state.data.get(metric) || [];
      const lastValue = existingData.length > 0 
        ? existingData[existingData.length - 1].value 
        : Math.random() * 10000;
      
      updateMetricData(metric, lastValue, 'simulation', userData);
    });
  }, [config.enableLiveCalculations, state.data, updateMetricData]);

  // Subscribe to data updates
  const subscribe = useCallback((callback: (event: DataUpdateEvent) => void) => {
    subscribersRef.current.add(callback);
    
    return () => {
      subscribersRef.current.delete(callback);
    };
  }, []);

  // Get data for metric
  const getMetricData = useCallback((metric: MetricOption): SwissFinancialDataPoint[] => {
    return state.data.get(metric) || [];
  }, [state.data]);

  // Get latest value for metric
  const getLatestValue = useCallback((metric: MetricOption): number | null => {
    const data = getMetricData(metric);
    return data.length > 0 ? data[data.length - 1].value : null;
  }, [getMetricData]);

  // Clear data for metric
  const clearMetricData = useCallback((metric: MetricOption) => {
    setState(prevState => {
      const newData = new Map(prevState.data);
      newData.delete(metric);
      return { ...prevState, data: newData };
    });
  }, []);

  // Clear all data
  const clearAllData = useCallback(() => {
    setState(prevState => ({
      ...prevState,
      data: new Map(),
      updateCount: 0,
      errors: [],
    }));
  }, []);

  // Start auto-refresh
  const startAutoRefresh = useCallback((userData?: any) => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    if (config.enableAutoRefresh) {
      intervalRef.current = setInterval(() => {
        simulateLiveUpdates(userData);
        processUpdateQueue();
      }, config.updateInterval);
    }
  }, [config.enableAutoRefresh, config.updateInterval, simulateLiveUpdates, processUpdateQueue]);

  // Stop auto-refresh
  const stopAutoRefresh = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopAutoRefresh();
    };
  }, [stopAutoRefresh]);

  return {
    // State
    data: state.data,
    lastUpdate: state.lastUpdate,
    isUpdating: state.isUpdating,
    updateCount: state.updateCount,
    errors: state.errors,

    // Data operations
    addDataPoint,
    updateMetricData,
    getMetricData,
    getLatestValue,
    clearMetricData,
    clearAllData,

    // Real-time features
    startAutoRefresh,
    stopAutoRefresh,
    subscribe,
    simulateLiveUpdates,

    // Calculations
    calculateNetWorth,
    calculateSavingsRate,
    calculateFIREProgress,
    calculateTaxOptimization,
  };
};

export default useRealTimeChartData;
