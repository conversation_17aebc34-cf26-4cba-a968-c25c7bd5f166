import { useState, useCallback } from 'react';
import { OnboardingStep, OnboardingContext } from '../components/onboarding/OnboardingFlowManager';

export type OnboardingAction = 'next' | 'previous' | 'skip' | 'exit';

interface OnboardingTransition {
  from: OnboardingStep;
  to: OnboardingStep;
  condition?: (context: OnboardingContext) => boolean;
  action?: OnboardingAction;
}

interface PersonaFlowConfig {
  persona: string;
  stepOrder: OnboardingStep[];
  skipableSteps: OnboardingStep[];
  requiredData: Record<OnboardingStep, string[]>;
}

export const useOnboardingStateMachine = () => {
  // Define valid transitions
  const transitions: OnboardingTransition[] = [
    // Standard flow
    { from: OnboardingStep.LANDING, to: OnboardingStep.REGISTRATION, action: 'next' },
    { from: OnboardingStep.REGISTRATION, to: OnboardingStep.PROFILE_BASIC, action: 'next' },
    { from: OnboardingStep.PROFILE_BASIC, to: OnboardingStep.PROFILE_SWISS, action: 'next' },
    { from: OnboardingStep.PROFILE_SWISS, to: OnboardingStep.PROFILE_GAMIFICATION, action: 'next' },
    { from: OnboardingStep.PROFILE_GAMIFICATION, to: OnboardingStep.SWISS_OPTIMIZATION, action: 'next' },
    { from: OnboardingStep.SWISS_OPTIMIZATION, to: OnboardingStep.FIRST_GOAL, action: 'next' },
    { from: OnboardingStep.FIRST_GOAL, to: OnboardingStep.GAMIFICATION_INTRO, action: 'next' },
    { from: OnboardingStep.GAMIFICATION_INTRO, to: OnboardingStep.COMMUNITY_WELCOME, action: 'next' },
    { from: OnboardingStep.COMMUNITY_WELCOME, to: OnboardingStep.CHALLENGE_SETUP, action: 'next' },
    { from: OnboardingStep.CHALLENGE_SETUP, to: OnboardingStep.COMPLETED, action: 'next' },

    // Backward transitions
    { from: OnboardingStep.REGISTRATION, to: OnboardingStep.LANDING, action: 'previous' },
    { from: OnboardingStep.PROFILE_BASIC, to: OnboardingStep.REGISTRATION, action: 'previous' },
    { from: OnboardingStep.PROFILE_SWISS, to: OnboardingStep.PROFILE_BASIC, action: 'previous' },
    { from: OnboardingStep.PROFILE_GAMIFICATION, to: OnboardingStep.PROFILE_SWISS, action: 'previous' },
    { from: OnboardingStep.SWISS_OPTIMIZATION, to: OnboardingStep.PROFILE_GAMIFICATION, action: 'previous' },
    { from: OnboardingStep.FIRST_GOAL, to: OnboardingStep.SWISS_OPTIMIZATION, action: 'previous' },
    { from: OnboardingStep.GAMIFICATION_INTRO, to: OnboardingStep.FIRST_GOAL, action: 'previous' },
    { from: OnboardingStep.COMMUNITY_WELCOME, to: OnboardingStep.GAMIFICATION_INTRO, action: 'previous' },
    { from: OnboardingStep.CHALLENGE_SETUP, to: OnboardingStep.COMMUNITY_WELCOME, action: 'previous' },

    // Skip transitions (for optional steps)
    { 
      from: OnboardingStep.PROFILE_GAMIFICATION, 
      to: OnboardingStep.SWISS_OPTIMIZATION, 
      action: 'skip',
      condition: (context) => context.personalization.persona === 'optimization_oliver'
    },
    { 
      from: OnboardingStep.COMMUNITY_WELCOME, 
      to: OnboardingStep.CHALLENGE_SETUP, 
      action: 'skip',
      condition: (context) => !context.user.communityInterest
    },

    // Persona-specific shortcuts
    {
      from: OnboardingStep.PROFILE_SWISS,
      to: OnboardingStep.SWISS_OPTIMIZATION,
      action: 'next',
      condition: (context) => context.personalization.persona === 'optimization_oliver'
    },
    {
      from: OnboardingStep.PROFILE_BASIC,
      to: OnboardingStep.GAMIFICATION_INTRO,
      action: 'next',
      condition: (context) => context.personalization.persona === 'motivation_maria'
    },
  ];

  // Persona-specific flow configurations
  const personaFlows: Record<string, PersonaFlowConfig> = {
    swiss_starter_sarah: {
      persona: 'swiss_starter_sarah',
      stepOrder: [
        OnboardingStep.LANDING,
        OnboardingStep.REGISTRATION,
        OnboardingStep.PROFILE_BASIC,
        OnboardingStep.PROFILE_SWISS,
        OnboardingStep.SWISS_OPTIMIZATION,
        OnboardingStep.FIRST_GOAL,
        OnboardingStep.GAMIFICATION_INTRO,
        OnboardingStep.COMMUNITY_WELCOME,
        OnboardingStep.CHALLENGE_SETUP,
        OnboardingStep.COMPLETED,
      ],
      skipableSteps: [OnboardingStep.COMMUNITY_WELCOME],
      requiredData: {
        [OnboardingStep.REGISTRATION]: ['email', 'canton', 'language'],
        [OnboardingStep.PROFILE_BASIC]: ['incomeRange', 'primaryGoal'],
        [OnboardingStep.PROFILE_SWISS]: ['pillar3aStatus', 'currentBank'],
        [OnboardingStep.SWISS_OPTIMIZATION]: [],
        [OnboardingStep.FIRST_GOAL]: ['goalType', 'targetAmount'],
        [OnboardingStep.GAMIFICATION_INTRO]: [],
        [OnboardingStep.COMMUNITY_WELCOME]: [],
        [OnboardingStep.CHALLENGE_SETUP]: ['challengeType'],
        [OnboardingStep.COMPLETED]: [],
        [OnboardingStep.LANDING]: [],
        [OnboardingStep.PROFILE_GAMIFICATION]: [],
      },
    },
    optimization_oliver: {
      persona: 'optimization_oliver',
      stepOrder: [
        OnboardingStep.LANDING,
        OnboardingStep.REGISTRATION,
        OnboardingStep.PROFILE_BASIC,
        OnboardingStep.PROFILE_SWISS,
        OnboardingStep.SWISS_OPTIMIZATION,
        OnboardingStep.FIRST_GOAL,
        OnboardingStep.COMMUNITY_WELCOME,
        OnboardingStep.CHALLENGE_SETUP,
        OnboardingStep.COMPLETED,
      ],
      skipableSteps: [OnboardingStep.GAMIFICATION_INTRO],
      requiredData: {
        [OnboardingStep.REGISTRATION]: ['email', 'canton', 'language'],
        [OnboardingStep.PROFILE_BASIC]: ['incomeRange', 'currentSavings', 'riskTolerance'],
        [OnboardingStep.PROFILE_SWISS]: ['pillar3aStatus', 'currentBank', 'taxComplexity'],
        [OnboardingStep.SWISS_OPTIMIZATION]: ['implementedOptimizations'],
        [OnboardingStep.FIRST_GOAL]: ['goalType', 'targetAmount', 'timeline'],
        [OnboardingStep.COMMUNITY_WELCOME]: ['communityRole'],
        [OnboardingStep.CHALLENGE_SETUP]: ['challengeType'],
        [OnboardingStep.COMPLETED]: [],
        [OnboardingStep.LANDING]: [],
        [OnboardingStep.PROFILE_GAMIFICATION]: [],
        [OnboardingStep.GAMIFICATION_INTRO]: [],
      },
    },
    motivation_maria: {
      persona: 'motivation_maria',
      stepOrder: [
        OnboardingStep.LANDING,
        OnboardingStep.REGISTRATION,
        OnboardingStep.PROFILE_BASIC,
        OnboardingStep.GAMIFICATION_INTRO,
        OnboardingStep.PROFILE_SWISS,
        OnboardingStep.FIRST_GOAL,
        OnboardingStep.SWISS_OPTIMIZATION,
        OnboardingStep.COMMUNITY_WELCOME,
        OnboardingStep.CHALLENGE_SETUP,
        OnboardingStep.COMPLETED,
      ],
      skipableSteps: [],
      requiredData: {
        [OnboardingStep.REGISTRATION]: ['email', 'canton', 'language'],
        [OnboardingStep.PROFILE_BASIC]: ['incomeRange', 'primaryGoal'],
        [OnboardingStep.GAMIFICATION_INTRO]: ['motivationStyle'],
        [OnboardingStep.PROFILE_SWISS]: ['pillar3aStatus'],
        [OnboardingStep.FIRST_GOAL]: ['goalType', 'targetAmount'],
        [OnboardingStep.SWISS_OPTIMIZATION]: [],
        [OnboardingStep.COMMUNITY_WELCOME]: ['communityParticipation'],
        [OnboardingStep.CHALLENGE_SETUP]: ['challengeType'],
        [OnboardingStep.COMPLETED]: [],
        [OnboardingStep.LANDING]: [],
        [OnboardingStep.PROFILE_GAMIFICATION]: [],
      },
    },
  };

  const canTransition = useCallback((
    currentStep: OnboardingStep,
    action: OnboardingAction,
    context: OnboardingContext
  ): boolean => {
    // Find valid transitions from current step
    const validTransitions = transitions.filter(
      t => t.from === currentStep && t.action === action
    );

    if (validTransitions.length === 0) {
      return false;
    }

    // Check conditions for each valid transition
    return validTransitions.some(transition => {
      if (transition.condition) {
        return transition.condition(context);
      }
      return true;
    });
  }, [transitions]);

  const getNextStep = useCallback(async (
    currentStep: OnboardingStep,
    context: OnboardingContext
  ): Promise<OnboardingStep> => {
    const persona = context.personalization.persona;
    
    // Use persona-specific flow if available
    if (persona && personaFlows[persona]) {
      const flow = personaFlows[persona];
      const currentIndex = flow.stepOrder.indexOf(currentStep);
      
      if (currentIndex >= 0 && currentIndex < flow.stepOrder.length - 1) {
        return flow.stepOrder[currentIndex + 1];
      }
    }

    // Fall back to standard transitions
    const validTransitions = transitions.filter(
      t => t.from === currentStep && t.action === 'next'
    );

    for (const transition of validTransitions) {
      if (!transition.condition || transition.condition(context)) {
        return transition.to;
      }
    }

    // Default fallback
    return OnboardingStep.COMPLETED;
  }, [personaFlows, transitions]);

  const getPreviousStep = useCallback((
    currentStep: OnboardingStep,
    context: OnboardingContext
  ): OnboardingStep | null => {
    const persona = context.personalization.persona;
    
    // Use persona-specific flow if available
    if (persona && personaFlows[persona]) {
      const flow = personaFlows[persona];
      const currentIndex = flow.stepOrder.indexOf(currentStep);
      
      if (currentIndex > 0) {
        return flow.stepOrder[currentIndex - 1];
      }
    }

    // Fall back to standard transitions
    const validTransitions = transitions.filter(
      t => t.from === currentStep && t.action === 'previous'
    );

    if (validTransitions.length > 0) {
      return validTransitions[0].to;
    }

    return null;
  }, [personaFlows, transitions]);

  const validateStepData = useCallback((
    step: OnboardingStep,
    data: any,
    context: OnboardingContext
  ): { isValid: boolean; missingFields: string[] } => {
    const persona = context.personalization.persona;
    const flow = persona ? personaFlows[persona] : null;
    
    if (!flow || !flow.requiredData[step]) {
      return { isValid: true, missingFields: [] };
    }

    const requiredFields = flow.requiredData[step];
    const missingFields = requiredFields.filter(field => {
      return !data[field] && data[field] !== 0 && data[field] !== false;
    });

    return {
      isValid: missingFields.length === 0,
      missingFields,
    };
  }, [personaFlows]);

  const saveProgress = useCallback(async (
    userId: string,
    context: OnboardingContext
  ): Promise<void> => {
    try {
      // Save to localStorage for immediate persistence
      localStorage.setItem(
        `onboarding_progress_${userId}`,
        JSON.stringify({
          ...context,
          lastSaved: new Date().toISOString(),
        })
      );

      // Save to backend (if available)
      if (typeof fetch !== 'undefined') {
        await fetch('/api/onboarding/progress', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            userId,
            progress: context,
          }),
        });
      }
    } catch (error) {
      console.error('Failed to save onboarding progress:', error);
    }
  }, []);

  const loadProgress = useCallback(async (
    userId: string
  ): Promise<OnboardingContext | null> => {
    try {
      // Try to load from localStorage first
      const localData = localStorage.getItem(`onboarding_progress_${userId}`);
      if (localData) {
        const parsed = JSON.parse(localData);
        // Check if data is recent (within 7 days)
        const lastSaved = new Date(parsed.lastSaved);
        const daysSinceLastSave = (Date.now() - lastSaved.getTime()) / (1000 * 60 * 60 * 24);
        
        if (daysSinceLastSave <= 7) {
          return parsed;
        }
      }

      // Try to load from backend
      if (typeof fetch !== 'undefined') {
        const response = await fetch(`/api/onboarding/progress/${userId}`);
        if (response.ok) {
          const data = await response.json();
          return data.progress;
        }
      }

      return null;
    } catch (error) {
      console.error('Failed to load onboarding progress:', error);
      return null;
    }
  }, []);

  const clearProgress = useCallback(async (userId: string): Promise<void> => {
    try {
      // Clear localStorage
      localStorage.removeItem(`onboarding_progress_${userId}`);

      // Clear backend data
      if (typeof fetch !== 'undefined') {
        await fetch(`/api/onboarding/progress/${userId}`, {
          method: 'DELETE',
        });
      }
    } catch (error) {
      console.error('Failed to clear onboarding progress:', error);
    }
  }, []);

  const getEstimatedTimeRemaining = useCallback((
    currentStep: OnboardingStep,
    context: OnboardingContext
  ): number => {
    const persona = context.personalization.persona;
    const flow = persona ? personaFlows[persona] : null;
    
    if (!flow) {
      return 0;
    }

    const currentIndex = flow.stepOrder.indexOf(currentStep);
    const remainingSteps = flow.stepOrder.length - currentIndex - 1;
    
    // Estimate 2-3 minutes per step on average
    const averageTimePerStep = persona === 'optimization_oliver' ? 2 : 3;
    
    return remainingSteps * averageTimePerStep;
  }, [personaFlows]);

  return {
    canTransition,
    getNextStep,
    getPreviousStep,
    validateStepData,
    saveProgress,
    loadProgress,
    clearProgress,
    getEstimatedTimeRemaining,
    personaFlows,
  };
};
