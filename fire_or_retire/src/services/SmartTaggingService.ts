import {
  TransactionTag,
  TaggedTransaction,
  TagCategory,
  SavingsGoal,
  GamificationError,
} from '../types/gamification';

export interface Transaction {
  id: string;
  userId: string;
  amount: number;
  description: string;
  category?: string;
  merchant?: string;
  date: Date;
  type: 'income' | 'expense' | 'transfer';
  account?: string;
  location?: string;
  metadata?: Record<string, any>;
}

export interface TaggingRule {
  id: string;
  name: string;
  description: string;
  conditions: TaggingCondition[];
  tags: string[];
  priority: number;
  isActive: boolean;
  isSwissSpecific: boolean;
}

export interface TaggingCondition {
  field: 'description' | 'merchant' | 'amount' | 'category' | 'location';
  operator: 'contains' | 'equals' | 'greater_than' | 'less_than' | 'regex' | 'starts_with' | 'ends_with';
  value: string | number;
  caseSensitive?: boolean;
}

export interface GoalAlignmentAnalysis {
  transactionId: string;
  alignmentScore: number; // -100 to 100
  affectedGoals: {
    goalId: string;
    impact: number; // -100 to 100
    reason: string;
  }[];
  recommendations: string[];
  tags: string[];
}

export class SmartTaggingService {
  private tags: Map<string, TransactionTag>;
  private rules: Map<string, TaggingRule>;
  private taggedTransactions: Map<string, TaggedTransaction>;
  private swissKeywords: Set<string>;
  private goalKeywords: Map<string, string[]>;

  constructor() {
    this.tags = new Map();
    this.rules = new Map();
    this.taggedTransactions = new Map();
    this.swissKeywords = new Set();
    this.goalKeywords = new Map();
    this.initializeDefaultTags();
    this.initializeDefaultRules();
    this.initializeSwissKeywords();
    this.initializeGoalKeywords();
  }

  /**
   * Initialize default transaction tags
   */
  private initializeDefaultTags(): void {
    const defaultTags: TransactionTag[] = [
      // Goal Booster Tags
      {
        id: 'savings_deposit',
        name: 'Savings Deposit',
        category: 'goal_booster',
        color: '#10B981',
        icon: '💰',
        xpModifier: 10,
        isAutoGenerated: true,
        isSwissSpecific: false,
        description: 'Direct contribution to savings goals',
      },
      {
        id: 'pillar3a_contribution',
        name: 'Pillar 3a Contribution',
        category: 'goal_booster',
        color: '#8B5CF6',
        icon: '🏛️',
        xpModifier: 15,
        isAutoGenerated: true,
        isSwissSpecific: true,
        description: 'Tax-advantaged retirement savings',
      },
      {
        id: 'investment_purchase',
        name: 'Investment Purchase',
        category: 'goal_booster',
        color: '#3B82F6',
        icon: '📈',
        xpModifier: 12,
        isAutoGenerated: true,
        isSwissSpecific: false,
        description: 'Investment in stocks, ETFs, or other assets',
      },

      // Goal Blocker Tags
      {
        id: 'impulse_purchase',
        name: 'Impulse Purchase',
        category: 'goal_blocker',
        color: '#EF4444',
        icon: '🛍️',
        xpModifier: -5,
        isAutoGenerated: true,
        isSwissSpecific: false,
        description: 'Unplanned spending that delays goals',
      },
      {
        id: 'luxury_expense',
        name: 'Luxury Expense',
        category: 'goal_blocker',
        color: '#F59E0B',
        icon: '💎',
        xpModifier: -3,
        isAutoGenerated: true,
        isSwissSpecific: false,
        description: 'High-cost luxury items or services',
      },
      {
        id: 'subscription_creep',
        name: 'Subscription Creep',
        category: 'goal_blocker',
        color: '#DC2626',
        icon: '📱',
        xpModifier: -2,
        isAutoGenerated: true,
        isSwissSpecific: false,
        description: 'Accumulating unnecessary subscriptions',
      },

      // Smart Choice Tags
      {
        id: 'bulk_purchase',
        name: 'Bulk Purchase',
        category: 'smart_choice',
        color: '#059669',
        icon: '📦',
        xpModifier: 5,
        isAutoGenerated: true,
        isSwissSpecific: false,
        description: 'Cost-effective bulk buying',
      },
      {
        id: 'discount_purchase',
        name: 'Discount Purchase',
        category: 'smart_choice',
        color: '#0D9488',
        icon: '🏷️',
        xpModifier: 3,
        isAutoGenerated: true,
        isSwissSpecific: false,
        description: 'Purchase with significant discount',
      },
      {
        id: 'cashback_earned',
        name: 'Cashback Earned',
        category: 'smart_choice',
        color: '#10B981',
        icon: '💳',
        xpModifier: 8,
        isAutoGenerated: true,
        isSwissSpecific: false,
        description: 'Cashback or rewards earned',
      },

      // Swiss Saver Tags
      {
        id: 'tax_deduction',
        name: 'Tax Deduction',
        category: 'swiss_saver',
        color: '#7C3AED',
        icon: '📊',
        xpModifier: 10,
        isAutoGenerated: true,
        isSwissSpecific: true,
        description: 'Tax-deductible expense',
      },
      {
        id: 'healthcare_optimization',
        name: 'Healthcare Optimization',
        category: 'swiss_saver',
        color: '#DC2626',
        icon: '🏥',
        xpModifier: 8,
        isAutoGenerated: true,
        isSwissSpecific: true,
        description: 'Optimized healthcare spending',
      },
      {
        id: 'public_transport',
        name: 'Public Transport',
        category: 'swiss_saver',
        color: '#059669',
        icon: '🚊',
        xpModifier: 5,
        isAutoGenerated: true,
        isSwissSpecific: true,
        description: 'Eco-friendly and cost-effective transport',
      },

      // Investment Tags
      {
        id: 'education_investment',
        name: 'Education Investment',
        category: 'investment',
        color: '#3B82F6',
        icon: '🎓',
        xpModifier: 12,
        isAutoGenerated: true,
        isSwissSpecific: false,
        description: 'Investment in personal or professional development',
      },
      {
        id: 'tool_purchase',
        name: 'Tool Purchase',
        category: 'investment',
        color: '#6366F1',
        icon: '🔧',
        xpModifier: 8,
        isAutoGenerated: true,
        isSwissSpecific: false,
        description: 'Tools that improve productivity or income',
      },
      {
        id: 'health_investment',
        name: 'Health Investment',
        category: 'investment',
        color: '#10B981',
        icon: '💪',
        xpModifier: 10,
        isAutoGenerated: true,
        isSwissSpecific: false,
        description: 'Investment in health and wellness',
      },

      // Reward Tags
      {
        id: 'goal_celebration',
        name: 'Goal Celebration',
        category: 'reward',
        color: '#F59E0B',
        icon: '🎉',
        xpModifier: 0,
        isAutoGenerated: true,
        isSwissSpecific: false,
        description: 'Earned reward for achieving goals',
      },
      {
        id: 'milestone_treat',
        name: 'Milestone Treat',
        category: 'reward',
        color: '#EC4899',
        icon: '🍰',
        xpModifier: 2,
        isAutoGenerated: true,
        isSwissSpecific: false,
        description: 'Small treat for reaching milestones',
      },
    ];

    defaultTags.forEach(tag => this.tags.set(tag.id, tag));
  }

  /**
   * Initialize default tagging rules
   */
  private initializeDefaultRules(): void {
    const defaultRules: TaggingRule[] = [
      // Savings and Investment Rules
      {
        id: 'savings_account_transfer',
        name: 'Savings Account Transfer',
        description: 'Transfers to savings accounts',
        conditions: [
          { field: 'description', operator: 'contains', value: 'savings', caseSensitive: false },
          { field: 'amount', operator: 'greater_than', value: 0 },
        ],
        tags: ['savings_deposit'],
        priority: 10,
        isActive: true,
        isSwissSpecific: false,
      },
      {
        id: 'pillar3a_detection',
        name: 'Pillar 3a Detection',
        description: 'Detect Pillar 3a contributions',
        conditions: [
          { field: 'description', operator: 'contains', value: 'pillar 3a', caseSensitive: false },
        ],
        tags: ['pillar3a_contribution'],
        priority: 15,
        isActive: true,
        isSwissSpecific: true,
      },
      {
        id: 'investment_platforms',
        name: 'Investment Platforms',
        description: 'Detect investment platform transactions',
        conditions: [
          { field: 'merchant', operator: 'contains', value: 'swissquote', caseSensitive: false },
        ],
        tags: ['investment_purchase'],
        priority: 12,
        isActive: true,
        isSwissSpecific: true,
      },

      // Impulse Purchase Detection
      {
        id: 'late_night_purchases',
        name: 'Late Night Purchases',
        description: 'Purchases made late at night (potential impulse)',
        conditions: [
          { field: 'amount', operator: 'greater_than', value: 50 },
        ],
        tags: ['impulse_purchase'],
        priority: 8,
        isActive: true,
        isSwissSpecific: false,
      },
      {
        id: 'luxury_brands',
        name: 'Luxury Brands',
        description: 'Purchases from luxury brands',
        conditions: [
          { field: 'merchant', operator: 'contains', value: 'louis vuitton', caseSensitive: false },
        ],
        tags: ['luxury_expense'],
        priority: 9,
        isActive: true,
        isSwissSpecific: false,
      },

      // Smart Choices
      {
        id: 'discount_keywords',
        name: 'Discount Keywords',
        description: 'Transactions with discount indicators',
        conditions: [
          { field: 'description', operator: 'contains', value: 'discount', caseSensitive: false },
        ],
        tags: ['discount_purchase'],
        priority: 7,
        isActive: true,
        isSwissSpecific: false,
      },
      {
        id: 'cashback_detection',
        name: 'Cashback Detection',
        description: 'Cashback and reward transactions',
        conditions: [
          { field: 'description', operator: 'contains', value: 'cashback', caseSensitive: false },
        ],
        tags: ['cashback_earned'],
        priority: 10,
        isActive: true,
        isSwissSpecific: false,
      },

      // Swiss-Specific Rules
      {
        id: 'sbb_transport',
        name: 'SBB Transport',
        description: 'Swiss public transport',
        conditions: [
          { field: 'merchant', operator: 'contains', value: 'sbb', caseSensitive: false },
        ],
        tags: ['public_transport'],
        priority: 8,
        isActive: true,
        isSwissSpecific: true,
      },
      {
        id: 'swiss_healthcare',
        name: 'Swiss Healthcare',
        description: 'Swiss healthcare providers',
        conditions: [
          { field: 'merchant', operator: 'contains', value: 'css', caseSensitive: false },
        ],
        tags: ['healthcare_optimization'],
        priority: 9,
        isActive: true,
        isSwissSpecific: true,
      },
    ];

    defaultRules.forEach(rule => this.rules.set(rule.id, rule));
  }

  /**
   * Initialize Swiss-specific keywords
   */
  private initializeSwissKeywords(): void {
    const keywords = [
      // Financial institutions
      'ubs', 'credit suisse', 'raiffeisen', 'postfinance', 'migros bank',
      // Insurance companies
      'css', 'swica', 'helsana', 'concordia', 'visana',
      // Transport
      'sbb', 'cff', 'ffs', 'postauto', 'tpg',
      // Retail
      'migros', 'coop', 'denner', 'aldi suisse', 'lidl schweiz',
      // Government
      'steuerverwaltung', 'ahv', 'iv', 'alk', 'gemeinde',
      // Telecom
      'swisscom', 'sunrise', 'salt', 'upc',
    ];
    
    keywords.forEach(keyword => this.swissKeywords.add(keyword.toLowerCase()));
  }

  /**
   * Initialize goal-related keywords
   */
  private initializeGoalKeywords(): void {
    this.goalKeywords.set('emergency_fund', ['emergency', 'reserve', 'buffer', 'safety']);
    this.goalKeywords.set('vacation', ['vacation', 'holiday', 'travel', 'trip', 'flight', 'hotel']);
    this.goalKeywords.set('home_purchase', ['house', 'home', 'property', 'mortgage', 'real estate']);
    this.goalKeywords.set('retirement', ['retirement', 'pension', 'pillar', '3a', '3b']);
    this.goalKeywords.set('education', ['education', 'course', 'training', 'university', 'school']);
    this.goalKeywords.set('vehicle', ['car', 'vehicle', 'auto', 'motorcycle', 'bike']);
    this.goalKeywords.set('investment', ['investment', 'stock', 'etf', 'fund', 'portfolio']);
    this.goalKeywords.set('debt_payoff', ['debt', 'loan', 'credit', 'payoff', 'repayment']);
  }

  /**
   * Auto-tag a transaction using ML-powered analysis
   */
  async autoTagTransaction(
    transaction: Transaction,
    userGoals: SavingsGoal[] = []
  ): Promise<TaggedTransaction> {
    const applicableTags: string[] = [];
    let goalAlignment = 0;

    // Apply rule-based tagging
    const ruleTags = this.applyTaggingRules(transaction);
    applicableTags.push(...ruleTags);

    // Apply ML-powered analysis (simulated)
    const mlTags = await this.performMLAnalysis(transaction);
    applicableTags.push(...mlTags);

    // Analyze goal alignment
    const alignmentAnalysis = this.analyzeGoalAlignment(transaction, userGoals);
    goalAlignment = alignmentAnalysis.alignmentScore;
    applicableTags.push(...alignmentAnalysis.tags);

    // Calculate XP based on tags
    const xpAwarded = this.calculateTagXP(applicableTags);

    const taggedTransaction: TaggedTransaction = {
      transactionId: transaction.id,
      userId: transaction.userId,
      tags: [...new Set(applicableTags)], // Remove duplicates
      goalAlignment,
      xpAwarded,
      autoTagged: true,
      userConfirmed: false,
      taggedAt: new Date(),
    };

    this.taggedTransactions.set(transaction.id, taggedTransaction);
    return taggedTransaction;
  }

  /**
   * Apply rule-based tagging
   */
  private applyTaggingRules(transaction: Transaction): string[] {
    const applicableTags: string[] = [];
    const sortedRules = Array.from(this.rules.values())
      .filter(rule => rule.isActive)
      .sort((a, b) => b.priority - a.priority);

    for (const rule of sortedRules) {
      if (this.evaluateRule(rule, transaction)) {
        applicableTags.push(...rule.tags);
      }
    }

    return applicableTags;
  }

  /**
   * Evaluate if a rule applies to a transaction
   */
  private evaluateRule(rule: TaggingRule, transaction: Transaction): boolean {
    return rule.conditions.every(condition => this.evaluateCondition(condition, transaction));
  }

  /**
   * Evaluate a single condition
   */
  private evaluateCondition(condition: TaggingCondition, transaction: Transaction): boolean {
    let fieldValue: any;
    
    switch (condition.field) {
      case 'description':
        fieldValue = transaction.description;
        break;
      case 'merchant':
        fieldValue = transaction.merchant || '';
        break;
      case 'amount':
        fieldValue = Math.abs(transaction.amount);
        break;
      case 'category':
        fieldValue = transaction.category || '';
        break;
      case 'location':
        fieldValue = transaction.location || '';
        break;
      default:
        return false;
    }

    const targetValue = condition.value;
    const caseSensitive = condition.caseSensitive ?? false;

    if (typeof fieldValue === 'string' && typeof targetValue === 'string') {
      const field = caseSensitive ? fieldValue : fieldValue.toLowerCase();
      const target = caseSensitive ? targetValue : targetValue.toLowerCase();

      switch (condition.operator) {
        case 'contains':
          return field.includes(target);
        case 'equals':
          return field === target;
        case 'starts_with':
          return field.startsWith(target);
        case 'ends_with':
          return field.endsWith(target);
        case 'regex':
          try {
            const regex = new RegExp(target, caseSensitive ? 'g' : 'gi');
            return regex.test(field);
          } catch {
            return false;
          }
        default:
          return false;
      }
    }

    if (typeof fieldValue === 'number' && typeof targetValue === 'number') {
      switch (condition.operator) {
        case 'equals':
          return fieldValue === targetValue;
        case 'greater_than':
          return fieldValue > targetValue;
        case 'less_than':
          return fieldValue < targetValue;
        default:
          return false;
      }
    }

    return false;
  }

  /**
   * Perform ML-powered analysis (simulated)
   */
  private async performMLAnalysis(transaction: Transaction): Promise<string[]> {
    const tags: string[] = [];
    const description = transaction.description.toLowerCase();
    const amount = Math.abs(transaction.amount);

    // Simulate ML analysis with heuristics
    
    // Swiss-specific detection
    if (this.containsSwissKeywords(description)) {
      if (description.includes('pillar') || description.includes('3a')) {
        tags.push('pillar3a_contribution');
      }
      if (description.includes('sbb') || description.includes('transport')) {
        tags.push('public_transport');
      }
    }

    // Amount-based analysis
    if (amount > 1000) {
      if (description.includes('investment') || description.includes('stock')) {
        tags.push('investment_purchase');
      } else if (this.isLikelyImpulsePurchase(transaction)) {
        tags.push('impulse_purchase');
      }
    }

    // Time-based analysis
    const hour = transaction.date.getHours();
    if ((hour >= 22 || hour <= 6) && amount > 50 && transaction.type === 'expense') {
      tags.push('impulse_purchase');
    }

    return tags;
  }

  /**
   * Check if description contains Swiss keywords
   */
  private containsSwissKeywords(description: string): boolean {
    return Array.from(this.swissKeywords).some(keyword => 
      description.includes(keyword)
    );
  }

  /**
   * Determine if transaction is likely an impulse purchase
   */
  private isLikelyImpulsePurchase(transaction: Transaction): boolean {
    const description = transaction.description.toLowerCase();
    const impulseKeywords = ['amazon', 'ebay', 'online', 'app store', 'google play'];
    const hour = transaction.date.getHours();
    
    return impulseKeywords.some(keyword => description.includes(keyword)) &&
           (hour >= 20 || hour <= 8) &&
           Math.abs(transaction.amount) > 100;
  }

  /**
   * Analyze goal alignment
   */
  analyzeGoalAlignment(
    transaction: Transaction,
    userGoals: SavingsGoal[]
  ): GoalAlignmentAnalysis {
    const analysis: GoalAlignmentAnalysis = {
      transactionId: transaction.id,
      alignmentScore: 0,
      affectedGoals: [],
      recommendations: [],
      tags: [],
    };

    const description = transaction.description.toLowerCase();
    const amount = Math.abs(transaction.amount);

    // Analyze each goal
    for (const goal of userGoals) {
      const keywords = this.goalKeywords.get(goal.category) || [];
      const hasKeywordMatch = keywords.some(keyword => description.includes(keyword));
      
      let impact = 0;
      let reason = '';

      if (transaction.type === 'expense') {
        if (hasKeywordMatch) {
          if (goal.category === 'debt_payoff') {
            impact = 50; // Debt payments are positive
            reason = 'Payment towards debt reduction goal';
            analysis.tags.push('goal_booster');
          } else {
            impact = -30; // Spending in goal category delays saving
            reason = `Spending in ${goal.category} category delays savings`;
            analysis.tags.push('goal_blocker');
          }
        } else if (amount > goal.monthlyContribution * 0.5) {
          impact = -20; // Large expenses delay all goals
          reason = 'Large expense reduces available savings';
          analysis.tags.push('goal_blocker');
        }
      } else if (transaction.type === 'income') {
        impact = 20; // Income helps all goals
        reason = 'Additional income accelerates goal achievement';
        analysis.tags.push('goal_booster');
      }

      if (impact !== 0) {
        analysis.affectedGoals.push({
          goalId: goal.id,
          impact,
          reason,
        });
      }
    }

    // Calculate overall alignment score
    if (analysis.affectedGoals.length > 0) {
      analysis.alignmentScore = analysis.affectedGoals.reduce(
        (sum, goal) => sum + goal.impact,
        0
      ) / analysis.affectedGoals.length;
    }

    // Generate recommendations
    if (analysis.alignmentScore < -20) {
      analysis.recommendations.push('Consider if this expense aligns with your financial goals');
      analysis.recommendations.push('Look for ways to reduce similar expenses in the future');
    } else if (analysis.alignmentScore > 20) {
      analysis.recommendations.push('Great choice! This transaction supports your goals');
    }

    return analysis;
  }

  /**
   * Calculate XP based on applied tags
   */
  private calculateTagXP(tagIds: string[]): number {
    let totalXP = 0;
    
    for (const tagId of tagIds) {
      const tag = this.tags.get(tagId);
      if (tag) {
        totalXP += tag.xpModifier;
      }
    }

    return Math.max(0, totalXP); // Ensure non-negative XP
  }

  /**
   * Get all available tags
   */
  getAllTags(): TransactionTag[] {
    return Array.from(this.tags.values());
  }

  /**
   * Get tags by category
   */
  getTagsByCategory(category: TagCategory): TransactionTag[] {
    return Array.from(this.tags.values()).filter(tag => tag.category === category);
  }

  /**
   * Get Swiss-specific tags
   */
  getSwissTags(): TransactionTag[] {
    return Array.from(this.tags.values()).filter(tag => tag.isSwissSpecific);
  }

  /**
   * Get tagged transaction
   */
  getTaggedTransaction(transactionId: string): TaggedTransaction | null {
    return this.taggedTransactions.get(transactionId) || null;
  }

  /**
   * Update transaction tags manually
   */
  updateTransactionTags(
    transactionId: string,
    tagIds: string[],
    userId: string
  ): TaggedTransaction {
    const existing = this.taggedTransactions.get(transactionId);
    const xpAwarded = this.calculateTagXP(tagIds);

    const updated: TaggedTransaction = {
      transactionId,
      userId,
      tags: tagIds,
      goalAlignment: existing?.goalAlignment || 0,
      xpAwarded,
      autoTagged: false,
      userConfirmed: true,
      taggedAt: new Date(),
    };

    this.taggedTransactions.set(transactionId, updated);
    return updated;
  }

  /**
   * Add custom tag
   */
  addCustomTag(tag: Omit<TransactionTag, 'id'>): TransactionTag {
    const id = `custom_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const newTag: TransactionTag = { ...tag, id };
    this.tags.set(id, newTag);
    return newTag;
  }

  /**
   * Add custom tagging rule
   */
  addCustomRule(rule: Omit<TaggingRule, 'id'>): TaggingRule {
    const id = `rule_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const newRule: TaggingRule = { ...rule, id };
    this.rules.set(id, newRule);
    return newRule;
  }
}

export default SmartTaggingService;
