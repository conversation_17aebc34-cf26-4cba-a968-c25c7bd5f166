import {
  UserLevel,
  UserProgress,
  LevelUpResult,
  XPTransaction,
  GamificationError,
} from '../types/gamification';

export class LevelProgressionSystem {
  private levelDefinitions: UserLevel[];
  private maxLevel: number;

  constructor() {
    this.levelDefinitions = this.generateLevelDefinitions();
    this.maxLevel = this.levelDefinitions.length;
  }

  /**
   * Generate level definitions with XP thresholds and benefits
   */
  private generateLevelDefinitions(): UserLevel[] {
    const levels: UserLevel[] = [];
    let cumulativeXP = 0;

    for (let level = 1; level <= 100; level++) {
      const xpRequired = this.calculateXPRequiredForLevel(level);
      const nextLevelXP = level < 100 ? this.calculateXPRequiredForLevel(level + 1) : Infinity;

      levels.push({
        level,
        title: this.getLevelTitle(level),
        minXP: cumulativeXP,
        maxXP: cumulativeXP + xpRequired,
        benefits: this.getLevelBenefits(level),
        unlocks: this.getLevelUnlocks(level),
        multiplier: this.getLevelMultiplier(level),
      });

      cumulativeXP += xpRequired;
    }

    return levels;
  }

  /**
   * Calculate XP required for a specific level
   */
  private calculateXPRequiredForLevel(level: number): number {
    if (level <= 10) {
      // Beginner levels: 1,000 XP per level
      return 1000;
    } else if (level <= 25) {
      // Intermediate levels: 1,500-3,000 XP per level
      return 1500 + ((level - 10) * 100);
    } else if (level <= 50) {
      // Advanced levels: 4,000-8,000 XP per level
      return 4000 + ((level - 25) * 160);
    } else if (level <= 100) {
      // Expert levels: 10,000-20,000 XP per level
      return 10000 + ((level - 50) * 200);
    } else {
      // Master levels: 25,000+ XP per level
      return 25000 + ((level - 100) * 1000);
    }
  }

  /**
   * Get level title based on level range
   */
  private getLevelTitle(level: number): string {
    if (level <= 10) {
      const titles = [
        'Newcomer', 'Learner', 'Saver', 'Budgeter', 'Planner',
        'Tracker', 'Organizer', 'Achiever', 'Focused', 'Dedicated'
      ];
      return titles[level - 1] || 'Beginner';
    } else if (level <= 25) {
      const titles = [
        'Intermediate Saver', 'Goal Setter', 'Budget Master', 'Smart Spender', 'Wise Investor',
        'Financial Student', 'Money Manager', 'Savings Expert', 'Budget Pro', 'Goal Crusher',
        'Financial Planner', 'Wealth Builder', 'Smart Investor', 'Money Wizard', 'Financial Guru'
      ];
      return titles[level - 11] || 'Intermediate';
    } else if (level <= 50) {
      const titles = [
        'Advanced Planner', 'Wealth Strategist', 'Investment Pro', 'Financial Advisor',
        'Money Master', 'Savings Champion', 'Budget Genius', 'Goal Achiever',
        'Financial Expert', 'Wealth Creator', 'Investment Guru', 'Money Mentor',
        'Financial Wizard', 'Wealth Architect', 'Investment Master'
      ];
      const index = Math.floor((level - 26) / 2);
      return titles[index] || 'Advanced';
    } else if (level <= 100) {
      const titles = [
        'Financial Expert', 'Wealth Master', 'Investment Legend', 'Money Sage',
        'Financial Titan', 'Wealth Emperor', 'Investment God', 'Money Oracle',
        'Financial Deity', 'Wealth Supreme'
      ];
      const index = Math.floor((level - 51) / 5);
      return titles[index] || 'Expert';
    } else {
      return 'Grandmaster';
    }
  }

  /**
   * Get benefits for reaching a specific level
   */
  private getLevelBenefits(level: number): string[] {
    const benefits: string[] = [];

    // Every 5 levels: XP bonus increase
    if (level % 5 === 0) {
      benefits.push(`+${Math.floor(level / 5)}% XP bonus`);
    }

    // Every 10 levels: Major feature unlock
    if (level % 10 === 0) {
      benefits.push('New dashboard theme unlocked');
      benefits.push('Advanced analytics access');
    }

    // Specific level benefits
    switch (level) {
      case 5:
        benefits.push('Goal sharing enabled');
        break;
      case 10:
        benefits.push('Achievement showcase unlocked');
        break;
      case 15:
        benefits.push('Custom goal categories');
        break;
      case 20:
        benefits.push('Advanced charts access');
        break;
      case 25:
        benefits.push('Leaderboard participation');
        break;
      case 30:
        benefits.push('Premium export features');
        break;
      case 40:
        benefits.push('AI financial insights');
        break;
      case 50:
        benefits.push('Priority customer support');
        break;
      case 75:
        benefits.push('Beta feature access');
        break;
      case 100:
        benefits.push('Lifetime premium features');
        break;
    }

    // Swiss-specific benefits
    if (level >= 15) {
      benefits.push('Swiss tax optimization tools');
    }
    if (level >= 25) {
      benefits.push('Advanced Pillar 3a tracking');
    }
    if (level >= 35) {
      benefits.push('Canton comparison premium features');
    }

    return benefits;
  }

  /**
   * Get feature unlocks for a specific level
   */
  private getLevelUnlocks(level: number): string[] {
    const unlocks: string[] = [];

    if (level >= 3) unlocks.push('goal_milestones');
    if (level >= 5) unlocks.push('social_sharing');
    if (level >= 8) unlocks.push('streak_protection');
    if (level >= 10) unlocks.push('achievement_showcase');
    if (level >= 12) unlocks.push('budget_categories');
    if (level >= 15) unlocks.push('custom_goals');
    if (level >= 18) unlocks.push('advanced_charts');
    if (level >= 20) unlocks.push('export_features');
    if (level >= 25) unlocks.push('leaderboards');
    if (level >= 30) unlocks.push('challenges');
    if (level >= 35) unlocks.push('ai_insights');
    if (level >= 40) unlocks.push('premium_analytics');
    if (level >= 50) unlocks.push('priority_support');
    if (level >= 60) unlocks.push('beta_features');
    if (level >= 75) unlocks.push('mentor_program');
    if (level >= 100) unlocks.push('lifetime_premium');

    return unlocks;
  }

  /**
   * Get XP multiplier for a specific level
   */
  private getLevelMultiplier(level: number): number {
    return 1 + (Math.floor(level / 5) * 0.01); // 1% increase every 5 levels
  }

  /**
   * Calculate user's current level based on total XP
   */
  calculateCurrentLevel(totalXP: number): UserLevel {
    for (let i = this.levelDefinitions.length - 1; i >= 0; i--) {
      const level = this.levelDefinitions[i];
      if (totalXP >= level.minXP) {
        return level;
      }
    }
    return this.levelDefinitions[0]; // Default to level 1
  }

  /**
   * Calculate progress to next level
   */
  calculateLevelProgress(totalXP: number): UserProgress {
    const currentLevel = this.calculateCurrentLevel(totalXP);
    const nextLevel = this.levelDefinitions[currentLevel.level] || currentLevel;
    
    const currentLevelXP = totalXP - currentLevel.minXP;
    const nextLevelXP = nextLevel.maxXP - currentLevel.minXP;
    const progressToNextLevel = nextLevelXP > 0 ? (currentLevelXP / nextLevelXP) * 100 : 100;

    return {
      userId: '', // Will be set by caller
      totalXP,
      currentLevel: currentLevel.level,
      currentLevelXP,
      nextLevelXP,
      progressToNextLevel: Math.min(100, progressToNextLevel),
      lastXPGain: new Date(),
      streakDays: 0, // Will be set by streak system
      longestStreak: 0,
      totalAchievements: 0,
      completedGoals: 0,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
  }

  /**
   * Process XP gain and check for level ups
   */
  processXPGain(
    currentProgress: UserProgress,
    xpTransaction: XPTransaction
  ): { updatedProgress: UserProgress; levelUpResult?: LevelUpResult } {
    const oldLevel = currentProgress.currentLevel;
    const newTotalXP = currentProgress.totalXP + xpTransaction.amount;
    
    const updatedProgress = this.calculateLevelProgress(newTotalXP);
    updatedProgress.userId = currentProgress.userId;
    updatedProgress.streakDays = currentProgress.streakDays;
    updatedProgress.longestStreak = currentProgress.longestStreak;
    updatedProgress.totalAchievements = currentProgress.totalAchievements;
    updatedProgress.completedGoals = currentProgress.completedGoals;
    updatedProgress.createdAt = currentProgress.createdAt;
    updatedProgress.lastXPGain = new Date();

    let levelUpResult: LevelUpResult | undefined;

    if (updatedProgress.currentLevel > oldLevel) {
      const oldLevelData = this.getLevelData(oldLevel);
      const newLevelData = this.getLevelData(updatedProgress.currentLevel);

      levelUpResult = {
        oldLevel,
        newLevel: updatedProgress.currentLevel,
        xpGained: xpTransaction.amount,
        newUnlocks: this.getNewUnlocks(oldLevel, updatedProgress.currentLevel),
        newBenefits: newLevelData.benefits,
      };
    }

    return { updatedProgress, levelUpResult };
  }

  /**
   * Get level data for a specific level
   */
  getLevelData(level: number): UserLevel {
    const levelData = this.levelDefinitions.find(l => l.level === level);
    if (!levelData) {
      throw new GamificationError(
        `Level ${level} not found`,
        'INVALID_LEVEL',
        { level }
      );
    }
    return levelData;
  }

  /**
   * Get new unlocks between two levels
   */
  private getNewUnlocks(oldLevel: number, newLevel: number): string[] {
    const newUnlocks: string[] = [];
    
    for (let level = oldLevel + 1; level <= newLevel; level++) {
      const levelData = this.getLevelData(level);
      newUnlocks.push(...levelData.unlocks);
    }

    return [...new Set(newUnlocks)]; // Remove duplicates
  }

  /**
   * Check if user has access to a feature based on level
   */
  hasFeatureAccess(userLevel: number, feature: string): boolean {
    for (let level = 1; level <= userLevel; level++) {
      const levelData = this.getLevelData(level);
      if (levelData.unlocks.includes(feature)) {
        return true;
      }
    }
    return false;
  }

  /**
   * Get all available features for a user level
   */
  getAvailableFeatures(userLevel: number): string[] {
    const features: string[] = [];
    
    for (let level = 1; level <= userLevel; level++) {
      const levelData = this.getLevelData(level);
      features.push(...levelData.unlocks);
    }

    return [...new Set(features)]; // Remove duplicates
  }

  /**
   * Calculate XP needed for next level
   */
  getXPToNextLevel(currentXP: number): number {
    const currentLevel = this.calculateCurrentLevel(currentXP);
    const nextLevel = this.levelDefinitions[currentLevel.level];
    
    if (!nextLevel) {
      return 0; // Already at max level
    }

    return nextLevel.maxXP - currentXP;
  }

  /**
   * Get level leaderboard data
   */
  getLevelLeaderboardData(userProgresses: UserProgress[]): Array<{
    userId: string;
    level: number;
    totalXP: number;
    title: string;
    rank: number;
  }> {
    return userProgresses
      .sort((a, b) => b.totalXP - a.totalXP)
      .map((progress, index) => {
        const levelData = this.getLevelData(progress.currentLevel);
        return {
          userId: progress.userId,
          level: progress.currentLevel,
          totalXP: progress.totalXP,
          title: levelData.title,
          rank: index + 1,
        };
      });
  }

  /**
   * Validate level progression integrity
   */
  validateLevelProgression(): boolean {
    for (let i = 1; i < this.levelDefinitions.length; i++) {
      const current = this.levelDefinitions[i - 1];
      const next = this.levelDefinitions[i];
      
      if (current.maxXP !== next.minXP) {
        console.error(`Level progression gap between level ${current.level} and ${next.level}`);
        return false;
      }
    }
    return true;
  }

  /**
   * Get level statistics
   */
  getLevelStatistics(): {
    totalLevels: number;
    totalXPRequired: number;
    averageXPPerLevel: number;
    levelDistribution: Record<string, number>;
  } {
    const totalXPRequired = this.levelDefinitions[this.levelDefinitions.length - 1].maxXP;
    const averageXPPerLevel = totalXPRequired / this.levelDefinitions.length;
    
    const levelDistribution = {
      beginner: 10,
      intermediate: 15,
      advanced: 25,
      expert: 50,
    };

    return {
      totalLevels: this.levelDefinitions.length,
      totalXPRequired,
      averageXPPerLevel,
      levelDistribution,
    };
  }
}

export default LevelProgressionSystem;
