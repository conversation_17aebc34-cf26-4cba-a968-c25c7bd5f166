import {
  XPSource,
  XPTransaction,
  XPCalculationResult,
  UserProgress,
  StreakData,
  DEFAULT_XP_RATES,
  SWISS_BONUS_MULTIPLIERS,
  STREAK_MULTIPLIERS,
  GamificationError,
} from '../types/gamification';

export class XPCalculationEngine {
  private xpRates: Record<XPSource, number>;
  private swissBonusMultipliers: Record<string, number>;
  private streakMultipliers: Record<number, number>;

  constructor(
    xpRates: Record<XPSource, number> = DEFAULT_XP_RATES,
    swissBonusMultipliers: Record<string, number> = SWISS_BONUS_MULTIPLIERS,
    streakMultipliers: Record<number, number> = STREAK_MULTIPLIERS
  ) {
    this.xpRates = xpRates;
    this.swissBonusMultipliers = swissBonusMultipliers;
    this.streakMultipliers = streakMultipliers;
  }

  /**
   * Calculate XP for a savings contribution
   */
  calculateSavingsXP(amount: number, isPillar3a: boolean = false): XPCalculationResult {
    const baseXP = Math.floor(amount / 10); // 1 XP per CHF 10
    let multiplier = 1;
    let bonusXP = 0;

    if (isPillar3a) {
      multiplier = this.swissBonusMultipliers.pillar3a || 1.2;
      bonusXP = Math.floor(baseXP * (multiplier - 1));
    }

    const totalXP = Math.floor(baseXP * multiplier);

    return {
      baseXP,
      multiplier,
      bonusXP,
      totalXP,
      source: 'savings_contribution',
      description: isPillar3a 
        ? `Pillar 3a contribution: CHF ${amount.toLocaleString()} (+${Math.round((multiplier - 1) * 100)}% Swiss bonus)`
        : `Savings contribution: CHF ${amount.toLocaleString()}`,
    };
  }

  /**
   * Calculate XP for goal achievement
   */
  calculateGoalAchievementXP(
    goalAmount: number,
    completionTimeBonus: number = 1,
    isSwissOptimized: boolean = false
  ): XPCalculationResult {
    const baseXP = Math.min(1000, Math.floor(goalAmount / 100));
    let multiplier = completionTimeBonus;

    if (isSwissOptimized) {
      multiplier *= this.swissBonusMultipliers.tax_optimization || 1.5;
    }

    const bonusXP = Math.floor(baseXP * (multiplier - 1));
    const totalXP = Math.floor(baseXP * multiplier);

    return {
      baseXP,
      multiplier,
      bonusXP,
      totalXP,
      source: 'goal_achievement',
      description: `Goal completed: CHF ${goalAmount.toLocaleString()} ${
        completionTimeBonus > 1 ? '(Early completion bonus)' : ''
      } ${isSwissOptimized ? '(Swiss optimization bonus)' : ''}`,
    };
  }

  /**
   * Calculate XP for streak maintenance
   */
  calculateStreakXP(streakDays: number, streakType: string = 'general'): XPCalculationResult {
    const baseXP = this.xpRates.streak_maintenance || 5;
    let multiplier = 1;

    // Apply streak multipliers based on streak length
    for (const [days, mult] of Object.entries(this.streakMultipliers)) {
      if (streakDays >= parseInt(days)) {
        multiplier = mult;
      }
    }

    const bonusXP = Math.floor(baseXP * (multiplier - 1));
    const totalXP = Math.floor(baseXP * multiplier);

    return {
      baseXP,
      multiplier,
      bonusXP,
      totalXP,
      source: 'streak_maintenance',
      description: `${streakDays}-day ${streakType} streak ${
        multiplier > 1 ? `(${multiplier}x multiplier)` : ''
      }`,
    };
  }

  /**
   * Calculate XP for Swiss-specific optimizations
   */
  calculateSwissOptimizationXP(
    optimizationType: 'tax' | 'healthcare' | 'canton_comparison' | 'pillar3a',
    amount?: number,
    metadata?: Record<string, any>
  ): XPCalculationResult {
    let baseXP = this.xpRates.swiss_optimization || 50;
    let multiplier = 1;
    let description = '';

    switch (optimizationType) {
      case 'tax':
        baseXP = this.xpRates.tax_optimization || 50;
        multiplier = this.swissBonusMultipliers.tax_optimization || 1.5;
        description = `Tax optimization action`;
        if (amount) {
          description += `: CHF ${amount.toLocaleString()} potential savings`;
        }
        break;

      case 'healthcare':
        baseXP = this.xpRates.healthcare_optimization || 100;
        multiplier = this.swissBonusMultipliers.healthcare || 1.3;
        description = `Healthcare optimization`;
        if (metadata?.deductibleChange) {
          description += `: Deductible optimized to CHF ${metadata.deductibleChange}`;
        }
        break;

      case 'canton_comparison':
        baseXP = this.xpRates.canton_comparison || 25;
        multiplier = this.swissBonusMultipliers.canton_comparison || 1.25;
        description = `Canton comparison analysis`;
        if (metadata?.cantonsCompared) {
          description += `: ${metadata.cantonsCompared} cantons compared`;
        }
        break;

      case 'pillar3a':
        baseXP = this.xpRates.pillar3a_contribution || 20;
        multiplier = this.swissBonusMultipliers.pillar3a || 1.2;
        description = `Pillar 3a optimization`;
        if (amount) {
          description += `: CHF ${amount.toLocaleString()} contribution`;
        }
        break;

      default:
        throw new GamificationError(
          `Unknown Swiss optimization type: ${optimizationType}`,
          'INVALID_OPTIMIZATION_TYPE'
        );
    }

    const bonusXP = Math.floor(baseXP * (multiplier - 1));
    const totalXP = Math.floor(baseXP * multiplier);

    return {
      baseXP,
      multiplier,
      bonusXP,
      totalXP,
      source: 'swiss_optimization',
      description: `${description} (+${Math.round((multiplier - 1) * 100)}% Swiss bonus)`,
    };
  }

  /**
   * Calculate XP for budget adherence
   */
  calculateBudgetAdherenceXP(
    adherencePercentage: number,
    categoryCount: number = 1
  ): XPCalculationResult {
    const baseXP = this.xpRates.budget_adherence || 10;
    const adherenceMultiplier = Math.max(0.5, adherencePercentage / 100);
    const categoryMultiplier = Math.min(2, 1 + (categoryCount - 1) * 0.1);
    
    const multiplier = adherenceMultiplier * categoryMultiplier;
    const bonusXP = Math.floor(baseXP * (multiplier - 1));
    const totalXP = Math.floor(baseXP * multiplier);

    return {
      baseXP,
      multiplier,
      bonusXP,
      totalXP,
      source: 'budget_adherence',
      description: `Budget adherence: ${adherencePercentage.toFixed(1)}% across ${categoryCount} categories`,
    };
  }

  /**
   * Calculate XP for financial education
   */
  calculateEducationXP(
    moduleType: 'basic' | 'intermediate' | 'advanced' | 'swiss_specific',
    completionScore?: number
  ): XPCalculationResult {
    let baseXP = this.xpRates.financial_education || 25;
    let multiplier = 1;

    // Adjust base XP by module difficulty
    switch (moduleType) {
      case 'basic':
        baseXP = 25;
        break;
      case 'intermediate':
        baseXP = 50;
        break;
      case 'advanced':
        baseXP = 100;
        break;
      case 'swiss_specific':
        baseXP = 75;
        multiplier = this.swissBonusMultipliers.tax_optimization || 1.5;
        break;
    }

    // Apply completion score bonus
    if (completionScore && completionScore > 80) {
      multiplier *= 1.25; // 25% bonus for high scores
    }

    const bonusXP = Math.floor(baseXP * (multiplier - 1));
    const totalXP = Math.floor(baseXP * multiplier);

    return {
      baseXP,
      multiplier,
      bonusXP,
      totalXP,
      source: 'financial_education',
      description: `${moduleType.replace('_', ' ')} education module completed${
        completionScore ? ` (${completionScore}% score)` : ''
      }${moduleType === 'swiss_specific' ? ' (Swiss bonus)' : ''}`,
    };
  }

  /**
   * Calculate XP with streak bonuses applied
   */
  calculateXPWithStreakBonus(
    baseCalculation: XPCalculationResult,
    userStreak: StreakData
  ): XPCalculationResult {
    if (!userStreak.isActive || userStreak.currentStreak < 7) {
      return baseCalculation;
    }

    let streakMultiplier = 1;
    for (const [days, mult] of Object.entries(this.streakMultipliers)) {
      if (userStreak.currentStreak >= parseInt(days)) {
        streakMultiplier = mult;
      }
    }

    if (streakMultiplier <= 1) {
      return baseCalculation;
    }

    const originalTotal = baseCalculation.totalXP;
    const streakBonus = Math.floor(originalTotal * (streakMultiplier - 1));
    const newTotal = originalTotal + streakBonus;

    return {
      ...baseCalculation,
      bonusXP: baseCalculation.bonusXP + streakBonus,
      totalXP: newTotal,
      multiplier: baseCalculation.multiplier * streakMultiplier,
      description: `${baseCalculation.description} + ${userStreak.currentStreak}-day streak bonus (${streakMultiplier}x)`,
    };
  }

  /**
   * Create XP transaction record
   */
  createXPTransaction(
    userId: string,
    calculation: XPCalculationResult,
    metadata?: Record<string, any>
  ): XPTransaction {
    return {
      id: this.generateTransactionId(),
      userId,
      source: calculation.source,
      amount: calculation.totalXP,
      multiplier: calculation.multiplier,
      baseAmount: calculation.baseXP,
      description: calculation.description,
      metadata,
      timestamp: new Date(),
    };
  }

  /**
   * Validate XP calculation parameters
   */
  validateCalculationParams(source: XPSource, params: any): boolean {
    switch (source) {
      case 'savings_contribution':
        return typeof params.amount === 'number' && params.amount > 0;
      
      case 'goal_achievement':
        return typeof params.goalAmount === 'number' && params.goalAmount > 0;
      
      case 'streak_maintenance':
        return typeof params.streakDays === 'number' && params.streakDays >= 0;
      
      case 'swiss_optimization':
        return ['tax', 'healthcare', 'canton_comparison', 'pillar3a'].includes(params.optimizationType);
      
      case 'budget_adherence':
        return typeof params.adherencePercentage === 'number' && 
               params.adherencePercentage >= 0 && 
               params.adherencePercentage <= 100;
      
      case 'financial_education':
        return ['basic', 'intermediate', 'advanced', 'swiss_specific'].includes(params.moduleType);
      
      default:
        return true;
    }
  }

  /**
   * Get XP rate for a specific source
   */
  getXPRate(source: XPSource): number {
    return this.xpRates[source] || 0;
  }

  /**
   * Update XP rates (for A/B testing or configuration changes)
   */
  updateXPRates(newRates: Partial<Record<XPSource, number>>): void {
    this.xpRates = { ...this.xpRates, ...newRates };
  }

  /**
   * Generate unique transaction ID
   */
  private generateTransactionId(): string {
    return `xp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Calculate time-based completion bonus
   */
  calculateTimeBonus(targetDate: Date, completionDate: Date): number {
    const targetTime = targetDate.getTime();
    const completionTime = completionDate.getTime();
    
    if (completionTime <= targetTime) {
      const daysEarly = Math.floor((targetTime - completionTime) / (1000 * 60 * 60 * 24));
      return Math.min(2.0, 1 + (daysEarly * 0.01)); // Max 2x bonus, 1% per day early
    }
    
    return 1; // No bonus for late completion
  }

  /**
   * Calculate compound XP for multiple actions
   */
  calculateCompoundXP(calculations: XPCalculationResult[]): XPCalculationResult {
    const totalBaseXP = calculations.reduce((sum, calc) => sum + calc.baseXP, 0);
    const totalBonusXP = calculations.reduce((sum, calc) => sum + calc.bonusXP, 0);
    const totalXP = calculations.reduce((sum, calc) => sum + calc.totalXP, 0);
    const avgMultiplier = totalBaseXP > 0 ? totalXP / totalBaseXP : 1;

    return {
      baseXP: totalBaseXP,
      multiplier: avgMultiplier,
      bonusXP: totalBonusXP,
      totalXP,
      source: 'achievement_unlock', // Compound actions often trigger achievements
      description: `Compound action: ${calculations.length} activities completed`,
    };
  }
}

export default XPCalculationEngine;
