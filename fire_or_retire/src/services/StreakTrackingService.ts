import {
  StreakData,
  UserProgress,
  XPCalculationResult,
  GamificationError,
} from '../types/gamification';

export interface StreakActivity {
  id: string;
  userId: string;
  type: 'savings' | 'budget_adherence' | 'goal_progress' | 'app_usage' | 'swiss_optimization';
  date: Date;
  amount?: number;
  metadata?: Record<string, any>;
}

export interface StreakMilestone {
  days: number;
  title: string;
  description: string;
  xpReward: number;
  multiplier: number;
  badge?: string;
  isSwissSpecific?: boolean;
}

export interface StreakProtection {
  id: string;
  userId: string;
  streakType: string;
  usedAt: Date;
  reason: string;
  daysProtected: number;
}

export class StreakTrackingService {
  private userStreaks: Map<string, Map<string, StreakData>>;
  private streakActivities: Map<string, StreakActivity[]>;
  private streakProtections: Map<string, StreakProtection[]>;
  private streakMilestones: StreakMilestone[];

  constructor() {
    this.userStreaks = new Map();
    this.streakActivities = new Map();
    this.streakProtections = new Map();
    this.streakMilestones = this.initializeStreakMilestones();
  }

  /**
   * Initialize streak milestones
   */
  private initializeStreakMilestones(): StreakMilestone[] {
    return [
      // Basic Milestones
      {
        days: 3,
        title: 'Getting Started',
        description: 'Complete 3 consecutive days of financial activity',
        xpReward: 25,
        multiplier: 1.1,
        badge: '🔥',
      },
      {
        days: 7,
        title: 'Week Warrior',
        description: 'Maintain a 7-day streak',
        xpReward: 75,
        multiplier: 1.5,
        badge: '⭐',
      },
      {
        days: 14,
        title: 'Two Week Champion',
        description: 'Keep going for 2 weeks straight',
        xpReward: 150,
        multiplier: 1.75,
        badge: '🌟',
      },
      {
        days: 30,
        title: 'Monthly Master',
        description: 'Complete a full month of consistent activity',
        xpReward: 300,
        multiplier: 2.0,
        badge: '💫',
      },
      {
        days: 60,
        title: 'Habit Builder',
        description: '2 months of financial discipline',
        xpReward: 600,
        multiplier: 2.25,
        badge: '🏆',
      },
      {
        days: 100,
        title: 'Centurion',
        description: 'Reach the legendary 100-day milestone',
        xpReward: 1000,
        multiplier: 2.5,
        badge: '👑',
      },
      {
        days: 180,
        title: 'Half Year Hero',
        description: '6 months of unwavering commitment',
        xpReward: 1800,
        multiplier: 2.75,
        badge: '🎖️',
      },
      {
        days: 365,
        title: 'Annual Achiever',
        description: 'A full year of financial excellence',
        xpReward: 3650,
        multiplier: 3.0,
        badge: '🏅',
      },

      // Swiss-Specific Milestones
      {
        days: 21,
        title: 'Swiss Saver',
        description: '3 weeks of Swiss financial optimization',
        xpReward: 210,
        multiplier: 1.8,
        badge: '🇨🇭',
        isSwissSpecific: true,
      },
      {
        days: 90,
        title: 'Pillar 3a Pro',
        description: 'Quarterly Pillar 3a contribution streak',
        xpReward: 900,
        multiplier: 2.3,
        badge: '🏛️',
        isSwissSpecific: true,
      },
    ];
  }

  /**
   * Initialize user streaks
   */
  initializeUserStreaks(userId: string): void {
    if (this.userStreaks.has(userId)) return;

    const streakTypes = ['savings', 'budget_adherence', 'goal_progress', 'app_usage', 'swiss_optimization'];
    const userStreakMap = new Map<string, StreakData>();

    streakTypes.forEach(type => {
      userStreakMap.set(type, {
        userId,
        type: type as any,
        currentStreak: 0,
        longestStreak: 0,
        lastActivityDate: new Date(),
        streakStartDate: new Date(),
        multiplier: 1,
        isActive: false,
        protectionUsed: 0,
        maxProtections: 3,
      });
    });

    this.userStreaks.set(userId, userStreakMap);
    this.streakActivities.set(userId, []);
    this.streakProtections.set(userId, []);
  }

  /**
   * Record streak activity
   */
  recordActivity(activity: Omit<StreakActivity, 'id'>): StreakActivity {
    const fullActivity: StreakActivity = {
      ...activity,
      id: this.generateActivityId(),
    };

    // Add to activities
    const userActivities = this.streakActivities.get(activity.userId) || [];
    userActivities.push(fullActivity);
    this.streakActivities.set(activity.userId, userActivities);

    // Update streak
    this.updateStreak(activity.userId, activity.type, activity.date);

    return fullActivity;
  }

  /**
   * Update streak for user and type
   */
  private updateStreak(userId: string, streakType: string, activityDate: Date): void {
    this.initializeUserStreaks(userId);
    
    const userStreaks = this.userStreaks.get(userId)!;
    const streak = userStreaks.get(streakType);
    
    if (!streak) return;

    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    const activityDateOnly = new Date(activityDate.getFullYear(), activityDate.getMonth(), activityDate.getDate());
    const lastActivityDateOnly = new Date(streak.lastActivityDate.getFullYear(), streak.lastActivityDate.getMonth(), streak.lastActivityDate.getDate());
    const todayOnly = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const yesterdayOnly = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate());

    // Check if activity is for today or yesterday
    if (activityDateOnly.getTime() === todayOnly.getTime() || 
        activityDateOnly.getTime() === yesterdayOnly.getTime()) {
      
      // Check if this continues the streak
      if (activityDateOnly.getTime() === lastActivityDateOnly.getTime()) {
        // Same day, no change to streak count
        return;
      } else if (activityDateOnly.getTime() === lastActivityDateOnly.getTime() + 86400000 || // Next day
                 (streak.currentStreak === 0)) { // Starting new streak
        
        // Continue or start streak
        if (streak.currentStreak === 0) {
          streak.streakStartDate = activityDate;
        }
        
        streak.currentStreak++;
        streak.isActive = true;
        streak.lastActivityDate = activityDate;
        
        // Update longest streak
        if (streak.currentStreak > streak.longestStreak) {
          streak.longestStreak = streak.currentStreak;
        }
        
        // Update multiplier based on current streak
        streak.multiplier = this.calculateStreakMultiplier(streak.currentStreak);
        
      } else {
        // Streak broken, reset
        this.resetStreak(streak, activityDate);
      }
    }
  }

  /**
   * Check and update all user streaks (daily maintenance)
   */
  checkStreakMaintenance(userId: string): { brokenStreaks: string[]; activeStreaks: string[] } {
    this.initializeUserStreaks(userId);
    
    const userStreaks = this.userStreaks.get(userId)!;
    const brokenStreaks: string[] = [];
    const activeStreaks: string[] = [];
    
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    userStreaks.forEach((streak, streakType) => {
      if (!streak.isActive) return;
      
      const lastActivityDateOnly = new Date(
        streak.lastActivityDate.getFullYear(),
        streak.lastActivityDate.getMonth(),
        streak.lastActivityDate.getDate()
      );
      
      const yesterdayOnly = new Date(
        yesterday.getFullYear(),
        yesterday.getMonth(),
        yesterday.getDate()
      );
      
      // If last activity was not yesterday or today, streak is broken
      if (lastActivityDateOnly.getTime() < yesterdayOnly.getTime()) {
        // Check if user has streak protection available
        if (this.canUseStreakProtection(userId, streakType)) {
          // Offer streak protection (this would trigger UI)
          console.log(`Streak protection available for ${streakType} streak`);
        } else {
          // Break the streak
          this.resetStreak(streak, today);
          brokenStreaks.push(streakType);
        }
      } else {
        activeStreaks.push(streakType);
      }
    });
    
    return { brokenStreaks, activeStreaks };
  }

  /**
   * Use streak protection
   */
  useStreakProtection(
    userId: string,
    streakType: string,
    reason: string = 'User requested'
  ): boolean {
    if (!this.canUseStreakProtection(userId, streakType)) {
      return false;
    }
    
    const userStreaks = this.userStreaks.get(userId);
    const streak = userStreaks?.get(streakType);
    
    if (!streak) return false;
    
    // Use protection
    streak.protectionUsed++;
    
    // Record protection usage
    const protection: StreakProtection = {
      id: this.generateProtectionId(),
      userId,
      streakType,
      usedAt: new Date(),
      reason,
      daysProtected: 1,
    };
    
    const userProtections = this.streakProtections.get(userId) || [];
    userProtections.push(protection);
    this.streakProtections.set(userId, userProtections);
    
    // Extend last activity date by one day
    const newLastActivity = new Date(streak.lastActivityDate);
    newLastActivity.setDate(newLastActivity.getDate() + 1);
    streak.lastActivityDate = newLastActivity;
    
    return true;
  }

  /**
   * Check if user can use streak protection
   */
  canUseStreakProtection(userId: string, streakType: string): boolean {
    const userStreaks = this.userStreaks.get(userId);
    const streak = userStreaks?.get(streakType);
    
    if (!streak) return false;
    
    return streak.protectionUsed < streak.maxProtections && streak.currentStreak >= 7;
  }

  /**
   * Reset streak
   */
  private resetStreak(streak: StreakData, resetDate: Date): void {
    streak.currentStreak = 0;
    streak.isActive = false;
    streak.multiplier = 1;
    streak.streakStartDate = resetDate;
  }

  /**
   * Calculate streak multiplier
   */
  private calculateStreakMultiplier(streakDays: number): number {
    const milestone = this.streakMilestones
      .filter(m => streakDays >= m.days)
      .sort((a, b) => b.days - a.days)[0];
    
    return milestone?.multiplier || 1;
  }

  /**
   * Get user streak data
   */
  getUserStreak(userId: string, streakType: string): StreakData | null {
    this.initializeUserStreaks(userId);
    const userStreaks = this.userStreaks.get(userId);
    return userStreaks?.get(streakType) || null;
  }

  /**
   * Get all user streaks
   */
  getAllUserStreaks(userId: string): Map<string, StreakData> {
    this.initializeUserStreaks(userId);
    return this.userStreaks.get(userId) || new Map();
  }

  /**
   * Get streak statistics
   */
  getStreakStatistics(userId: string): {
    totalActiveStreaks: number;
    longestCurrentStreak: number;
    longestEverStreak: number;
    totalStreakDays: number;
    averageStreakLength: number;
    protectionsUsed: number;
    protectionsAvailable: number;
    nextMilestone?: StreakMilestone;
  } {
    this.initializeUserStreaks(userId);
    const userStreaks = this.userStreaks.get(userId)!;
    const userProtections = this.streakProtections.get(userId) || [];
    
    let totalActiveStreaks = 0;
    let longestCurrentStreak = 0;
    let longestEverStreak = 0;
    let totalStreakDays = 0;
    let totalStreaks = 0;
    let protectionsAvailable = 0;
    
    userStreaks.forEach(streak => {
      if (streak.isActive) {
        totalActiveStreaks++;
      }
      
      if (streak.currentStreak > longestCurrentStreak) {
        longestCurrentStreak = streak.currentStreak;
      }
      
      if (streak.longestStreak > longestEverStreak) {
        longestEverStreak = streak.longestStreak;
      }
      
      totalStreakDays += streak.longestStreak;
      totalStreaks++;
      protectionsAvailable += (streak.maxProtections - streak.protectionUsed);
    });
    
    const averageStreakLength = totalStreaks > 0 ? totalStreakDays / totalStreaks : 0;
    
    // Find next milestone
    const nextMilestone = this.streakMilestones
      .filter(m => m.days > longestCurrentStreak)
      .sort((a, b) => a.days - b.days)[0];
    
    return {
      totalActiveStreaks,
      longestCurrentStreak,
      longestEverStreak,
      totalStreakDays,
      averageStreakLength,
      protectionsUsed: userProtections.length,
      protectionsAvailable,
      nextMilestone,
    };
  }

  /**
   * Get streak milestones
   */
  getStreakMilestones(): StreakMilestone[] {
    return this.streakMilestones;
  }

  /**
   * Get achieved milestones for user
   */
  getAchievedMilestones(userId: string): StreakMilestone[] {
    const stats = this.getStreakStatistics(userId);
    return this.streakMilestones.filter(m => m.days <= stats.longestEverStreak);
  }

  /**
   * Calculate XP for streak activity
   */
  calculateStreakXP(userId: string, streakType: string, baseXP: number = 5): XPCalculationResult {
    const streak = this.getUserStreak(userId, streakType);
    
    if (!streak || !streak.isActive) {
      return {
        baseXP,
        multiplier: 1,
        bonusXP: 0,
        totalXP: baseXP,
        source: 'streak_maintenance',
        description: `${streakType} activity`,
      };
    }
    
    const multiplier = streak.multiplier;
    const bonusXP = Math.floor(baseXP * (multiplier - 1));
    const totalXP = Math.floor(baseXP * multiplier);
    
    return {
      baseXP,
      multiplier,
      bonusXP,
      totalXP,
      source: 'streak_maintenance',
      description: `${streakType} streak (${streak.currentStreak} days, ${multiplier}x multiplier)`,
    };
  }

  /**
   * Get recent activities
   */
  getRecentActivities(userId: string, limit: number = 10): StreakActivity[] {
    const activities = this.streakActivities.get(userId) || [];
    return activities
      .sort((a, b) => b.date.getTime() - a.date.getTime())
      .slice(0, limit);
  }

  /**
   * Get streak protections
   */
  getStreakProtections(userId: string): StreakProtection[] {
    return this.streakProtections.get(userId) || [];
  }

  /**
   * Generate activity ID
   */
  private generateActivityId(): string {
    return `activity_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Generate protection ID
   */
  private generateProtectionId(): string {
    return `protection_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Check if user has activity today
   */
  hasActivityToday(userId: string, streakType: string): boolean {
    const activities = this.streakActivities.get(userId) || [];
    const today = new Date();
    const todayOnly = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    
    return activities.some(activity => {
      const activityDateOnly = new Date(
        activity.date.getFullYear(),
        activity.date.getMonth(),
        activity.date.getDate()
      );
      return activity.type === streakType && 
             activityDateOnly.getTime() === todayOnly.getTime();
    });
  }

  /**
   * Get streak calendar data
   */
  getStreakCalendar(userId: string, streakType: string, year: number, month: number): boolean[] {
    const activities = this.streakActivities.get(userId) || [];
    const daysInMonth = new Date(year, month + 1, 0).getDate();
    const calendar: boolean[] = new Array(daysInMonth).fill(false);
    
    activities
      .filter(activity => activity.type === streakType)
      .forEach(activity => {
        const activityDate = new Date(activity.date);
        if (activityDate.getFullYear() === year && activityDate.getMonth() === month) {
          const day = activityDate.getDate() - 1; // 0-indexed
          if (day >= 0 && day < daysInMonth) {
            calendar[day] = true;
          }
        }
      });
    
    return calendar;
  }
}

export default StreakTrackingService;
