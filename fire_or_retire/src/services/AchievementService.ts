import {
  Achievement,
  UserAchievement,
  AchievementCategory,
  AchievementRarity,
  AchievementRequirement,
  UnlockCondition,
  UserProgress,
  SavingsGoal,
  AchievementAlreadyUnlockedError,
  GamificationError,
} from '../types/gamification';

export class AchievementService {
  private achievements: Map<string, Achievement>;
  private userAchievements: Map<string, UserAchievement[]>; // userId -> achievements

  constructor() {
    this.achievements = new Map();
    this.userAchievements = new Map();
    this.initializeAchievements();
  }

  /**
   * Initialize all achievement definitions
   */
  private initializeAchievements(): void {
    const achievementDefinitions = this.createAchievementDefinitions();
    achievementDefinitions.forEach(achievement => {
      this.achievements.set(achievement.id, achievement);
    });
  }

  /**
   * Create all achievement definitions
   */
  private createAchievementDefinitions(): Achievement[] {
    return [
      // Saver Achievements
      {
        id: 'first_savings',
        name: 'First Steps',
        description: 'Make your first savings contribution',
        icon: '💰',
        category: 'saver',
        rarity: 'common',
        xpReward: 50,
        requirements: [{ type: 'savings_amount', value: 1 }],
        unlockConditions: [],
        isSecret: false,
        isSwissSpecific: false,
        sortOrder: 1,
      },
      {
        id: 'savings_1k',
        name: 'Building Momentum',
        description: 'Save your first CHF 1,000',
        icon: '🎯',
        category: 'saver',
        rarity: 'common',
        xpReward: 100,
        requirements: [{ type: 'savings_amount', value: 1000 }],
        unlockConditions: [],
        isSecret: false,
        isSwissSpecific: false,
        sortOrder: 2,
      },
      {
        id: 'savings_10k',
        name: 'Serious Saver',
        description: 'Accumulate CHF 10,000 in savings',
        icon: '💎',
        category: 'saver',
        rarity: 'rare',
        xpReward: 500,
        requirements: [{ type: 'savings_amount', value: 10000 }],
        unlockConditions: [],
        isSecret: false,
        isSwissSpecific: false,
        sortOrder: 3,
      },
      {
        id: 'savings_100k',
        name: 'Wealth Builder',
        description: 'Reach CHF 100,000 in total savings',
        icon: '👑',
        category: 'saver',
        rarity: 'epic',
        xpReward: 2000,
        requirements: [{ type: 'savings_amount', value: 100000 }],
        unlockConditions: [],
        isSecret: false,
        isSwissSpecific: false,
        sortOrder: 4,
      },
      {
        id: 'savings_1m',
        name: 'Financial Freedom',
        description: 'Achieve CHF 1,000,000 in total wealth',
        icon: '🏆',
        category: 'saver',
        rarity: 'legendary',
        xpReward: 10000,
        requirements: [{ type: 'savings_amount', value: 1000000 }],
        unlockConditions: [],
        isSecret: false,
        isSwissSpecific: false,
        sortOrder: 5,
      },

      // Streak Achievements
      {
        id: 'streak_7',
        name: 'Getting Started',
        description: 'Maintain a 7-day savings streak',
        icon: '🔥',
        category: 'streak',
        rarity: 'common',
        xpReward: 75,
        requirements: [{ type: 'streak_days', value: 7 }],
        unlockConditions: [],
        isSecret: false,
        isSwissSpecific: false,
        sortOrder: 10,
      },
      {
        id: 'streak_30',
        name: 'Building Habits',
        description: 'Maintain a 30-day savings streak',
        icon: '🌟',
        category: 'streak',
        rarity: 'rare',
        xpReward: 200,
        requirements: [{ type: 'streak_days', value: 30 }],
        unlockConditions: [],
        isSecret: false,
        isSwissSpecific: false,
        sortOrder: 11,
      },
      {
        id: 'streak_100',
        name: 'Committed',
        description: 'Maintain a 100-day savings streak',
        icon: '💪',
        category: 'streak',
        rarity: 'epic',
        xpReward: 750,
        requirements: [{ type: 'streak_days', value: 100 }],
        unlockConditions: [],
        isSecret: false,
        isSwissSpecific: false,
        sortOrder: 12,
      },
      {
        id: 'streak_365',
        name: 'Dedicated',
        description: 'Maintain a full year savings streak',
        icon: '🎖️',
        category: 'streak',
        rarity: 'legendary',
        xpReward: 3000,
        requirements: [{ type: 'streak_days', value: 365 }],
        unlockConditions: [],
        isSecret: false,
        isSwissSpecific: false,
        sortOrder: 13,
      },

      // Goal Achievements
      {
        id: 'first_goal',
        name: 'Goal Setter',
        description: 'Create your first savings goal',
        icon: '🎯',
        category: 'goal',
        rarity: 'common',
        xpReward: 50,
        requirements: [{ type: 'goal_completed', value: 0, metadata: { created: true } }],
        unlockConditions: [],
        isSecret: false,
        isSwissSpecific: false,
        sortOrder: 20,
      },
      {
        id: 'goal_completed_1',
        name: 'Achiever',
        description: 'Complete your first savings goal',
        icon: '✅',
        category: 'goal',
        rarity: 'common',
        xpReward: 150,
        requirements: [{ type: 'goal_completed', value: 1 }],
        unlockConditions: [],
        isSecret: false,
        isSwissSpecific: false,
        sortOrder: 21,
      },
      {
        id: 'goal_completed_5',
        name: 'Goal Crusher',
        description: 'Complete 5 savings goals',
        icon: '🏅',
        category: 'goal',
        rarity: 'rare',
        xpReward: 500,
        requirements: [{ type: 'goal_completed', value: 5 }],
        unlockConditions: [],
        isSecret: false,
        isSwissSpecific: false,
        sortOrder: 22,
      },
      {
        id: 'goal_completed_10',
        name: 'Master Planner',
        description: 'Complete 10 savings goals',
        icon: '🎖️',
        category: 'goal',
        rarity: 'epic',
        xpReward: 1500,
        requirements: [{ type: 'goal_completed', value: 10 }],
        unlockConditions: [],
        isSecret: false,
        isSwissSpecific: false,
        sortOrder: 23,
      },

      // Swiss-Specific Achievements
      {
        id: 'pillar3a_first',
        name: 'Swiss Saver',
        description: 'Make your first Pillar 3a contribution',
        icon: '🏛️',
        category: 'swiss',
        rarity: 'common',
        xpReward: 100,
        requirements: [{ type: 'swiss_action', value: 1, metadata: { type: 'pillar3a' } }],
        unlockConditions: [],
        isSecret: false,
        isSwissSpecific: true,
        sortOrder: 30,
      },
      {
        id: 'pillar3a_max',
        name: 'Tax Optimizer',
        description: 'Maximize your annual Pillar 3a contribution',
        icon: '💰',
        category: 'swiss',
        rarity: 'rare',
        xpReward: 300,
        requirements: [{ type: 'savings_amount', value: 7056, metadata: { pillar3a: true, annual: true } }],
        unlockConditions: [],
        isSecret: false,
        isSwissSpecific: true,
        sortOrder: 31,
      },
      {
        id: 'canton_explorer',
        name: 'Canton Explorer',
        description: 'Compare 5 different cantons',
        icon: '🗺️',
        category: 'swiss',
        rarity: 'common',
        xpReward: 150,
        requirements: [{ type: 'feature_used', value: 5, metadata: { feature: 'canton_comparison' } }],
        unlockConditions: [],
        isSecret: false,
        isSwissSpecific: true,
        sortOrder: 32,
      },
      {
        id: 'healthcare_optimizer',
        name: 'Healthcare Optimizer',
        description: 'Optimize your healthcare deductible',
        icon: '🏥',
        category: 'swiss',
        rarity: 'rare',
        xpReward: 200,
        requirements: [{ type: 'swiss_action', value: 1, metadata: { type: 'healthcare' } }],
        unlockConditions: [],
        isSecret: false,
        isSwissSpecific: true,
        sortOrder: 33,
      },
      {
        id: 'swiss_expert',
        name: 'Swiss Financial Expert',
        description: 'Use all Swiss-specific features',
        icon: '🇨🇭',
        category: 'swiss',
        rarity: 'epic',
        xpReward: 1000,
        requirements: [
          { type: 'swiss_action', value: 1, metadata: { type: 'pillar3a' } },
          { type: 'swiss_action', value: 1, metadata: { type: 'tax_optimization' } },
          { type: 'swiss_action', value: 1, metadata: { type: 'healthcare' } },
          { type: 'feature_used', value: 3, metadata: { feature: 'canton_comparison' } },
        ],
        unlockConditions: [],
        isSecret: false,
        isSwissSpecific: true,
        sortOrder: 34,
      },

      // Learning Achievements
      {
        id: 'student',
        name: 'Student',
        description: 'Complete your first financial education module',
        icon: '📚',
        category: 'learning',
        rarity: 'common',
        xpReward: 75,
        requirements: [{ type: 'feature_used', value: 1, metadata: { feature: 'education' } }],
        unlockConditions: [],
        isSecret: false,
        isSwissSpecific: false,
        sortOrder: 40,
      },
      {
        id: 'scholar',
        name: 'Scholar',
        description: 'Complete 10 financial education modules',
        icon: '🎓',
        category: 'learning',
        rarity: 'rare',
        xpReward: 400,
        requirements: [{ type: 'feature_used', value: 10, metadata: { feature: 'education' } }],
        unlockConditions: [],
        isSecret: false,
        isSwissSpecific: false,
        sortOrder: 41,
      },
      {
        id: 'swiss_scholar',
        name: 'Swiss Financial Scholar',
        description: 'Complete all Swiss-specific education modules',
        icon: '🏛️',
        category: 'learning',
        rarity: 'epic',
        xpReward: 800,
        requirements: [{ type: 'feature_used', value: 5, metadata: { feature: 'swiss_education' } }],
        unlockConditions: [],
        isSecret: false,
        isSwissSpecific: true,
        sortOrder: 42,
      },

      // Social Achievements
      {
        id: 'social_first',
        name: 'Community Member',
        description: 'Share your first achievement',
        icon: '👥',
        category: 'social',
        rarity: 'common',
        xpReward: 50,
        requirements: [{ type: 'feature_used', value: 1, metadata: { feature: 'social_sharing' } }],
        unlockConditions: [{ type: 'level', value: 5 }],
        isSecret: false,
        isSwissSpecific: false,
        sortOrder: 50,
      },
      {
        id: 'leaderboard_top10',
        name: 'Top Performer',
        description: 'Reach top 10 on the leaderboard',
        icon: '🏆',
        category: 'social',
        rarity: 'rare',
        xpReward: 300,
        requirements: [{ type: 'feature_used', value: 1, metadata: { feature: 'leaderboard_top10' } }],
        unlockConditions: [{ type: 'level', value: 25 }],
        isSecret: false,
        isSwissSpecific: false,
        sortOrder: 51,
      },

      // Discovery Achievements
      {
        id: 'explorer',
        name: 'Explorer',
        description: 'Discover 5 different features',
        icon: '🔍',
        category: 'discovery',
        rarity: 'common',
        xpReward: 100,
        requirements: [{ type: 'feature_used', value: 5, metadata: { feature: 'any' } }],
        unlockConditions: [],
        isSecret: false,
        isSwissSpecific: false,
        sortOrder: 60,
      },
      {
        id: 'power_user',
        name: 'Power User',
        description: 'Use advanced features regularly',
        icon: '⚡',
        category: 'discovery',
        rarity: 'rare',
        xpReward: 250,
        requirements: [{ type: 'feature_used', value: 20, metadata: { feature: 'advanced' } }],
        unlockConditions: [{ type: 'level', value: 20 }],
        isSecret: false,
        isSwissSpecific: false,
        sortOrder: 61,
      },

      // Secret Achievements
      {
        id: 'night_owl',
        name: 'Night Owl',
        description: 'Make a contribution after midnight',
        icon: '🦉',
        category: 'discovery',
        rarity: 'rare',
        xpReward: 150,
        requirements: [{ type: 'feature_used', value: 1, metadata: { feature: 'night_contribution' } }],
        unlockConditions: [],
        isSecret: true,
        isSwissSpecific: false,
        sortOrder: 70,
      },
      {
        id: 'early_bird',
        name: 'Early Bird',
        description: 'Make a contribution before 6 AM',
        icon: '🐦',
        category: 'discovery',
        rarity: 'rare',
        xpReward: 150,
        requirements: [{ type: 'feature_used', value: 1, metadata: { feature: 'early_contribution' } }],
        unlockConditions: [],
        isSecret: true,
        isSwissSpecific: false,
        sortOrder: 71,
      },
      {
        id: 'perfectionist',
        name: 'Perfectionist',
        description: 'Complete a goal exactly on the target date',
        icon: '🎯',
        category: 'goal',
        rarity: 'epic',
        xpReward: 500,
        requirements: [{ type: 'goal_completed', value: 1, metadata: { perfect_timing: true } }],
        unlockConditions: [],
        isSecret: true,
        isSwissSpecific: false,
        sortOrder: 72,
      },
    ];
  }

  /**
   * Get all achievements
   */
  getAllAchievements(): Achievement[] {
    return Array.from(this.achievements.values()).sort((a, b) => a.sortOrder - b.sortOrder);
  }

  /**
   * Get achievements by category
   */
  getAchievementsByCategory(category: AchievementCategory): Achievement[] {
    return this.getAllAchievements().filter(achievement => achievement.category === category);
  }

  /**
   * Get achievements by rarity
   */
  getAchievementsByRarity(rarity: AchievementRarity): Achievement[] {
    return this.getAllAchievements().filter(achievement => achievement.rarity === rarity);
  }

  /**
   * Get Swiss-specific achievements
   */
  getSwissAchievements(): Achievement[] {
    return this.getAllAchievements().filter(achievement => achievement.isSwissSpecific);
  }

  /**
   * Get user's achievements
   */
  getUserAchievements(userId: string): UserAchievement[] {
    return this.userAchievements.get(userId) || [];
  }

  /**
   * Get user's unlocked achievements
   */
  getUserUnlockedAchievements(userId: string): UserAchievement[] {
    return this.getUserAchievements(userId).filter(ua => ua.isCompleted);
  }

  /**
   * Get user's achievement progress
   */
  getUserAchievementProgress(userId: string, achievementId: string): UserAchievement | null {
    const userAchievements = this.getUserAchievements(userId);
    return userAchievements.find(ua => ua.achievementId === achievementId) || null;
  }

  /**
   * Check if user has unlocked an achievement
   */
  hasUserUnlockedAchievement(userId: string, achievementId: string): boolean {
    const progress = this.getUserAchievementProgress(userId, achievementId);
    return progress?.isCompleted || false;
  }

  /**
   * Initialize user achievements
   */
  initializeUserAchievements(userId: string): void {
    if (this.userAchievements.has(userId)) return;

    const userAchievements: UserAchievement[] = this.getAllAchievements().map(achievement => ({
      id: `${userId}_${achievement.id}`,
      userId,
      achievementId: achievement.id,
      unlockedAt: new Date(),
      progress: 0,
      isCompleted: false,
      notificationSent: false,
    }));

    this.userAchievements.set(userId, userAchievements);
  }

  /**
   * Update achievement progress
   */
  updateAchievementProgress(
    userId: string,
    achievementId: string,
    progress: number
  ): UserAchievement | null {
    const userAchievements = this.getUserAchievements(userId);
    const achievementProgress = userAchievements.find(ua => ua.achievementId === achievementId);
    
    if (!achievementProgress) return null;

    achievementProgress.progress = Math.min(100, Math.max(0, progress));
    
    if (achievementProgress.progress >= 100 && !achievementProgress.isCompleted) {
      achievementProgress.isCompleted = true;
      achievementProgress.unlockedAt = new Date();
    }

    return achievementProgress;
  }

  /**
   * Check and unlock achievements based on user activity
   */
  checkAndUnlockAchievements(
    userId: string,
    activityType: string,
    activityData: any,
    userProgress: UserProgress,
    userGoals: SavingsGoal[]
  ): UserAchievement[] {
    this.initializeUserAchievements(userId);
    const newlyUnlocked: UserAchievement[] = [];

    this.getAllAchievements().forEach(achievement => {
      if (this.hasUserUnlockedAchievement(userId, achievement.id)) return;

      if (this.checkAchievementRequirements(achievement, activityType, activityData, userProgress, userGoals)) {
        const unlockedAchievement = this.unlockAchievement(userId, achievement.id);
        if (unlockedAchievement) {
          newlyUnlocked.push(unlockedAchievement);
        }
      }
    });

    return newlyUnlocked;
  }

  /**
   * Check if achievement requirements are met
   */
  private checkAchievementRequirements(
    achievement: Achievement,
    activityType: string,
    activityData: any,
    userProgress: UserProgress,
    userGoals: SavingsGoal[]
  ): boolean {
    // Check unlock conditions first
    for (const condition of achievement.unlockConditions) {
      if (!this.checkUnlockCondition(condition, userProgress)) {
        return false;
      }
    }

    // Check all requirements
    return achievement.requirements.every(requirement => 
      this.checkRequirement(requirement, activityType, activityData, userProgress, userGoals)
    );
  }

  /**
   * Check unlock condition
   */
  private checkUnlockCondition(condition: UnlockCondition, userProgress: UserProgress): boolean {
    switch (condition.type) {
      case 'level':
        return userProgress.currentLevel >= (condition.value as number);
      case 'achievement':
        return this.hasUserUnlockedAchievement(userProgress.userId, condition.value as string);
      default:
        return true;
    }
  }

  /**
   * Check individual requirement
   */
  private checkRequirement(
    requirement: AchievementRequirement,
    activityType: string,
    activityData: any,
    userProgress: UserProgress,
    userGoals: SavingsGoal[]
  ): boolean {
    switch (requirement.type) {
      case 'savings_amount':
        return this.checkSavingsAmount(requirement, activityData, userGoals);
      case 'goal_completed':
        return userProgress.completedGoals >= requirement.value;
      case 'streak_days':
        return userProgress.streakDays >= requirement.value;
      case 'xp_total':
        return userProgress.totalXP >= requirement.value;
      case 'feature_used':
        return this.checkFeatureUsage(requirement, activityType, activityData);
      case 'swiss_action':
        return this.checkSwissAction(requirement, activityType, activityData);
      default:
        return false;
    }
  }

  /**
   * Check savings amount requirement
   */
  private checkSavingsAmount(
    requirement: AchievementRequirement,
    activityData: any,
    userGoals: SavingsGoal[]
  ): boolean {
    if (requirement.metadata?.pillar3a) {
      const pillar3aGoals = userGoals.filter(g => g.category === 'pillar3a');
      const totalPillar3a = pillar3aGoals.reduce((sum, g) => sum + g.currentAmount, 0);
      return totalPillar3a >= requirement.value;
    }

    const totalSavings = userGoals.reduce((sum, g) => sum + g.currentAmount, 0);
    return totalSavings >= requirement.value;
  }

  /**
   * Check feature usage requirement
   */
  private checkFeatureUsage(
    requirement: AchievementRequirement,
    activityType: string,
    activityData: any
  ): boolean {
    if (!requirement.metadata?.feature) return false;
    
    const requiredFeature = requirement.metadata.feature;
    return activityType === requiredFeature || activityData?.feature === requiredFeature;
  }

  /**
   * Check Swiss action requirement
   */
  private checkSwissAction(
    requirement: AchievementRequirement,
    activityType: string,
    activityData: any
  ): boolean {
    if (!requirement.metadata?.type) return false;
    
    const requiredType = requirement.metadata.type;
    return activityType === 'swiss_action' && activityData?.type === requiredType;
  }

  /**
   * Unlock achievement for user
   */
  private unlockAchievement(userId: string, achievementId: string): UserAchievement | null {
    const userAchievements = this.getUserAchievements(userId);
    const achievementProgress = userAchievements.find(ua => ua.achievementId === achievementId);
    
    if (!achievementProgress || achievementProgress.isCompleted) {
      return null;
    }

    achievementProgress.isCompleted = true;
    achievementProgress.progress = 100;
    achievementProgress.unlockedAt = new Date();
    achievementProgress.notificationSent = false;

    return achievementProgress;
  }

  /**
   * Get achievement statistics for user
   */
  getAchievementStatistics(userId: string): {
    totalAchievements: number;
    unlockedAchievements: number;
    completionPercentage: number;
    rarityBreakdown: Record<AchievementRarity, number>;
    categoryBreakdown: Record<AchievementCategory, number>;
    swissAchievements: number;
  } {
    const allAchievements = this.getAllAchievements();
    const userUnlocked = this.getUserUnlockedAchievements(userId);

    const rarityBreakdown: Record<AchievementRarity, number> = {
      common: 0,
      rare: 0,
      epic: 0,
      legendary: 0,
    };

    const categoryBreakdown: Record<AchievementCategory, number> = {
      saver: 0,
      streak: 0,
      goal: 0,
      swiss: 0,
      efficiency: 0,
      learning: 0,
      social: 0,
      discovery: 0,
    };

    userUnlocked.forEach(ua => {
      const achievement = this.achievements.get(ua.achievementId);
      if (achievement) {
        rarityBreakdown[achievement.rarity]++;
        categoryBreakdown[achievement.category]++;
      }
    });

    const swissAchievements = userUnlocked.filter(ua => {
      const achievement = this.achievements.get(ua.achievementId);
      return achievement?.isSwissSpecific;
    }).length;

    return {
      totalAchievements: allAchievements.length,
      unlockedAchievements: userUnlocked.length,
      completionPercentage: (userUnlocked.length / allAchievements.length) * 100,
      rarityBreakdown,
      categoryBreakdown,
      swissAchievements,
    };
  }

  /**
   * Get next achievements to unlock
   */
  getNextAchievements(userId: string, limit: number = 5): Achievement[] {
    const userAchievements = this.getUserAchievements(userId);
    const unlockedIds = new Set(userAchievements.filter(ua => ua.isCompleted).map(ua => ua.achievementId));
    
    return this.getAllAchievements()
      .filter(achievement => !unlockedIds.has(achievement.id) && !achievement.isSecret)
      .slice(0, limit);
  }
}

export default AchievementService;
