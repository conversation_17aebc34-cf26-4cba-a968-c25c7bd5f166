import {
  UserProgress,
  XPT<PERSON>saction,
  XPSource,
  LevelUpResult,
  XPCalculationResult,
  StreakData,
  GamificationError,
} from '../types/gamification';
import { XPCalculationEngine } from './XPCalculationEngine';
import { LevelProgressionSystem } from './LevelProgressionSystem';

export class UserProgressService {
  private xpEngine: XPCalculationEngine;
  private levelSystem: LevelProgressionSystem;
  private progressCache: Map<string, UserProgress>;

  constructor() {
    this.xpEngine = new XPCalculationEngine();
    this.levelSystem = new LevelProgressionSystem();
    this.progressCache = new Map();
  }

  /**
   * Initialize user progress for a new user
   */
  async initializeUserProgress(userId: string): Promise<UserProgress> {
    const initialProgress: UserProgress = {
      userId,
      totalXP: 0,
      currentLevel: 1,
      currentLevelXP: 0,
      nextLevelXP: 1000, // First level requires 1000 XP
      progressToNextLevel: 0,
      lastXPGain: new Date(),
      streakDays: 0,
      longestStreak: 0,
      totalAchievements: 0,
      completedGoals: 0,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    this.progressCache.set(userId, initialProgress);
    return initialProgress;
  }

  /**
   * Get user progress with caching
   */
  async getUserProgress(userId: string): Promise<UserProgress> {
    // Check cache first
    const cached = this.progressCache.get(userId);
    if (cached) {
      return cached;
    }

    // In a real implementation, this would fetch from database
    // For now, initialize new progress
    return this.initializeUserProgress(userId);
  }

  /**
   * Award XP for savings contribution
   */
  async awardSavingsXP(
    userId: string,
    amount: number,
    isPillar3a: boolean = false,
    metadata?: Record<string, any>
  ): Promise<{ progress: UserProgress; levelUp?: LevelUpResult; xpGained: number }> {
    const calculation = this.xpEngine.calculateSavingsXP(amount, isPillar3a);
    return this.processXPAward(userId, calculation, metadata);
  }

  /**
   * Award XP for goal achievement
   */
  async awardGoalAchievementXP(
    userId: string,
    goalAmount: number,
    targetDate: Date,
    completionDate: Date = new Date(),
    isSwissOptimized: boolean = false,
    metadata?: Record<string, any>
  ): Promise<{ progress: UserProgress; levelUp?: LevelUpResult; xpGained: number }> {
    const timeBonus = this.xpEngine.calculateTimeBonus(targetDate, completionDate);
    const calculation = this.xpEngine.calculateGoalAchievementXP(goalAmount, timeBonus, isSwissOptimized);
    return this.processXPAward(userId, calculation, metadata);
  }

  /**
   * Award XP for streak maintenance
   */
  async awardStreakXP(
    userId: string,
    streakDays: number,
    streakType: string = 'general',
    metadata?: Record<string, any>
  ): Promise<{ progress: UserProgress; levelUp?: LevelUpResult; xpGained: number }> {
    const calculation = this.xpEngine.calculateStreakXP(streakDays, streakType);
    return this.processXPAward(userId, calculation, metadata);
  }

  /**
   * Award XP for Swiss optimization actions
   */
  async awardSwissOptimizationXP(
    userId: string,
    optimizationType: 'tax' | 'healthcare' | 'canton_comparison' | 'pillar3a',
    amount?: number,
    metadata?: Record<string, any>
  ): Promise<{ progress: UserProgress; levelUp?: LevelUpResult; xpGained: number }> {
    const calculation = this.xpEngine.calculateSwissOptimizationXP(optimizationType, amount, metadata);
    return this.processXPAward(userId, calculation, metadata);
  }

  /**
   * Award XP for budget adherence
   */
  async awardBudgetAdherenceXP(
    userId: string,
    adherencePercentage: number,
    categoryCount: number = 1,
    metadata?: Record<string, any>
  ): Promise<{ progress: UserProgress; levelUp?: LevelUpResult; xpGained: number }> {
    const calculation = this.xpEngine.calculateBudgetAdherenceXP(adherencePercentage, categoryCount);
    return this.processXPAward(userId, calculation, metadata);
  }

  /**
   * Award XP for financial education
   */
  async awardEducationXP(
    userId: string,
    moduleType: 'basic' | 'intermediate' | 'advanced' | 'swiss_specific',
    completionScore?: number,
    metadata?: Record<string, any>
  ): Promise<{ progress: UserProgress; levelUp?: LevelUpResult; xpGained: number }> {
    const calculation = this.xpEngine.calculateEducationXP(moduleType, completionScore);
    return this.processXPAward(userId, calculation, metadata);
  }

  /**
   * Process XP award with streak bonuses and level progression
   */
  private async processXPAward(
    userId: string,
    calculation: XPCalculationResult,
    metadata?: Record<string, any>
  ): Promise<{ progress: UserProgress; levelUp?: LevelUpResult; xpGained: number }> {
    const currentProgress = await this.getUserProgress(userId);
    
    // Apply streak bonuses if applicable
    const userStreak = await this.getUserStreak(userId);
    const finalCalculation = this.xpEngine.calculateXPWithStreakBonus(calculation, userStreak);

    // Create XP transaction
    const transaction = this.xpEngine.createXPTransaction(userId, finalCalculation, metadata);

    // Process level progression
    const { updatedProgress, levelUpResult } = this.levelSystem.processXPGain(currentProgress, transaction);

    // Update cache
    this.progressCache.set(userId, updatedProgress);

    // In a real implementation, save to database here
    await this.saveProgressToDatabase(updatedProgress, transaction);

    return {
      progress: updatedProgress,
      levelUp: levelUpResult,
      xpGained: finalCalculation.totalXP,
    };
  }

  /**
   * Get user's current streak data
   */
  private async getUserStreak(userId: string): Promise<StreakData> {
    // In a real implementation, this would fetch from database
    // For now, return a default streak
    return {
      userId,
      type: 'general',
      currentStreak: 0,
      longestStreak: 0,
      lastActivityDate: new Date(),
      streakStartDate: new Date(),
      multiplier: 1,
      isActive: false,
      protectionUsed: 0,
      maxProtections: 3,
    };
  }

  /**
   * Update user achievement count
   */
  async updateAchievementCount(userId: string, increment: number = 1): Promise<UserProgress> {
    const progress = await this.getUserProgress(userId);
    progress.totalAchievements += increment;
    progress.updatedAt = new Date();
    
    this.progressCache.set(userId, progress);
    await this.saveProgressToDatabase(progress);
    
    return progress;
  }

  /**
   * Update completed goals count
   */
  async updateCompletedGoalsCount(userId: string, increment: number = 1): Promise<UserProgress> {
    const progress = await this.getUserProgress(userId);
    progress.completedGoals += increment;
    progress.updatedAt = new Date();
    
    this.progressCache.set(userId, progress);
    await this.saveProgressToDatabase(progress);
    
    return progress;
  }

  /**
   * Get user's level information
   */
  async getUserLevelInfo(userId: string): Promise<{
    currentLevel: number;
    title: string;
    benefits: string[];
    unlocks: string[];
    xpToNextLevel: number;
    progressPercentage: number;
  }> {
    const progress = await this.getUserProgress(userId);
    const levelData = this.levelSystem.getLevelData(progress.currentLevel);
    const xpToNextLevel = this.levelSystem.getXPToNextLevel(progress.totalXP);

    return {
      currentLevel: progress.currentLevel,
      title: levelData.title,
      benefits: levelData.benefits,
      unlocks: levelData.unlocks,
      xpToNextLevel,
      progressPercentage: progress.progressToNextLevel,
    };
  }

  /**
   * Check if user has access to a feature
   */
  async hasFeatureAccess(userId: string, feature: string): Promise<boolean> {
    const progress = await this.getUserProgress(userId);
    return this.levelSystem.hasFeatureAccess(progress.currentLevel, feature);
  }

  /**
   * Get user's available features
   */
  async getAvailableFeatures(userId: string): Promise<string[]> {
    const progress = await this.getUserProgress(userId);
    return this.levelSystem.getAvailableFeatures(progress.currentLevel);
  }

  /**
   * Get XP history for a user
   */
  async getXPHistory(
    userId: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<XPTransaction[]> {
    // In a real implementation, this would fetch from database
    // For now, return empty array
    return [];
  }

  /**
   * Get user's rank among all users
   */
  async getUserRank(userId: string): Promise<{
    rank: number;
    totalUsers: number;
    percentile: number;
  }> {
    // In a real implementation, this would query database for rankings
    // For now, return placeholder data
    return {
      rank: 1,
      totalUsers: 1,
      percentile: 100,
    };
  }

  /**
   * Bulk award XP for multiple actions
   */
  async bulkAwardXP(
    userId: string,
    calculations: XPCalculationResult[],
    metadata?: Record<string, any>
  ): Promise<{ progress: UserProgress; levelUp?: LevelUpResult; totalXPGained: number }> {
    const compoundCalculation = this.xpEngine.calculateCompoundXP(calculations);
    return this.processXPAward(userId, compoundCalculation, metadata);
  }

  /**
   * Reset user progress (for testing or admin purposes)
   */
  async resetUserProgress(userId: string): Promise<UserProgress> {
    this.progressCache.delete(userId);
    return this.initializeUserProgress(userId);
  }

  /**
   * Get progress statistics
   */
  async getProgressStatistics(userId: string): Promise<{
    totalXP: number;
    currentLevel: number;
    achievementsUnlocked: number;
    goalsCompleted: number;
    streakDays: number;
    xpThisWeek: number;
    xpThisMonth: number;
    levelUpDate?: Date;
  }> {
    const progress = await this.getUserProgress(userId);
    
    // In a real implementation, calculate XP for periods from database
    const xpThisWeek = 0; // Placeholder
    const xpThisMonth = 0; // Placeholder

    return {
      totalXP: progress.totalXP,
      currentLevel: progress.currentLevel,
      achievementsUnlocked: progress.totalAchievements,
      goalsCompleted: progress.completedGoals,
      streakDays: progress.streakDays,
      xpThisWeek,
      xpThisMonth,
      levelUpDate: progress.lastXPGain,
    };
  }

  /**
   * Save progress to database (placeholder)
   */
  private async saveProgressToDatabase(
    progress: UserProgress,
    transaction?: XPTransaction
  ): Promise<void> {
    // In a real implementation, this would save to database
    console.log(`Saving progress for user ${progress.userId}:`, {
      totalXP: progress.totalXP,
      level: progress.currentLevel,
      transaction: transaction ? {
        source: transaction.source,
        amount: transaction.amount,
        description: transaction.description,
      } : null,
    });
  }

  /**
   * Clear progress cache
   */
  clearCache(): void {
    this.progressCache.clear();
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { size: number; users: string[] } {
    return {
      size: this.progressCache.size,
      users: Array.from(this.progressCache.keys()),
    };
  }
}

export default UserProgressService;
