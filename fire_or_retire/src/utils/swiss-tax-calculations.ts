/**
 * Swiss Tax Calculation Utilities
 *
 * This module contains all Swiss-specific tax calculation functions including:
 * - Federal tax calculations
 * - Cantonal tax calculations
 * - Municipal tax calculations
 * - Wealth tax calculations
 * - Tax optimization strategies
 */

import Decimal from 'decimal.js';

// Configure Decimal.js for tax precision
Decimal.config({
  precision: 28,
  rounding: Decimal.ROUND_HALF_UP,
  toExpNeg: -7,
  toExpPos: 21,
});

// ============================================================================
// TYPES AND INTERFACES
// ============================================================================

export interface TaxBracket {
  min: number;
  max: number;
  rate: number;
  base: number;
}

export interface CantonTaxConfig {
  code: string;
  name: string;
  baseTax: TaxBracket[];
  multiplier: number;
  wealthTaxRate: number;
  wealthTaxExemption: {
    single: number;
    married: number;
  };
}

export interface TaxResult {
  federal: number;
  cantonal: number;
  municipal: number;
  total: number;
  effectiveRate: number;
}

export interface WealthTaxResult {
  cantonal: number;
  municipal: number;
  total: number;
}

export type CivilStatus = 'single' | 'married' | 'divorced' | 'widowed';
export type CantonCode =
  | 'AG'
  | 'AI'
  | 'AR'
  | 'BE'
  | 'BL'
  | 'BS'
  | 'FR'
  | 'GE'
  | 'GL'
  | 'GR'
  | 'JU'
  | 'LU'
  | 'NE'
  | 'NW'
  | 'OW'
  | 'SG'
  | 'SH'
  | 'SO'
  | 'SZ'
  | 'TG'
  | 'TI'
  | 'UR'
  | 'VD'
  | 'VS'
  | 'ZG'
  | 'ZH';

// ============================================================================
// FEDERAL TAX BRACKETS (2024)
// ============================================================================

const FEDERAL_TAX_BRACKETS: TaxBracket[] = [
  { min: 0, max: 14500, rate: 0, base: 0 },
  { min: 14500, max: 31600, rate: 0.77, base: 0 },
  { min: 31600, max: 41400, rate: 0.88, base: 131.65 },
  { min: 41400, max: 55200, rate: 2.64, base: 217.85 },
  { min: 55200, max: 72500, rate: 2.97, base: 582.05 },
  { min: 72500, max: 78100, rate: 5.94, base: 1095.86 },
  { min: 78100, max: 103600, rate: 6.6, base: 1428.5 },
  { min: 103600, max: 134600, rate: 8.8, base: 3111.5 },
  { min: 134600, max: 176000, rate: 11.0, base: 5839.5 },
  { min: 176000, max: 755200, rate: 13.2, base: 10393.5 },
  { min: 755200, max: Infinity, rate: 11.5, base: 86837.9 },
];

// ============================================================================
// CANTONAL TAX CONFIGURATIONS
// ============================================================================

const CANTONAL_TAX_CONFIGS: Record<CantonCode, CantonTaxConfig> = {
  ZH: {
    code: 'ZH',
    name: 'Zürich',
    baseTax: [
      { min: 0, max: 6700, rate: 0, base: 0 },
      { min: 6700, max: 11600, rate: 2.0, base: 0 },
      { min: 11600, max: 15600, rate: 3.0, base: 98 },
      { min: 15600, max: 20200, rate: 4.0, base: 218 },
      { min: 20200, max: 24800, rate: 5.0, base: 402 },
      { min: 24800, max: 29400, rate: 6.0, base: 632 },
      { min: 29400, max: 34600, rate: 7.0, base: 908 },
      { min: 34600, max: 40400, rate: 8.0, base: 1272 },
      { min: 40400, max: 46800, rate: 9.0, base: 1736 },
      { min: 46800, max: 54400, rate: 10.0, base: 2312 },
      { min: 54400, max: 63600, rate: 11.0, base: 3072 },
      { min: 63600, max: 74800, rate: 12.0, base: 4084 },
      { min: 74800, max: 88400, rate: 13.0, base: 5428 },
      { min: 88400, max: Infinity, rate: 13.0, base: 7196 },
    ],
    multiplier: 1.0,
    wealthTaxRate: 0.5,
    wealthTaxExemption: { single: 200000, married: 400000 },
  },
  GE: {
    code: 'GE',
    name: 'Genève',
    baseTax: [
      { min: 0, max: 9615, rate: 0, base: 0 },
      { min: 9615, max: 13665, rate: 0.68, base: 0 },
      { min: 13665, max: 19230, rate: 3.0, base: 27.54 },
      { min: 19230, max: 24038, rate: 4.5, base: 194.49 },
      { min: 24038, max: 30769, rate: 5.94, base: 410.85 },
      { min: 30769, max: 38461, rate: 7.83, base: 810.27 },
      { min: 38461, max: 48076, rate: 9.45, base: 1412.58 },
      { min: 48076, max: 60096, rate: 10.8, base: 2321.16 },
      { min: 60096, max: 76923, rate: 12.15, base: 3619.32 },
      { min: 76923, max: 96153, rate: 13.23, base: 5663.73 },
      { min: 96153, max: Infinity, rate: 17.76, base: 8208.36 },
    ],
    multiplier: 1.0,
    wealthTaxRate: 1.0,
    wealthTaxExemption: { single: 0, married: 0 },
  },
  VD: {
    code: 'VD',
    name: 'Vaud',
    baseTax: [
      { min: 0, max: 17800, rate: 0, base: 0 },
      { min: 17800, max: 22700, rate: 1.0, base: 0 },
      { min: 22700, max: 27600, rate: 2.0, base: 49 },
      { min: 27600, max: 32500, rate: 3.0, base: 147 },
      { min: 32500, max: 37400, rate: 4.0, base: 294 },
      { min: 37400, max: 42300, rate: 5.0, base: 490 },
      { min: 42300, max: 47200, rate: 6.0, base: 735 },
      { min: 47200, max: 52100, rate: 7.0, base: 1029 },
      { min: 52100, max: 57000, rate: 8.0, base: 1372 },
      { min: 57000, max: 61900, rate: 9.0, base: 1764 },
      { min: 61900, max: 66800, rate: 10.0, base: 2205 },
      { min: 66800, max: 71700, rate: 11.0, base: 2695 },
      { min: 71700, max: 76600, rate: 12.0, base: 3234 },
      { min: 76600, max: Infinity, rate: 12.8, base: 3822 },
    ],
    multiplier: 1.0,
    wealthTaxRate: 0.3,
    wealthTaxExemption: { single: 75000, married: 150000 },
  },
  BE: {
    code: 'BE',
    name: 'Bern',
    baseTax: [
      { min: 0, max: 15000, rate: 0, base: 0 },
      { min: 15000, max: 20000, rate: 0.22, base: 0 },
      { min: 20000, max: 25000, rate: 1.1, base: 11 },
      { min: 25000, max: 30000, rate: 2.2, base: 66 },
      { min: 30000, max: 35000, rate: 3.3, base: 176 },
      { min: 35000, max: 40000, rate: 4.4, base: 341 },
      { min: 40000, max: 45000, rate: 5.5, base: 561 },
      { min: 45000, max: 50000, rate: 6.6, base: 836 },
      { min: 50000, max: 55000, rate: 7.7, base: 1166 },
      { min: 55000, max: 60000, rate: 8.8, base: 1551 },
      { min: 60000, max: 65000, rate: 9.9, base: 1991 },
      { min: 65000, max: 70000, rate: 11.0, base: 2486 },
      { min: 70000, max: Infinity, rate: 12.1, base: 3036 },
    ],
    multiplier: 1.0,
    wealthTaxRate: 0.4,
    wealthTaxExemption: { single: 100000, married: 200000 },
  },
  ZG: {
    code: 'ZG',
    name: 'Zug',
    baseTax: [
      { min: 0, max: 14000, rate: 0, base: 0 },
      { min: 14000, max: 20000, rate: 0.5, base: 0 },
      { min: 20000, max: 26000, rate: 1.0, base: 30 },
      { min: 26000, max: 32000, rate: 1.5, base: 90 },
      { min: 32000, max: 38000, rate: 2.0, base: 180 },
      { min: 38000, max: 44000, rate: 2.5, base: 300 },
      { min: 44000, max: 50000, rate: 3.0, base: 450 },
      { min: 50000, max: 56000, rate: 3.5, base: 630 },
      { min: 56000, max: 62000, rate: 4.0, base: 840 },
      { min: 62000, max: 68000, rate: 4.5, base: 1080 },
      { min: 68000, max: 74000, rate: 5.0, base: 1350 },
      { min: 74000, max: 80000, rate: 5.5, base: 1650 },
      { min: 80000, max: Infinity, rate: 6.0, base: 1980 },
    ],
    multiplier: 0.8, // Zug has lower cantonal multiplier
    wealthTaxRate: 0.2,
    wealthTaxExemption: { single: 200000, married: 400000 },
  },
  BS: {
    code: 'BS',
    name: 'Basel-Stadt',
    baseTax: [
      { min: 0, max: 19600, rate: 0, base: 0 },
      { min: 19600, max: 31300, rate: 0.86, base: 0 },
      { min: 31300, max: 41100, rate: 2.97, base: 100.62 },
      { min: 41100, max: 55200, rate: 5.94, base: 391.74 },
      { min: 55200, max: 72500, rate: 8.91, base: 1229.28 },
      { min: 72500, max: 78100, rate: 11.88, base: 2771.73 },
      { min: 78100, max: 103600, rate: 13.86, base: 3437.01 },
      { min: 103600, max: 134600, rate: 15.84, base: 6974.01 },
      { min: 134600, max: 176000, rate: 17.82, base: 11884.41 },
      { min: 176000, max: Infinity, rate: 19.8, base: 19263.81 },
    ],
    multiplier: 1.0,
    wealthTaxRate: 0.5,
    wealthTaxExemption: { single: 200000, married: 400000 },
  },
};

// Add simplified configs for remaining cantons
const REMAINING_CANTONS: CantonCode[] = [
  'AG',
  'AI',
  'AR',
  'BL',
  'FR',
  'GL',
  'GR',
  'JU',
  'LU',
  'NE',
  'NW',
  'OW',
  'SG',
  'SH',
  'SO',
  'SZ',
  'TG',
  'TI',
  'UR',
  'VS',
];

REMAINING_CANTONS.forEach(canton => {
  CANTONAL_TAX_CONFIGS[canton] = {
    code: canton,
    name: canton,
    baseTax: [
      { min: 0, max: 15000, rate: 0, base: 0 },
      { min: 15000, max: 30000, rate: 2.0, base: 0 },
      { min: 30000, max: 50000, rate: 4.0, base: 300 },
      { min: 50000, max: 75000, rate: 6.0, base: 1100 },
      { min: 75000, max: 100000, rate: 8.0, base: 2600 },
      { min: 100000, max: Infinity, rate: 10.0, base: 4600 },
    ],
    multiplier: 1.0,
    wealthTaxRate: 0.3,
    wealthTaxExemption: { single: 100000, married: 200000 },
  };
});

// ============================================================================
// CORE TAX CALCULATION FUNCTIONS
// ============================================================================

/**
 * Calculate progressive tax using tax brackets
 */
function calculateProgressiveTax(
  income: number,
  brackets: TaxBracket[]
): number {
  if (income <= 0) return 0;

  const incomeDecimal = new Decimal(income);
  let totalTax = new Decimal(0);

  for (const bracket of brackets) {
    if (incomeDecimal.lte(bracket.min)) break;

    const taxableInBracket = Decimal.min(
      incomeDecimal.minus(bracket.min),
      new Decimal(bracket.max - bracket.min)
    );

    const bracketTax = taxableInBracket
      .mul(bracket.rate / 100)
      .plus(bracket.base);
    totalTax = totalTax.plus(bracketTax);

    if (incomeDecimal.lte(bracket.max)) break;
  }

  return totalTax.toNumber();
}

/**
 * Calculate Swiss federal income tax
 */
export function calculateFederalTax(taxableIncome: number): number {
  return calculateProgressiveTax(taxableIncome, FEDERAL_TAX_BRACKETS);
}

/**
 * Calculate cantonal income tax
 */
export function calculateCantonalTax(
  taxableIncome: number,
  canton: CantonCode,
  municipalMultiplier: number = 1.0
): { cantonal: number; municipal: number; total: number } {
  const config = CANTONAL_TAX_CONFIGS[canton];
  if (!config) {
    throw new Error(`Invalid canton code: ${canton}`);
  }

  const baseTax = calculateProgressiveTax(taxableIncome, config.baseTax);
  const cantonal = baseTax * config.multiplier;
  const municipal = cantonal * municipalMultiplier;

  return {
    cantonal,
    municipal,
    total: cantonal + municipal,
  };
}

/**
 * Calculate complete Swiss income tax
 */
export function calculateSwissIncomeTax(
  grossIncome: number,
  canton: CantonCode,
  civilStatus: CivilStatus = 'single',
  municipalMultiplier: number = 1.0,
  deductions: number = 0
): TaxResult {
  // Calculate taxable income after deductions
  const taxableIncome = Math.max(0, grossIncome - deductions);

  // Apply civil status adjustments (simplified)
  const adjustedIncome =
    civilStatus === 'married' ? taxableIncome * 0.9 : taxableIncome;

  // Calculate taxes
  const federal = calculateFederalTax(adjustedIncome);
  const cantonalResult = calculateCantonalTax(
    adjustedIncome,
    canton,
    municipalMultiplier
  );

  const total = federal + cantonalResult.total;
  const effectiveRate = grossIncome > 0 ? total / grossIncome : 0;

  return {
    federal,
    cantonal: cantonalResult.cantonal,
    municipal: cantonalResult.municipal,
    total,
    effectiveRate,
  };
}

/**
 * Calculate Swiss wealth tax
 */
export function calculateWealthTax(
  netWorth: number,
  canton: CantonCode,
  civilStatus: CivilStatus = 'single',
  municipalMultiplier: number = 1.0
): WealthTaxResult {
  const config = CANTONAL_TAX_CONFIGS[canton];
  if (!config) {
    throw new Error(`Invalid canton code: ${canton}`);
  }

  const exemption =
    civilStatus === 'married'
      ? config.wealthTaxExemption.married
      : config.wealthTaxExemption.single;

  const taxableWealth = Math.max(0, netWorth - exemption);
  const cantonal = (taxableWealth * config.wealthTaxRate) / 1000; // Per mille
  const municipal = cantonal * municipalMultiplier;

  return {
    cantonal,
    municipal,
    total: cantonal + municipal,
  };
}

/**
 * Get all available cantons
 */
export function getAvailableCantons(): CantonCode[] {
  return Object.keys(CANTONAL_TAX_CONFIGS) as CantonCode[];
}

/**
 * Get canton configuration
 */
export function getCantonConfig(canton: CantonCode): CantonTaxConfig {
  const config = CANTONAL_TAX_CONFIGS[canton];
  if (!config) {
    throw new Error(`Invalid canton code: ${canton}`);
  }
  return config;
}

/**
 * Compare tax burden across cantons
 */
export function compareCantonTaxes(
  grossIncome: number,
  civilStatus: CivilStatus = 'single',
  netWorth: number = 0
): Array<{
  canton: CantonCode;
  name: string;
  incomeTax: number;
  wealthTax: number;
  totalTax: number;
  effectiveRate: number;
}> {
  const results = getAvailableCantons().map(canton => {
    const config = getCantonConfig(canton);
    const incomeTaxResult = calculateSwissIncomeTax(
      grossIncome,
      canton,
      civilStatus
    );
    const wealthTaxResult = calculateWealthTax(netWorth, canton, civilStatus);

    const totalTax = incomeTaxResult.total + wealthTaxResult.total;
    const effectiveRate = grossIncome > 0 ? totalTax / grossIncome : 0;

    return {
      canton,
      name: config.name,
      incomeTax: incomeTaxResult.total,
      wealthTax: wealthTaxResult.total,
      totalTax,
      effectiveRate,
    };
  });

  return results.sort((a, b) => a.totalTax - b.totalTax);
}

export default {
  calculateFederalTax,
  calculateCantonalTax,
  calculateSwissIncomeTax,
  calculateWealthTax,
  getAvailableCantons,
  getCantonConfig,
  compareCantonTaxes,
};
