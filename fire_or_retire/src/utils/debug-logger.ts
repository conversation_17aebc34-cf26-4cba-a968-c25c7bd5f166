/**
 * Enhanced Debug Logger for Swiss Budget Pro
 * Captures runtime errors, performance metrics, and debugging information
 */

export interface DebugLogEntry {
  id: string;
  timestamp: string;
  level: 'error' | 'warn' | 'info' | 'debug';
  category: string;
  message: string;
  data?: any;
  stack?: string;
  userAgent?: string;
  url?: string;
  sessionId?: string;
}

export interface PerformanceMetric {
  name: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  metadata?: Record<string, any>;
}

class DebugLogger {
  private logs: DebugLogEntry[] = [];
  private performanceMetrics: PerformanceMetric[] = [];
  private sessionId: string;
  private maxLogs: number = 1000;
  private isEnabled: boolean = true;

  constructor() {
    this.sessionId = this.generateSessionId();
    this.setupGlobalErrorHandlers();
    this.setupPerformanceMonitoring();
    this.log('info', 'system', 'Debug logger initialized', {
      sessionId: this.sessionId,
    });
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private setupGlobalErrorHandlers(): void {
    // Capture unhandled errors
    window.addEventListener('error', event => {
      this.log('error', 'runtime', 'Unhandled error', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        error: event.error?.stack,
      });
    });

    // Capture unhandled promise rejections
    window.addEventListener('unhandledrejection', event => {
      this.log('error', 'promise', 'Unhandled promise rejection', {
        reason: event.reason,
        stack: event.reason?.stack,
      });
    });

    // Capture console errors and warnings
    const originalConsoleError = console.error;
    const originalConsoleWarn = console.warn;

    console.error = (...args) => {
      this.log('error', 'console', 'Console error', { args });
      originalConsoleError.apply(console, args);
    };

    console.warn = (...args) => {
      this.log('warn', 'console', 'Console warning', { args });
      originalConsoleWarn.apply(console, args);
    };
  }

  private setupPerformanceMonitoring(): void {
    // Monitor navigation timing
    if (window.performance && window.performance.timing) {
      window.addEventListener('load', () => {
        const timing = window.performance.timing;
        const navigationStart = timing.navigationStart;

        this.log('info', 'performance', 'Page load metrics', {
          domContentLoaded: timing.domContentLoadedEventEnd - navigationStart,
          loadComplete: timing.loadEventEnd - navigationStart,
          domInteractive: timing.domInteractive - navigationStart,
          firstPaint: this.getFirstPaint(),
          firstContentfulPaint: this.getFirstContentfulPaint(),
        });
      });
    }

    // Monitor resource loading
    if (window.performance && window.performance.getEntriesByType) {
      setTimeout(() => {
        const resources = window.performance.getEntriesByType('resource');
        const slowResources = resources.filter(
          (resource: any) => resource.duration > 1000
        );

        if (slowResources.length > 0) {
          this.log('warn', 'performance', 'Slow resources detected', {
            count: slowResources.length,
            resources: slowResources.map((r: any) => ({
              name: r.name,
              duration: r.duration,
              size: r.transferSize,
            })),
          });
        }
      }, 5000);
    }
  }

  private getFirstPaint(): number | null {
    try {
      const paintEntries = window.performance.getEntriesByType('paint');
      const firstPaint = paintEntries.find(
        entry => entry.name === 'first-paint'
      );
      return firstPaint ? firstPaint.startTime : null;
    } catch {
      return null;
    }
  }

  private getFirstContentfulPaint(): number | null {
    try {
      const paintEntries = window.performance.getEntriesByType('paint');
      const fcp = paintEntries.find(
        entry => entry.name === 'first-contentful-paint'
      );
      return fcp ? fcp.startTime : null;
    } catch {
      return null;
    }
  }

  public log(
    level: 'error' | 'warn' | 'info' | 'debug',
    category: string,
    message: string,
    data?: any
  ): string {
    if (!this.isEnabled) return '';

    const id = `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const entry: DebugLogEntry = {
      id,
      timestamp: new Date().toISOString(),
      level,
      category,
      message,
      data,
      userAgent: navigator.userAgent,
      url: window.location.href,
      sessionId: this.sessionId,
    };

    // Add stack trace for errors
    if (level === 'error') {
      entry.stack = new Error().stack;
    }

    this.logs.push(entry);

    // Maintain log size limit
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs);
    }

    // Store in localStorage for persistence
    this.persistLogs();

    // Console output with styling
    this.outputToConsole(entry);

    return id;
  }

  private outputToConsole(entry: DebugLogEntry): void {
    const styles = {
      error: 'color: #ff6b6b; font-weight: bold;',
      warn: 'color: #ffa726; font-weight: bold;',
      info: 'color: #42a5f5;',
      debug: 'color: #66bb6a;',
    };

    const prefix = `[${entry.level.toUpperCase()}] [${entry.category}]`;
    const style = styles[entry.level];

    console.groupCollapsed(`%c${prefix} ${entry.message}`, style);
    console.log('Timestamp:', entry.timestamp);
    console.log('Session ID:', entry.sessionId);
    if (entry.data) {
      console.log('Data:', entry.data);
    }
    if (entry.stack) {
      console.log('Stack:', entry.stack);
    }
    console.groupEnd();
  }

  public startPerformanceMetric(
    name: string,
    metadata?: Record<string, any>
  ): string {
    const metric: PerformanceMetric = {
      name,
      startTime: performance.now(),
      metadata,
    };

    this.performanceMetrics.push(metric);
    return name;
  }

  public endPerformanceMetric(name: string): number | null {
    const metric = this.performanceMetrics.find(
      m => m.name === name && !m.endTime
    );
    if (!metric) return null;

    metric.endTime = performance.now();
    metric.duration = metric.endTime - metric.startTime;

    this.log('info', 'performance', `Metric: ${name}`, {
      duration: metric.duration,
      metadata: metric.metadata,
    });

    return metric.duration;
  }

  public getRecentLogs(count: number = 50): DebugLogEntry[] {
    return this.logs.slice(-count);
  }

  public getLogsByCategory(category: string): DebugLogEntry[] {
    return this.logs.filter(log => log.category === category);
  }

  public getLogsByLevel(level: string): DebugLogEntry[] {
    return this.logs.filter(log => log.level === level);
  }

  public exportLogs(): string {
    const exportData = {
      sessionId: this.sessionId,
      exportTimestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      logs: this.logs,
      performanceMetrics: this.performanceMetrics,
      systemInfo: this.getSystemInfo(),
    };

    return JSON.stringify(exportData, null, 2);
  }

  public downloadLogs(): void {
    const data = this.exportLogs();
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `swiss-budget-pro-debug-${this.sessionId}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  private getSystemInfo(): Record<string, any> {
    return {
      userAgent: navigator.userAgent,
      language: navigator.language,
      platform: navigator.platform,
      cookieEnabled: navigator.cookieEnabled,
      onLine: navigator.onLine,
      screen: {
        width: screen.width,
        height: screen.height,
        colorDepth: screen.colorDepth,
      },
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight,
      },
      localStorage: {
        available: this.isLocalStorageAvailable(),
        usage: this.getLocalStorageUsage(),
      },
      memory: (performance as any).memory
        ? {
            usedJSHeapSize: (performance as any).memory.usedJSHeapSize,
            totalJSHeapSize: (performance as any).memory.totalJSHeapSize,
            jsHeapSizeLimit: (performance as any).memory.jsHeapSizeLimit,
          }
        : null,
    };
  }

  private isLocalStorageAvailable(): boolean {
    try {
      const test = '__localStorage_test__';
      localStorage.setItem(test, test);
      localStorage.removeItem(test);
      return true;
    } catch {
      return false;
    }
  }

  private getLocalStorageUsage(): { used: number; total: number } | null {
    try {
      let used = 0;
      for (const key in localStorage) {
        if (localStorage.hasOwnProperty(key)) {
          used += localStorage[key].length + key.length;
        }
      }
      return { used, total: 5 * 1024 * 1024 }; // 5MB typical limit
    } catch {
      return null;
    }
  }

  private persistLogs(): void {
    try {
      const recentLogs = this.logs.slice(-100); // Keep last 100 logs
      localStorage.setItem(
        'swissBudgetPro_debug_logs',
        JSON.stringify(recentLogs)
      );
    } catch (error) {
      console.warn('Could not persist debug logs:', error);
    }
  }

  public loadPersistedLogs(): void {
    try {
      const stored = localStorage.getItem('swissBudgetPro_debug_logs');
      if (stored) {
        const logs = JSON.parse(stored);
        this.logs = [...logs, ...this.logs];
      }
    } catch (error) {
      console.warn('Could not load persisted logs:', error);
    }
  }

  public clearLogs(): void {
    this.logs = [];
    this.performanceMetrics = [];
    localStorage.removeItem('swissBudgetPro_debug_logs');
    this.log('info', 'system', 'Debug logs cleared');
  }

  public enable(): void {
    this.isEnabled = true;
    this.log('info', 'system', 'Debug logging enabled');
  }

  public disable(): void {
    this.log('info', 'system', 'Debug logging disabled');
    this.isEnabled = false;
  }

  public getStats(): Record<string, any> {
    const logsByLevel = this.logs.reduce(
      (acc, log) => {
        acc[log.level] = (acc[log.level] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>
    );

    const logsByCategory = this.logs.reduce(
      (acc, log) => {
        acc[log.category] = (acc[log.category] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>
    );

    return {
      totalLogs: this.logs.length,
      logsByLevel,
      logsByCategory,
      performanceMetrics: this.performanceMetrics.length,
      sessionId: this.sessionId,
      isEnabled: this.isEnabled,
    };
  }
}

// Create singleton instance
export const debugLogger = new DebugLogger();

// Global access for debugging
if (typeof window !== 'undefined') {
  (window as any).debugLogger = debugLogger;
}

export default debugLogger;
