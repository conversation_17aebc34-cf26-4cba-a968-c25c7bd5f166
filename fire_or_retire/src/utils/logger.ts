/**
 * Comprehensive Runtime Logging System
 * Captures errors, warnings, performance metrics, and user interactions
 */

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  CRITICAL = 4,
}

export interface LogEntry {
  timestamp: string;
  level: LogLevel;
  category: string;
  message: string;
  data?: any;
  stack?: string;
  userAgent?: string;
  url?: string;
  userId?: string;
  sessionId: string;
}

export interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: string;
  category: 'render' | 'calculation' | 'api' | 'user-interaction';
  metadata?: any;
}

class RuntimeLogger {
  private logs: LogEntry[] = [];
  private performanceMetrics: PerformanceMetric[] = [];
  private sessionId: string;
  private maxLogs = 1000;
  private maxMetrics = 500;
  private isEnabled = true;

  constructor() {
    this.sessionId = this.generateSessionId();
    this.initializeGlobalErrorHandlers();
    this.initializePerformanceMonitoring();
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private initializeGlobalErrorHandlers(): void {
    // Capture unhandled JavaScript errors
    window.addEventListener('error', event => {
      this.error('Global Error', event.error?.message || event.message, {
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        stack: event.error?.stack,
      });
    });

    // Capture unhandled promise rejections
    window.addEventListener('unhandledrejection', event => {
      this.error(
        'Unhandled Promise Rejection',
        event.reason?.message || String(event.reason),
        {
          reason: event.reason,
          stack: event.reason?.stack,
        }
      );
    });

    // Capture React errors (if using React Error Boundary)
    window.addEventListener('react-error', ((event: CustomEvent) => {
      this.error('React Error', event.detail.message, {
        componentStack: event.detail.componentStack,
        errorBoundary: event.detail.errorBoundary,
        stack: event.detail.stack,
      });
    }) as EventListener);
  }

  private initializePerformanceMonitoring(): void {
    // Monitor page load performance
    window.addEventListener('load', () => {
      setTimeout(() => {
        const navigation = performance.getEntriesByType(
          'navigation'
        )[0] as PerformanceNavigationTiming;
        if (navigation) {
          this.recordPerformance(
            'page-load',
            navigation.loadEventEnd - navigation.fetchStart,
            'render',
            {
              domContentLoaded:
                navigation.domContentLoadedEventEnd - navigation.fetchStart,
              firstPaint: this.getFirstPaint(),
              firstContentfulPaint: this.getFirstContentfulPaint(),
            }
          );
        }
      }, 0);
    });

    // Monitor resource loading
    const observer = new PerformanceObserver(list => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === 'resource') {
          const resource = entry as PerformanceResourceTiming;
          this.recordPerformance(
            `resource-${resource.name.split('/').pop()}`,
            resource.duration,
            'api',
            {
              type: resource.initiatorType,
              size: resource.transferSize,
              cached: resource.transferSize === 0,
            }
          );
        }
      }
    });

    try {
      observer.observe({ entryTypes: ['resource', 'measure', 'navigation'] });
    } catch (e) {
      this.warn(
        'Performance Observer',
        'Could not initialize performance monitoring',
        { error: e }
      );
    }
  }

  private getFirstPaint(): number | null {
    const paintEntries = performance.getEntriesByType('paint');
    const firstPaint = paintEntries.find(entry => entry.name === 'first-paint');
    return firstPaint ? firstPaint.startTime : null;
  }

  private getFirstContentfulPaint(): number | null {
    const paintEntries = performance.getEntriesByType('paint');
    const fcp = paintEntries.find(
      entry => entry.name === 'first-contentful-paint'
    );
    return fcp ? fcp.startTime : null;
  }

  private createLogEntry(
    level: LogLevel,
    category: string,
    message: string,
    data?: any,
    stack?: string
  ): LogEntry {
    return {
      timestamp: new Date().toISOString(),
      level,
      category,
      message,
      data,
      stack,
      userAgent: navigator.userAgent,
      url: window.location.href,
      sessionId: this.sessionId,
    };
  }

  debug(category: string, message: string, data?: any): void {
    if (!this.isEnabled) return;
    const entry = this.createLogEntry(LogLevel.DEBUG, category, message, data);
    this.addLog(entry);
    console.debug(`[${category}] ${message}`, data);
  }

  info(category: string, message: string, data?: any): void {
    if (!this.isEnabled) return;
    const entry = this.createLogEntry(LogLevel.INFO, category, message, data);
    this.addLog(entry);
    console.info(`[${category}] ${message}`, data);
  }

  warn(category: string, message: string, data?: any): void {
    if (!this.isEnabled) return;
    const entry = this.createLogEntry(LogLevel.WARN, category, message, data);
    this.addLog(entry);
    console.warn(`[${category}] ${message}`, data);
  }

  error(category: string, message: string, data?: any, error?: Error): void {
    if (!this.isEnabled) return;
    const stack = error?.stack || new Error().stack;
    const entry = this.createLogEntry(
      LogLevel.ERROR,
      category,
      message,
      data,
      stack
    );
    this.addLog(entry);
    console.error(`[${category}] ${message}`, data, error);
  }

  critical(category: string, message: string, data?: any, error?: Error): void {
    if (!this.isEnabled) return;
    const stack = error?.stack || new Error().stack;
    const entry = this.createLogEntry(
      LogLevel.CRITICAL,
      category,
      message,
      data,
      stack
    );
    this.addLog(entry);
    console.error(`[CRITICAL][${category}] ${message}`, data, error);

    // Send critical errors immediately to external service if configured
    this.sendCriticalError(entry);
  }

  recordPerformance(
    name: string,
    value: number,
    category: PerformanceMetric['category'],
    metadata?: any
  ): void {
    if (!this.isEnabled) return;

    const metric: PerformanceMetric = {
      name,
      value,
      timestamp: new Date().toISOString(),
      category,
      metadata,
    };

    this.addPerformanceMetric(metric);

    // Log slow operations
    if (value > 1000) {
      // More than 1 second
      this.warn('Performance', `Slow operation detected: ${name}`, {
        duration: value,
        metadata,
      });
    }
  }

  // User interaction tracking
  trackUserAction(action: string, data?: any): void {
    this.info('User Action', action, data);
    this.recordPerformance(
      `user-action-${action}`,
      performance.now(),
      'user-interaction',
      data
    );
  }

  // Financial calculation tracking
  trackCalculation(
    calculationType: string,
    inputs: any,
    outputs: any,
    duration: number
  ): void {
    this.info('Calculation', `${calculationType} completed`, {
      inputs,
      outputs,
      duration,
    });
    this.recordPerformance(`calc-${calculationType}`, duration, 'calculation', {
      inputs,
      outputs,
    });
  }

  private addLog(entry: LogEntry): void {
    this.logs.push(entry);
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs);
    }
  }

  private addPerformanceMetric(metric: PerformanceMetric): void {
    this.performanceMetrics.push(metric);
    if (this.performanceMetrics.length > this.maxMetrics) {
      this.performanceMetrics = this.performanceMetrics.slice(-this.maxMetrics);
    }
  }

  private async sendCriticalError(entry: LogEntry): Promise<void> {
    try {
      // This would send to your error reporting service
      // For now, we'll just store it locally with a flag
      localStorage.setItem(
        `critical_error_${Date.now()}`,
        JSON.stringify(entry)
      );
    } catch (e) {
      console.error('Failed to send critical error:', e);
    }
  }

  // Export logs for debugging
  exportLogs(): {
    logs: LogEntry[];
    metrics: PerformanceMetric[];
    sessionId: string;
  } {
    return {
      logs: [...this.logs],
      metrics: [...this.performanceMetrics],
      sessionId: this.sessionId,
    };
  }

  // Filter logs by level or category
  getLogs(level?: LogLevel, category?: string): LogEntry[] {
    return this.logs.filter(log => {
      if (level !== undefined && log.level < level) return false;
      if (category && log.category !== category) return false;
      return true;
    });
  }

  // Get performance summary
  getPerformanceSummary(): any {
    const summary = {
      totalMetrics: this.performanceMetrics.length,
      categories: {} as any,
      slowOperations: this.performanceMetrics.filter(m => m.value > 1000),
      averages: {} as any,
    };

    // Group by category
    this.performanceMetrics.forEach(metric => {
      if (!summary.categories[metric.category]) {
        summary.categories[metric.category] = [];
      }
      summary.categories[metric.category].push(metric);
    });

    // Calculate averages
    Object.keys(summary.categories).forEach(category => {
      const metrics = summary.categories[category];
      summary.averages[category] =
        metrics.reduce(
          (sum: number, m: PerformanceMetric) => sum + m.value,
          0
        ) / metrics.length;
    });

    return summary;
  }

  // Clear logs (useful for testing)
  clearLogs(): void {
    this.logs = [];
    this.performanceMetrics = [];
  }

  // Enable/disable logging
  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
  }
}

// Create singleton instance
export const logger = new RuntimeLogger();

// Export convenience functions
export const logDebug = (category: string, message: string, data?: any) =>
  logger.debug(category, message, data);
export const logInfo = (category: string, message: string, data?: any) =>
  logger.info(category, message, data);
export const logWarn = (category: string, message: string, data?: any) =>
  logger.warn(category, message, data);
export const logError = (
  category: string,
  message: string,
  data?: any,
  error?: Error
) => logger.error(category, message, data, error);
export const logCritical = (
  category: string,
  message: string,
  data?: any,
  error?: Error
) => logger.critical(category, message, data, error);
export const trackPerformance = (
  name: string,
  value: number,
  category: PerformanceMetric['category'],
  metadata?: any
) => logger.recordPerformance(name, value, category, metadata);
export const trackUserAction = (action: string, data?: any) =>
  logger.trackUserAction(action, data);
export const trackCalculation = (
  type: string,
  inputs: any,
  outputs: any,
  duration: number
) => logger.trackCalculation(type, inputs, outputs, duration);
