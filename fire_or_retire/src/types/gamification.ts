// Gamification System Types and Interfaces

export type XPSource = 
  | 'savings_contribution'
  | 'goal_achievement'
  | 'streak_maintenance'
  | 'budget_adherence'
  | 'financial_education'
  | 'swiss_optimization'
  | 'tax_optimization'
  | 'pillar3a_contribution'
  | 'healthcare_optimization'
  | 'canton_comparison'
  | 'achievement_unlock'
  | 'social_interaction'
  | 'feature_discovery';

export type AchievementCategory = 
  | 'saver'
  | 'streak'
  | 'goal'
  | 'swiss'
  | 'efficiency'
  | 'learning'
  | 'social'
  | 'discovery';

export type AchievementRarity = 'common' | 'rare' | 'epic' | 'legendary';

export type SavingsGoalCategory = 
  | 'emergency_fund'
  | 'vacation'
  | 'home_purchase'
  | 'retirement'
  | 'education'
  | 'vehicle'
  | 'investment'
  | 'debt_payoff'
  | 'pillar3a'
  | 'custom';

export type SavingsGoalPriority = 'critical' | 'high' | 'medium' | 'low';
export type SavingsGoalStatus = 'active' | 'paused' | 'completed' | 'cancelled';

export type TagCategory = 
  | 'goal_booster'
  | 'goal_blocker'
  | 'smart_choice'
  | 'swiss_saver'
  | 'investment'
  | 'reward'
  | 'neutral';

// Core XP and Level System
export interface XPTransaction {
  id: string;
  userId: string;
  source: XPSource;
  amount: number;
  multiplier: number;
  baseAmount: number;
  description: string;
  metadata?: Record<string, any>;
  timestamp: Date;
}

export interface UserLevel {
  level: number;
  title: string;
  minXP: number;
  maxXP: number;
  benefits: string[];
  unlocks: string[];
  multiplier: number;
}

export interface UserProgress {
  userId: string;
  totalXP: number;
  currentLevel: number;
  currentLevelXP: number;
  nextLevelXP: number;
  progressToNextLevel: number; // 0-100 percentage
  lastXPGain: Date;
  streakDays: number;
  longestStreak: number;
  totalAchievements: number;
  completedGoals: number;
  createdAt: Date;
  updatedAt: Date;
}

// Achievement System
export interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  category: AchievementCategory;
  rarity: AchievementRarity;
  xpReward: number;
  requirements: AchievementRequirement[];
  unlockConditions: UnlockCondition[];
  isSecret: boolean;
  isSwissSpecific: boolean;
  sortOrder: number;
}

export interface AchievementRequirement {
  type: 'xp_total' | 'goal_completed' | 'streak_days' | 'savings_amount' | 'feature_used' | 'swiss_action';
  value: number;
  metadata?: Record<string, any>;
}

export interface UnlockCondition {
  type: 'level' | 'achievement' | 'goal' | 'feature_access';
  value: string | number;
}

export interface UserAchievement {
  id: string;
  userId: string;
  achievementId: string;
  unlockedAt: Date;
  progress: number; // 0-100 percentage
  isCompleted: boolean;
  notificationSent: boolean;
}

// Savings Goals System
export interface SavingsGoal {
  id: string;
  userId: string;
  name: string;
  description: string;
  category: SavingsGoalCategory;
  targetAmount: number;
  currentAmount: number;
  monthlyContribution: number;
  targetDate: Date;
  priority: SavingsGoalPriority;
  status: SavingsGoalStatus;
  autoContribute: boolean;
  swissSpecific: boolean;
  tags: string[];
  milestones: GoalMilestone[];
  linkedAccounts: string[];
  sharingSettings: SharingSettings;
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
}

export interface GoalMilestone {
  id: string;
  percentage: number; // 25, 50, 75, 100
  amount: number;
  isReached: boolean;
  reachedAt?: Date;
  xpReward: number;
  celebrationShown: boolean;
}

export interface SharingSettings {
  isPublic: boolean;
  shareProgress: boolean;
  shareAchievements: boolean;
  allowComments: boolean;
}

// Streak System
export interface StreakData {
  userId: string;
  type: 'savings' | 'budget_adherence' | 'goal_progress' | 'app_usage';
  currentStreak: number;
  longestStreak: number;
  lastActivityDate: Date;
  streakStartDate: Date;
  multiplier: number;
  isActive: boolean;
  protectionUsed: number; // Number of streak protections used
  maxProtections: number; // Maximum protections allowed
}

// Tagging System
export interface TransactionTag {
  id: string;
  name: string;
  category: TagCategory;
  color: string;
  icon: string;
  xpModifier: number; // Positive for good, negative for bad
  isAutoGenerated: boolean;
  isSwissSpecific: boolean;
  description: string;
}

export interface TaggedTransaction {
  transactionId: string;
  userId: string;
  tags: string[];
  goalAlignment: number; // -100 to 100, how much this helps/hurts goals
  xpAwarded: number;
  autoTagged: boolean;
  userConfirmed: boolean;
  taggedAt: Date;
}

// Swiss-Specific Features
export interface SwissOptimizationAction {
  id: string;
  userId: string;
  type: 'pillar3a_contribution' | 'tax_optimization' | 'healthcare_optimization' | 'canton_comparison';
  amount?: number;
  description: string;
  xpAwarded: number;
  potentialSavings: number;
  actionTaken: boolean;
  actionDate?: Date;
  createdAt: Date;
}

// Leaderboard and Social Features
export interface LeaderboardEntry {
  userId: string;
  username: string;
  avatar?: string;
  totalXP: number;
  level: number;
  rank: number;
  monthlyXP: number;
  achievementCount: number;
  isAnonymous: boolean;
}

export interface SocialChallenge {
  id: string;
  name: string;
  description: string;
  type: 'savings' | 'streak' | 'goal_completion' | 'swiss_optimization';
  startDate: Date;
  endDate: Date;
  participants: string[];
  rewards: ChallengeReward[];
  isActive: boolean;
  isSwissSpecific: boolean;
}

export interface ChallengeReward {
  rank: number;
  xpReward: number;
  badgeId?: string;
  description: string;
}

// Notification System
export interface GamificationNotification {
  id: string;
  userId: string;
  type: 'achievement_unlock' | 'level_up' | 'goal_milestone' | 'streak_warning' | 'challenge_invite';
  title: string;
  message: string;
  data: Record<string, any>;
  isRead: boolean;
  priority: 'low' | 'medium' | 'high';
  scheduledFor?: Date;
  sentAt?: Date;
  createdAt: Date;
}

// Analytics and Insights
export interface GamificationAnalytics {
  userId: string;
  period: 'daily' | 'weekly' | 'monthly' | 'yearly';
  xpEarned: number;
  achievementsUnlocked: number;
  goalsCompleted: number;
  streakDaysActive: number;
  socialInteractions: number;
  swissOptimizationsUsed: number;
  engagementScore: number; // 0-100
  calculatedAt: Date;
}

// Configuration and Settings
export interface GamificationConfig {
  xpRates: Record<XPSource, number>;
  levelThresholds: number[];
  streakMultipliers: Record<number, number>;
  achievementDefinitions: Achievement[];
  swissBonusMultipliers: Record<string, number>;
  notificationSettings: NotificationSettings;
}

export interface NotificationSettings {
  achievementUnlocks: boolean;
  levelUps: boolean;
  goalMilestones: boolean;
  streakReminders: boolean;
  challengeInvites: boolean;
  weeklyProgress: boolean;
  customTime?: string; // HH:MM format
}

// Utility Types
export interface XPCalculationResult {
  baseXP: number;
  multiplier: number;
  bonusXP: number;
  totalXP: number;
  source: XPSource;
  description: string;
}

export interface LevelUpResult {
  oldLevel: number;
  newLevel: number;
  xpGained: number;
  newUnlocks: string[];
  newBenefits: string[];
}

export interface GoalProgressUpdate {
  goalId: string;
  oldAmount: number;
  newAmount: number;
  progressPercentage: number;
  milestonesReached: GoalMilestone[];
  xpAwarded: number;
  isCompleted: boolean;
}

// Error Types
export class GamificationError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: Record<string, any>
  ) {
    super(message);
    this.name = 'GamificationError';
  }
}

export class InsufficientXPError extends GamificationError {
  constructor(required: number, available: number) {
    super(
      `Insufficient XP: required ${required}, available ${available}`,
      'INSUFFICIENT_XP',
      { required, available }
    );
  }
}

export class InvalidGoalError extends GamificationError {
  constructor(goalId: string, reason: string) {
    super(
      `Invalid goal ${goalId}: ${reason}`,
      'INVALID_GOAL',
      { goalId, reason }
    );
  }
}

export class AchievementAlreadyUnlockedError extends GamificationError {
  constructor(achievementId: string) {
    super(
      `Achievement ${achievementId} already unlocked`,
      'ACHIEVEMENT_ALREADY_UNLOCKED',
      { achievementId }
    );
  }
}

// Constants
export const DEFAULT_XP_RATES: Record<XPSource, number> = {
  savings_contribution: 1, // 1 XP per CHF 10
  goal_achievement: 100, // Base XP for goal completion
  streak_maintenance: 5, // Daily streak XP
  budget_adherence: 10, // Weekly budget adherence
  financial_education: 25, // Per module completed
  swiss_optimization: 50, // Swiss-specific optimizations
  tax_optimization: 50, // Tax optimization actions
  pillar3a_contribution: 20, // Pillar 3a contributions (with 20% bonus)
  healthcare_optimization: 100, // Healthcare deductible optimization
  canton_comparison: 25, // Canton comparison usage
  achievement_unlock: 0, // Varies by achievement
  social_interaction: 5, // Social features usage
  feature_discovery: 15, // New feature discovery
};

export const SWISS_BONUS_MULTIPLIERS = {
  pillar3a: 1.2, // 20% bonus for Pillar 3a actions
  tax_optimization: 1.5, // 50% bonus for tax optimizations
  healthcare: 1.3, // 30% bonus for healthcare optimizations
  canton_comparison: 1.25, // 25% bonus for canton comparisons
};

export const STREAK_MULTIPLIERS: Record<number, number> = {
  7: 1.5, // 7-day streak: 1.5x multiplier
  30: 2.0, // 30-day streak: 2x multiplier
  100: 2.5, // 100-day streak: 2.5x multiplier
  365: 3.0, // 365-day streak: 3x multiplier
};

export default {
  XPSource,
  AchievementCategory,
  AchievementRarity,
  SavingsGoalCategory,
  SavingsGoalPriority,
  SavingsGoalStatus,
  TagCategory,
  DEFAULT_XP_RATES,
  SWISS_BONUS_MULTIPLIERS,
  STREAK_MULTIPLIERS,
};
