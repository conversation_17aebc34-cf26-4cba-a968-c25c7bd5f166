/**
 * Expense Management Types and Interfaces
 * Comprehensive expense tracking with Swiss-specific categories
 */

export type ExpenseCategory =
  | 'housing'
  | 'utilities'
  | 'food'
  | 'transportation'
  | 'insurance'
  | 'healthcare'
  | 'education'
  | 'entertainment'
  | 'clothing'
  | 'personal_care'
  | 'gifts_donations'
  | 'debt_payments'
  | 'savings_investments'
  | 'taxes'
  | 'other';

export type ExpensePriority = 'essential' | 'important' | 'nice-to-have';

export type ExpenseFrequency =
  | 'monthly'
  | 'quarterly'
  | 'annually'
  | 'one-time';

export interface Expense {
  id: string;
  category: ExpenseCategory;
  subcategory?: string;
  name: string;
  amount: number;
  priority: ExpensePriority;
  frequency: ExpenseFrequency;
  description?: string;
  isActive: boolean;
  tags?: string[];
  createdAt: string;
  updatedAt: string;
}

export interface ExpenseCategoryInfo {
  id: ExpenseCategory;
  name: string;
  nameDE: string;
  icon: string;
  description: string;
  descriptionDE: string;
  defaultPriority: ExpensePriority;
  swissSpecific: boolean;
  subcategories?: string[];
}

export interface ExpenseSummary {
  totalMonthly: number;
  totalAnnual: number;
  byCategory: Record<ExpenseCategory, number>;
  byPriority: Record<ExpensePriority, number>;
  essentialExpenses: number;
  nonEssentialExpenses: number;
  swissSpecificExpenses: number;
}

export interface ExpenseAnalysis {
  summary: ExpenseSummary;
  recommendations: ExpenseRecommendation[];
  trends: ExpenseTrend[];
  budgetHealth: BudgetHealth;
}

export interface ExpenseRecommendation {
  id: string;
  type: 'reduce' | 'optimize' | 'eliminate' | 'review';
  category: ExpenseCategory;
  title: string;
  titleDE: string;
  description: string;
  descriptionDE: string;
  potentialSavings: number;
  difficulty: 'easy' | 'medium' | 'hard';
  timeframe: 'immediate' | 'short-term' | 'long-term';
  swissSpecific: boolean;
}

export interface ExpenseTrend {
  category: ExpenseCategory;
  monthlyChange: number;
  trend: 'increasing' | 'decreasing' | 'stable';
  significance: 'low' | 'medium' | 'high';
}

export interface BudgetHealth {
  score: number; // 0-100
  status: 'excellent' | 'good' | 'fair' | 'poor' | 'critical';
  factors: BudgetFactor[];
}

export interface BudgetFactor {
  factor: string;
  impact: 'positive' | 'negative' | 'neutral';
  weight: number;
  description: string;
}

// Swiss-specific expense categories with detailed information
export const SWISS_EXPENSE_CATEGORIES: ExpenseCategoryInfo[] = [
  {
    id: 'housing',
    name: 'Housing',
    nameDE: 'Wohnen',
    icon: '🏠',
    description: 'Rent, mortgage, property taxes, maintenance',
    descriptionDE: 'Miete, Hypothek, Steuern, Unterhalt',
    defaultPriority: 'essential',
    swissSpecific: true,
    subcategories: [
      'Rent/Mortgage',
      'Property Tax',
      'Maintenance',
      'Utilities (included)',
    ],
  },
  {
    id: 'utilities',
    name: 'Utilities',
    nameDE: 'Nebenkosten',
    icon: '⚡',
    description: 'Electricity, heating, water, internet, phone',
    descriptionDE: 'Strom, Heizung, Wasser, Internet, Telefon',
    defaultPriority: 'essential',
    swissSpecific: false,
    subcategories: [
      'Electricity',
      'Heating',
      'Water',
      'Internet',
      'Phone',
      'Waste Management',
    ],
  },
  {
    id: 'food',
    name: 'Food & Groceries',
    nameDE: 'Lebensmittel',
    icon: '🛒',
    description: 'Groceries, dining out, beverages',
    descriptionDE: 'Einkäufe, Restaurants, Getränke',
    defaultPriority: 'essential',
    swissSpecific: false,
    subcategories: [
      'Groceries',
      'Restaurants',
      'Takeout',
      'Beverages',
      'Special Occasions',
    ],
  },
  {
    id: 'transportation',
    name: 'Transportation',
    nameDE: 'Transport',
    icon: '🚗',
    description: 'Public transport, car expenses, fuel, parking',
    descriptionDE: 'ÖV, Auto, Benzin, Parkplätze',
    defaultPriority: 'essential',
    swissSpecific: true,
    subcategories: [
      'Public Transport',
      'Car Payment',
      'Fuel',
      'Insurance',
      'Maintenance',
      'Parking',
    ],
  },
  {
    id: 'insurance',
    name: 'Insurance',
    nameDE: 'Versicherungen',
    icon: '🛡️',
    description: 'Health, liability, household, life insurance',
    descriptionDE: 'Kranken-, Haftpflicht-, Hausrat-, Lebensversicherung',
    defaultPriority: 'essential',
    swissSpecific: true,
    subcategories: [
      'Health Insurance',
      'Liability Insurance',
      'Household Insurance',
      'Life Insurance',
      'Disability Insurance',
    ],
  },
  {
    id: 'healthcare',
    name: 'Healthcare',
    nameDE: 'Gesundheit',
    icon: '🏥',
    description: 'Medical expenses, dental, pharmacy, deductibles',
    descriptionDE: 'Arztkosten, Zahnarzt, Apotheke, Selbstbehalt',
    defaultPriority: 'essential',
    swissSpecific: true,
    subcategories: [
      'Doctor Visits',
      'Dental Care',
      'Pharmacy',
      'Deductible',
      'Alternative Medicine',
    ],
  },
  {
    id: 'taxes',
    name: 'Taxes',
    nameDE: 'Steuern',
    icon: '📋',
    description: 'Income tax, wealth tax, property tax',
    descriptionDE: 'Einkommenssteuer, Vermögenssteuer, Liegenschaftssteuer',
    defaultPriority: 'essential',
    swissSpecific: true,
    subcategories: [
      'Federal Tax',
      'Cantonal Tax',
      'Municipal Tax',
      'Wealth Tax',
      'Property Tax',
    ],
  },
  {
    id: 'education',
    name: 'Education',
    nameDE: 'Bildung',
    icon: '📚',
    description: 'Courses, books, training, certifications',
    descriptionDE: 'Kurse, Bücher, Weiterbildung, Zertifikate',
    defaultPriority: 'important',
    swissSpecific: false,
    subcategories: [
      'Professional Development',
      'Language Courses',
      'Books',
      'Online Courses',
      'Certifications',
    ],
  },
  {
    id: 'entertainment',
    name: 'Entertainment',
    nameDE: 'Unterhaltung',
    icon: '🎬',
    description: 'Movies, concerts, hobbies, subscriptions',
    descriptionDE: 'Kino, Konzerte, Hobbys, Abonnemente',
    defaultPriority: 'nice-to-have',
    swissSpecific: false,
    subcategories: [
      'Streaming Services',
      'Movies/Theater',
      'Concerts',
      'Hobbies',
      'Sports',
      'Travel',
    ],
  },
  {
    id: 'clothing',
    name: 'Clothing',
    nameDE: 'Kleidung',
    icon: '👕',
    description: 'Clothes, shoes, accessories',
    descriptionDE: 'Kleider, Schuhe, Accessoires',
    defaultPriority: 'important',
    swissSpecific: false,
    subcategories: [
      'Work Clothes',
      'Casual Wear',
      'Shoes',
      'Accessories',
      'Special Occasions',
    ],
  },
  {
    id: 'personal_care',
    name: 'Personal Care',
    nameDE: 'Körperpflege',
    icon: '💄',
    description: 'Haircuts, cosmetics, personal hygiene',
    descriptionDE: 'Friseur, Kosmetik, Körperpflege',
    defaultPriority: 'important',
    swissSpecific: false,
    subcategories: [
      'Haircuts',
      'Cosmetics',
      'Personal Hygiene',
      'Spa/Wellness',
    ],
  },
  {
    id: 'gifts_donations',
    name: 'Gifts & Donations',
    nameDE: 'Geschenke & Spenden',
    icon: '🎁',
    description: 'Gifts, charitable donations, tips',
    descriptionDE: 'Geschenke, Spenden, Trinkgelder',
    defaultPriority: 'nice-to-have',
    swissSpecific: false,
    subcategories: [
      'Birthday Gifts',
      'Holiday Gifts',
      'Charitable Donations',
      'Tips',
    ],
  },
  {
    id: 'debt_payments',
    name: 'Debt Payments',
    nameDE: 'Schulden',
    icon: '💳',
    description: 'Credit card payments, loans, interest',
    descriptionDE: 'Kreditkarten, Darlehen, Zinsen',
    defaultPriority: 'essential',
    swissSpecific: false,
    subcategories: [
      'Credit Cards',
      'Personal Loans',
      'Student Loans',
      'Interest Payments',
    ],
  },
  {
    id: 'savings_investments',
    name: 'Savings & Investments',
    nameDE: 'Sparen & Investitionen',
    icon: '💰',
    description: 'Emergency fund, investments, retirement savings',
    descriptionDE: 'Notgroschen, Investitionen, Altersvorsorge',
    defaultPriority: 'essential',
    swissSpecific: true,
    subcategories: [
      'Emergency Fund',
      'Pillar 3a',
      'Investment Portfolio',
      'Crypto',
      'Real Estate',
    ],
  },
  {
    id: 'other',
    name: 'Other',
    nameDE: 'Sonstiges',
    icon: '📦',
    description: 'Miscellaneous expenses',
    descriptionDE: 'Verschiedene Ausgaben',
    defaultPriority: 'nice-to-have',
    swissSpecific: false,
    subcategories: ['Miscellaneous', 'Unexpected Expenses'],
  },
];

// Utility functions for expense calculations
export const calculateMonthlyAmount = (expense: Expense): number => {
  switch (expense.frequency) {
    case 'monthly':
      return expense.amount;
    case 'quarterly':
      return expense.amount / 3;
    case 'annually':
      return expense.amount / 12;
    case 'one-time':
      return 0; // One-time expenses don't contribute to monthly calculations
    default:
      return 0;
  }
};

export const calculateAnnualAmount = (expense: Expense): number => {
  switch (expense.frequency) {
    case 'monthly':
      return expense.amount * 12;
    case 'quarterly':
      return expense.amount * 4;
    case 'annually':
      return expense.amount;
    case 'one-time':
      return expense.amount;
    default:
      return 0;
  }
};

export const getCategoryInfo = (
  category: ExpenseCategory
): ExpenseCategoryInfo => {
  return (
    SWISS_EXPENSE_CATEGORIES.find(cat => cat.id === category) ||
    SWISS_EXPENSE_CATEGORIES[SWISS_EXPENSE_CATEGORIES.length - 1]
  );
};
