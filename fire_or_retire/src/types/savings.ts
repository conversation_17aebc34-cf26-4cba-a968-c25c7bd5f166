/**
 * Savings Goals Management Types and Interfaces
 * Comprehensive savings tracking with Swiss-specific goals
 */

export type SavingsGoalType =
  | 'emergency_fund'
  | 'pillar_3a'
  | 'investment_portfolio'
  | 'house_deposit'
  | 'vacation'
  | 'education'
  | 'car'
  | 'wedding'
  | 'retirement'
  | 'debt_payoff'
  | 'business'
  | 'other';

export type SavingsGoalPriority = 'critical' | 'high' | 'medium' | 'low';

export type SavingsGoalStatus = 'active' | 'completed' | 'paused' | 'cancelled';

export interface SavingsGoal {
  id: string;
  type: SavingsGoalType;
  name: string;
  description?: string;
  targetAmount: number;
  currentAmount: number;
  monthlyContribution: number;
  priority: SavingsGoalPriority;
  status: SavingsGoalStatus;
  targetDate?: string;
  autoContribute: boolean;
  swissSpecific: boolean;
  tags?: string[];
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
}

export interface SavingsGoalTypeInfo {
  id: SavingsGoalType;
  name: string;
  nameDE: string;
  icon: string;
  description: string;
  descriptionDE: string;
  defaultPriority: SavingsGoalPriority;
  swissSpecific: boolean;
  suggestedAmount?: number;
  maxAnnualContribution?: number;
  taxAdvantaged?: boolean;
}

export interface SavingsAnalysis {
  totalMonthlyContributions: number;
  totalTargetAmount: number;
  totalCurrentAmount: number;
  overallProgress: number;
  projectedCompletionDate: string | null;
  monthlyShortfall: number;
  priorityBreakdown: Record<SavingsGoalPriority, number>;
  typeBreakdown: Record<SavingsGoalType, number>;
  recommendations: SavingsRecommendation[];
}

export interface SavingsRecommendation {
  id: string;
  type:
    | 'increase_contribution'
    | 'rebalance_priorities'
    | 'tax_optimization'
    | 'timeline_adjustment';
  goalId?: string;
  title: string;
  titleDE: string;
  description: string;
  descriptionDE: string;
  impact: 'high' | 'medium' | 'low';
  effort: 'easy' | 'medium' | 'hard';
  potentialSavings?: number;
  timeframeDays?: number;
}

// Swiss-specific savings goal types with detailed information
export const SWISS_SAVINGS_GOAL_TYPES: SavingsGoalTypeInfo[] = [
  {
    id: 'emergency_fund',
    name: 'Emergency Fund',
    nameDE: 'Notgroschen',
    icon: '🛡️',
    description: 'Essential safety net for unexpected expenses',
    descriptionDE: 'Wichtiger Schutz für unerwartete Ausgaben',
    defaultPriority: 'critical',
    swissSpecific: false,
    suggestedAmount: 15000, // 3-6 months of expenses
    taxAdvantaged: false,
  },
  {
    id: 'pillar_3a',
    name: 'Pillar 3a',
    nameDE: 'Säule 3a',
    icon: '🏛️',
    description: 'Tax-advantaged retirement savings (Swiss)',
    descriptionDE: 'Steuerlich begünstigte Altersvorsorge',
    defaultPriority: 'critical',
    swissSpecific: true,
    suggestedAmount: 7056, // 2024 limit
    maxAnnualContribution: 7056,
    taxAdvantaged: true,
  },
  {
    id: 'investment_portfolio',
    name: 'Investment Portfolio',
    nameDE: 'Anlageportfolio',
    icon: '📈',
    description: 'Long-term wealth building through investments',
    descriptionDE: 'Langfristiger Vermögensaufbau durch Investitionen',
    defaultPriority: 'high',
    swissSpecific: false,
    taxAdvantaged: false,
  },
  {
    id: 'house_deposit',
    name: 'House Deposit',
    nameDE: 'Eigenkapital Haus',
    icon: '🏠',
    description: 'Down payment for property purchase',
    descriptionDE: 'Eigenkapital für Immobilienkauf',
    defaultPriority: 'high',
    swissSpecific: true,
    suggestedAmount: 200000, // 20% of average Swiss property
    taxAdvantaged: false,
  },
  {
    id: 'retirement',
    name: 'Additional Retirement',
    nameDE: 'Zusätzliche Altersvorsorge',
    icon: '🌅',
    description: 'Retirement savings beyond mandatory pillars',
    descriptionDE: 'Altersvorsorge über die obligatorischen Säulen hinaus',
    defaultPriority: 'high',
    swissSpecific: true,
    taxAdvantaged: false,
  },
  {
    id: 'vacation',
    name: 'Vacation Fund',
    nameDE: 'Ferienfonds',
    icon: '✈️',
    description: 'Travel and vacation expenses',
    descriptionDE: 'Reise- und Ferienausgaben',
    defaultPriority: 'medium',
    swissSpecific: false,
    suggestedAmount: 5000,
    taxAdvantaged: false,
  },
  {
    id: 'education',
    name: 'Education Fund',
    nameDE: 'Bildungsfonds',
    icon: '🎓',
    description: 'Professional development and education',
    descriptionDE: 'Weiterbildung und Ausbildung',
    defaultPriority: 'medium',
    swissSpecific: false,
    suggestedAmount: 10000,
    taxAdvantaged: false,
  },
  {
    id: 'car',
    name: 'Car Fund',
    nameDE: 'Auto-Fonds',
    icon: '🚗',
    description: 'Vehicle purchase or replacement',
    descriptionDE: 'Fahrzeugkauf oder -ersatz',
    defaultPriority: 'medium',
    swissSpecific: false,
    suggestedAmount: 25000,
    taxAdvantaged: false,
  },
  {
    id: 'wedding',
    name: 'Wedding Fund',
    nameDE: 'Hochzeitsfonds',
    icon: '💒',
    description: 'Wedding and celebration expenses',
    descriptionDE: 'Hochzeits- und Feierausgaben',
    defaultPriority: 'medium',
    swissSpecific: false,
    suggestedAmount: 30000,
    taxAdvantaged: false,
  },
  {
    id: 'debt_payoff',
    name: 'Debt Payoff',
    nameDE: 'Schuldenabbau',
    icon: '💳',
    description: 'Paying off high-interest debt',
    descriptionDE: 'Abbau von hochverzinslichen Schulden',
    defaultPriority: 'critical',
    swissSpecific: false,
    taxAdvantaged: false,
  },
  {
    id: 'business',
    name: 'Business Fund',
    nameDE: 'Geschäftsfonds',
    icon: '💼',
    description: 'Starting or expanding a business',
    descriptionDE: 'Gründung oder Erweiterung eines Unternehmens',
    defaultPriority: 'medium',
    swissSpecific: false,
    suggestedAmount: 50000,
    taxAdvantaged: false,
  },
  {
    id: 'other',
    name: 'Other Goal',
    nameDE: 'Anderes Ziel',
    icon: '🎯',
    description: 'Custom savings goal',
    descriptionDE: 'Individuelles Sparziel',
    defaultPriority: 'low',
    swissSpecific: false,
    taxAdvantaged: false,
  },
];

// Utility functions for savings calculations
export const calculateProgress = (goal: SavingsGoal): number => {
  if (goal.targetAmount <= 0) return 0;
  return Math.min(100, (goal.currentAmount / goal.targetAmount) * 100);
};

export const calculateMonthsToCompletion = (
  goal: SavingsGoal
): number | null => {
  if (goal.monthlyContribution <= 0) return null;
  const remaining = goal.targetAmount - goal.currentAmount;
  if (remaining <= 0) return 0;
  return Math.ceil(remaining / goal.monthlyContribution);
};

export const calculateProjectedCompletionDate = (
  goal: SavingsGoal
): Date | null => {
  const months = calculateMonthsToCompletion(goal);
  if (months === null) return null;

  const completionDate = new Date();
  completionDate.setMonth(completionDate.getMonth() + months);
  return completionDate;
};

export const getGoalTypeInfo = (type: SavingsGoalType): SavingsGoalTypeInfo => {
  return (
    SWISS_SAVINGS_GOAL_TYPES.find(goalType => goalType.id === type) ||
    SWISS_SAVINGS_GOAL_TYPES[SWISS_SAVINGS_GOAL_TYPES.length - 1]
  );
};

export const getPriorityWeight = (priority: SavingsGoalPriority): number => {
  switch (priority) {
    case 'critical':
      return 4;
    case 'high':
      return 3;
    case 'medium':
      return 2;
    case 'low':
      return 1;
    default:
      return 1;
  }
};

export const calculateOptimalAllocation = (
  goals: SavingsGoal[],
  totalMonthlyBudget: number
): Record<string, number> => {
  const activeGoals = goals.filter(g => g.status === 'active');
  const totalWeight = activeGoals.reduce(
    (sum, goal) => sum + getPriorityWeight(goal.priority),
    0
  );

  if (totalWeight === 0) return {};

  const allocation: Record<string, number> = {};

  activeGoals.forEach(goal => {
    const weight = getPriorityWeight(goal.priority);
    const baseAllocation = (weight / totalWeight) * totalMonthlyBudget;

    // Ensure we don't exceed annual limits for tax-advantaged accounts
    const goalTypeInfo = getGoalTypeInfo(goal.type);
    if (goalTypeInfo.maxAnnualContribution) {
      const maxMonthly = goalTypeInfo.maxAnnualContribution / 12;
      allocation[goal.id] = Math.min(baseAllocation, maxMonthly);
    } else {
      allocation[goal.id] = baseAllocation;
    }
  });

  return allocation;
};

export const generateSavingsRecommendations = (
  goals: SavingsGoal[],
  monthlyIncome: number,
  monthlyExpenses: number
): SavingsRecommendation[] => {
  const recommendations: SavingsRecommendation[] = [];
  const availableForSavings = monthlyIncome - monthlyExpenses;
  const currentSavingsRate = goals.reduce(
    (sum, g) => sum + g.monthlyContribution,
    0
  );

  // Check if emergency fund exists and is adequate
  const emergencyFund = goals.find(g => g.type === 'emergency_fund');
  if (!emergencyFund || emergencyFund.targetAmount < monthlyExpenses * 3) {
    recommendations.push({
      id: 'emergency_fund_priority',
      type: 'rebalance_priorities',
      title: 'Prioritize Emergency Fund',
      titleDE: 'Notgroschen priorisieren',
      description:
        'Build an emergency fund covering 3-6 months of expenses before other goals',
      descriptionDE:
        'Bauen Sie einen Notgroschen für 3-6 Monate Ausgaben auf, bevor Sie andere Ziele verfolgen',
      impact: 'high',
      effort: 'easy',
      timeframeDays: 30,
    });
  }

  // Check Pillar 3a optimization
  const pillar3a = goals.find(g => g.type === 'pillar_3a');
  if (!pillar3a || pillar3a.monthlyContribution < 7056 / 12) {
    recommendations.push({
      id: 'pillar_3a_optimization',
      type: 'tax_optimization',
      title: 'Maximize Pillar 3a Contributions',
      titleDE: 'Säule 3a Beiträge maximieren',
      description: 'Contribute the maximum CHF 7,056 annually to save on taxes',
      descriptionDE:
        'Zahlen Sie jährlich maximal CHF 7,056 ein, um Steuern zu sparen',
      impact: 'high',
      effort: 'easy',
      potentialSavings: 1500, // Estimated tax savings
      timeframeDays: 7,
    });
  }

  // Check if savings rate is too low
  if (currentSavingsRate < availableForSavings * 0.2) {
    recommendations.push({
      id: 'increase_savings_rate',
      type: 'increase_contribution',
      title: 'Increase Overall Savings Rate',
      titleDE: 'Sparquote erhöhen',
      description: 'Consider saving at least 20% of your available income',
      descriptionDE:
        'Erwägen Sie, mindestens 20% Ihres verfügbaren Einkommens zu sparen',
      impact: 'high',
      effort: 'medium',
      timeframeDays: 30,
    });
  }

  return recommendations;
};
