export type IncomeFrequency =
  | 'weekly'
  | 'monthly'
  | 'quarterly'
  | 'annually'
  | 'one_time';

export type IncomeCategoryId =
  | 'freelance'
  | 'rental'
  | 'dividends'
  | 'side_business'
  | 'consulting'
  | 'royalties'
  | 'ahv_pension'
  | 'pillar_2'
  | 'other';

export interface IncomeCategory {
  id: IncomeCategoryId;
  name: string;
  icon: string;
  swissSpecific: boolean;
  description: string;
  taxImplications: string;
}

export interface AdditionalIncome {
  id: string;
  name: string;
  category: IncomeCategoryId;
  amount: number;
  frequency: IncomeFrequency;
  isActive: boolean;
  startDate?: string;
  endDate?: string;
  taxRate?: number; // Percentage (0.25 = 25%)
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface IncomeProjection {
  year: number;
  totalIncome: number;
  breakdown: {
    categoryId: IncomeCategoryId;
    amount: number;
    percentage: number;
  }[];
}

// Utility functions
export const calculateMonthlyAmount = (income: AdditionalIncome): number => {
  if (!income.isActive) return 0;

  switch (income.frequency) {
    case 'weekly':
      return income.amount * 4.33; // Average weeks per month
    case 'monthly':
      return income.amount;
    case 'quarterly':
      return income.amount / 3;
    case 'annually':
      return income.amount / 12;
    case 'one_time':
      return 0; // One-time income doesn't contribute to monthly
    default:
      return 0;
  }
};

export const calculateAnnualAmount = (income: AdditionalIncome): number => {
  if (!income.isActive) return 0;

  switch (income.frequency) {
    case 'weekly':
      return income.amount * 52;
    case 'monthly':
      return income.amount * 12;
    case 'quarterly':
      return income.amount * 4;
    case 'annually':
      return income.amount;
    case 'one_time':
      return income.amount;
    default:
      return 0;
  }
};

export const calculateTotalMonthlyIncome = (
  incomes: AdditionalIncome[]
): number => {
  return incomes
    .filter(income => income.isActive)
    .reduce((sum, income) => sum + calculateMonthlyAmount(income), 0);
};

export const calculateTotalAnnualIncome = (
  incomes: AdditionalIncome[]
): number => {
  return incomes
    .filter(income => income.isActive)
    .reduce((sum, income) => sum + calculateAnnualAmount(income), 0);
};

export const calculateNetIncome = (income: AdditionalIncome): number => {
  const grossAmount = calculateAnnualAmount(income);
  const taxRate = income.taxRate || 0;
  return grossAmount * (1 - taxRate);
};

export const calculateTotalNetIncome = (
  incomes: AdditionalIncome[]
): number => {
  return incomes
    .filter(income => income.isActive)
    .reduce((sum, income) => sum + calculateNetIncome(income), 0);
};

export const getIncomeBreakdown = (
  incomes: AdditionalIncome[]
): { categoryId: IncomeCategoryId; amount: number; percentage: number }[] => {
  const activeIncomes = incomes.filter(income => income.isActive);
  const totalIncome = calculateTotalAnnualIncome(activeIncomes);

  const breakdown = new Map<IncomeCategoryId, number>();

  activeIncomes.forEach(income => {
    const amount = calculateAnnualAmount(income);
    const current = breakdown.get(income.category) || 0;
    breakdown.set(income.category, current + amount);
  });

  return Array.from(breakdown.entries()).map(([categoryId, amount]) => ({
    categoryId,
    amount,
    percentage: totalIncome > 0 ? (amount / totalIncome) * 100 : 0,
  }));
};

export const getSwissSpecificIncomes = (
  incomes: AdditionalIncome[]
): AdditionalIncome[] => {
  const swissCategories = ['ahv_pension', 'pillar_2'];
  return incomes.filter(income => swissCategories.includes(income.category));
};

export const calculatePensionIncome = (incomes: AdditionalIncome[]): number => {
  const pensionCategories = ['ahv_pension', 'pillar_2'];
  return incomes
    .filter(
      income => income.isActive && pensionCategories.includes(income.category)
    )
    .reduce((sum, income) => sum + calculateAnnualAmount(income), 0);
};

export const getIncomeProjections = (
  incomes: AdditionalIncome[],
  years: number,
  growthRate: number = 0.03 // 3% default growth
): IncomeProjection[] => {
  const projections: IncomeProjection[] = [];

  for (let year = 0; year < years; year++) {
    const currentYear = new Date().getFullYear() + year;
    const growthMultiplier = Math.pow(1 + growthRate, year);

    const breakdown = getIncomeBreakdown(incomes).map(item => ({
      ...item,
      amount: item.amount * growthMultiplier,
    }));

    const totalIncome = breakdown.reduce((sum, item) => sum + item.amount, 0);

    // Recalculate percentages
    breakdown.forEach(item => {
      item.percentage = totalIncome > 0 ? (item.amount / totalIncome) * 100 : 0;
    });

    projections.push({
      year: currentYear,
      totalIncome,
      breakdown,
    });
  }

  return projections;
};

export const getIncomeRecommendations = (
  incomes: AdditionalIncome[]
): string[] => {
  const recommendations: string[] = [];
  const totalMonthlyIncome = calculateTotalMonthlyIncome(incomes);
  const breakdown = getIncomeBreakdown(incomes);

  // Check diversification
  if (breakdown.length < 2) {
    recommendations.push(
      'Consider diversifying your income sources to reduce risk'
    );
  }

  // Check for Swiss pension optimization
  const pensionIncome = calculatePensionIncome(incomes);
  if (pensionIncome === 0) {
    recommendations.push(
      'Consider planning for Swiss pension income (AHV and Pillar 2)'
    );
  }

  // Check for passive income
  const passiveCategories = ['rental', 'dividends', 'royalties'];
  const passiveIncome = incomes
    .filter(
      income => income.isActive && passiveCategories.includes(income.category)
    )
    .reduce((sum, income) => sum + calculateMonthlyAmount(income), 0);

  const passivePercentage =
    totalMonthlyIncome > 0 ? (passiveIncome / totalMonthlyIncome) * 100 : 0;

  if (passivePercentage < 20) {
    recommendations.push(
      'Consider building passive income streams (rental, dividends, royalties)'
    );
  }

  // Check tax optimization
  const highTaxIncomes = incomes.filter(
    income => income.isActive && income.taxRate && income.taxRate > 0.3
  );

  if (highTaxIncomes.length > 0) {
    recommendations.push(
      'Review tax optimization strategies for high-tax income sources'
    );
  }

  // Check for business income
  const businessCategories = ['freelance', 'side_business', 'consulting'];
  const businessIncome = incomes
    .filter(
      income => income.isActive && businessCategories.includes(income.category)
    )
    .reduce((sum, income) => sum + calculateMonthlyAmount(income), 0);

  if (businessIncome > totalMonthlyIncome * 0.5) {
    recommendations.push(
      'Consider incorporating your business for potential tax benefits'
    );
  }

  return recommendations;
};

export const SWISS_INCOME_CATEGORIES: IncomeCategory[] = [
  {
    id: 'freelance',
    name: 'Freelance Work',
    icon: '💼',
    swissSpecific: false,
    description: 'Independent contractor and freelance income',
    taxImplications:
      'Self-employment tax applies, VAT registration may be required',
  },
  {
    id: 'rental',
    name: 'Rental Income',
    icon: '🏠',
    swissSpecific: false,
    description: 'Income from rental properties',
    taxImplications:
      'Taxable as income, maintenance and depreciation deductions available',
  },
  {
    id: 'dividends',
    name: 'Dividends',
    icon: '📈',
    swissSpecific: false,
    description: 'Dividend income from investments',
    taxImplications:
      'Swiss withholding tax (35%) may apply, partial tax credit available',
  },
  {
    id: 'side_business',
    name: 'Side Business',
    icon: '🚀',
    swissSpecific: false,
    description: 'Income from side business or startup',
    taxImplications: 'Business income tax rules apply, expenses deductible',
  },
  {
    id: 'consulting',
    name: 'Consulting',
    icon: '🎯',
    swissSpecific: false,
    description: 'Professional consulting services',
    taxImplications:
      'Professional income, VAT registration required above CHF 100,000',
  },
  {
    id: 'royalties',
    name: 'Royalties',
    icon: '🎨',
    swissSpecific: false,
    description: 'Intellectual property royalties',
    taxImplications: 'Taxable as income, may qualify for reduced rates',
  },
  {
    id: 'ahv_pension',
    name: 'AHV Pension',
    icon: '🇨🇭',
    swissSpecific: true,
    description: 'Swiss state pension (AHV/AVS)',
    taxImplications: 'Taxable pension income, no withholding tax',
  },
  {
    id: 'pillar_2',
    name: 'Pillar 2 Pension',
    icon: '🏛️',
    swissSpecific: true,
    description: 'Occupational pension fund',
    taxImplications:
      'Reduced tax rate for lump sum withdrawal, regular rate for annuity',
  },
  {
    id: 'other',
    name: 'Other Income',
    icon: '💰',
    swissSpecific: false,
    description: 'Other miscellaneous income sources',
    taxImplications: 'Varies by income type, consult tax advisor',
  },
];
