@tailwind base;
@tailwind components;
@tailwind utilities;

/* Chart container styles for full frame display */
.enhanced-chart-container {
  width: 100%;
}

.enhanced-chart-container > div {
  display: flex;
  flex-direction: column;
  width: 100%;
  margin-bottom: 1.5rem;
}

/* Ensure SVG charts fill their containers */
.enhanced-chart-container svg {
  width: 100% !important;
  height: 450px !important;
  max-width: 100%;
}

/* Chart responsive behavior */
@media (min-width: 768px) {
  .enhanced-chart-container svg {
    height: 500px !important;
  }
}

@media (min-width: 1024px) {
  .enhanced-chart-container svg {
    height: 550px !important;
  }
}

@media (min-width: 1280px) {
  .enhanced-chart-container svg {
    height: 600px !important;
  }
}
