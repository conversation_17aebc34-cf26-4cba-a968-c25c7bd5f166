/**
 * Comprehensive test suite for the gamification system
 * This demonstrates how to test all components of the gamification framework
 */

import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { XPCalculationEngine } from '../services/XPCalculationEngine';
import { LevelProgressionSystem } from '../services/LevelProgressionSystem';
import { UserProgressService } from '../services/UserProgressService';
import AchievementService from '../services/AchievementService';
import SmartTaggingService from '../services/SmartTaggingService';
import StreakTrackingService from '../services/StreakTrackingService';
import { UserProgress, SavingsGoal } from '../types/gamification';

describe('Gamification System Tests', () => {
  let xpEngine: XPCalculationEngine;
  let levelSystem: LevelProgressionSystem;
  let userProgressService: UserProgressService;
  let achievementService: AchievementService;
  let taggingService: SmartTaggingService;
  let streakService: StreakTrackingService;

  const testUserId = 'test_user_123';

  beforeEach(() => {
    xpEngine = new XPCalculationEngine();
    levelSystem = new LevelProgressionSystem();
    userProgressService = new UserProgressService();
    achievementService = new AchievementService();
    taggingService = new SmartTaggingService();
    streakService = new StreakTrackingService();
  });

  describe('XP Calculation Engine', () => {
    it('should calculate basic savings XP correctly', () => {
      const result = xpEngine.calculateSavingsXP(1000, false);
      expect(result.baseXP).toBe(100); // 1 XP per 10 CHF
      expect(result.totalXP).toBe(100);
      expect(result.source).toBe('savings_contribution');
    });

    it('should apply Swiss Pillar 3a bonus', () => {
      const result = xpEngine.calculateSavingsXP(1000, true);
      expect(result.baseXP).toBe(100);
      expect(result.bonusXP).toBe(20); // 20% bonus
      expect(result.totalXP).toBe(120);
    });

    it('should calculate goal achievement XP with time bonus', () => {
      const targetDate = new Date();
      targetDate.setDate(targetDate.getDate() + 30); // 30 days from now
      const completionDate = new Date();
      completionDate.setDate(completionDate.getDate() + 20); // Completed 10 days early

      const timeBonus = xpEngine.calculateTimeBonus(targetDate, completionDate);
      expect(timeBonus).toBeGreaterThan(1); // Should have early completion bonus

      const result = xpEngine.calculateGoalAchievementXP(10000, timeBonus, false);
      expect(result.totalXP).toBeGreaterThan(result.baseXP);
    });

    it('should calculate streak XP with multipliers', () => {
      const result = xpEngine.calculateStreakXP(30, 'savings');
      expect(result.baseXP).toBe(30); // 1 XP per day
      expect(result.multiplier).toBeGreaterThan(1); // Should have streak bonus
      expect(result.totalXP).toBeGreaterThan(result.baseXP);
    });

    it('should calculate Swiss optimization XP', () => {
      const result = xpEngine.calculateSwissOptimizationXP('pillar3a', 1000);
      expect(result.baseXP).toBe(50);
      expect(result.bonusXP).toBeGreaterThan(0); // Swiss bonus
      expect(result.totalXP).toBeGreaterThan(result.baseXP);
    });
  });

  describe('Level Progression System', () => {
    it('should calculate correct XP requirements for levels', () => {
      expect(levelSystem.getXPRequiredForLevel(1)).toBe(0);
      expect(levelSystem.getXPRequiredForLevel(2)).toBe(1000);
      expect(levelSystem.getXPRequiredForLevel(10)).toBe(9000);
      expect(levelSystem.getXPRequiredForLevel(25)).toBe(37500);
    });

    it('should determine correct level from XP', () => {
      expect(levelSystem.getLevelFromXP(0)).toBe(1);
      expect(levelSystem.getLevelFromXP(1000)).toBe(2);
      expect(levelSystem.getLevelFromXP(5000)).toBe(6);
      expect(levelSystem.getLevelFromXP(50000)).toBe(26);
    });

    it('should process level up correctly', () => {
      const initialProgress: UserProgress = {
        userId: testUserId,
        totalXP: 900,
        currentLevel: 1,
        currentLevelXP: 900,
        nextLevelXP: 1000,
        progressToNextLevel: 90,
        lastXPGain: new Date(),
        streakDays: 0,
        longestStreak: 0,
        totalAchievements: 0,
        completedGoals: 0,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const xpTransaction = {
        id: 'tx_1',
        userId: testUserId,
        amount: 200,
        source: 'savings_contribution' as const,
        description: 'Test XP gain',
        timestamp: new Date(),
        metadata: {},
      };

      const result = levelSystem.processXPGain(initialProgress, xpTransaction);
      
      expect(result.updatedProgress.totalXP).toBe(1100);
      expect(result.updatedProgress.currentLevel).toBe(2);
      expect(result.levelUpResult).toBeDefined();
      expect(result.levelUpResult?.newLevel).toBe(2);
    });

    it('should unlock features at correct levels', () => {
      expect(levelSystem.hasFeatureAccess(1, 'basic_goals')).toBe(true);
      expect(levelSystem.hasFeatureAccess(5, 'goal_sharing')).toBe(true);
      expect(levelSystem.hasFeatureAccess(4, 'goal_sharing')).toBe(false);
      expect(levelSystem.hasFeatureAccess(25, 'leaderboards')).toBe(true);
      expect(levelSystem.hasFeatureAccess(24, 'leaderboards')).toBe(false);
    });
  });

  describe('Achievement System', () => {
    beforeEach(() => {
      achievementService.initializeUserAchievements(testUserId);
    });

    it('should initialize user achievements', () => {
      const userAchievements = achievementService.getUserAchievements(testUserId);
      expect(userAchievements.length).toBeGreaterThan(0);
      expect(userAchievements.every(ua => !ua.isCompleted)).toBe(true);
    });

    it('should unlock savings achievements', () => {
      const mockProgress: UserProgress = {
        userId: testUserId,
        totalXP: 1000,
        currentLevel: 2,
        currentLevelXP: 0,
        nextLevelXP: 1500,
        progressToNextLevel: 0,
        lastXPGain: new Date(),
        streakDays: 0,
        longestStreak: 0,
        totalAchievements: 0,
        completedGoals: 0,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const mockGoals: SavingsGoal[] = [{
        id: 'goal_1',
        userId: testUserId,
        name: 'Emergency Fund',
        description: 'Build emergency fund',
        category: 'emergency_fund',
        targetAmount: 10000,
        currentAmount: 1000,
        monthlyContribution: 500,
        targetDate: new Date(),
        priority: 'high',
        status: 'active',
        autoContribute: false,
        swissSpecific: false,
        tags: [],
        milestones: [],
        linkedAccounts: [],
        sharingSettings: {
          isPublic: false,
          shareProgress: false,
          shareAchievements: false,
          allowComments: false,
        },
        createdAt: new Date(),
        updatedAt: new Date(),
      }];

      const newAchievements = achievementService.checkAndUnlockAchievements(
        testUserId,
        'savings_contribution',
        { amount: 1000 },
        mockProgress,
        mockGoals
      );

      expect(newAchievements.length).toBeGreaterThan(0);
      expect(newAchievements.some(ua => ua.achievementId === 'first_savings')).toBe(true);
    });

    it('should calculate achievement statistics', () => {
      // Manually unlock some achievements for testing
      achievementService.updateAchievementProgress(testUserId, 'first_savings', 100);
      achievementService.updateAchievementProgress(testUserId, 'savings_1k', 100);

      const stats = achievementService.getAchievementStatistics(testUserId);
      expect(stats.unlockedAchievements).toBe(2);
      expect(stats.completionPercentage).toBeGreaterThan(0);
      expect(stats.rarityBreakdown.common).toBeGreaterThan(0);
    });
  });

  describe('Smart Tagging System', () => {
    it('should auto-tag transactions correctly', async () => {
      const transaction = {
        id: 'tx_1',
        userId: testUserId,
        amount: -150,
        description: 'Migros Supermarket',
        merchant: 'Migros',
        date: new Date(),
        type: 'expense' as const,
      };

      const taggedTransaction = await taggingService.autoTagTransaction(transaction);
      
      expect(taggedTransaction.tags.length).toBeGreaterThan(0);
      expect(taggedTransaction.autoTagged).toBe(true);
      expect(taggedTransaction.xpAwarded).toBeDefined();
    });

    it('should detect Swiss-specific transactions', async () => {
      const pillar3aTransaction = {
        id: 'tx_2',
        userId: testUserId,
        amount: -588,
        description: 'Pillar 3a Contribution',
        date: new Date(),
        type: 'expense' as const,
      };

      const taggedTransaction = await taggingService.autoTagTransaction(pillar3aTransaction);
      
      expect(taggedTransaction.tags).toContain('pillar3a_contribution');
      expect(taggedTransaction.xpAwarded).toBeGreaterThan(0);
    });

    it('should analyze goal alignment', () => {
      const transaction = {
        id: 'tx_3',
        userId: testUserId,
        amount: -2000,
        description: 'Luxury Watch Purchase',
        date: new Date(),
        type: 'expense' as const,
      };

      const mockGoals: SavingsGoal[] = [{
        id: 'goal_1',
        userId: testUserId,
        name: 'Emergency Fund',
        category: 'emergency_fund',
        monthlyContribution: 500,
        targetAmount: 10000,
        currentAmount: 5000,
        targetDate: new Date(),
        priority: 'high',
        status: 'active',
        autoContribute: false,
        swissSpecific: false,
        tags: [],
        milestones: [],
        linkedAccounts: [],
        sharingSettings: {
          isPublic: false,
          shareProgress: false,
          shareAchievements: false,
          allowComments: false,
        },
        name: 'Emergency Fund',
        description: 'Build emergency fund',
        createdAt: new Date(),
        updatedAt: new Date(),
      }];

      const analysis = taggingService.analyzeGoalAlignment(transaction, mockGoals);
      
      expect(analysis.alignmentScore).toBeLessThan(0); // Negative impact
      expect(analysis.affectedGoals.length).toBeGreaterThan(0);
      expect(analysis.recommendations.length).toBeGreaterThan(0);
    });
  });

  describe('Streak Tracking System', () => {
    beforeEach(() => {
      streakService.initializeUserStreaks(testUserId);
    });

    it('should initialize user streaks', () => {
      const streaks = streakService.getAllUserStreaks(testUserId);
      expect(streaks.size).toBeGreaterThan(0);
      expect(streaks.has('savings')).toBe(true);
      expect(streaks.has('swiss_optimization')).toBe(true);
    });

    it('should record activities and update streaks', () => {
      const activity = streakService.recordActivity({
        userId: testUserId,
        type: 'savings',
        date: new Date(),
        amount: 100,
      });

      expect(activity.id).toBeDefined();
      
      const streak = streakService.getUserStreak(testUserId, 'savings');
      expect(streak?.currentStreak).toBe(1);
      expect(streak?.isActive).toBe(true);
    });

    it('should calculate streak multipliers', () => {
      // Simulate a 30-day streak
      for (let i = 0; i < 30; i++) {
        const date = new Date();
        date.setDate(date.getDate() - (29 - i));
        streakService.recordActivity({
          userId: testUserId,
          type: 'savings',
          date,
        });
      }

      const xpResult = streakService.calculateStreakXP(testUserId, 'savings', 10);
      expect(xpResult.multiplier).toBeGreaterThan(1);
      expect(xpResult.totalXP).toBeGreaterThan(xpResult.baseXP);
    });

    it('should handle streak protection', () => {
      // Build a 10-day streak
      for (let i = 0; i < 10; i++) {
        const date = new Date();
        date.setDate(date.getDate() - (9 - i));
        streakService.recordActivity({
          userId: testUserId,
          type: 'savings',
          date,
        });
      }

      const canProtect = streakService.canUseStreakProtection(testUserId, 'savings');
      expect(canProtect).toBe(true);

      const protectionUsed = streakService.useStreakProtection(testUserId, 'savings');
      expect(protectionUsed).toBe(true);

      const streak = streakService.getUserStreak(testUserId, 'savings');
      expect(streak?.protectionUsed).toBe(1);
    });

    it('should calculate streak statistics', () => {
      // Record some activities
      streakService.recordActivity({
        userId: testUserId,
        type: 'savings',
        date: new Date(),
      });
      
      streakService.recordActivity({
        userId: testUserId,
        type: 'budget_adherence',
        date: new Date(),
      });

      const stats = streakService.getStreakStatistics(testUserId);
      expect(stats.totalActiveStreaks).toBe(2);
      expect(stats.longestCurrentStreak).toBeGreaterThan(0);
      expect(stats.protectionsAvailable).toBeGreaterThan(0);
    });
  });

  describe('Integration Tests', () => {
    it('should integrate XP calculation with level progression', async () => {
      const initialProgress = await userProgressService.initializeUserProgress(testUserId);
      expect(initialProgress.currentLevel).toBe(1);
      expect(initialProgress.totalXP).toBe(0);

      // Award enough XP to level up
      const result = await userProgressService.awardSavingsXP(testUserId, 10000, false);
      
      expect(result.progress.totalXP).toBe(1000); // 1 XP per 10 CHF
      expect(result.progress.currentLevel).toBe(2);
      expect(result.levelUp).toBeDefined();
      expect(result.levelUp?.newLevel).toBe(2);
    });

    it('should integrate achievements with progress tracking', async () => {
      await userProgressService.initializeUserProgress(testUserId);
      achievementService.initializeUserAchievements(testUserId);

      // Award XP and check for achievements
      await userProgressService.awardSavingsXP(testUserId, 1000, false);
      
      const userAchievements = achievementService.getUserAchievements(testUserId);
      const firstSavingsAchievement = userAchievements.find(ua => ua.achievementId === 'first_savings');
      
      // This would be unlocked in a real integration
      expect(firstSavingsAchievement).toBeDefined();
    });

    it('should integrate tagging with XP awards', async () => {
      const transaction = {
        id: 'tx_integration',
        userId: testUserId,
        amount: -588,
        description: 'Pillar 3a Monthly Contribution',
        date: new Date(),
        type: 'expense' as const,
      };

      const taggedTransaction = await taggingService.autoTagTransaction(transaction);
      
      // Should award positive XP for Pillar 3a contribution
      expect(taggedTransaction.xpAwarded).toBeGreaterThan(0);
      expect(taggedTransaction.tags).toContain('pillar3a_contribution');
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid user IDs gracefully', () => {
      expect(() => {
        achievementService.initializeUserAchievements('');
      }).not.toThrow();
    });

    it('should handle invalid XP amounts', () => {
      const result = xpEngine.calculateSavingsXP(-100, false);
      expect(result.totalXP).toBe(0); // Should not award negative XP
    });

    it('should handle missing streak data', () => {
      const streak = streakService.getUserStreak('nonexistent_user', 'savings');
      expect(streak).toBeNull();
    });
  });

  describe('Performance Tests', () => {
    it('should handle large numbers of achievements efficiently', () => {
      const startTime = Date.now();
      
      for (let i = 0; i < 1000; i++) {
        achievementService.initializeUserAchievements(`user_${i}`);
      }
      
      const endTime = Date.now();
      expect(endTime - startTime).toBeLessThan(1000); // Should complete in under 1 second
    });

    it('should handle bulk XP calculations efficiently', () => {
      const startTime = Date.now();
      
      for (let i = 0; i < 1000; i++) {
        xpEngine.calculateSavingsXP(Math.random() * 1000, Math.random() > 0.5);
      }
      
      const endTime = Date.now();
      expect(endTime - startTime).toBeLessThan(100); // Should be very fast
    });
  });
});

// Mock data for testing
export const mockUserProgress: UserProgress = {
  userId: 'test_user',
  totalXP: 2500,
  currentLevel: 3,
  currentLevelXP: 500,
  nextLevelXP: 1500,
  progressToNextLevel: 33.33,
  lastXPGain: new Date(),
  streakDays: 15,
  longestStreak: 30,
  totalAchievements: 5,
  completedGoals: 2,
  createdAt: new Date(),
  updatedAt: new Date(),
};

export const mockSavingsGoal: SavingsGoal = {
  id: 'goal_emergency',
  userId: 'test_user',
  name: 'Emergency Fund',
  description: 'Build a 6-month emergency fund',
  category: 'emergency_fund',
  targetAmount: 15000,
  currentAmount: 7500,
  monthlyContribution: 1250,
  targetDate: new Date(Date.now() + 6 * 30 * 24 * 60 * 60 * 1000), // 6 months from now
  priority: 'critical',
  status: 'active',
  autoContribute: true,
  swissSpecific: false,
  tags: ['emergency', 'security'],
  milestones: [
    {
      id: 'milestone_25',
      percentage: 25,
      amount: 3750,
      isReached: true,
      reachedAt: new Date(),
      xpReward: 100,
      celebrationShown: true,
    },
    {
      id: 'milestone_50',
      percentage: 50,
      amount: 7500,
      isReached: true,
      reachedAt: new Date(),
      xpReward: 200,
      celebrationShown: true,
    },
    {
      id: 'milestone_75',
      percentage: 75,
      amount: 11250,
      isReached: false,
      xpReward: 300,
      celebrationShown: false,
    },
    {
      id: 'milestone_100',
      percentage: 100,
      amount: 15000,
      isReached: false,
      xpReward: 500,
      celebrationShown: false,
    },
  ],
  linkedAccounts: [],
  sharingSettings: {
    isPublic: false,
    shareProgress: true,
    shareAchievements: true,
    allowComments: false,
  },
  createdAt: new Date(),
  updatedAt: new Date(),
};
