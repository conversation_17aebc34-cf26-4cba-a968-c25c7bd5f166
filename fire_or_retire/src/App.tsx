import React, { useEffect, useState } from 'react';
import {
  Navigate,
  Route,
  BrowserRouter as Router,
  Routes,
} from 'react-router-dom';
import { AdminPage } from './components/admin/AdminPage';
import { FireOrRetireCalculator } from './components/FireOrRetireCalculator';
import LogViewer from './components/LogViewer';
import { useLogger } from './hooks/useLogger';

const App: React.FC = () => {
  const [showLogViewer, setShowLogViewer] = useState(false);
  const { logInfo, logError } = useLogger('App');

  useEffect(() => {
    logInfo('Application started', {
      userAgent: navigator.userAgent,
      url: window.location.href,
      timestamp: new Date().toISOString(),
    });

    // Add keyboard shortcut to toggle log viewer (Ctrl+Shift+L)
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey && event.shiftKey && event.key === 'L') {
        event.preventDefault();
        setShowLogViewer(prev => !prev);
      }
    };

    window.addEventListener('keydown', handleKeyDown);

    // Log any unhandled errors
    const handleError = (event: ErrorEvent) => {
      logError('Unhandled error', event.message, {
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
      });
    };

    window.addEventListener('error', handleError);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('error', handleError);
    };
  }, [logInfo, logError]);

  return (
    <>
      <Router>
        <Routes>
          <Route path='/admin' element={<AdminPage darkMode={true} />} />
          <Route
            path='/'
            element={<FireOrRetireCalculator darkMode={true} />}
          />
          <Route path='*' element={<Navigate to='/' replace />} />
        </Routes>
      </Router>

      {/* Debug Log Viewer */}
      <LogViewer
        isOpen={showLogViewer}
        onClose={() => setShowLogViewer(false)}
      />

      {/* Debug Toggle Button (only in development) */}
      {process.env.NODE_ENV === 'development' && (
        <button
          onClick={() => setShowLogViewer(true)}
          className='fixed bottom-4 right-4 bg-blue-600 text-white p-2 rounded-full shadow-lg hover:bg-blue-700 z-40'
          title='Open Debug Logs (Ctrl+Shift+L)'
        >
          🐛
        </button>
      )}
    </>
  );
};

export default App;
