{"validation": {"required": "<PERSON><PERSON> ist erford<PERSON>lich", "invalidEmail": "Bitte geben Si<PERSON> eine gültige E-Mail-Adresse ein", "invalidPhone": "Bitte geben Sie eine gültige Schweizer Telefonnummer ein (z.B. +41 79 123 45 67)", "invalidAmount": "Bitte geben Si<PERSON> einen gültigen Betrag in CHF ein", "invalidPercentage": "Bitte geben Sie einen Prozentsatz zwischen 0 und 100 ein", "minAmount": "Der Betrag muss mindestens CHF {{min}} sein", "maxAmount": "Der Betrag darf CHF {{max}} nicht überschreiten", "minValue": "Der <PERSON>rt muss mindestens {{min}} sein", "maxValue": "Der Wert darf {{max}} nicht überschreiten", "invalidAge": "Bitte geben Sie ein gültiges Alter zwischen 18 und 100 Jahren ein", "invalidDate": "<PERSON>te geben Si<PERSON> ein gültiges Datum ein (TT.MM.JJJJ)", "futureDate": "Das Datum darf nicht in der Zukunft liegen", "pastDate": "Das Datum darf nicht in der Vergangenheit liegen", "invalidFormat": "Das Format ist ungültig", "invalidPostalCode": "Bitte geben Si<PERSON> eine gültige Schweizer Postleitzahl ein (4 Ziffern)"}, "calculation": {"insufficientData": "Unzureichende Daten für Berechnung", "invalidInput": "Ungültige Eingabeparameter", "calculationError": "Fehler bei der Berechnung", "negativeResult": "Berechnung ergab negativen Wert", "divisionByZero": "Division durch <PERSON><PERSON>", "overflow": "Zahl zu gross für Berechnung"}, "data": {"loadError": "Fehler beim Laden der Daten", "saveError": "Fehler beim Speichern der Daten", "corruptedData": "Daten scheinen beschädigt zu sein", "missingData": "Erforderliche Daten fehlen", "invalidData": "Datenformat ist ungültig", "outdatedData": "Daten könnten veraltet sein"}, "network": {"connectionError": "Netzwerkverbindungsfehler", "timeout": "Anfrage-Timeout", "serverError": "Serverfehler aufgetreten", "notFound": "Ressource nicht gefunden", "unauthorized": "Unbefugter Zugriff", "forbidden": "Zugriff verboten"}, "application": {"unexpectedError": "Ein unerwarteter Fehler ist aufgetreten", "featureUnavailable": "Diese Funktion ist derzeit nicht verfügbar", "browserNotSupported": "<PERSON><PERSON> Browser wird nicht unterstützt", "javascriptDisabled": "JavaScript muss aktiviert sein", "cookiesDisabled": "Cookies müssen aktiviert sein", "localStorageUnavailable": "Lokaler Speicher ist nicht verfügbar"}, "swiss": {"invalidCanton": "Bitte wählen Sie einen gültigen Schweizer Kanton aus", "pillar3aExceeded": "Der Säule 3a Beitrag überschreitet die gesetzliche Jahresgrenze von CHF 7'056", "invalidTaxYear": "Bitte wählen Sie ein gültiges Steuerjahr (aktuelles oder vergangenes Jahr)", "missingTaxData": "Steuerdaten für den gewählten Kanton sind derzeit nicht verfügbar", "invalidCivilStatus": "Bitte wählen Si<PERSON> einen gültigen Zivilstand aus", "invalidFranchise": "Bitte wählen Sie eine gültige Krankenversicherungs-Franchise aus", "invalidWorkPercentage": "Das Arbeitspensum muss zwischen 10% und 100% liegen"}, "messages": {"tryAgain": "Bitte versuchen Sie es erneut", "contactSupport": "Falls das Problem weiterhin besteht, kontaktieren Sie den Support", "checkConnection": "Bitte überprüfen Sie Ihre Internetverbindung", "refreshPage": "Versuchen Sie, die Seite zu aktualisieren", "clearCache": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON>, <PERSON><PERSON>-<PERSON><PERSON> zu leeren", "updateBrowser": "Bitte aktualisieren Sie Ihren Browser"}, "actions": {"retry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cancel": "Abbrechen", "close": "<PERSON><PERSON><PERSON><PERSON>", "reload": "Neu laden", "goBack": "Zurück gehen", "contactSupport": "Support kontaktieren"}}