{"title": "Intelligente Automatisierungs-Engine", "description": "Generieren Sie personalisierte Finanzstrategien, individuelle Rechner und schweizspezifische Optimierungen mit unserem fortschrittlichen regelbasierten Automatisierungssystem.", "profile": {"title": "Ihr Finanzprofil", "age": "Alter", "canton": "<PERSON><PERSON>", "fire_target": "FIRE-Ziel", "retirement_age": "Pensionsalter"}, "buttons": {"generate_strategy": "Intelligente Strategie generieren", "generating": "Generiere...", "use_calculator": "<PERSON><PERSON><PERSON> verwenden", "view_code": "Code anzeigen", "download_report": "<PERSON><PERSON>t herunterladen", "export_strategy": "Strategie exportieren"}, "status": {"initializing": "Automatisierungs-Engine wird initialisiert...", "analyzing": "Ihr Finanzprofil wird analysiert...", "generating_strategy": "Personalisierte Strategie wird generiert...", "creating_calculators": "Individuelle Rechner werden erstellt...", "optimizing": "Optimierung für Schweizer Vorschriften...", "finalizing": "Empfehlungen werden finalisiert...", "completed": "Strategiegenerierung erfolgreich abgeschlossen!"}, "errors": {"no_user_data": "Benutzerdaten sind erford<PERSON>lich, um eine Strategie zu generieren", "generation_failed": "Strategiegenerierung fehlgeschlagen. Bitte versuchen Sie es erneut.", "calculator_generation_failed": "Rechner-Generierung fehlgeschlagen. Bitte versuchen Sie es erneut.", "invalid_canton": "Ungültiger Kanton angegeben", "insufficient_data": "Unzureichende Finanzdaten für eine genaue Analyse"}, "results": {"strategy_title": "Generierte Finanzstrategie", "calculators_title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "optimizations_title": "Schweizer Optimierungen", "compliance_status": "Schweizer Compliance", "calculator_description": "Personalisierter {{type}}-Rechner für Ihre spezifische Situation generiert", "confidence_score": "Vertrauenswert", "estimated_savings": "Geschätzte jährliche Einsparungen"}, "calculators": {"fire": "FIRE-<PERSON><PERSON><PERSON>", "tax": "Steuer-Optimierer", "healthcare": "Gesundheitskosten-Optimierer", "investment": "Investment-Analyzer", "mortgage": "Hypotheken-Re<PERSON>ner"}, "optimization": {"difficulty": "Schwierigkeit", "timeframe": "Zeitrahmen", "potential_savings": "Potenzielle Einsparungen", "implementation_steps": "Umsetzungsschritte", "automation_available": "Automatisierung verfügbar"}, "quick_actions": {"title": "Schnellaktionen", "fire_calculator": "FIRE-<PERSON><PERSON><PERSON>", "tax_optimizer": "Steuer-Optimierer", "healthcare_optimizer": "Gesundheitskosten-Optimierer", "investment_analyzer": "Investment-Analyzer"}, "strategy_types": {"conservative": "Konservative Strategie", "moderate": "Moderate Strategie", "aggressive": "Aggressive Strategie", "custom": "Individuelle Strategie"}, "recommendations": {"title": "Personalisierte Empfehlungen", "high_priority": "Hohe Priorität", "medium_priority": "Mittlere Priorität", "low_priority": "Niedrige Priorität", "tax_optimization": "Steueroptimierung", "investment_strategy": "Anlagestrategie", "healthcare_savings": "Gesundheitskosteneinsparungen", "retirement_planning": "Pensionsplanung"}, "automation_features": {"title": "Automatisierungsfunktionen", "code_generation": "Dynamische Code-Generierung", "rule_based_decisions": "Regelbasierte Entscheidungsfindung", "swiss_compliance": "Schweizer Compliance-Validierung", "personalized_calculators": "Personalisierte Rechner", "optimization_engine": "Optimierungs-Engine", "workflow_automation": "Workflow-Automatisierung"}, "swiss_specific": {"title": "Schweizspezifische Funktionen", "cantonal_optimization": "Kantonale Steueroptimierung", "pillar_3a": "Säule 3a Optimierung", "healthcare_franchise": "Krankenkassen-Franchise-Optimierung", "withholding_tax": "Verrechnungssteuer-Strategien", "cross_border": "Grenzüberschreitende Überlegungen"}, "generated_code": {"title": "Generierter Code", "language": "<PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON>", "generated_at": "Generiert am", "version": "Version", "dependencies": "Abhängigkeiten", "security_score": "Sicherheitswert", "performance_score": "Leistungswert", "test_coverage": "Testabdeckung"}, "workflows": {"title": "Automatisierungs-Workflows", "monthly_review": "Monatliche Finanzüberprüfung", "quarterly_rebalancing": "Quartalsweise Portfolio-Neugewichtung", "annual_tax_optimization": "Jährliche Steueroptimierung", "goal_tracking": "Zielverfolgung", "risk_monitoring": "Risikoüberwachung"}, "compliance": {"title": "Schweizer Compliance-Status", "compliant": "Konform", "warning": "<PERSON><PERSON><PERSON>", "non_compliant": "Nicht konform", "regulations_checked": "Überprüfte Vorschriften", "compliance_score": "Compliance-Wert", "last_updated": "Zuletzt aktualisiert"}, "performance": {"title": "Leistungsmetriken", "generation_time": "Generierungszeit", "code_quality": "Code-Qualität", "optimization_level": "Optimierungsgrad", "memory_usage": "Speicherverbrauch", "execution_speed": "Ausführungsgeschwindigkeit"}, "export": {"title": "Export-Optionen", "pdf_report": "PDF-Bericht", "excel_spreadsheet": "Excel-Tabelle", "json_data": "JSON-Daten", "typescript_code": "TypeScript-Code", "documentation": "Dokumentation"}, "help": {"title": "Hilfe & Dokumentation", "getting_started": "<PERSON><PERSON><PERSON>", "user_guide": "Benutzerhandbuch", "api_reference": "API-Referenz", "examples": "<PERSON><PERSON><PERSON><PERSON>", "troubleshooting": "Fehlerbehebung", "contact_support": "Support kontaktieren"}, "advanced": {"title": "Erweiterte Optionen", "custom_rules": "Benutzerdefinierte Regeln", "api_integration": "API-Integration", "webhook_configuration": "Webhook-Konfiguration", "batch_processing": "Stapelverarbeitung", "data_export": "Datenexport"}, "security": {"title": "Sicherheitsfunktionen", "code_validation": "Code-Validierung", "input_sanitization": "Eingabe-Bereinigung", "audit_trail": "Audit-Trail", "encryption": "Datenverschlüsselung", "access_control": "Zugriffskontrolle"}, "notifications": {"strategy_generated": "Ihre personalisierte Strategie wurde erfolgreich generiert!", "calculator_ready": "Ihr individueller Rechner ist einsatzbereit.", "optimization_found": "Neue Optimierungsmöglichkeit gefunden!", "compliance_warning": "Compliance-Warnung in Ihrer Strategie erkannt.", "automation_completed": "Automatisierungs-Workflow erfolgreich abgeschlossen."}, "tooltips": {"fire_calculator": "Generieren Sie einen personalisierten FIRE-Rechner basierend auf Ihrem Finanzprofil und Schweizer Vorschriften", "tax_optimizer": "<PERSON>rst<PERSON>n Sie eine Steueroptimierungsstrategie, die auf Ihren Kanton und Ihr Einkommensniveau zugeschnitten ist", "healthcare_optimizer": "Optimieren Sie Ihre Krankenkassenkosten mit Franchise- und Prämienanalyse", "compliance_score": "<PERSON><PERSON>, wie gut Ihre Strategie mit den Schweizer Finanzvorschriften übereinstimmt", "confidence_score": "Zeigt die Zuverlässigkeit der generierten Empfehlungen basierend auf der Datenqualität an"}}