{"income": {"title": "Einkommen", "monthlyIncome": "Mona<PERSON>einkommen", "annualIncome": "Jahreseinkommen", "grossIncome": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "netIncome": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "primaryEmployment": "Hauptanstellung", "companyIncome": "Einkommen aus Selbständigkeit", "contractWork": "Nebentätigkeiten", "investmentIncome": "Kapitalerträge", "passiveIncome": "Passive Einkünfte", "workTimePercentage": "Arbeitspensum", "growthRate": "Jährliche Lohnsteigerung", "startYear": "<PERSON><PERSON>r", "salaryIncrease": "Lohnerhöhung", "bonus": "Bonus/13. <PERSON><PERSON><PERSON><PERSON>", "overtime": "Überstunden"}, "expenses": {"title": "Ausgaben", "monthlyExpenses": "Monatsausgaben", "annualExpenses": "Jahresausgaben", "essential": "Notwendige Ausgaben", "discretionary": "Freiwillige Ausgaben", "housing": "Wohnen & Nebenkosten", "food": "Lebensmittel & Haushalt", "transportation": "Mobilität & Transport", "insurance": "Versicherungen", "healthcare": "Gesundheitskosten", "entertainment": "Freizeit & Unterhaltung", "travel": "Ferien & Reisen", "shopping": "Kleidung & persönliche Ausgaben", "other": "Sonstige Ausgaben", "category": "Ausgabenkategorie", "amount": "Betrag", "rent": "Miete/Hypothek", "utilities": "<PERSON><PERSON><PERSON><PERSON>", "taxes": "Steuern", "childcare": "Kinderbetreuung"}, "savings": {"title": "Ersparnisse", "totalSavings": "Gesamtersparnisse", "currentSavings": "Aktuelle Ersparnisse", "monthlySavings": "Monatliche Ersparnisse", "savingsRate": "Sparquote", "emergencyFund": "Notfallfonds", "investmentPortfolio": "Anlageportfolio", "retirementSavings": "Altersvorsorge", "targetAmount": "Zielbetrag", "timeHorizon": "Zeithorizont"}, "investments": {"title": "<PERSON><PERSON><PERSON>", "portfolio": "Anlageportfolio", "stocks": "Aktien", "bonds": "Obligationen", "etfs": "ETFs (Indexfonds)", "realEstate": "Immobilien", "commodities": "<PERSON><PERSON><PERSON><PERSON>", "cash": "Liquidität", "allocation": "Vermögensaufteilung", "returns": "Ren<PERSON>en", "expectedReturn": "<PERSON><PERSON><PERSON><PERSON> Rendite", "historicalReturn": "Historische Rendite", "riskLevel": "R<PERSON><PERSON>iveau", "volatility": "Schwankungsbreite", "diversification": "Diversifikation", "rebalancing": "Rebalancing", "fees": "Gebühren", "performance": "Wertentwicklung"}, "calculations": {"title": "Berechnungen", "fireNumber": "FIRE-<PERSON><PERSON>", "monthsToFire": "Monate bis FIRE", "retirementAge": "Pensionsalter", "withdrawalRate": "Entnahmerate", "safeWithdrawalRate": "Sichere Entnahmerate", "projectedValue": "Prognostizierter Wert", "futureValue": "Zukunftswert", "presentValue": "Barwert", "compoundGrowth": "Zinseszins", "inflation": "Inflation", "realReturn": "Realrendite", "nominalReturn": "Nominalrendite"}, "analysis": {"title": "Analyse", "scenario": "<PERSON><PERSON><PERSON>", "bestCase": "<PERSON><PERSON>", "worstCase": "Schlechtest<PERSON> Szenario", "mostLikely": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "probability": "Wahrscheinlichkeit", "confidence": "<PERSON><PERSON><PERSON><PERSON>", "monteCarlo": "Monte Carlo", "simulation": "Simulation", "results": "Ergebnisse", "summary": "Zusammenfassung", "breakdown": "Aufschlüsselung", "comparison": "Vergleich"}, "performance": {"title": "Performance", "ytd": "Jahr bis heute", "oneYear": "1 Jahr", "threeYear": "3 Jahre", "fiveYear": "5 Jahre", "tenYear": "10 Jahre", "inception": "Seit Beginn", "benchmark": "Benchmark", "outperformance": "Überperformance", "underperformance": "Unterperformance", "tracking": "Tracking"}}