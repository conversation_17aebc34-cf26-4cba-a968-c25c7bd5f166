{"title": "Intelligent Automation Engine", "description": "Generate personalized financial strategies, custom calculators, and Swiss-specific optimizations using our advanced rule-based automation system.", "profile": {"title": "Your Financial Profile", "age": "Age", "canton": "Canton", "fire_target": "FIRE Target", "retirement_age": "Retirement Age"}, "buttons": {"generate_strategy": "Generate Intelligent Strategy", "generating": "Generating...", "use_calculator": "Use Calculator", "view_code": "View Code", "download_report": "Download Report", "export_strategy": "Export Strategy"}, "status": {"initializing": "Initializing automation engine...", "analyzing": "Analyzing your financial profile...", "generating_strategy": "Generating personalized strategy...", "creating_calculators": "Creating custom calculators...", "optimizing": "Optimizing for Swiss regulations...", "finalizing": "Finalizing recommendations...", "completed": "Strategy generation completed successfully!"}, "errors": {"no_user_data": "User data is required to generate strategy", "generation_failed": "Failed to generate strategy. Please try again.", "calculator_generation_failed": "Failed to generate calculator. Please try again.", "invalid_canton": "Invalid canton specified", "insufficient_data": "Insufficient financial data for accurate analysis"}, "results": {"strategy_title": "Generated Financial Strategy", "calculators_title": "Custom Calculators", "optimizations_title": "Swiss Optimizations", "compliance_status": "Swiss Compliance", "calculator_description": "Personalized {{type}} calculator generated for your specific situation", "confidence_score": "Confidence Score", "estimated_savings": "Estimated Annual Savings"}, "calculators": {"fire": "FIRE Calculator", "tax": "Tax Optimizer", "healthcare": "Healthcare Cost Optimizer", "investment": "Investment Analyzer", "mortgage": "Mortgage Calculator"}, "optimization": {"difficulty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "timeframe": "Timeframe", "potential_savings": "Potential Savings", "implementation_steps": "Implementation Steps", "automation_available": "Automation Available"}, "quick_actions": {"title": "Quick Actions", "fire_calculator": "FIRE Calculator", "tax_optimizer": "Tax Optimizer", "healthcare_optimizer": "Healthcare Optimizer", "investment_analyzer": "Investment Analyzer"}, "strategy_types": {"conservative": "Conservative Strategy", "moderate": "Moderate Strategy", "aggressive": "Aggressive Strategy", "custom": "Custom Strategy"}, "recommendations": {"title": "Personalized Recommendations", "high_priority": "High Priority", "medium_priority": "Medium Priority", "low_priority": "Low Priority", "tax_optimization": "Tax Optimization", "investment_strategy": "Investment Strategy", "healthcare_savings": "Healthcare Savings", "retirement_planning": "Retirement Planning"}, "automation_features": {"title": "Automation Features", "code_generation": "Dynamic Code Generation", "rule_based_decisions": "Rule-Based Decision Making", "swiss_compliance": "Swiss Compliance Validation", "personalized_calculators": "Personalized Calculators", "optimization_engine": "Optimization Engine", "workflow_automation": "Workflow Automation"}, "swiss_specific": {"title": "Swiss-Specific Features", "cantonal_optimization": "Cantonal Tax Optimization", "pillar_3a": "Pillar 3a Optimization", "healthcare_franchise": "Healthcare Franchise Optimization", "withholding_tax": "Withholding Tax Strategies", "cross_border": "Cross-Border Considerations"}, "generated_code": {"title": "Generated Code", "language": "Language", "type": "Type", "generated_at": "Generated At", "version": "Version", "dependencies": "Dependencies", "security_score": "Security Score", "performance_score": "Performance Score", "test_coverage": "Test Coverage"}, "workflows": {"title": "Automation Workflows", "monthly_review": "Monthly Financial Review", "quarterly_rebalancing": "Quarterly Portfolio Rebalancing", "annual_tax_optimization": "Annual Tax Optimization", "goal_tracking": "Goal Progress Tracking", "risk_monitoring": "Risk Monitoring"}, "compliance": {"title": "Swiss Compliance Status", "compliant": "Compliant", "warning": "Warning", "non_compliant": "Non-Compliant", "regulations_checked": "Regulations Checked", "compliance_score": "Compliance Score", "last_updated": "Last Updated"}, "performance": {"title": "Performance Metrics", "generation_time": "Generation Time", "code_quality": "Code Quality", "optimization_level": "Optimization Level", "memory_usage": "Memory Usage", "execution_speed": "Execution Speed"}, "export": {"title": "Export Options", "pdf_report": "PDF Report", "excel_spreadsheet": "Excel Spreadsheet", "json_data": "JSON Data", "typescript_code": "TypeScript Code", "documentation": "Documentation"}, "help": {"title": "Help & Documentation", "getting_started": "Getting Started", "user_guide": "User Guide", "api_reference": "API Reference", "examples": "Examples", "troubleshooting": "Troubleshooting", "contact_support": "Contact Support"}, "advanced": {"title": "Advanced Options", "custom_rules": "Custom Rules", "api_integration": "API Integration", "webhook_configuration": "Webhook Configuration", "batch_processing": "Batch Processing", "data_export": "Data Export"}, "security": {"title": "Security Features", "code_validation": "Code Validation", "input_sanitization": "Input Sanitization", "audit_trail": "Audit Trail", "encryption": "Data Encryption", "access_control": "Access Control"}, "notifications": {"strategy_generated": "Your personalized strategy has been generated successfully!", "calculator_ready": "Your custom calculator is ready to use.", "optimization_found": "New optimization opportunity found!", "compliance_warning": "Compliance warning detected in your strategy.", "automation_completed": "Automation workflow completed successfully."}, "tooltips": {"fire_calculator": "Generate a personalized FIRE calculator based on your financial profile and Swiss regulations", "tax_optimizer": "Create a tax optimization strategy tailored to your canton and income level", "healthcare_optimizer": "Optimize your healthcare insurance costs with franchise and premium analysis", "compliance_score": "Measures how well your strategy aligns with Swiss financial regulations", "confidence_score": "Indicates the reliability of the generated recommendations based on data quality"}}