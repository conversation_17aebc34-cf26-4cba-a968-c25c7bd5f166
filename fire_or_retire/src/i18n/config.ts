/**
 * i18n Configuration for Swiss Budget Pro
 * Supports German, French, Italian, and English for Switzerland
 */

import i18n from 'i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import { initReactI18next } from 'react-i18next';

// Translation resources
const resources = {
  'de-CH': {
    translation: {
      // Fire or Retire Calculator translations
      fire: {
        title: 'Fire or Retire Calculator',
        subtitle: 'Swiss Budget Pro - Finanzplanungs-Anwendung',
        tabs: {
          dashboard: 'Dashboard',
          planning: 'Planung',
          analysis: 'Analyse',
          healthcare: 'Gesundheit',
          advanced: 'Erweitert',
        },
        metrics: {
          currentSavings: 'Aktuelle Ersparnisse',
          fireProgress: 'FIRE Fortschritt',
          yearsToRetirement: 'Jahre bis Rente',
          savingsRate: 'Sparquote',
          monthlyIncome: 'Monatseinkommen',
        },
        planning: {
          basicInfo: 'Grundinformationen',
          currentAge: 'Aktuelles Alter',
          retirementAge: 'Gewünschtes Rentenalter',
          currentSavings: 'Aktuelle Ersparnisse (CHF)',
          incomeExpenses: 'Einkommen & Ausgaben',
          monthlyIncome: 'Monatseinkommen (CHF)',
          monthlyExpenses: 'Monatsausgaben (CHF)',
          monthlySavings: 'Monatliche Ersparnisse',
        },
        analysis: {
          title: 'Finanzanalyse',
          assumptions: 'Anlage-Annahmen',
          expectedReturn: 'Erwartete Jahresrendite (%)',
          inflationRate: 'Inflationsrate (%)',
          withdrawalRate: 'Entnahmerate (%)',
          projections: 'FIRE Projektionen',
          targetAmount: 'Ziel FIRE Betrag',
          yearsToFire: 'Jahre bis FIRE',
          retirementIncome: 'Monatliches Renteneinkommen',
        },
        quickActions: {
          title: 'Schnellaktionen',
          updateFinances: 'Einkommen & Ausgaben aktualisieren',
          viewProjections: 'Projektionen anzeigen',
          optimizeHealthcare: 'Gesundheitskosten optimieren',
        },
      },
      language: {
        switch: 'Sprache wechseln',
        current: 'Aktuelle Sprache',
      },
      admin: {
        title: 'Swiss Budget Pro Admin',
        subtitle: 'System-Konfiguration & Verwaltung',
        lastSaved: 'Zuletzt gespeichert',
        unsavedChanges: 'Ungespeicherte Änderungen',
        tabs: {
          inflation: 'Inflation',
          market: 'Marktdaten',
          tax: 'Steuern',
          healthcare: 'Gesundheit',
          system: 'System',
          importExport: 'Import/Export',
          audit: 'Audit-Log',
        },
        auth: {
          title: 'Admin-Zugang',
          subtitle: 'Swiss Budget Pro Administration',
          username: 'Benutzername',
          password: 'Passwort',
          login: 'Anmelden',
          logout: 'Abmelden',
          loading: 'Laden...',
          demoCredentials: 'Demo-Zugangsdaten',
        },
        inflation: {
          title: 'Inflations-Konfiguration',
          description:
            'Konfiguration der Inflationsraten und wirtschaftlichen Annahmen',
          currentCPI: 'Aktueller VPI',
          coreCPI: 'Kern-VPI',
          historicalAverage: 'Historischer Durchschnitt',
          targetInflation: 'SNB-Ziel',
          housingCosts: 'Wohnkosten',
          healthcareCosts: 'Gesundheitskosten',
          energyCosts: 'Energiekosten',
          foodCosts: 'Lebensmittelkosten',
          forecast12M: '12-Monats-Prognose',
          forecast24M: '24-Monats-Prognose',
          dataSource: 'Datenquelle & Updates',
          source: 'Datenquelle',
          lastUpdated: 'Zuletzt aktualisiert',
          autoUpdate: 'Auto-Update von Quelle',
          refresh: 'Jetzt aktualisieren',
        },
        market: {
          title: 'Marktdaten-Konfiguration',
          description:
            'Konfiguration von Marktindizes, erwarteten Renditen und Risikoannahmen',
          smiIndex: 'SMI Index',
          smiChange: 'SMI Veränderung',
          spiIndex: 'SPI Index',
          bondYield10Y: '10-Jahr Anleihenrendite',
          bondYield2Y: '2-Jahr Anleihenrendite',
          volatilityIndex: 'Volatilitätsindex',
          expectedReturns: 'Erwartete Jahresrenditen',
          stockReturns: 'Aktien',
          bondReturns: 'Anleihen',
          cashReturns: 'Bargeld',
          realEstateReturns: 'Immobilien',
          commodityReturns: 'Rohstoffe',
        },
        tax: {
          title: 'Steuer-Konfiguration',
          description:
            'Konfiguration der Schweizer Bundes- und Kantonssteuersätze',
          federalRates: 'Bundessteuer-Stufen',
          cantonalRates: 'Kantonale Steuersätze',
          socialInsurance: 'Sozialversicherungsbeiträge',
          pillar3a: 'Säule 3a (Private Altersvorsorge)',
          addBracket: 'Stufe hinzufügen',
          remove: 'Entfernen',
          bracket: 'Stufe',
          minIncome: 'Min. Einkommen',
          maxIncome: 'Max. Einkommen',
          taxRate: 'Steuersatz',
          selectCanton: 'Kanton auswählen',
          cantonalRate: 'Kantonaler Steuersatz',
          wealthTax: 'Vermögenssteuersatz',
          wealthTaxThreshold: 'Vermögenssteuer-Schwelle',
        },
        healthcare: {
          title: 'Gesundheits-Konfiguration',
          description:
            'Konfiguration von Gesundheitsprämien, Selbstbehalten und Subventionsschwellen',
          deductibles: 'Gesundheits-Selbstbehalte',
          cantonalPremiums: 'Kantonale Gesundheitsprämien',
          subsidyThresholds: 'Prämienverbilligungs-Schwellen',
          costInflation: 'Gesundheitskosten-Inflation',
          addDeductible: 'Selbstbehalt hinzufügen',
          selectCanton: 'Kanton auswählen',
          averagePremium: 'Durchschnittsprämie',
          premiumRange: 'Prämienbereich',
          minPremium: 'Min',
          maxPremium: 'Max',
          singleThreshold: 'Einzelperson',
          coupleThreshold: 'Paar',
          familyThreshold: 'Familie',
          annualInflation: 'Jährliche Gesundheitsinflation',
        },
        system: {
          title: 'System-Konfiguration',
          description:
            'Konfiguration von System-Standards, Features und Leistungseinstellungen',
          calculationDefaults: 'Berechnungs-Standards',
          userInterface: 'Benutzeroberflächen-Einstellungen',
          features: 'Feature-Schalter',
          performance: 'Leistungseinstellungen',
          withdrawalRate: 'Standard-Entnahmerate',
          safeWithdrawalRate: 'Sichere Entnahmerate',
          conservativeWithdrawalRate: 'Konservative Entnahmerate',
          investmentReturn: 'Erwartete Anlagerendite',
          bondReturn: 'Erwartete Anleihenrendite',
          cashReturn: 'Erwartete Bargeldrendite',
          defaultLanguage: 'Standardsprache',
          defaultCurrency: 'Standardwährung',
          decimalSeparator: 'Dezimaltrennzeichen',
          thousandsSeparator: 'Tausendertrennzeichen',
          dateFormat: 'Datumsformat',
          enableAdvancedCalculations: 'Erweiterte Berechnungen',
          enableTaxOptimization: 'Steueroptimierung',
          enableHealthcareOptimization: 'Gesundheitsoptimierung',
          enableCantonComparison: 'Kantonsvergleich',
          enableExportFeatures: 'Export-Features',
          autoSaveInterval: 'Auto-Speicher-Intervall',
          calculationTimeout: 'Berechnungs-Timeout',
          maxHistoryEntries: 'Max. Verlaufseinträge',
        },
        importExport: {
          title: 'Konfigurations-Import/Export',
          description:
            'Sicherung und Wiederherstellung von Systemkonfigurationseinstellungen',
          currentConfig: 'Aktuelle Konfiguration',
          export: 'Konfiguration exportieren',
          import: 'Konfiguration importieren',
          version: 'Version',
          lastModified: 'Zuletzt geändert',
          configSize: 'Konfigurationsgröße',
          sections: 'Konfigurationsabschnitte',
          exportDescription:
            'Laden Sie die aktuelle Konfiguration als JSON-Datei herunter',
          importDescription: 'Laden Sie eine Konfigurations-JSON-Datei hoch',
          dropZone: 'Konfigurationsdatei hier ablegen',
          selectFile: 'Datei auswählen',
          importing: 'Konfiguration wird importiert...',
          importSuccess: 'Konfiguration erfolgreich importiert!',
          exportSuccess: 'Konfiguration erfolgreich exportiert!',
        },
        audit: {
          title: 'Konfigurations-Audit-Log',
          description:
            'Verfolgung aller Konfigurationsänderungen und Systemmodifikationen',
          filters: 'Filter & Suche',
          filterAction: 'Nach Aktion filtern',
          filterSection: 'Nach Abschnitt filtern',
          search: 'Suchen',
          searchPlaceholder:
            'Beschreibungen, Benutzer, Abschnitte durchsuchen...',
          showing: 'Zeige',
          of: 'von',
          entries: 'Einträgen',
          noEntries: 'Keine Audit-Einträge',
          noMatchingEntries:
            'Keine Einträge entsprechen Ihren aktuellen Filtern.',
          noEntriesYet:
            'Es wurden noch keine Konfigurationsänderungen protokolliert.',
          statistics: 'Audit-Statistiken',
          oldValue: 'Alter Wert',
          newValue: 'Neuer Wert',
          export: 'Log exportieren',
          allActions: 'Alle Aktionen',
          allSections: 'Alle Abschnitte',
        },
        validation: {
          inflationRange: 'Inflationsrate muss zwischen -10% und 20% liegen',
          sectorInflationRange:
            'Sektorinflation muss zwischen -20% und 50% liegen',
          forecastRange: 'Prognose muss zwischen -5% und 15% liegen',
          taxRateRange: 'Steuersatz muss zwischen 0% und 100% liegen',
          incomeRange: 'Einkommen muss zwischen 0 und 10.000.000 liegen',
          amountRange: 'Betrag muss zwischen {min} und {max} liegen',
          percentageRange: 'Prozentsatz muss zwischen 0% und 100% liegen',
          timeoutRange: 'Timeout muss zwischen 1 und 300 Sekunden liegen',
          intervalRange: 'Intervall muss zwischen 5 und 300 Sekunden liegen',
        },
        resetConfirm: {
          title: 'Auf Standards zurücksetzen',
          message:
            'Dies setzt alle Konfigurationen auf Standardwerte zurück. Diese Aktion kann nicht rückgängig gemacht werden.',
          cancel: 'Abbrechen',
          confirm: 'Alle zurücksetzen',
        },
      },
    },
  },
  'en-CH': {
    translation: {
      // Fire or Retire Calculator translations
      fire: {
        title: 'Fire or Retire Calculator',
        subtitle: 'Swiss Budget Pro - Financial Planning Application',
        tabs: {
          dashboard: 'Dashboard',
          planning: 'Planning',
          analysis: 'Analysis',
          healthcare: 'Healthcare',
          advanced: 'Advanced',
        },
        metrics: {
          currentSavings: 'Current Savings',
          fireProgress: 'FIRE Progress',
          yearsToRetirement: 'Years to Retirement',
          savingsRate: 'Savings Rate',
          monthlyIncome: 'Monthly Income',
        },
        planning: {
          basicInfo: 'Basic Information',
          currentAge: 'Current Age',
          retirementAge: 'Target Retirement Age',
          currentSavings: 'Current Savings (CHF)',
          incomeExpenses: 'Income & Expenses',
          monthlyIncome: 'Monthly Income (CHF)',
          monthlyExpenses: 'Monthly Expenses (CHF)',
          monthlySavings: 'Monthly Savings',
        },
        analysis: {
          title: 'Financial Analysis',
          assumptions: 'Investment Assumptions',
          expectedReturn: 'Expected Annual Return (%)',
          inflationRate: 'Inflation Rate (%)',
          withdrawalRate: 'Withdrawal Rate (%)',
          projections: 'FIRE Projections',
          targetAmount: 'Target FIRE Amount',
          yearsToFire: 'Years to FIRE',
          retirementIncome: 'Monthly Retirement Income',
        },
        quickActions: {
          title: 'Quick Actions',
          updateFinances: 'Update Income & Expenses',
          viewProjections: 'View Projections',
          optimizeHealthcare: 'Optimize Healthcare',
        },
      },
      language: {
        switch: 'Switch Language',
        current: 'Current Language',
      },
      admin: {
        title: 'Swiss Budget Pro Admin',
        subtitle: 'System Configuration & Management',
        lastSaved: 'Last saved',
        unsavedChanges: 'Unsaved changes',
        tabs: {
          inflation: 'Inflation',
          market: 'Market Data',
          tax: 'Tax',
          healthcare: 'Healthcare',
          system: 'System',
          importExport: 'Import/Export',
          audit: 'Audit Log',
        },
        auth: {
          title: 'Admin Access',
          subtitle: 'Swiss Budget Pro Administration',
          username: 'Username',
          password: 'Password',
          login: 'Login',
          logout: 'Logout',
          loading: 'Loading...',
          demoCredentials: 'Demo Credentials',
        },
        inflation: {
          title: 'Inflation Configuration',
          description:
            'Configure inflation rates and economic assumptions for financial calculations',
          currentCPI: 'Current CPI',
          coreCPI: 'Core CPI',
          historicalAverage: 'Historical Average',
          targetInflation: 'SNB Target',
          housingCosts: 'Housing Costs',
          healthcareCosts: 'Healthcare Costs',
          energyCosts: 'Energy Costs',
          foodCosts: 'Food Costs',
          forecast12M: '12-Month Forecast',
          forecast24M: '24-Month Forecast',
          dataSource: 'Data Source & Updates',
          source: 'Data Source',
          lastUpdated: 'Last Updated',
          autoUpdate: 'Auto-update from source',
          refresh: 'Refresh Now',
        },
        market: {
          title: 'Market Data Configuration',
          description:
            'Configure market indices, expected returns, and risk assumptions for investment calculations',
          smiIndex: 'SMI Index',
          smiChange: 'SMI Change',
          spiIndex: 'SPI Index',
          bondYield10Y: '10-Year Bond Yield',
          bondYield2Y: '2-Year Bond Yield',
          volatilityIndex: 'Volatility Index',
          expectedReturns: 'Expected Annual Returns',
          stockReturns: 'Stocks',
          bondReturns: 'Bonds',
          cashReturns: 'Cash',
          realEstateReturns: 'Real Estate',
          commodityReturns: 'Commodities',
        },
        tax: {
          title: 'Tax Configuration',
          description:
            'Configure Swiss federal and cantonal tax rates, social insurance, and Pillar 3a settings',
          federalRates: 'Federal Tax Brackets',
          cantonalRates: 'Cantonal Tax Rates',
          socialInsurance: 'Social Insurance Contributions',
          pillar3a: 'Pillar 3a (Private Retirement Savings)',
          addBracket: 'Add Bracket',
          remove: 'Remove',
          bracket: 'Bracket',
          minIncome: 'Min Income',
          maxIncome: 'Max Income',
          taxRate: 'Tax Rate',
          selectCanton: 'Select Canton',
          cantonalRate: 'Cantonal Tax Rate',
          wealthTax: 'Wealth Tax Rate',
          wealthTaxThreshold: 'Wealth Tax Threshold',
        },
        healthcare: {
          title: 'Healthcare Configuration',
          description:
            'Configure healthcare premiums, deductibles, and subsidy thresholds for Swiss cantons',
          deductibles: 'Healthcare Deductibles',
          cantonalPremiums: 'Cantonal Healthcare Premiums',
          subsidyThresholds: 'Premium Subsidy Thresholds',
          costInflation: 'Healthcare Cost Inflation',
          addDeductible: 'Add Deductible',
          selectCanton: 'Select Canton',
          averagePremium: 'Average Premium',
          premiumRange: 'Premium Range',
          minPremium: 'Min',
          maxPremium: 'Max',
          singleThreshold: 'Single Person',
          coupleThreshold: 'Couple',
          familyThreshold: 'Family',
          annualInflation: 'Annual Healthcare Inflation',
        },
        system: {
          title: 'System Configuration',
          description:
            'Configure system defaults, features, and performance settings',
          calculationDefaults: 'Calculation Defaults',
          userInterface: 'User Interface Settings',
          features: 'Feature Toggles',
          performance: 'Performance Settings',
          withdrawalRate: 'Standard Withdrawal Rate',
          safeWithdrawalRate: 'Safe Withdrawal Rate',
          conservativeWithdrawalRate: 'Conservative Withdrawal Rate',
          investmentReturn: 'Expected Investment Return',
          bondReturn: 'Expected Bond Return',
          cashReturn: 'Expected Cash Return',
          defaultLanguage: 'Default Language',
          defaultCurrency: 'Default Currency',
          decimalSeparator: 'Decimal Separator',
          thousandsSeparator: 'Thousands Separator',
          dateFormat: 'Date Format',
          enableAdvancedCalculations: 'Advanced Calculations',
          enableTaxOptimization: 'Tax Optimization',
          enableHealthcareOptimization: 'Healthcare Optimization',
          enableCantonComparison: 'Canton Comparison',
          enableExportFeatures: 'Export Features',
          autoSaveInterval: 'Auto-Save Interval',
          calculationTimeout: 'Calculation Timeout',
          maxHistoryEntries: 'Max History Entries',
        },
        importExport: {
          title: 'Configuration Import/Export',
          description: 'Backup and restore system configuration settings',
          currentConfig: 'Current Configuration',
          export: 'Export Configuration',
          import: 'Import Configuration',
          version: 'Version',
          lastModified: 'Last Modified',
          configSize: 'Configuration Size',
          sections: 'Configuration Sections',
          exportDescription:
            'Download the current configuration as a JSON file for backup or transfer to another system.',
          importDescription:
            'Upload a configuration JSON file to restore or update system settings.',
          dropZone: 'Drop configuration file here',
          selectFile: 'Select File',
          importing: 'Importing configuration...',
          importSuccess: 'Configuration imported successfully!',
          exportSuccess: 'Configuration exported successfully!',
        },
        audit: {
          title: 'Configuration Audit Log',
          description:
            'Track all configuration changes and system modifications',
          filters: 'Filters & Search',
          filterAction: 'Filter by Action',
          filterSection: 'Filter by Section',
          search: 'Search',
          searchPlaceholder: 'Search descriptions, users, sections...',
          showing: 'Showing',
          of: 'of',
          entries: 'entries',
          noEntries: 'No Audit Entries',
          noMatchingEntries: 'No entries match your current filters.',
          noEntriesYet: 'No configuration changes have been logged yet.',
          statistics: 'Audit Statistics',
          oldValue: 'Old Value',
          newValue: 'New Value',
          export: 'Export Log',
          allActions: 'All Actions',
          allSections: 'All Sections',
        },
        validation: {
          inflationRange: 'Inflation rate must be between -10% and 20%',
          sectorInflationRange: 'Sector inflation must be between -20% and 50%',
          forecastRange: 'Forecast must be between -5% and 15%',
          taxRateRange: 'Tax rate must be between 0% and 100%',
          incomeRange: 'Income must be between 0 and 10,000,000',
          amountRange: 'Amount must be between {min} and {max}',
          percentageRange: 'Percentage must be between 0% and 100%',
          timeoutRange: 'Timeout must be between 1 and 300 seconds',
          intervalRange: 'Interval must be between 5 and 300 seconds',
        },
        resetConfirm: {
          title: 'Reset to Defaults',
          message:
            'This will reset all configuration to default values. This action cannot be undone.',
          cancel: 'Cancel',
          confirm: 'Reset All',
        },
      },
    },
  },
};

i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    lng: 'de-CH', // Set Swiss German as default language
    fallbackLng: 'de-CH', // Fallback to Swiss German
    debug: false,

    interpolation: {
      escapeValue: false, // React already does escaping
    },

    detection: {
      order: ['localStorage', 'navigator', 'htmlTag'],
      caches: ['localStorage'],
      lookupLocalStorage: 'swissBudgetPro_language',
    },
  });

export default i18n;
