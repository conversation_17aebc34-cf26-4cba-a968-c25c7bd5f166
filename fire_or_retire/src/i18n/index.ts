import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// Import translation resources
import enCommon from '../locales/en/common.json';
import enFinancial from '../locales/en/financial.json';
import enSwiss from '../locales/en/swiss.json';
import enForms from '../locales/en/forms.json';
import enReports from '../locales/en/reports.json';
import enErrors from '../locales/en/errors.json';

import deCommon from '../locales/de/common.json';
import deFinancial from '../locales/de/financial.json';
import deSwiss from '../locales/de/swiss.json';
import deForms from '../locales/de/forms.json';
import deReports from '../locales/de/reports.json';
import deErrors from '../locales/de/errors.json';

// Translation resources
const resources = {
  en: {
    common: enCommon,
    financial: enFinancial,
    swiss: enSwiss,
    forms: enForms,
    reports: enReports,
    errors: enErrors,
  },
  de: {
    common: deCommon,
    financial: deFinancial,
    swiss: deSwiss,
    forms: deForms,
    reports: deReports,
    errors: deErrors,
  },
};

// Language detection configuration
const detectionOptions = {
  // Order of language detection methods
  order: ['localStorage', 'navigator', 'htmlTag', 'path', 'subdomain'],

  // Cache user language preference
  caches: ['localStorage'],

  // localStorage key for language preference
  lookupLocalStorage: 'swissBudgetPro_language',

  // Don't detect from query string or cookie for security
  excludeCacheFor: ['cimode'],

  // Check all available languages
  checkWhitelist: true,
};

// Initialize i18next
i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    // Available languages
    supportedLngs: ['en', 'de'],

    // Fallback language
    fallbackLng: 'en',

    // Default namespace
    defaultNS: 'common',

    // Available namespaces
    ns: ['common', 'financial', 'swiss', 'forms', 'reports', 'errors'],

    // Translation resources
    resources,

    // Language detection
    detection: detectionOptions,

    // Interpolation options
    interpolation: {
      escapeValue: false, // React already escapes values
      formatSeparator: ',',
    },

    // React options
    react: {
      useSuspense: false, // Disable suspense for now
      bindI18n: 'languageChanged',
      bindI18nStore: '',
      transEmptyNodeValue: '',
      transSupportBasicHtmlNodes: true,
      transKeepBasicHtmlNodesFor: ['br', 'strong', 'i', 'em'],
    },

    // Debug mode (disable in production)
    debug: process.env.NODE_ENV === 'development',

    // Key separator
    keySeparator: '.',

    // Namespace separator
    nsSeparator: ':',

    // Return objects for missing keys
    returnObjects: false,

    // Return empty string for missing keys
    returnEmptyString: false,

    // Return null for missing keys
    returnNull: false,

    // Postprocess missing keys
    saveMissing: process.env.NODE_ENV === 'development',

    // Missing key handler for development
    missingKeyHandler: (lng, ns, key, fallbackValue) => {
      if (process.env.NODE_ENV === 'development') {
        console.warn(
          `Missing translation key: ${ns}:${key} for language: ${lng}`
        );
      }
    },
  });

export default i18n;
