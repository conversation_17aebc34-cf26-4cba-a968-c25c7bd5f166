import React, { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useGamification } from '../hooks/useGamification';
import GamificationHub from '../components/gamification/GamificationHub';
import TransactionTagger from '../components/gamification/TransactionTagger';
import { Transaction } from '../services/SmartTaggingService';

/**
 * Comprehensive example showing how to integrate the gamification system
 * into a Swiss financial planning application
 */

interface GamificationIntegrationExampleProps {
  userId: string;
  darkMode: boolean;
}

const GamificationIntegrationExample: React.FC<GamificationIntegrationExampleProps> = ({
  userId,
  darkMode,
}) => {
  const { t } = useTranslation();
  const [activeDemo, setActiveDemo] = useState<'hub' | 'transactions' | 'savings' | 'goals'>('hub');

  // Use the gamification hook
  const {
    progress,
    isLoading,
    error,
    recentLevelUp,
    recentAchievements,
    awardSavingsXP,
    awardGoalAchievementXP,
    awardSwissOptimizationXP,
    awardBudgetAdherenceXP,
    awardEducationXP,
    hasFeatureAccess,
    getAvailableFeatures,
  } = useGamification(userId);

  // Example: Savings contribution with gamification
  const handleSavingsContribution = useCallback(async (amount: number, isPillar3a: boolean = false) => {
    try {
      // Award XP for the savings contribution
      await awardSavingsXP(amount, isPillar3a);
      
      // Show success message
      console.log(`Savings contribution of CHF ${amount} recorded with XP reward!`);
      
      // Check for feature unlocks
      const features = await getAvailableFeatures();
      console.log('Available features:', features);
      
    } catch (error) {
      console.error('Failed to process savings contribution:', error);
    }
  }, [awardSavingsXP, getAvailableFeatures]);

  // Example: Goal achievement with gamification
  const handleGoalAchievement = useCallback(async (
    goalAmount: number,
    targetDate: Date,
    isSwissOptimized: boolean = false
  ) => {
    try {
      // Award XP for goal achievement
      await awardGoalAchievementXP(goalAmount, targetDate, isSwissOptimized);
      
      console.log(`Goal of CHF ${goalAmount} achieved with bonus XP!`);
      
    } catch (error) {
      console.error('Failed to process goal achievement:', error);
    }
  }, [awardGoalAchievementXP]);

  // Example: Swiss optimization actions
  const handleSwissOptimization = useCallback(async (
    type: 'tax' | 'healthcare' | 'canton_comparison' | 'pillar3a',
    amount?: number
  ) => {
    try {
      await awardSwissOptimizationXP(type, amount);
      
      console.log(`Swiss optimization (${type}) completed with bonus XP!`);
      
    } catch (error) {
      console.error('Failed to process Swiss optimization:', error);
    }
  }, [awardSwissOptimizationXP]);

  // Example: Budget adherence tracking
  const handleBudgetAdherence = useCallback(async (
    adherencePercentage: number,
    categoryCount: number = 1
  ) => {
    try {
      await awardBudgetAdherenceXP(adherencePercentage, categoryCount);
      
      console.log(`Budget adherence of ${adherencePercentage}% recorded with XP!`);
      
    } catch (error) {
      console.error('Failed to process budget adherence:', error);
    }
  }, [awardBudgetAdherenceXP]);

  // Example: Financial education completion
  const handleEducationCompletion = useCallback(async (
    moduleType: 'basic' | 'intermediate' | 'advanced' | 'swiss_specific',
    score?: number
  ) => {
    try {
      await awardEducationXP(moduleType, score);
      
      console.log(`Education module (${moduleType}) completed with XP reward!`);
      
    } catch (error) {
      console.error('Failed to process education completion:', error);
    }
  }, [awardEducationXP]);

  // Example transaction for tagging demo
  const exampleTransaction: Transaction = {
    id: 'tx_example_001',
    userId,
    amount: -150.50,
    description: 'Migros Supermarket Purchase',
    merchant: 'Migros',
    date: new Date(),
    type: 'expense',
    category: 'groceries',
    location: 'Zurich, Switzerland',
  };

  // Demo actions
  const demoActions = [
    {
      title: 'Savings Contribution',
      description: 'Simulate a CHF 500 savings contribution',
      action: () => handleSavingsContribution(500),
      icon: '💰',
    },
    {
      title: 'Pillar 3a Contribution',
      description: 'Simulate a CHF 588 Pillar 3a contribution (monthly max)',
      action: () => handleSavingsContribution(588, true),
      icon: '🏛️',
    },
    {
      title: 'Goal Achievement',
      description: 'Simulate achieving a CHF 10,000 emergency fund goal',
      action: () => handleGoalAchievement(10000, new Date(), false),
      icon: '🎯',
    },
    {
      title: 'Swiss Tax Optimization',
      description: 'Simulate using Swiss tax optimization features',
      action: () => handleSwissOptimization('tax'),
      icon: '📊',
    },
    {
      title: 'Canton Comparison',
      description: 'Simulate comparing different Swiss cantons',
      action: () => handleSwissOptimization('canton_comparison'),
      icon: '🗺️',
    },
    {
      title: 'Budget Adherence',
      description: 'Simulate 95% budget adherence across 5 categories',
      action: () => handleBudgetAdherence(95, 5),
      icon: '📈',
    },
    {
      title: 'Swiss Education',
      description: 'Simulate completing Swiss-specific financial education',
      action: () => handleEducationCompletion('swiss_specific', 95),
      icon: '🎓',
    },
  ];

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="gamification-integration-example">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2">
          🎮 {t('example.title', 'Gamification Integration Example')}
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mb-4">
          {t('example.description', 
            'This example demonstrates how to integrate the comprehensive gamification system into a Swiss financial planning application.'
          )}
        </p>

        {/* Current Progress Display */}
        {progress && (
          <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'} mb-4`}>
            <div className="flex items-center justify-between">
              <div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Current Progress</div>
                <div className="text-lg font-bold">
                  Level {progress.currentLevel} • {progress.totalXP.toLocaleString()} XP
                </div>
              </div>
              <div className="text-right">
                <div className="text-sm text-gray-600 dark:text-gray-400">Next Level</div>
                <div className="text-lg font-bold">
                  {progress.progressToNextLevel.toFixed(1)}%
                </div>
              </div>
            </div>
            <div className={`w-full bg-gray-300 rounded-full h-2 mt-2 ${darkMode ? 'bg-gray-600' : ''}`}>
              <div
                className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full transition-all duration-1000"
                style={{ width: `${progress.progressToNextLevel}%` }}
              />
            </div>
          </div>
        )}

        {/* Demo Navigation */}
        <div className="flex space-x-2 mb-4">
          {[
            { id: 'hub', name: 'Gamification Hub', icon: '🎮' },
            { id: 'transactions', name: 'Transaction Tagging', icon: '🏷️' },
            { id: 'savings', name: 'Savings Demo', icon: '💰' },
            { id: 'goals', name: 'Goals Demo', icon: '🎯' },
          ].map((demo) => (
            <button
              key={demo.id}
              onClick={() => setActiveDemo(demo.id as any)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all ${
                activeDemo === demo.id
                  ? 'bg-blue-600 text-white'
                  : darkMode
                  ? 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                  : 'bg-white text-gray-700 hover:bg-gray-50 shadow-sm'
              }`}
            >
              <span>{demo.icon}</span>
              <span>{demo.name}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Demo Content */}
      {activeDemo === 'hub' && (
        <GamificationHub
          userId={userId}
          darkMode={darkMode}
        />
      )}

      {activeDemo === 'transactions' && (
        <div>
          <h2 className="text-xl font-bold mb-4">
            🏷️ {t('example.transactions.title', 'Transaction Tagging Demo')}
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {t('example.transactions.description', 
              'This demonstrates how transactions are automatically tagged and analyzed for goal alignment.'
            )}
          </p>
          <TransactionTagger
            transaction={exampleTransaction}
            darkMode={darkMode}
            onTagsUpdated={(taggedTransaction) => {
              console.log('Transaction tagged:', taggedTransaction);
            }}
            onXPAwarded={(xp, tags) => {
              console.log(`Awarded ${xp} XP for tags:`, tags);
            }}
          />
        </div>
      )}

      {activeDemo === 'savings' && (
        <div>
          <h2 className="text-xl font-bold mb-4">
            💰 {t('example.savings.title', 'Savings & XP Demo')}
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            {t('example.savings.description', 
              'Try these actions to see how the gamification system rewards different financial behaviors.'
            )}
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {demoActions.map((action, index) => (
              <div
                key={index}
                className={`p-4 rounded-lg border-2 transition-all hover:scale-105 cursor-pointer ${
                  darkMode
                    ? 'border-gray-600 bg-gray-800 hover:border-gray-500'
                    : 'border-gray-300 bg-white hover:border-gray-400 shadow-sm'
                }`}
                onClick={action.action}
              >
                <div className="text-3xl mb-2">{action.icon}</div>
                <h3 className="font-bold mb-2">{action.title}</h3>
                <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  {action.description}
                </p>
                <div className="mt-3">
                  <button className="w-full px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium">
                    {t('example.tryAction', 'Try Action')}
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {activeDemo === 'goals' && (
        <div>
          <h2 className="text-xl font-bold mb-4">
            🎯 {t('example.goals.title', 'Goals Integration Demo')}
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            {t('example.goals.description', 
              'This shows how savings goals integrate with the gamification system for maximum motivation.'
            )}
          </p>
          
          {/* Feature Access Demo */}
          <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'} mb-6`}>
            <h3 className="font-bold mb-3">
              🔓 {t('example.featureAccess', 'Feature Access Based on Level')}
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {[
                { feature: 'goal_milestones', level: 3, name: 'Goal Milestones' },
                { feature: 'social_sharing', level: 5, name: 'Social Sharing' },
                { feature: 'achievement_showcase', level: 10, name: 'Achievement Showcase' },
                { feature: 'advanced_charts', level: 18, name: 'Advanced Charts' },
                { feature: 'leaderboards', level: 25, name: 'Leaderboards' },
                { feature: 'ai_insights', level: 35, name: 'AI Insights' },
              ].map((item) => (
                <div
                  key={item.feature}
                  className={`p-3 rounded-lg text-center ${
                    progress && progress.currentLevel >= item.level
                      ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                      : 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400'
                  }`}
                >
                  <div className="font-medium">{item.name}</div>
                  <div className="text-sm">Level {item.level}</div>
                  <div className="text-xs">
                    {progress && progress.currentLevel >= item.level ? '✅ Unlocked' : '🔒 Locked'}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Integration Code Example */}
          <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-white shadow-sm'}`}>
            <h3 className="font-bold mb-3">
              💻 {t('example.codeExample', 'Integration Code Example')}
            </h3>
            <pre className={`text-sm overflow-x-auto p-3 rounded ${
              darkMode ? 'bg-gray-900 text-gray-300' : 'bg-gray-100 text-gray-800'
            }`}>
{`// Example: Integrating gamification with savings
const { awardSavingsXP, hasFeatureAccess } = useGamification(userId);

const handleSavingsContribution = async (amount, isPillar3a) => {
  // Award XP for the contribution
  await awardSavingsXP(amount, isPillar3a);
  
  // Check for new feature unlocks
  const hasAdvancedCharts = await hasFeatureAccess('advanced_charts');
  if (hasAdvancedCharts) {
    // Show advanced visualization options
  }
};

// Example: Swiss-specific optimization
const handlePillar3aContribution = async (amount) => {
  await awardSwissOptimizationXP('pillar3a', amount);
  // 20% bonus XP for Swiss optimization!
};`}
            </pre>
          </div>
        </div>
      )}

      {/* Level Up Notification */}
      {recentLevelUp && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className={`p-8 rounded-lg max-w-md w-full mx-4 ${darkMode ? 'bg-gray-800' : 'bg-white'}`}>
            <div className="text-center">
              <div className="text-6xl mb-4">🎉</div>
              <h2 className="text-2xl font-bold mb-2 text-yellow-500">Level Up!</h2>
              <p className="text-lg mb-4">
                You've reached level {recentLevelUp.newLevel}!
              </p>
              {recentLevelUp.newUnlocks.length > 0 && (
                <div className="mb-4">
                  <h3 className="font-semibold mb-2">New Features Unlocked:</h3>
                  <ul className="text-sm space-y-1">
                    {recentLevelUp.newUnlocks.map((unlock, index) => (
                      <li key={index} className="text-green-600 dark:text-green-400">
                        ✨ {unlock.replace('_', ' ')}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
              <button
                onClick={() => window.location.reload()}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Awesome!
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default GamificationIntegrationExample;
