import React from 'react';
import { createRoot } from 'react-dom/client';
import { I18nextProvider } from 'react-i18next';
import App from './App';
import { AdminConfigProvider } from './contexts/AdminConfigContext';
import i18n from './i18n/config';
import './index.css';

const container = document.getElementById('root')!;
const root = createRoot(container);

root.render(
  <React.StrictMode>
    <I18nextProvider i18n={i18n}>
      <AdminConfigProvider>
        <App />
      </AdminConfigProvider>
    </I18nextProvider>
  </React.StrictMode>
);
