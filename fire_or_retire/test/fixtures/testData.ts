// Test fixtures and mock data for consistent testing

export const defaultExpenses = [
  { id: 1, category: 'Housing (Rent/Mortgage)', amount: '2551', essential: true },
  { id: 2, category: 'Utilities', amount: '90', essential: true },
  { id: 3, category: 'Food & Groceries', amount: '500', essential: true },
  { id: 4, category: 'Transportation', amount: '250', essential: true },
  { id: 5, category: 'Insurance', amount: '200', essential: true },
];

export const defaultSavings = [
  { id: 1, goal: 'Emergency Fund', amount: '500' },
  { id: 2, goal: 'Retirement (Pillar 3a)', amount: '1280' },
  { id: 3, goal: 'Investment Portfolio', amount: '1000' },
];

export const defaultFinancialData = {
  monthlyIncome: '10159.95',
  incomePercentage: 100,
  companyIncome: '2500',
  companyIncomeStartYear: 2027,
  companyIncomeGrowthRate: 3,
  hsluIncome: '800',
  ruagIncome: '2300',
  currentAge: 46,
  retirementAge: 55,
  currentSavings: '300000',
  expectedReturn: 5,
  inflationRate: 1.59,
  targetRetirementIncome: '6000',
  emergencyFundTargetMonths: 6,
  currentPensionLeavingBenefits: '408231.90',
  targetOverallSavingsRate: 20,
};

export const testScenarios = {
  youngProfessional: {
    ...defaultFinancialData,
    currentAge: 25,
    retirementAge: 65,
    monthlyIncome: '8000',
    currentSavings: '50000',
    expenses: [
      { id: 1, category: 'Rent', amount: '1800', essential: true },
      { id: 2, category: 'Food', amount: '400', essential: true },
      { id: 3, category: 'Transportation', amount: '200', essential: true },
      { id: 4, category: 'Entertainment', amount: '300', essential: false },
    ],
    savings: [
      { id: 1, goal: 'Emergency Fund', amount: '600' },
      { id: 2, goal: 'Retirement (Pillar 3a)', amount: '588' },
      { id: 3, goal: 'Investment Portfolio', amount: '1000' },
    ],
  },

  midCareerFamily: {
    ...defaultFinancialData,
    currentAge: 40,
    retirementAge: 60,
    monthlyIncome: '12000',
    currentSavings: '200000',
    expenses: [
      { id: 1, category: 'Mortgage', amount: '3000', essential: true },
      { id: 2, category: 'Utilities', amount: '150', essential: true },
      { id: 3, category: 'Food & Groceries', amount: '800', essential: true },
      { id: 4, category: 'Childcare', amount: '1200', essential: true },
      { id: 5, category: 'Insurance', amount: '400', essential: true },
      { id: 6, category: 'Family Activities', amount: '500', essential: false },
    ],
    savings: [
      { id: 1, goal: 'Emergency Fund', amount: '800' },
      { id: 2, goal: 'Retirement (Pillar 3a)', amount: '588' },
      { id: 3, goal: 'Investment Portfolio', amount: '1500' },
      { id: 4, goal: 'Children Education', amount: '600' },
    ],
  },

  nearRetirement: {
    ...defaultFinancialData,
    currentAge: 58,
    retirementAge: 62,
    monthlyIncome: '15000',
    currentSavings: '800000',
    expenses: [
      { id: 1, category: 'Housing', amount: '2000', essential: true },
      { id: 2, category: 'Utilities', amount: '120', essential: true },
      { id: 3, category: 'Food & Groceries', amount: '600', essential: true },
      { id: 4, category: 'Healthcare', amount: '300', essential: true },
      { id: 5, category: 'Travel', amount: '800', essential: false },
    ],
    savings: [
      { id: 1, goal: 'Emergency Fund', amount: '500' },
      { id: 2, goal: 'Retirement (Pillar 3a)', amount: '588' },
      { id: 3, goal: 'Investment Portfolio', amount: '3000' },
    ],
  },

  highEarner: {
    ...defaultFinancialData,
    currentAge: 35,
    retirementAge: 50,
    monthlyIncome: '20000',
    companyIncome: '5000',
    currentSavings: '500000',
    expenses: [
      { id: 1, category: 'Luxury Apartment', amount: '4000', essential: true },
      { id: 2, category: 'Utilities', amount: '200', essential: true },
      { id: 3, category: 'Food & Dining', amount: '1000', essential: true },
      { id: 4, category: 'Transportation', amount: '800', essential: true },
      { id: 5, category: 'Insurance', amount: '500', essential: true },
      { id: 6, category: 'Luxury Expenses', amount: '2000', essential: false },
    ],
    savings: [
      { id: 1, goal: 'Emergency Fund', amount: '1000' },
      { id: 2, goal: 'Retirement (Pillar 3a)', amount: '588' },
      { id: 3, goal: 'Investment Portfolio', amount: '5000' },
      { id: 4, goal: 'Real Estate Investment', amount: '3000' },
    ],
  },
};

export const edgeCases = {
  zeroIncome: {
    ...defaultFinancialData,
    monthlyIncome: '0',
    companyIncome: '0',
    hsluIncome: '0',
    ruagIncome: '0',
  },

  negativeValues: {
    ...defaultFinancialData,
    monthlyIncome: '-1000',
    currentSavings: '-50000',
  },

  extremelyHighValues: {
    ...defaultFinancialData,
    monthlyIncome: '999999',
    currentSavings: '50000000',
    targetRetirementIncome: '100000',
  },

  invalidAges: {
    ...defaultFinancialData,
    currentAge: 150,
    retirementAge: 200,
  },
};

export const localStorageTestData = {
  complete: {
    'swissBudgetPro_darkMode': true,
    'swissBudgetPro_monthlyIncome': '12000',
    'swissBudgetPro_incomePercentage': 80,
    'swissBudgetPro_companyIncome': '3000',
    'swissBudgetPro_companyIncomeStartYear': 2025,
    'swissBudgetPro_companyIncomeGrowthRate': 5,
    'swissBudgetPro_hsluIncome': '1000',
    'swissBudgetPro_ruagIncome': '2500',
    'swissBudgetPro_expenses': JSON.stringify(defaultExpenses),
    'swissBudgetPro_savings': JSON.stringify(defaultSavings),
    'swissBudgetPro_currentAge': 35,
    'swissBudgetPro_retirementAge': 60,
    'swissBudgetPro_currentSavings': '250000',
    'swissBudgetPro_expectedReturn': 6,
    'swissBudgetPro_inflationRate': 2,
    'swissBudgetPro_targetRetirementIncome': '8000',
    'swissBudgetPro_emergencyFundTargetMonths': 8,
    'swissBudgetPro_currentPensionLeavingBenefits': '500000',
    'swissBudgetPro_targetOverallSavingsRate': 25,
    'swissBudgetPro_activeTab': 'budget',
    'swissBudgetPro_showTargetAnalysis': true,
  },

  partial: {
    'swissBudgetPro_monthlyIncome': '15000',
    'swissBudgetPro_darkMode': false,
    'swissBudgetPro_currentAge': 40,
    // Missing other keys to test fallback behavior
  },

  corrupted: {
    'swissBudgetPro_expenses': 'invalid-json{',
    'swissBudgetPro_savings': '{"incomplete": json',
    'swissBudgetPro_monthlyIncome': 'not-a-number',
    'swissBudgetPro_darkMode': 'invalid-boolean',
  },
};

export const calculationTestCases = {
  savingsRate: [
    { totalSavings: 2000, totalIncome: 10000, expected: 20 },
    { totalSavings: 0, totalIncome: 10000, expected: 0 },
    { totalSavings: 5000, totalIncome: 0, expected: 0 },
    { totalSavings: 3000, totalIncome: 6000, expected: 50 },
  ],

  compoundInterest: [
    { principal: 10000, rate: 5, years: 10, monthly: 0, expected: 16288.95 },
    { principal: 0, rate: 5, years: 10, monthly: 1000, expected: 155176.93 },
    { principal: 10000, rate: 0, years: 10, monthly: 0, expected: 10000 },
    { principal: 50000, rate: 7, years: 20, monthly: 500, expected: 437654.35 },
  ],

  fireNumber: [
    { annualExpenses: 60000, expected: 1500000 },
    { annualExpenses: 40000, expected: 1000000 },
    { annualExpenses: 0, expected: 0 },
    { annualExpenses: 100000, expected: 2500000 },
  ],
};
