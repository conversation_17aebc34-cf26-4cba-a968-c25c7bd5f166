# Swiss Budget Pro - Nix Development Environment

## Overview

This project uses <PERSON> for reproducible development environments. All development, testing, and building should happen within the Nix shell to ensure consistency across different machines and environments.

## Prerequisites

### Install Nix

**Linux/macOS:**
```bash
curl -L https://nixos.org/nix/install | sh
```

**NixOS:**
<PERSON> is already installed!

### Optional: Install direnv (Recommended)

**With Nix:**
```bash
nix-env -iA nixpkgs.direnv
```

**Other methods:**
- macOS: `brew install direnv`
- Ubuntu/Debian: `apt install direnv`

Then add to your shell config (`~/.bashrc`, `~/.zshrc`, etc.):
```bash
eval "$(direnv hook bash)"  # for bash
eval "$(direnv hook zsh)"   # for zsh
```

## Getting Started

### Method 1: Manual Nix Shell (Always works)

```bash
# Enter the development environment
nix-shell

# You'll see a welcome message with available commands
# All npm commands and development tools are now available
```

### Method 2: Automatic with direnv (Recommended)

```bash
# Allow direnv for this directory (one-time setup)
direnv allow

# The environment will automatically load when you cd into the directory
# and unload when you leave
```

## Available Tools in Nix Shell

### Core Development
- **Node.js 20** (Latest LTS)
- **npm, yarn, pnpm** (Package managers)
- **Git** (Version control)

### Build Tools
- **Python 3** (For native module compilation)
- **GCC** (C compiler)
- **Make** (Build system)
- **pkg-config** (Package configuration)

### Testing & Browsers
- **Chromium** (For E2E testing)
- **Firefox** (Alternative browser)

### Utilities
- **jq** (JSON processor)
- **tree** (Directory visualization)
- **htop** (Process monitoring)
- **vim/nano** (Text editors)

## Development Workflow

### 1. Enter the Environment
```bash
# If using direnv
cd /path/to/swiss-budget-pro  # Auto-loads environment

# If using manual nix-shell
nix-shell
```

### 2. Development Commands
```bash
# Install dependencies (auto-runs on first shell entry)
npm install

# Start development server
npm run dev

# Run tests
npm test
npm run test:watch
npm run test:ui
npm run test:coverage

# Custom test runner
./scripts/test.sh
./scripts/test.sh coverage
./scripts/test.sh watch
```

### 3. Build and Deploy
```bash
# Build for production
npm run build

# Preview production build
npm run preview
```

## Environment Features

### Automatic Setup
- **Dependencies**: Auto-installs npm packages on first entry
- **Environment Variables**: Optimized for development
- **Local npm**: Uses project-local npm cache and global packages

### Reproducibility
- **Consistent Node.js version** across all machines
- **Same build tools** for everyone
- **Isolated environment** doesn't affect system packages

### Performance Optimizations
- **Local npm cache** (`.npm-cache/`)
- **Disabled update notifications**
- **Optimized logging levels**

## Project Structure in Nix Environment

```
swiss-budget-pro/
├── shell.nix              # Nix environment definition
├── .envrc                 # direnv configuration
├── retire.tsx             # Main application
├── package.json           # Node.js dependencies
├── vite.config.ts         # Build configuration
├── test/                  # Test suite
│   ├── setup.ts           # Test configuration
│   ├── components/        # Component tests
│   ├── integration/       # Integration tests
│   └── fixtures/          # Test data
├── scripts/
│   └── test.sh           # Custom test runner
└── .npm-cache/           # Local npm cache (auto-created)
```

## Troubleshooting

### Common Issues

**"nix-shell not found"**
```bash
# Install Nix first
curl -L https://nixos.org/nix/install | sh
source ~/.nix-profile/etc/profile.d/nix.sh
```

**"direnv: command not found"**
```bash
# Install direnv
nix-env -iA nixpkgs.direnv
# Add to shell config and restart terminal
```

**Node modules not found**
```bash
# Re-enter the shell to trigger auto-install
exit
nix-shell
```

**Permission issues**
```bash
# Ensure npm global directory is writable
rm -rf .npm-global
mkdir -p .npm-global
```

### Updating Dependencies

**Update Nix packages:**
```bash
# Update nixpkgs channel
nix-channel --update
# Re-enter shell
exit && nix-shell
```

**Update npm packages:**
```bash
# Inside nix-shell
npm update
```

## Benefits of Nix Environment

### For Developers
- ✅ **Consistent environment** across all machines
- ✅ **No system pollution** - isolated development
- ✅ **Easy onboarding** - one command setup
- ✅ **Reproducible builds** - same results everywhere

### For CI/CD
- ✅ **Identical environment** in CI as development
- ✅ **Faster builds** with Nix caching
- ✅ **Reliable deployments** with reproducible builds

### For Team Collaboration
- ✅ **No "works on my machine"** issues
- ✅ **Simplified setup** for new team members
- ✅ **Version consistency** across the team

## Advanced Usage

### Custom Nix Packages
Add to `shell.nix` buildInputs:
```nix
buildInputs = with pkgs; [
  # existing packages...
  your-custom-package
];
```

### Environment Variables
Add to shellHook in `shell.nix`:
```nix
shellHook = ''
  export YOUR_VARIABLE="value"
  # existing shellHook...
'';
```

### Development Scripts
All scripts should assume they're running in the Nix environment:
```bash
#!/usr/bin/env bash
# This script assumes nix-shell environment
npm test
```

## Getting Help

- **Nix Manual**: https://nixos.org/manual/nix/stable/
- **Nixpkgs Search**: https://search.nixos.org/packages
- **direnv Documentation**: https://direnv.net/

Remember: **Always work within the Nix shell for this project!** 🚀
