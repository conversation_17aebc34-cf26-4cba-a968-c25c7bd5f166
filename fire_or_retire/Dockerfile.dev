# Development Dockerfile for Fire or Retire Calculator
# Optimized for volume mapping and live development

FROM node:20-alpine as development

# Install development tools
RUN apk add --no-cache \
    git \
    wget \
    curl \
    bash \
    vim

# Set working directory
WORKDIR /app

# Copy package files first for better caching
COPY package*.json ./

# Install all dependencies (including dev dependencies)
RUN npm ci

# Create directories that will be volume mapped
RUN mkdir -p src public dist

# Copy the rest of the application (this will be overridden by volume mapping)
COPY . .

# Expose development server port
EXPOSE 5173

# Expose preview server port (for production builds)
EXPOSE 4173

# Create a startup script for development
RUN echo '#!/bin/bash' > /app/start-dev.sh && \
    echo 'echo "🔥 Starting Fire or Retire Calculator in Development Mode..."' >> /app/start-dev.sh && \
    echo 'echo "📁 Working directory: $(pwd)"' >> /app/start-dev.sh && \
    echo 'echo "📦 Node version: $(node --version)"' >> /app/start-dev.sh && \
    echo 'echo "📦 NPM version: $(npm --version)"' >> /app/start-dev.sh && \
    echo 'echo "🔍 Checking if node_modules exists..."' >> /app/start-dev.sh && \
    echo 'if [ ! -d "node_modules" ]; then' >> /app/start-dev.sh && \
    echo '  echo "📦 Installing dependencies..."' >> /app/start-dev.sh && \
    echo '  npm ci' >> /app/start-dev.sh && \
    echo 'fi' >> /app/start-dev.sh && \
    echo 'echo "🚀 Starting Vite development server..."' >> /app/start-dev.sh && \
    echo 'exec npm run dev -- --host 0.0.0.0 --port 5173' >> /app/start-dev.sh && \
    chmod +x /app/start-dev.sh

# Create a build script for development
RUN echo '#!/bin/bash' > /app/build-dev.sh && \
    echo 'echo "🔨 Building application for development..."' >> /app/build-dev.sh && \
    echo 'npm run build' >> /app/build-dev.sh && \
    echo 'echo "🚀 Starting preview server..."' >> /app/build-dev.sh && \
    echo 'exec npm run preview -- --host 0.0.0.0 --port 4173' >> /app/build-dev.sh && \
    chmod +x /app/build-dev.sh

# Health check for development
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:5173/ || exit 1

# Default command (can be overridden)
CMD ["/app/start-dev.sh"]
