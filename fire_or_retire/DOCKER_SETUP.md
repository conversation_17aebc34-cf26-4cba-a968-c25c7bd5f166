# 🔥 Fire or Retire - Docker Setup Guide

## Overview

This guide covers the Docker deployment setup for **Swiss Budget Pro** (Fire or Retire Calculator), a comprehensive financial planning and retirement calculator application.

## 🚀 Quick Start

### Prerequisites

- Docker and Docker Compose installed
- Traefik reverse proxy running (or create the network manually)

### One-Command Startup

```bash
./start-services.sh
```

The application will be available at:

- **http://localhost:4173** (Direct access)
- **http://fire.docker.localhost** (Via Traefik - requires domain setup)

## 📋 Available Commands

### Service Management

```bash
# Start services
./start-services.sh start

# Stop services
./start-services.sh stop

# Restart services
./start-services.sh restart

# View logs
./start-services.sh logs

# Check status
./start-services.sh status

# Cleanup
./start-services.sh cleanup
```

### Manual Docker Commands

```bash
# Build and start
docker compose up --build -d

# Stop and remove
docker compose down --remove-orphans

# View logs
docker compose logs -f

# Check status
docker compose ps
```

## 🏗️ Architecture

### Container Structure

- **Base Image**: Node.js 20 Alpine (multi-stage build)
- **Web Server**: Vite preview server
- **Port**: 4173 (internal)
- **Domain**: fire.docker.localhost
- **Network**: traefik_network

### Build Process

1. **Build Stage**: Install dependencies and build the React application
2. **Production Stage**: Create optimized runtime container with built assets
3. **Health Checks**: Automated health monitoring
4. **Traefik Integration**: Automatic service discovery and routing

## 🌐 Network Configuration

### Traefik Integration

The application automatically registers with Traefik using these labels:

```yaml
labels:
  - 'traefik.enable=true'
  - 'traefik.http.routers.fire-or-retire.rule=Host(`fire.docker.localhost`)'
  - 'traefik.http.services.fire-or-retire-service.loadbalancer.server.port=4173'
```

### Network Setup

- **External Network**: `traefik_network` (connects to Traefik)
- **Internal Network**: `fire-or-retire-internal` (isolated internal communication)

## 🔧 Configuration

### Environment Variables

```bash
NODE_ENV=production
VITE_APP_NAME=Swiss Budget Pro
VITE_APP_VERSION=1.0.0
```

### Health Checks

- **Interval**: 30 seconds
- **Timeout**: 10 seconds
- **Retries**: 3
- **Start Period**: 40 seconds

## 📊 Monitoring

### Container Health

```bash
# Check container health
docker inspect fire-or-retire-app --format='{{.State.Health.Status}}'

# View health check logs
docker inspect fire-or-retire-app --format='{{range .State.Health.Log}}{{.Output}}{{end}}'
```

### Application Logs

```bash
# Follow logs
docker compose logs -f fire-or-retire

# View recent logs
docker compose logs --tail=50 fire-or-retire
```

## 🛠️ Troubleshooting

### Common Issues

1. **Traefik Network Missing**

   ```bash
   docker network create traefik_network
   ```

2. **Port Conflicts**

   - Check if port 4173 is already in use
   - Modify docker-compose.yml if needed

3. **Build Failures**

   ```bash
   # Clean build
   docker compose build --no-cache
   ```

4. **Health Check Failures**

   ```bash
   # Check application logs
   docker compose logs fire-or-retire

   # Test health endpoint manually
   docker compose exec fire-or-retire wget --spider http://localhost:4173/
   ```

### Debug Mode

```bash
# Run with debug output
DEBUG=1 ./start-services.sh

# Interactive container access
docker compose exec fire-or-retire sh
```

## 🔒 Security

### Container Security

- Non-root user execution
- Minimal Alpine Linux base
- No unnecessary packages
- Health check monitoring

### Network Security

- Isolated internal network
- Traefik-managed external access
- No direct port exposure

## 📈 Performance

### Optimization Features

- Multi-stage Docker build
- Minimal production image
- Efficient caching layers
- Health-based load balancing

### Resource Usage

- **Memory**: ~50MB runtime
- **CPU**: Minimal (static serving)
- **Storage**: ~100MB image size

## 🔄 Updates and Maintenance

### Updating the Application

```bash
# Pull latest changes
git pull origin main

# Rebuild and restart
./start-services.sh restart
```

### Container Maintenance

```bash
# Clean up unused images
docker image prune -f

# Clean up unused volumes
docker volume prune -f

# Full system cleanup
docker system prune -af
```

## 📝 Development vs Production

This setup is optimized for **production deployment**. For development, use:

```bash
# Development mode
npm run dev

# Or use the dev container
code .devcontainer
```

## 🎯 Features

The deployed application includes:

- 📊 **Financial Planning Tools**
- 🏦 **Swiss Tax Calculations**
- 💰 **Retirement Planning**
- 🏥 **Healthcare Cost Optimization**
- 📈 **Investment Analysis**
- 🌍 **Multi-language Support** (EN/DE)
- 📱 **Responsive Design**

---

**🔥 Fire or Retire Calculator** - Your comprehensive Swiss financial planning companion!
