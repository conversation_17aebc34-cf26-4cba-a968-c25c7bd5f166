import { defineConfig, devices } from '@playwright/test';

/**
 * Swiss Budget Pro - Playwright E2E Testing Configuration
 * Comprehensive testing setup for Swiss financial planning application
 * @see https://playwright.dev/docs/test-configuration
 */
export default defineConfig({
  testDir: './tests/e2e',

  /* Run tests in files in parallel */
  fullyParallel: true,

  /* Fail the build on CI if you accidentally left test.only in the source code. */
  forbidOnly: !!process.env.CI,

  /* Retry on CI only */
  retries: process.env.CI ? 2 : 1,

  /* Opt out of parallel tests on CI. */
  workers: process.env.CI ? 2 : undefined,

  /* Enhanced reporter configuration for Swiss Budget Pro */
  reporter: [
    ['html', { outputFolder: 'test-results/html-report' }],
    ['json', { outputFile: 'test-results/results.json' }],
    ['junit', { outputFile: 'test-results/junit.xml' }],
    ['line'],
    ...(process.env.CI ? [['github']] : []),
    // Add Allure reporter for detailed reporting
    // ['allure-playwright', { outputFolder: 'test-results/allure-results' }],
  ],
  /* Shared settings for all the projects below. See https://playwright.dev/docs/api/class-testoptions. */
  use: {
    /* Base URL to use in actions like `await page.goto('/')`. */
    baseURL: process.env.BASE_URL || 'http://localhost:5173',

    /* Collect trace when retrying the failed test. See https://playwright.dev/docs/trace-viewer */
    trace: 'retain-on-failure',

    /* Take screenshot only on failures */
    screenshot: 'only-on-failure',

    /* Record video only on failures */
    video: 'retain-on-failure',

    /* Global timeout for each action - increased for complex financial calculations */
    actionTimeout: 20000, // 20 seconds for Swiss tax calculations

    /* Global timeout for navigation - increased for data loading */
    navigationTimeout: 60000, // 60 seconds for comprehensive data loading

    /* Swiss-specific locale settings */
    locale: 'de-CH',
    timezoneId: 'Europe/Zurich',

    /* Extra HTTP headers for Swiss context */
    extraHTTPHeaders: {
      'Accept-Language':
        'de-CH,de;q=0.9,fr-CH,fr;q=0.8,it-CH,it;q=0.7,en;q=0.6',
    },

    /* Enhanced logging for debugging */
    launchOptions: {
      // Enable browser console logs and Swiss-specific args
      args: process.env.CI
        ? [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
          ]
        : ['--enable-features=NetworkServiceLogging'],
    },

    /* Ignore HTTPS errors for local development */
    ignoreHTTPSErrors: true,
  },

  /* Configure projects for major browsers */
  projects: [
    {
      name: 'chromium',
      use: {
        ...devices['Desktop Chrome'],
        // Swiss Budget Pro specific viewport
        viewport: { width: 1280, height: 720 },
      },
    },

    {
      name: 'firefox',
      use: {
        ...devices['Desktop Firefox'],
        viewport: { width: 1280, height: 720 },
      },
    },

    {
      name: 'webkit',
      use: {
        ...devices['Desktop Safari'],
        viewport: { width: 1280, height: 720 },
      },
    },

    /* Test against mobile viewports. */
    {
      name: 'Mobile Chrome',
      use: {
        ...devices['Pixel 5'],
        // Test Swiss Budget Pro mobile responsiveness
      },
    },
    {
      name: 'Mobile Safari',
      use: {
        ...devices['iPhone 12'],
      },
    },

    /* Test against branded browsers. */
    {
      name: 'Microsoft Edge',
      use: { ...devices['Desktop Edge'], channel: 'msedge' },
    },

    // Swiss locale-specific testing
    {
      name: 'swiss-german',
      use: {
        ...devices['Desktop Chrome'],
        locale: 'de-CH',
        timezoneId: 'Europe/Zurich',
        viewport: { width: 1280, height: 720 },
      },
      testMatch: [
        '**/localization/**/*.spec.ts',
        '**/critical-flows/**/*.spec.ts',
      ],
    },

    {
      name: 'swiss-french',
      use: {
        ...devices['Desktop Chrome'],
        locale: 'fr-CH',
        timezoneId: 'Europe/Zurich',
        viewport: { width: 1280, height: 720 },
      },
      testMatch: ['**/localization/**/*.spec.ts'],
    },

    // Accessibility testing
    {
      name: 'accessibility',
      use: {
        ...devices['Desktop Chrome'],
        reducedMotion: 'reduce',
        viewport: { width: 1280, height: 720 },
      },
      testMatch: ['**/accessibility/**/*.spec.ts'],
    },

    // Performance testing
    {
      name: 'performance',
      use: {
        ...devices['Desktop Chrome'],
        viewport: { width: 1280, height: 720 },
        launchOptions: {
          args: [
            '--enable-features=NetworkServiceLogging',
            '--disable-web-security',
          ],
        },
      },
      testMatch: ['**/performance/**/*.spec.ts'],
    },
  ],

  /* Run your local dev server before starting the tests */
  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:5173',
    reuseExistingServer: !process.env.CI,
    timeout: 120 * 1000, // 2 minutes
    stdout: 'pipe', // Capture server logs
    stderr: 'pipe', // Capture server errors
    env: {
      ...process.env,
      // Enable verbose Vite logging for debugging
      DEBUG: process.env.DEBUG || 'vite:*',
      VITE_LOG_LEVEL: 'info',
    },
  },

  /* Global test timeout - increased for complex Swiss financial calculations */
  timeout: 60 * 1000, // 60 seconds

  /* Expect timeout for assertions - increased for calculation validation */
  expect: {
    timeout: 10 * 1000, // 10 seconds
  },

  /* Test output directory */
  outputDir: 'test-results/',

  /* Global setup and teardown for Swiss Budget Pro */
  globalSetup: './tests/setup/global-setup.ts',
  globalTeardown: './tests/setup/global-teardown.ts',

  /* Test metadata for Swiss Budget Pro */
  metadata: {
    'test-suite': 'Swiss Budget Pro E2E Tests',
    version: '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    'target-url': process.env.BASE_URL || 'http://localhost:5173',
  },
});
