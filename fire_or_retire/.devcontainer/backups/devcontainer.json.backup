{"name": "Swiss Budget Pro Development", "image": "mcr.microsoft.com/devcontainers/typescript-node:1-20-bullseye", "features": {"ghcr.io/devcontainers/features/git:1": {}, "ghcr.io/devcontainers/features/github-cli:1": {}, "ghcr.io/devcontainers/features/docker-in-docker:2": {}, "ghcr.io/devcontainers/features/common-utils:2": {"installZsh": true, "configureZshAsDefaultShell": true, "installOhMyZsh": true, "upgradePackages": true}}, "customizations": {"vscode": {"extensions": ["ms-vscode.vscode-typescript-next", "bradlc.vscode-tailwindcss", "esbenp.prettier-vscode", "ms-vscode.vscode-json", "ms-playwright.playwright", "vitest.explorer", "ms-vscode.test-adapter-converter", "formulahendry.auto-rename-tag", "christian-kohler.path-intellisense", "ms-vscode.vscode-eslint", "usernamehw.errorlens", "ms-vscode.vscode-npm-script", "ms-vscode.references-view", "ms-vscode.vscode-typescript-next", "ms-vscode.js-debug", "ms-vscode.vscode-react-native"], "settings": {"typescript.preferences.quoteStyle": "single", "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": true}, "files.associations": {"*.tsx": "typescriptreact"}, "emmet.includeLanguages": {"typescriptreact": "html"}, "terminal.integrated.defaultProfile.linux": "zsh", "git.autofetch": true, "git.enableSmartCommit": true, "workbench.startupEditor": "readme", "explorer.confirmDelete": false, "explorer.confirmDragAndDrop": false, "typescript.updateImportsOnFileMove.enabled": "always", "javascript.updateImportsOnFileMove.enabled": "always"}}}, "forwardPorts": [3000, 5173, 4173, 51204], "portsAttributes": {"3000": {"label": "Development Server", "onAutoForward": "notify"}, "5173": {"label": "Vite Dev Server", "onAutoForward": "notify"}, "4173": {"label": "Vite Preview", "onAutoForward": "silent"}, "51204": {"label": "Vitest UI", "onAutoForward": "silent"}}, "postCreateCommand": "bash .devcontainer/setup.sh", "postStartCommand": "npm install", "remoteUser": "node", "workspaceFolder": "/workspaces/swiss-budget-pro", "mounts": ["source=${localWorkspaceFolder}/.devcontainer/zsh-history,target=/home/<USER>/.zsh_history,type=bind,consistency=cached"], "containerEnv": {"NODE_ENV": "development", "TERM": "xterm-256color"}, "runArgs": ["--init"]}