# Swiss Budget Pro - Development Container
FROM mcr.microsoft.com/devcontainers/typescript-node:1-20-bullseye

# Set the working directory
WORKDIR /workspaces/swiss-budget-pro

# Install additional system packages
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    git \
    vim \
    nano \
    htop \
    tree \
    jq \
    unzip \
    build-essential \
    python3 \
    python3-pip \
    && rm -rf /var/lib/apt/lists/*

# Install global npm packages
RUN npm install -g \
    typescript \
    ts-node \
    prettier \
    eslint \
    serve \
    http-server \
    @vitejs/create-vite

# Create directories for npm global packages
RUN mkdir -p /home/<USER>/.npm-global && \
    chown -R node:node /home/<USER>/.npm-global

# Switch to node user
USER node

# Set npm global directory
RUN npm config set prefix /home/<USER>/.npm-global

# Add npm global bin to PATH
ENV PATH="/home/<USER>/.npm-global/bin:${PATH}"

# Set environment variables
ENV NODE_ENV=development
ENV TERM=xterm-256color

# Create workspace directory
RUN mkdir -p /workspaces/swiss-budget-pro

# Set the default command
CMD ["sleep", "infinity"]
