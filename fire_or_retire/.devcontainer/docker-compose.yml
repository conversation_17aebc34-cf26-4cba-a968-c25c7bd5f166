version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    volumes:
      - ../..:/workspaces:cached
    command: sleep infinity
    environment:
      - NODE_ENV=development
    ports:
      - "3000:3000"
      - "5173:5173"
      - "4173:4173"
      - "51204:51204"
    networks:
      - swiss-budget-pro-network

  # Optional: Add a database service for future features
  # postgres:
  #   image: postgres:15-alpine
  #   restart: unless-stopped
  #   environment:
  #     POSTGRES_DB: swiss_budget_pro
  #     POSTGRES_USER: developer
  #     POSTGRES_PASSWORD: devpassword
  #   ports:
  #     - "5432:5432"
  #   volumes:
  #     - postgres-data:/var/lib/postgresql/data
  #   networks:
  #     - swiss-budget-pro-network

  # Optional: Add Redis for caching
  # redis:
  #   image: redis:7-alpine
  #   restart: unless-stopped
  #   ports:
  #     - "6379:6379"
  #   networks:
  #     - swiss-budget-pro-network

networks:
  swiss-budget-pro-network:
    driver: bridge

# volumes:
#   postgres-data:
