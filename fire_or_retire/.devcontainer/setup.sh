#!/bin/bash

# Swiss Budget Pro - DevContainer Setup Script
# This script runs after the container is created

set -e

echo "🚀 Setting up Swiss Budget Pro Development Environment"
echo "====================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Update system packages
print_status "Updating system packages..."
sudo apt-get update -qq

# Install additional development tools
print_status "Installing additional development tools..."
sudo apt-get install -y -qq \
    curl \
    wget \
    git \
    vim \
    nano \
    htop \
    tree \
    jq \
    unzip \
    build-essential \
    python3 \
    python3-pip

# Install global npm packages
print_status "Installing global npm packages..."
npm install -g \
    typescript \
    ts-node \
    prettier \
    eslint \
    serve \
    http-server

# Verify Node.js and npm versions
print_status "Verifying environment..."
echo "Node.js version: $(node --version)"
echo "npm version: $(npm --version)"
echo "TypeScript version: $(tsc --version)"

# Set up git configuration (if not already set)
if [ -z "$(git config --global user.name)" ]; then
    print_warning "Git user.name not set. You may want to configure it:"
    echo "  git config --global user.name 'Your Name'"
fi

if [ -z "$(git config --global user.email)" ]; then
    print_warning "Git user.email not set. You may want to configure it:"
    echo "  git config --global user.email '<EMAIL>'"
fi

# Create useful aliases
print_status "Setting up shell aliases..."
cat >> ~/.zshrc << 'EOF'

# Swiss Budget Pro Development Aliases
alias ll='ls -alF'
alias la='ls -A'
alias l='ls -CF'
alias ..='cd ..'
alias ...='cd ../..'
alias grep='grep --color=auto'

# Project specific aliases
alias dev='npm run dev'
alias build='npm run build'
alias test='npm test'
alias test-watch='npm run test:watch'
alias test-ui='npm run test:ui'
alias test-coverage='npm run test:coverage'

# Git aliases
alias gs='git status'
alias ga='git add'
alias gc='git commit'
alias gp='git push'
alias gl='git log --oneline'
alias gd='git diff'

# Quick project commands
alias start='npm install && npm run dev'
alias clean='rm -rf node_modules package-lock.json && npm install'
alias fresh='npm run clean && npm install'

EOF

# Create a welcome message
print_status "Creating welcome message..."
cat > ~/.welcome.sh << 'EOF'
#!/bin/bash

echo "🚀 Swiss Budget Pro Development Environment"
echo "=========================================="
echo ""
echo "📦 Environment Info:"
echo "  • Node.js: $(node --version)"
echo "  • npm: $(npm --version)"
echo "  • TypeScript: $(tsc --version)"
echo ""
echo "🧪 Testing Commands:"
echo "  • npm test              - Run all tests"
echo "  • npm run test:watch    - Watch mode"
echo "  • npm run test:ui       - Visual test runner"
echo "  • npm run test:coverage - Coverage report"
echo ""
echo "🔧 Development Commands:"
echo "  • npm run dev           - Start development server"
echo "  • npm run build         - Build for production"
echo "  • npm run preview       - Preview production build"
echo ""
echo "📁 Project Structure:"
echo "  • retire.tsx            - Main application"
echo "  • test/                 - Test suite"
echo "  • .devcontainer/        - Development container config"
echo ""
echo "💡 Quick Start:"
echo "  1. Run 'npm install' to install dependencies"
echo "  2. Run 'npm run dev' to start development"
echo "  3. Run 'npm test' to run tests"
echo ""
echo "🎯 Ready to develop! Happy coding! 🎉"
echo ""
EOF

chmod +x ~/.welcome.sh

# Add welcome message to shell startup
echo "" >> ~/.zshrc
echo "# Show welcome message" >> ~/.zshrc
echo "~/.welcome.sh" >> ~/.zshrc

# Create development directories
print_status "Creating development directories..."
mkdir -p ~/.npm-global
mkdir -p ~/.cache

# Set npm global directory
npm config set prefix ~/.npm-global
echo 'export PATH=~/.npm-global/bin:$PATH' >> ~/.zshrc

# Create .env template if it doesn't exist
if [ ! -f .env ]; then
    print_status "Creating .env template..."
    cat > .env << 'EOF'
# Swiss Budget Pro Environment Variables
NODE_ENV=development
VITE_APP_NAME="Swiss Budget Pro"
VITE_APP_VERSION="1.0.0"

# Add your environment variables here
# VITE_API_URL=http://localhost:3001
# VITE_ANALYTICS_ID=your-analytics-id
EOF
fi

# Set up git hooks directory
if [ -d .git ]; then
    print_status "Setting up git hooks..."
    mkdir -p .git/hooks
    
    # Pre-commit hook to run tests
    cat > .git/hooks/pre-commit << 'EOF'
#!/bin/bash
echo "Running tests before commit..."
npm test
if [ $? -ne 0 ]; then
    echo "Tests failed. Commit aborted."
    exit 1
fi
EOF
    chmod +x .git/hooks/pre-commit
fi

# Create useful scripts directory
print_status "Creating utility scripts..."
mkdir -p scripts

# Create a development status script
cat > scripts/status.sh << 'EOF'
#!/bin/bash

echo "📊 Swiss Budget Pro - Development Status"
echo "========================================"
echo ""

# Check if dependencies are installed
if [ -d "node_modules" ]; then
    echo "✅ Dependencies installed"
else
    echo "❌ Dependencies not installed (run 'npm install')"
fi

# Check git status
if [ -d ".git" ]; then
    echo "🌿 Git branch: $(git branch --show-current 2>/dev/null || echo 'unknown')"
    echo "📊 Git status:"
    git status --porcelain | head -5
    if [ $(git status --porcelain | wc -l) -gt 5 ]; then
        echo "   ... and $(( $(git status --porcelain | wc -l) - 5 )) more files"
    fi
else
    echo "❌ Not a git repository"
fi

echo ""
echo "🔧 Available commands:"
echo "  • npm run dev    - Start development"
echo "  • npm test       - Run tests"
echo "  • npm run build  - Build for production"
EOF

chmod +x scripts/status.sh

# Final setup
print_status "Finalizing setup..."

# Source the new shell configuration
source ~/.zshrc 2>/dev/null || true

print_success "DevContainer setup completed!"
print_status "Container is ready for Swiss Budget Pro development"
echo ""
echo "🎯 Next steps:"
echo "  1. Open a new terminal to see the welcome message"
echo "  2. Run 'npm install' to install project dependencies"
echo "  3. Run 'npm run dev' to start developing"
echo ""
