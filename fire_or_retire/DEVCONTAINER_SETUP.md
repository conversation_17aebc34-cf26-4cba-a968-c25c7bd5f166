# Swiss Budget Pro - DevContainer Setup

## Overview

This project uses **VS Code DevContainers** to provide a consistent, reproducible development environment. The devcontainer ensures that all developers work with the same Node.js version, dependencies, and tools, eliminating "works on my machine" issues.

## Prerequisites

### Required Software
- **Docker Desktop** (Windows/Mac) or **Docker Engine** (Linux)
- **Visual Studio Code**
- **Dev Containers extension** for VS Code

### Installation Steps

#### 1. Install Docker
**Windows/Mac:**
- Download and install [Docker Desktop](https://www.docker.com/products/docker-desktop/)
- Start Docker Desktop

**Linux:**
```bash
# Install Docker Engine
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Add your user to docker group
sudo usermod -aG docker $USER
newgrp docker
```

#### 2. Install VS Code
- Download from [code.visualstudio.com](https://code.visualstudio.com/)

#### 3. Install Dev Containers Extension
- Open VS Code
- Go to Extensions (Ctrl+Shift+X)
- Search for "Dev Containers"
- Install the official Microsoft extension

## Getting Started

### Method 1: Clone and Open (Recommended)
```bash
# Clone the repository
git clone <repository-url>
cd swiss-budget-pro

# Open in VS Code
code .

# VS Code will detect the devcontainer and prompt to reopen in container
# Click "Reopen in Container"
```

### Method 2: Command Palette
1. Open VS Code
2. Press `Ctrl+Shift+P` (or `Cmd+Shift+P` on Mac)
3. Type "Dev Containers: Open Folder in Container"
4. Select the project folder

### Method 3: Remote Repository
1. Press `Ctrl+Shift+P`
2. Type "Dev Containers: Clone Repository in Container Volume"
3. Enter the repository URL

## DevContainer Features

### Environment Specifications
- **Base Image**: `mcr.microsoft.com/devcontainers/typescript-node:1-20-bullseye`
- **Node.js**: Version 20 (Latest LTS)
- **Package Manager**: npm, yarn, pnpm available
- **Shell**: Zsh with Oh My Zsh
- **User**: `node` (non-root for security)

### Pre-installed Tools
- **Development**: TypeScript, ESLint, Prettier
- **Testing**: Vitest, Playwright
- **Utilities**: Git, curl, wget, jq, tree, htop
- **Build Tools**: Python3, build-essential

### VS Code Extensions (Auto-installed)
- TypeScript and JavaScript support
- Tailwind CSS IntelliSense
- Prettier code formatter
- ESLint
- Vitest Test Explorer
- Error Lens
- Auto Rename Tag
- Path IntelliSense

### Port Forwarding
- **3000**: Development server
- **5173**: Vite dev server
- **4173**: Vite preview
- **51204**: Vitest UI

## Development Workflow

### First Time Setup
```bash
# Container automatically runs setup script
# Install project dependencies
npm install

# Start development
npm run dev
```

### Daily Development
```bash
# Start development server
npm run dev

# Run tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with UI
npm run test:ui

# Build for production
npm run build
```

### Useful Aliases (Pre-configured)
```bash
# Development
dev          # npm run dev
build        # npm run build
test         # npm test
test-watch   # npm run test:watch
test-ui      # npm run test:ui

# Git shortcuts
gs           # git status
ga           # git add
gc           # git commit
gp           # git push

# Project management
start        # npm install && npm run dev
clean        # rm -rf node_modules && npm install
fresh        # clean install
```

## Container Configuration

### File Structure
```
.devcontainer/
├── devcontainer.json    # Main configuration
├── Dockerfile          # Custom container setup
├── docker-compose.yml  # Multi-service setup
├── setup.sh           # Post-creation script
└── zsh-history        # Persistent command history
```

### Key Configuration Options

#### devcontainer.json
- **Image**: Pre-built TypeScript/Node.js container
- **Features**: Git, GitHub CLI, Docker-in-Docker
- **Extensions**: Comprehensive VS Code extension pack
- **Port forwarding**: Development and testing ports
- **Post-creation**: Automatic setup script execution

#### Custom Features
- **Persistent shell history**: Command history preserved across restarts
- **Git hooks**: Pre-commit testing
- **Environment variables**: Development-optimized settings
- **Welcome message**: Helpful information on container start

## Troubleshooting

### Common Issues

#### Container Won't Start
```bash
# Check Docker is running
docker --version

# Rebuild container
Ctrl+Shift+P → "Dev Containers: Rebuild Container"
```

#### Port Already in Use
```bash
# Check what's using the port
lsof -i :3000

# Kill the process or change port in vite.config.ts
```

#### Permission Issues
```bash
# Container runs as 'node' user, not root
# File permissions should be handled automatically
```

#### Slow Performance
```bash
# On Windows/Mac, ensure Docker Desktop has enough resources
# Settings → Resources → Advanced
# Recommended: 4GB RAM, 2 CPUs minimum
```

### Reset Container
```bash
# Complete reset
Ctrl+Shift+P → "Dev Containers: Rebuild Container Without Cache"
```

## Advanced Usage

### Adding New Tools
Edit `.devcontainer/setup.sh`:
```bash
# Add to the setup script
sudo apt-get install -y your-tool
npm install -g your-global-package
```

### Custom Environment Variables
Edit `.devcontainer/devcontainer.json`:
```json
"containerEnv": {
  "YOUR_VARIABLE": "value"
}
```

### Database Services
Uncomment database services in `docker-compose.yml`:
```yaml
postgres:
  image: postgres:15-alpine
  # ... configuration
```

### VS Code Settings
Customize in `.devcontainer/devcontainer.json`:
```json
"customizations": {
  "vscode": {
    "settings": {
      "your.setting": "value"
    }
  }
}
```

## Benefits

### For Developers
- ✅ **Consistent environment** across all machines
- ✅ **Zero setup time** - everything pre-configured
- ✅ **Isolated development** - no system pollution
- ✅ **Easy onboarding** for new team members

### For Teams
- ✅ **No environment drift** - everyone uses identical setup
- ✅ **Reproducible builds** - same results everywhere
- ✅ **Easy CI/CD** - same environment in development and production
- ✅ **Version control** - environment configuration is versioned

### For Project
- ✅ **Modern tooling** - Latest Node.js, TypeScript, testing tools
- ✅ **Best practices** - Pre-configured linting, formatting, testing
- ✅ **Performance** - Optimized for development workflow
- ✅ **Extensible** - Easy to add new tools and services

## Getting Help

### VS Code DevContainers
- [Official Documentation](https://code.visualstudio.com/docs/devcontainers/containers)
- [Container Registry](https://github.com/devcontainers/images)

### Docker
- [Docker Documentation](https://docs.docker.com/)
- [Docker Desktop](https://docs.docker.com/desktop/)

### Project Specific
- Check the welcome message in the terminal
- Run `scripts/status.sh` for project status
- Use the pre-configured aliases for common tasks

**Happy coding in your containerized environment! 🚀**
