===============
Troubleshooting
===============

Quick Solutions to Common Gamification Issues
=============================================

This guide helps you resolve the most common issues with Swiss Budget Pro's gamification system. Most problems can be solved quickly with these step-by-step solutions.

.. image:: _static/troubleshooting-hero.png
   :alt: Troubleshooting Guide
   :align: center

🔧 **Quick Diagnostic Checklist**

Before diving into specific issues, try these general solutions:
1. Refresh your browser or restart the mobile app
2. Check your internet connection
3. Verify you're logged into the correct account
4. Clear browser cache or app data
5. Update to the latest app version

XP & Leveling Issues
===================

XP Not Updating
---------------

**Symptoms**: XP doesn't increase after actions, delayed updates, incorrect amounts

**Solutions**:

🔄 **Immediate Fixes**
   1. **Refresh the page** - XP updates may take 30-60 seconds
   2. **Check minimum amounts** - Savings need CHF 10 minimum
   3. **Verify account connections** - Ensure bank accounts are properly linked
   4. **Review action requirements** - Some XP sources have specific conditions

📊 **Check XP Sources**
   .. code-block:: none
   
      Navigate to: Profile → XP History
      Look for: Recent transactions and their XP awards
      Verify: Amounts match expected calculations
      Contact support if: Discrepancies persist after 24 hours

🇨🇭 **Swiss Bonus Issues**
   - **Pillar 3a not recognized**: Verify account is properly categorized
   - **Canton bonuses missing**: Check profile location settings
   - **Tax optimization not credited**: Ensure features are used correctly

**Advanced Troubleshooting**:
   - Clear browser cache and cookies
   - Disable browser extensions temporarily
   - Try incognito/private browsing mode
   - Contact support with specific transaction details

Level-Up Not Triggering
-----------------------

**Symptoms**: Reached XP threshold but no level-up notification

**Solutions**:

✅ **Verification Steps**
   1. **Check exact XP requirements** - Level 2 needs 1,000 XP, Level 3 needs 2,500 XP
   2. **Force refresh** - Close and reopen app completely
   3. **Check notification settings** - Ensure level-up alerts are enabled
   4. **Review XP history** - Verify total XP calculation

🎉 **Missing Celebrations**
   - Check notification permissions in device settings
   - Verify celebration animations are enabled in preferences
   - Try triggering level-up on different device
   - Contact support if celebrations consistently fail

Achievement Issues
=================

Achievements Not Unlocking
--------------------------

**Symptoms**: Met requirements but achievement remains locked

**Solutions**:

🏆 **Requirement Verification**
   1. **Read exact requirements** - Some achievements have multiple conditions
   2. **Check level prerequisites** - Some achievements require minimum levels
   3. **Verify time periods** - Some require consecutive days or specific timeframes
   4. **Review Swiss-specific conditions** - Canton residence, Pillar 3a setup, etc.

📋 **Common Achievement Issues**
   .. list-table:: Achievement Troubleshooting
      :header-rows: 1
      :widths: 30 35 35

      * - Achievement
        - Common Issue
        - Solution
      * - First Savings
        - Minimum amount not met
        - Ensure CHF 10+ contribution
      * - Swiss Saver
        - Pillar 3a not recognized
        - Verify account categorization
      * - Week Warrior
        - Streak broken unknowingly
        - Check streak calendar for gaps
      * - Goal Setter
        - Goal not properly saved
        - Recreate goal with all fields

🔄 **Force Refresh Achievements**
   - Navigate to Achievements page
   - Pull down to refresh (mobile) or F5 (web)
   - Wait 2-3 minutes for processing
   - Contact support if still missing after 24 hours

Secret Achievements Not Appearing
---------------------------------

**Symptoms**: Completed hidden requirements but no unlock

**Solutions**:

🕵️ **Secret Achievement Tips**
   - **Be patient** - Secret achievements may take longer to process
   - **Try variations** - Experiment with different amounts, times, or methods
   - **Check community forums** - Other users may have discovered triggers
   - **Document your actions** - Keep track of what you've tried

🤫 **Known Secret Triggers**
   - **Night Owl**: Contribution between midnight and 6 AM
   - **Early Bird**: Contribution between 5 AM and 7 AM
   - **Lucky Number**: Specific contribution amounts (try CHF 77, 111, 333)
   - **Swiss Precision**: Exact timing with Swiss events or holidays

Streak Issues
============

Streak Broken Unexpectedly
--------------------------

**Symptoms**: Streak shows as broken despite daily actions

**Solutions**:

🔥 **Streak Verification**
   1. **Check exact timing** - Streaks require actions within 24-hour periods
   2. **Verify minimum amounts** - CHF 10 for savings, full budget check for adherence
   3. **Review timezone settings** - Ensure correct timezone in profile
   4. **Check weekend actions** - Streaks continue through weekends

📅 **Streak Calendar Review**
   .. code-block:: none
   
      Navigate to: Streaks → Calendar View
      Look for: Red days indicating missed actions
      Check: Exact times of contributions
      Verify: All required actions were completed

🛡️ **Streak Protection Issues**
   - **Protection not available**: Need 7+ day streak to use protection
   - **Protection not working**: Must be used within 24 hours of missed day
   - **No protections left**: Limited to 3 per month per streak type

**Recovery Steps**:
   1. **Don't panic** - Streak breaks are learning opportunities
   2. **Start immediately** - Begin new streak the next day
   3. **Adjust minimums** - Lower daily requirements if needed
   4. **Set better reminders** - Use multiple notification methods

Streak Not Starting
-------------------

**Symptoms**: Actions completed but streak counter stays at 0

**Solutions**:

🚀 **Streak Initialization**
   - **Complete full action** - Not just partial requirements
   - **Wait for processing** - Streaks update within 1 hour
   - **Check action validity** - Ensure action meets all criteria
   - **Verify account connections** - Automatic tracking requires linked accounts

Goal Issues
==========

Goals Not Tracking Progress
---------------------------

**Symptoms**: Contributions made but goal progress unchanged

**Solutions**:

🎯 **Goal Configuration Check**
   1. **Verify goal settings** - Ensure correct target amount and timeline
   2. **Check contribution allocation** - Verify contributions assigned to correct goal
   3. **Review account linking** - Ensure goal is connected to right accounts
   4. **Confirm currency settings** - All amounts should be in CHF

💰 **Contribution Tracking**
   .. code-block:: none
   
      Navigate to: Goals → [Goal Name] → Contributions
      Verify: All contributions are listed
      Check: Amounts and dates are correct
      Ensure: No duplicate or missing entries

🔗 **Account Connection Issues**
   - **Manual entry required**: Some goals need manual contribution logging
   - **Bank sync delays**: Automatic updates may take 24-48 hours
   - **Category mismatches**: Ensure transactions categorized correctly

Goal Milestones Not Celebrating
-------------------------------

**Symptoms**: Reached milestone percentage but no celebration

**Solutions**:

🎉 **Milestone Verification**
   - **Check exact percentages** - 25%, 50%, 75%, 100% trigger celebrations
   - **Verify calculation** - Current amount ÷ target amount × 100
   - **Review milestone history** - Check if already celebrated
   - **Enable celebrations** - Ensure milestone notifications are turned on

Swiss Features Issues
====================

Pillar 3a Not Recognized
------------------------

**Symptoms**: Pillar 3a contributions not getting Swiss bonuses

**Solutions**:

🏛️ **Pillar 3a Setup**
   1. **Verify account type** - Ensure account is categorized as Pillar 3a
   2. **Check provider recognition** - Major providers (Viac, finpension) auto-detected
   3. **Manual categorization** - Tag transactions as pillar3a_contribution
   4. **Update profile** - Ensure Swiss residency and employment status correct

🇨🇭 **Swiss Bonus Troubleshooting**
   .. list-table:: Swiss Feature Issues
      :header-rows: 1
      :widths: 30 35 35

      * - Feature
        - Common Issue
        - Solution
      * - Pillar 3a Bonus
        - Account not recognized
        - Manually categorize transactions
      * - Canton Comparison
        - Location not set
        - Update profile location
      * - Tax Optimization
        - Features not unlocked
        - Reach required level (15+)
      * - Healthcare Bonus
        - Deductible not set
        - Complete health profile

Canton Features Not Working
--------------------------

**Symptoms**: Canton comparison or optimization tools unavailable

**Solutions**:

🗺️ **Canton Setup**
   - **Set location** - Ensure Swiss canton selected in profile
   - **Verify residency** - Confirm Swiss residency status
   - **Check level requirements** - Advanced canton features need Level 20+
   - **Update tax information** - Provide necessary tax details

Technical Issues
===============

App Performance Problems
-----------------------

**Symptoms**: Slow loading, crashes, unresponsive interface

**Solutions**:

⚡ **Performance Optimization**
   1. **Close other apps** - Free up device memory
   2. **Clear app cache** - Remove temporary files
   3. **Update app** - Install latest version
   4. **Restart device** - Clear system memory
   5. **Check storage space** - Ensure adequate free space

🌐 **Browser-Specific Issues**
   - **Clear browser cache** - Remove stored data
   - **Disable extensions** - Temporarily turn off ad blockers
   - **Try different browser** - Test with Chrome, Firefox, Safari
   - **Check JavaScript** - Ensure JavaScript is enabled

Sync Issues Between Devices
---------------------------

**Symptoms**: Different data on mobile vs. web, missing updates

**Solutions**:

🔄 **Sync Troubleshooting**
   1. **Check internet connection** - Ensure stable connection on all devices
   2. **Force sync** - Pull down to refresh on mobile, F5 on web
   3. **Log out and back in** - Refresh authentication tokens
   4. **Wait for propagation** - Changes may take 5-10 minutes to sync

📱 **Device-Specific Steps**
   - **Mobile**: Force close app, clear cache, restart
   - **Web**: Clear cookies, disable cache, hard refresh (Ctrl+F5)
   - **Multiple accounts**: Verify logged into same account on all devices

Bank Connection Issues
=====================

Account Not Connecting
----------------------

**Symptoms**: Unable to link bank account, connection errors

**Solutions**:

🏦 **Connection Troubleshooting**
   1. **Verify credentials** - Double-check username and password
   2. **Check bank support** - Ensure your bank is supported
   3. **Update bank app** - Some banks require latest app version
   4. **Try different method** - Use manual entry if automatic fails
   5. **Contact bank** - Some banks require API access approval

🔐 **Security Considerations**
   - **Two-factor authentication** - May need to approve connection
   - **Bank notifications** - Check for security alerts from bank
   - **VPN interference** - Disable VPN during connection setup
   - **Firewall settings** - Ensure app can access internet

Transactions Not Importing
--------------------------

**Symptoms**: Connected but transactions not appearing

**Solutions**:

📊 **Import Troubleshooting**
   - **Check date range** - Verify import period settings
   - **Review account selection** - Ensure correct accounts selected
   - **Wait for processing** - Initial import may take 24-48 hours
   - **Manual refresh** - Force transaction sync

Getting Additional Help
======================

When to Contact Support
----------------------

📞 **Contact Support If**:
   - Issues persist after trying all troubleshooting steps
   - XP calculations seem consistently wrong
   - Achievements missing for over 24 hours
   - Bank connections fail repeatedly
   - Data appears corrupted or lost

📧 **How to Contact Support**:
   - **Email**: <EMAIL>
   - **Live Chat**: Available 9 AM - 6 PM CET
   - **Community Forum**: community.swissbudgetpro.ch
   - **In-App Support**: Use feedback button in app

📋 **Information to Include**:
   - Detailed description of the issue
   - Steps you've already tried
   - Screenshots or error messages
   - Device type and app version
   - Approximate time when issue occurred

Community Resources
------------------

👥 **Community Help**:
   - **Forum Search**: Check if others had similar issues
   - **User Guides**: Community-created troubleshooting guides
   - **Expert Users**: Experienced users often provide quick help
   - **Swiss-Specific Help**: Local users understand Swiss banking quirks

📚 **Self-Help Resources**:
   - **Video Tutorials**: Step-by-step visual guides
   - **FAQ Section**: Common questions and answers
   - **Feature Guides**: Detailed documentation for each feature
   - **Best Practices**: Tips from successful users

Prevention Tips
==============

Avoiding Common Issues
---------------------

🛡️ **Preventive Measures**:
   1. **Regular Updates** - Keep app updated to latest version
   2. **Stable Internet** - Use reliable connection for important actions
   3. **Backup Important Data** - Export data regularly
   4. **Monitor XP Trends** - Notice issues early through regular checking
   5. **Read Release Notes** - Stay informed about changes and known issues

📱 **Best Practices**:
   - **Single Device Setup** - Configure features on one device first
   - **Gradual Feature Adoption** - Don't enable everything at once
   - **Regular Maintenance** - Weekly review of goals, streaks, and achievements
   - **Community Engagement** - Stay connected with other users for tips

.. tip::
   **Pro Tip**: Most gamification issues resolve themselves within 24 hours. If you're experiencing problems, try the basic troubleshooting steps and wait a day before contacting support!

.. seealso::
   - :doc:`faq` - Frequently asked questions
   - :doc:`getting-started` - Basic setup guidance
   - :doc:`swiss-features` - Swiss-specific troubleshooting
   - Community Forum: community.swissbudgetpro.ch
