=============
Smart Tagging
=============

Transform Every Transaction into Financial Intelligence
======================================================

Smart tagging automatically categorizes your transactions and analyzes their impact on your financial goals. This AI-powered system turns mundane spending data into actionable insights while rewarding smart financial decisions with XP.

.. image:: _static/smart-tagging-hero.png
   :alt: Smart Tagging System
   :align: center

🧠 **How Smart Tagging Works**

Our ML-powered system analyzes transaction descriptions, merchant information, amounts, and patterns to automatically assign relevant tags. Each tag carries XP modifiers that reward goal-supporting behaviors and gently discourage goal-hindering ones.

Tagging Categories & XP Impact
==============================

Goal Booster Tags 🎯
--------------------

These tags identify transactions that directly support your financial goals:

.. list-table:: Goal Booster Tag Examples
   :header-rows: 1
   :widths: 25 25 25 25

   * - Tag
     - XP Modifier
     - Examples
     - Swiss Context
   * - savings_deposit
     - +15 XP
     - Bank transfers to savings
     - PostFinance, UBS savings
   * - pillar3a_contribution
     - +12 XP
     - Retirement contributions
     - Viac, finpension, bank 3a
   * - investment_purchase
     - +10 XP
     - Stock, fund purchases
     - Swissquote, Interactive Brokers
   * - debt_payment
     - +8 XP
     - Extra loan payments
     - Mortgage, credit payments
   * - emergency_fund
     - +10 XP
     - Emergency fund deposits
     - Dedicated savings accounts

Smart Choice Tags 💡
--------------------

Recognize financially intelligent spending decisions:

.. list-table:: Smart Choice Tag Examples
   :header-rows: 1
   :widths: 25 25 25 25

   * - Tag
     - XP Modifier
     - Examples
     - Swiss Context
   * - bulk_purchase
     - +8 XP
     - Costco, bulk buying
     - Denner, Aldi bulk deals
   * - discount_shopping
     - +6 XP
     - Sale items, coupons
     - Migros actions, Coop deals
   * - cashback_earned
     - +5 XP
     - Credit card rewards
     - Cumulus, Supercard points
   * - price_comparison
     - +4 XP
     - Best price shopping
     - Comparis, TopPreise usage
   * - seasonal_purchase
     - +3 XP
     - Off-season buying
     - Winter gear in summer

Swiss Saver Tags 🇨🇭
---------------------

Celebrate Swiss-specific financial optimizations:

.. list-table:: Swiss Saver Tag Examples
   :header-rows: 1
   :widths: 25 25 25 25

   * - Tag
     - XP Modifier
     - Examples
     - Swiss Benefits
   * - public_transport
     - +10 XP
     - SBB, local transport
     - Tax deductible, eco-friendly
   * - tax_deductible
     - +8 XP
     - Professional expenses
     - Reduces taxable income
   * - healthcare_hsa
     - +6 XP
     - Health savings account
     - Deductible optimization
   * - swiss_insurance
     - +5 XP
     - Local insurance payments
     - Mandatory coverage
   * - canton_services
     - +4 XP
     - Government services
     - Local tax payments

Investment Tags 📈
-----------------

Encourage long-term wealth building:

.. list-table:: Investment Tag Examples
   :header-rows: 1
   :widths: 25 25 25 25

   * - Tag
     - XP Modifier
     - Examples
     - Investment Type
   * - education_investment
     - +12 XP
     - Courses, certifications
     - Career development
   * - tool_investment
     - +8 XP
     - Professional tools
     - Income-generating assets
   * - health_investment
     - +6 XP
     - Gym, healthy food
     - Long-term health costs
   * - home_improvement
     - +5 XP
     - Property upgrades
     - Asset value increase
   * - skill_development
     - +4 XP
     - Books, workshops
     - Knowledge building

Reward Tags 🎁
--------------

Acknowledge earned treats and celebrations:

.. list-table:: Reward Tag Examples
   :header-rows: 1
   :widths: 25 25 25 25

   * - Tag
     - XP Modifier
     - Examples
     - Context
   * - goal_celebration
     - +2 XP
     - Milestone rewards
     - Earned treats
   * - achievement_reward
     - +2 XP
     - Success celebrations
     - Planned rewards
   * - social_investment
     - +1 XP
     - Friend activities
     - Relationship building
   * - mental_health
     - +1 XP
     - Stress relief
     - Well-being maintenance
   * - planned_treat
     - 0 XP
     - Budgeted fun
     - Controlled indulgence

Goal Blocker Tags ⚠️
--------------------

Identify spending that may hinder financial progress:

.. list-table:: Goal Blocker Tag Examples
   :header-rows: 1
   :widths: 25 25 25 25

   * - Tag
     - XP Modifier
     - Examples
     - Impact
   * - impulse_purchase
     - -5 XP
     - Unplanned buying
     - Budget disruption
   * - luxury_spending
     - -3 XP
     - High-end items
     - Opportunity cost
   * - subscription_creep
     - -2 XP
     - Unused services
     - Recurring waste
   * - convenience_fee
     - -2 XP
     - ATM fees, delivery
     - Avoidable costs
   * - late_payment_fee
     - -5 XP
     - Penalty charges
     - Poor planning

Neutral Tags ⚖️
---------------

Essential expenses with no XP impact:

.. list-table:: Neutral Tag Examples
   :header-rows: 1
   :widths: 25 25 25 25

   * - Tag
     - XP Modifier
     - Examples
     - Category
   * - essential_groceries
     - 0 XP
     - Basic food items
     - Necessary expenses
   * - utilities
     - 0 XP
     - Electricity, water
     - Fixed costs
   * - rent_mortgage
     - 0 XP
     - Housing payments
     - Mandatory expenses
   * - insurance_premium
     - 0 XP
     - Required coverage
     - Risk management
   * - transportation
     - 0 XP
     - Commuting costs
     - Work-related

Swiss Merchant Recognition
=========================

Automatic Swiss Business Detection
----------------------------------

🏪 **Major Swiss Retailers**
   - **Migros**: Automatically tagged as smart_choice for actions
   - **Coop**: Recognized for bulk purchases and member deals
   - **Denner**: Tagged for discount shopping
   - **Manor**: Luxury vs. necessity distinction
   - **Galaxus**: Online shopping categorization

🏦 **Swiss Financial Institutions**
   - **UBS, Credit Suisse**: Investment and banking services
   - **PostFinance**: Savings and payment services
   - **Raiffeisen**: Local banking recognition
   - **Swissquote**: Investment platform transactions
   - **Viac, finpension**: Pillar 3a providers

🚊 **Swiss Transport**
   - **SBB**: Public transport with tax deduction tags
   - **TPG, VBZ**: Local transport systems
   - **Mobility**: Car sharing services
   - **PubliBike**: Bike sharing recognition
   - **Parking meters**: Urban mobility costs

🏥 **Swiss Healthcare**
   - **Pharmacies**: Health investment tags
   - **Hospitals**: Healthcare expense tracking
   - **Insurance companies**: Premium payment recognition
   - **Fitness centers**: Health investment categorization
   - **Medical practices**: Healthcare cost tracking

Goal Alignment Analysis
======================

Impact Scoring System
--------------------

📊 **Alignment Score Calculation**
   Each transaction receives an alignment score from -10 to +10 based on:
   - Tag XP modifiers
   - Goal category relevance
   - Amount relative to goal targets
   - Timing within goal timeline
   - Historical pattern analysis

🎯 **Goal Impact Categories**
   - **Highly Supportive** (+7 to +10): Direct goal contributions
   - **Supportive** (+3 to +6): Indirectly helpful actions
   - **Neutral** (-2 to +2): No significant impact
   - **Hindering** (-6 to -3): Slows goal progress
   - **Highly Hindering** (-10 to -7): Significantly damages goals

Real-time Recommendations
------------------------

💡 **Smart Suggestions**
   Based on transaction analysis, the system provides:
   - **Alternative Options**: "Consider Migros instead of Manor for 15% savings"
   - **Goal Reminders**: "This purchase delays your emergency fund by 2 days"
   - **Optimization Tips**: "Use Cumulus points to reduce this expense"
   - **Swiss Benefits**: "This qualifies for tax deduction - save receipt"

🔄 **Behavioral Nudges**
   - **Spending Alerts**: Warnings for goal-hindering patterns
   - **Opportunity Highlights**: Suggestions for goal-boosting actions
   - **Swiss Optimization**: Reminders about Swiss-specific benefits
   - **Timing Advice**: Optimal timing for major purchases

Tagging Accuracy & Learning
===========================

Machine Learning Improvements
-----------------------------

🧠 **Continuous Learning**
   The system improves through:
   - **User Corrections**: Manual tag adjustments improve future accuracy
   - **Pattern Recognition**: Learning from successful user behaviors
   - **Swiss Context**: Understanding local merchant and cultural patterns
   - **Seasonal Adjustments**: Adapting to Swiss seasonal spending patterns

📈 **Accuracy Metrics**
   - **Initial Accuracy**: 85% for common Swiss merchants
   - **User-Trained Accuracy**: 95%+ after 30 days of corrections
   - **Swiss-Specific Accuracy**: 90% for local businesses
   - **Goal Alignment Accuracy**: 88% for impact predictions

Manual Tag Management
--------------------

✏️ **Tag Editing Options**
   - **Add Tags**: Include additional relevant tags
   - **Remove Tags**: Delete incorrect auto-tags
   - **Modify XP**: Adjust XP impact for personal goals
   - **Create Custom**: Build personal tag categories
   - **Bulk Edit**: Apply changes to similar transactions

🎯 **Custom Tag Creation**
   - **Personal Categories**: Create tags for unique situations
   - **Family Tags**: Shared categories for household goals
   - **Business Tags**: Professional expense categorization
   - **Seasonal Tags**: Holiday or event-specific categories

Analytics & Insights
====================

Spending Pattern Analysis
------------------------

📊 **Tag-Based Analytics**
   - **XP by Category**: See which tag types generate most XP
   - **Goal Impact Trends**: Track alignment score changes over time
   - **Swiss Optimization**: Monitor Swiss-specific tag usage
   - **Behavioral Patterns**: Identify spending habit trends

🎯 **Goal Alignment Reports**
   - **Monthly Alignment Score**: Overall goal support rating
   - **Category Breakdown**: Which spending categories help/hinder goals
   - **Improvement Opportunities**: Specific areas for optimization
   - **Swiss Benefit Utilization**: How well you're using Swiss advantages

Predictive Insights
-------------------

🔮 **Future Impact Modeling**
   - **Goal Timeline Predictions**: How current spending affects goal completion
   - **Optimization Opportunities**: Potential improvements and their impact
   - **Risk Assessments**: Spending patterns that threaten goals
   - **Swiss Benefit Maximization**: Unused opportunities for optimization

💡 **Personalized Recommendations**
   - **Spending Adjustments**: Specific changes to improve goal alignment
   - **Swiss Feature Usage**: Underutilized Swiss optimization opportunities
   - **Habit Formation**: Suggestions for building better financial habits
   - **Goal Prioritization**: Recommendations for goal focus based on spending

Community Features
=================

Shared Tagging Intelligence
---------------------------

👥 **Community Contributions**
   - **Merchant Database**: Shared knowledge of Swiss businesses
   - **Tag Suggestions**: Community-recommended tags for transactions
   - **Best Practices**: Successful tagging strategies from other users
   - **Swiss Insights**: Local knowledge about Swiss financial optimization

🏆 **Tagging Achievements**
   - **Tagging Master**: Accurately tag 100 transactions (+200 XP)
   - **Swiss Expert**: Identify 50 Swiss-specific optimizations (+300 XP)
   - **Community Helper**: Contribute 25 merchant identifications (+150 XP)
   - **Goal Optimizer**: Achieve 90%+ goal alignment score (+400 XP)

Privacy & Control
================

Data Protection
--------------

🔒 **Privacy Safeguards**
   - **Local Processing**: Tag analysis happens on your device when possible
   - **Anonymized Learning**: ML improvements use anonymized data only
   - **Opt-out Options**: Disable automatic tagging if preferred
   - **Data Retention**: Control how long tagging data is stored

🎛️ **User Control**
   - **Tagging Preferences**: Customize which categories to use
   - **XP Modifiers**: Adjust XP values for personal priorities
   - **Auto-tag Settings**: Control automation level
   - **Sharing Options**: Choose what tagging data to share

Common Tagging Questions
=======================

❓ **"Can I disagree with automatic tags?"**
   Absolutely! Edit any tag to improve accuracy. Your corrections help the system learn your preferences.

❓ **"Why did I get negative XP for a purchase?"**
   Some tags carry negative XP to highlight spending that may hinder your goals. It's feedback, not punishment.

❓ **"How accurate is Swiss merchant recognition?"**
   About 90% accurate for major Swiss businesses, improving constantly through community contributions.

❓ **"Can I create my own tags?"**
   Yes! Create custom tags for personal situations, family goals, or business expenses.

❓ **"Do tags affect my actual finances?"**
   No, tags only provide insights and XP. They don't change your actual account balances or transactions.

❓ **"How do I maximize XP from tagging?"**
   Focus on goal-supporting purchases, use Swiss optimization opportunities, and maintain consistent positive spending patterns.

Next Steps
==========

Ready to optimize your transaction tagging? Here's your action plan:

1. **Review Recent Transactions** - Check auto-tags for accuracy
2. **Correct Any Mistakes** - Edit tags to improve future accuracy
3. **Explore Tag Categories** - Understand XP modifiers for each type
4. **Set Up Goal Alignment** - Connect tags to your specific goals
5. **Monitor Insights** - Use analytics to optimize spending patterns

.. tip::
   **Pro Tip**: The most successful users spend 5 minutes weekly reviewing and correcting tags. This small investment dramatically improves the accuracy and value of their financial insights!

.. seealso::
   - :doc:`savings-goals` - Connect tagging to your goals
   - :doc:`xp-system` - Understand tag XP modifiers
   - :doc:`swiss-features` - Maximize Swiss-specific tags
   - :doc:`getting-started` - Set up your first tags
