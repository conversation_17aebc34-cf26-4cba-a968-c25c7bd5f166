===============
Streak Tracking
===============

Build Unstoppable Financial Habits Through Streak Power
=======================================================

Streak tracking transforms sporadic financial actions into powerful daily habits. By maintaining consistent streaks, you unlock exponential XP multipliers and build the foundation for long-term financial success.

.. image:: _static/streak-tracking-hero.png
   :alt: Streak Tracking System
   :align: center

🔥 **The Power of Streaks**

Research shows that it takes 21-66 days to form a habit. Our streak system gamifies this process, providing immediate rewards for consistency while building behaviors that compound over time.

Streak Types & Mechanics
========================

Available Streak Categories
---------------------------

💰 **Savings Streaks**
   - **Action**: Daily savings contribution (any amount)
   - **Minimum**: CHF 10 per contribution
   - **XP Base**: 5 XP per day
   - **Swiss Bonus**: +20% for Pillar 3a contributions

📊 **Budget Adherence Streaks**
   - **Action**: Stay within daily/weekly budget limits
   - **Tracking**: Automatic via connected accounts
   - **XP Base**: 5 XP per day
   - **Bonus**: +50% for perfect adherence

🎯 **Goal Progress Streaks**
   - **Action**: Make progress toward any active goal
   - **Minimum**: Any contribution amount
   - **XP Base**: 5 XP per day
   - **Bonus**: +25% for multiple goals

📱 **App Usage Streaks**
   - **Action**: Open app and check financial status
   - **Minimum**: 30 seconds of engagement
   - **XP Base**: 3 XP per day
   - **Bonus**: +10% for feature exploration

🇨🇭 **Swiss Optimization Streaks**
   - **Action**: Use Swiss-specific features daily
   - **Examples**: Pillar 3a, canton comparison, tax tools
   - **XP Base**: 8 XP per day
   - **Swiss Bonus**: +30% for comprehensive usage

Streak Multiplier System
========================

Progressive Multiplier Scale
---------------------------

.. list-table:: Streak Multiplier Progression
   :header-rows: 1
   :widths: 20 20 20 40

   * - Streak Length
     - Multiplier
     - Daily XP
     - Milestone Reward
   * - 1-2 days
     - 1.0x
     - 5 XP
     - Getting started
   * - 3-6 days
     - 1.1x
     - 5.5 XP
     - Building momentum (+25 XP)
   * - 7-13 days
     - 1.5x
     - 7.5 XP
     - Week warrior (+75 XP)
   * - 14-20 days
     - 1.75x
     - 8.75 XP
     - Two-week champion (+150 XP)
   * - 21-29 days
     - 1.9x
     - 9.5 XP
     - Habit former (+200 XP)
   * - 30-59 days
     - 2.0x
     - 10 XP
     - Monthly master (+300 XP)
   * - 60-99 days
     - 2.25x
     - 11.25 XP
     - Consistency king (+500 XP)
   * - 100-179 days
     - 2.5x
     - 12.5 XP
     - Centurion (+1,000 XP)
   * - 180-364 days
     - 2.75x
     - 13.75 XP
     - Half-year hero (+2,000 XP)
   * - 365+ days
     - 3.0x
     - 15 XP
     - Annual achiever (+3,650 XP)

Swiss-Specific Multipliers
--------------------------

🏛️ **Pillar 3a Streak Bonuses**
   - Base multiplier + 20% for Pillar 3a contributions
   - Monthly consistency bonus: +50 XP
   - Annual maximum streak: +500 XP

🗺️ **Canton Optimization Streaks**
   - Daily canton feature usage: +30% multiplier
   - Weekly optimization actions: +100 XP
   - Monthly relocation analysis: +200 XP

🏥 **Healthcare Optimization Streaks**
   - Daily health expense tracking: +25% multiplier
   - Weekly deductible optimization: +75 XP
   - Monthly insurance review: +150 XP

Streak Protection System
=======================

Protection Mechanics
--------------------

🛡️ **How Protection Works**
   - Available for streaks of 7+ days
   - 3 protections per streak type per month
   - Protects against single-day breaks
   - Must be used within 24 hours of missed day

🔄 **Protection Renewal**
   - Resets monthly for active users
   - Bonus protections for premium users
   - Achievement-based protection bonuses
   - Community challenge protections

⚡ **Smart Protection Suggestions**
   - Automatic protection offers for long streaks
   - Risk assessment for upcoming breaks
   - Vacation mode protection
   - Emergency situation handling

Protection Strategy
------------------

🎯 **When to Use Protection**
   - Streaks of 30+ days (high value)
   - Approaching major milestones
   - Unexpected emergencies
   - Technical issues or travel

💡 **Protection Conservation**
   - Save for longest streaks
   - Don't waste on short streaks
   - Plan around known breaks
   - Use vacation mode instead

📅 **Vacation Mode**
   - Pause streaks for planned breaks
   - Available for 3-14 day periods
   - Maintains streak without daily actions
   - Limited uses per year

Streak Building Strategies
=========================

Starting Your First Streak
--------------------------

🚀 **Day 1-7: Foundation**
   1. **Choose One Streak Type**: Start with savings or app usage
   2. **Set Minimum Viable Action**: CHF 10 savings or 30-second app check
   3. **Link to Existing Habit**: Attach to morning coffee or evening routine
   4. **Set Daily Reminder**: Use push notifications or calendar alerts
   5. **Track Visually**: Use the streak calendar for motivation

📈 **Day 8-21: Momentum**
   1. **Increase Consistency**: Same time each day
   2. **Add Second Streak**: Layer in budget adherence or goal progress
   3. **Celebrate Milestones**: Acknowledge week warrior achievement
   4. **Join Community**: Share progress and get encouragement
   5. **Plan for Obstacles**: Identify potential break scenarios

🏆 **Day 22+: Mastery**
   1. **Optimize Actions**: Increase contribution amounts gradually
   2. **Stack Streaks**: Maintain multiple streak types
   3. **Mentor Others**: Help community members start streaks
   4. **Plan Long-term**: Set 100-day and 365-day targets
   5. **Prepare Protection**: Save protections for critical moments

Advanced Streak Techniques
--------------------------

⚡ **Streak Stacking**
   Combine multiple streak types for maximum XP:
   
   .. code-block:: none
   
      Daily Routine Example:
      1. Morning: Check app (3 XP × 1.5x = 4.5 XP)
      2. Lunch: Make savings contribution (5 XP × 1.5x = 7.5 XP)
      3. Evening: Review budget adherence (5 XP × 1.5x = 7.5 XP)
      Total: 19.5 XP per day with 7-day streaks

🇨🇭 **Swiss Optimization Stacking**
   Maximize Swiss bonuses within streaks:
   
   .. code-block:: none
   
      Swiss Power Day:
      1. Pillar 3a contribution (8 XP × 2.0x × 1.2 = 19.2 XP)
      2. Canton comparison check (5 XP × 2.0x × 1.3 = 13 XP)
      3. Tax optimization review (5 XP × 2.0x × 1.3 = 13 XP)
      Total: 45.2 XP per day with Swiss focus

🎯 **Micro-Habit Building**
   Start impossibly small and scale up:
   - Week 1: CHF 5 daily savings
   - Week 2: CHF 10 daily savings
   - Week 3: CHF 15 daily savings
   - Month 2: CHF 25 daily savings
   - Month 3: CHF 50 daily savings

Streak Recovery & Maintenance
=============================

Handling Streak Breaks
----------------------

😔 **When Streaks Break**
   1. **Don't Despair**: Breaks are learning opportunities
   2. **Analyze the Cause**: Identify what led to the break
   3. **Restart Immediately**: Begin new streak the next day
   4. **Adjust Strategy**: Make the habit easier or more automatic
   5. **Use Protection**: Save remaining protections for longer streaks

🔄 **Recovery Strategies**
   - **Immediate Restart**: Don't wait for Monday or next month
   - **Lower the Bar**: Reduce minimum action requirements
   - **Add Automation**: Set up automatic contributions
   - **Increase Reminders**: More frequent notifications
   - **Find Accountability**: Partner with friends or community

📈 **Building Resilience**
   - **Plan for Obstacles**: Identify likely break scenarios
   - **Create Backup Plans**: Alternative actions for difficult days
   - **Build Buffer Habits**: Multiple ways to maintain streaks
   - **Practice Self-Compassion**: Treat breaks as data, not failures

Long-term Streak Maintenance
----------------------------

🏃‍♂️ **Avoiding Burnout**
   - **Sustainable Minimums**: Keep daily requirements achievable
   - **Periodic Reviews**: Adjust streak goals quarterly
   - **Celebration Rituals**: Acknowledge major milestones
   - **Community Support**: Share challenges and successes

⚖️ **Balancing Multiple Streaks**
   - **Priority System**: Rank streaks by importance
   - **Resource Allocation**: Don't overcommit to too many streaks
   - **Seasonal Adjustments**: Adapt to life changes
   - **Strategic Breaks**: Sometimes stopping one streak helps others

🎯 **Evolution Strategy**
   - **Gradual Increases**: Slowly raise contribution amounts
   - **New Streak Types**: Add complexity over time
   - **Swiss Feature Integration**: Incorporate new tools as they unlock
   - **Mentorship Role**: Help others as you advance

Streak Analytics & Insights
===========================

Performance Tracking
--------------------

📊 **Key Metrics**
   - **Current Streak Length**: Days for each streak type
   - **Longest Streak**: Personal record for each category
   - **Total Streak Days**: Cumulative across all types
   - **Streak Success Rate**: Percentage of days with successful actions
   - **XP from Streaks**: Total XP earned through streak multipliers

📈 **Trend Analysis**
   - **Weekly Patterns**: Identify strongest and weakest days
   - **Monthly Cycles**: Spot seasonal or cyclical trends
   - **Correlation Analysis**: Link streak success to other factors
   - **Predictive Modeling**: Forecast streak break risks

🎯 **Optimization Opportunities**
   - **Best Streak Times**: Identify optimal daily timing
   - **Habit Stacking**: Find successful combination patterns
   - **Risk Factors**: Understand what leads to breaks
   - **Success Predictors**: Identify factors that support long streaks

Community Features
-----------------

🏆 **Streak Leaderboards**
   - **Current Streaks**: Longest active streaks by category
   - **All-time Records**: Historical longest streaks
   - **Swiss Optimization**: Specialized Swiss feature streaks
   - **Community Challenges**: Group streak competitions

👥 **Social Support**
   - **Streak Buddies**: Partner with friends for accountability
   - **Community Challenges**: Join group streak events
   - **Mentorship Program**: Learn from experienced streak builders
   - **Success Stories**: Share and learn from others' journeys

💬 **Discussion Forums**
   - **Streak Strategies**: Share tips and techniques
   - **Motivation Support**: Encourage others during difficult periods
   - **Break Recovery**: Help others restart after breaks
   - **Milestone Celebrations**: Celebrate community achievements

Streak Achievements
==================

Streak-Specific Achievements
---------------------------

🔥 **Consistency Achievements**
   - **Getting Started**: 3-day streak (+25 XP)
   - **Week Warrior**: 7-day streak (+75 XP)
   - **Monthly Master**: 30-day streak (+300 XP)
   - **Centurion**: 100-day streak (+1,000 XP)
   - **Annual Achiever**: 365-day streak (+3,650 XP)

⚡ **Multi-Streak Achievements**
   - **Double Duty**: Maintain 2 streak types for 7 days (+150 XP)
   - **Triple Threat**: Maintain 3 streak types for 14 days (+400 XP)
   - **Streak Master**: Maintain 4+ streak types for 30 days (+1,000 XP)

🇨🇭 **Swiss Streak Achievements**
   - **Swiss Saver**: 21-day Pillar 3a streak (+200 XP)
   - **Canton Explorer**: 30-day Swiss feature streak (+300 XP)
   - **Swiss Master**: 90-day comprehensive Swiss streak (+750 XP)

🛡️ **Protection Achievements**
   - **Streak Saver**: Use first streak protection (+50 XP)
   - **Strategic Protector**: Save streak over 50 days with protection (+200 XP)
   - **Protection Master**: Use all protections wisely in one month (+300 XP)

Common Streak Questions
======================

❓ **"What happens if I miss a day?"**
   Your streak resets to 0, but you can restart immediately. Use streak protection if available for streaks 7+ days.

❓ **"Can I maintain multiple streaks simultaneously?"**
   Yes! Many users maintain 2-4 different streak types. Start with one and add others gradually.

❓ **"What's the minimum action to maintain a streak?"**
   Varies by type: CHF 10 for savings, budget adherence check, any goal contribution, 30-second app usage, or any Swiss feature use.

❓ **"How do I handle weekends and holidays?"**
   Streaks run continuously. Plan weekend actions or use vacation mode for planned breaks.

❓ **"Are there any streak limits?"**
   No upper limit! The longest recorded streak is 847 days for savings contributions.

❓ **"Can I pause a streak for vacation?"**
   Yes, vacation mode pauses streaks for 3-14 days without breaking them. Limited uses per year.

Next Steps
==========

Ready to start your first streak? Here's your action plan:

1. **Choose Your Streak Type** - Start with savings or app usage
2. **Set Your Minimum Action** - Make it impossibly easy to succeed
3. **Link to Existing Habit** - Attach to something you already do daily
4. **Set Up Reminders** - Use notifications and calendar alerts
5. **Track Your Progress** - Use the visual streak calendar for motivation

.. tip::
   **Pro Tip**: The most successful streak builders start with ridiculously small actions. A CHF 5 daily savings streak is infinitely better than a CHF 100 streak that breaks after 3 days!

.. seealso::
   - :doc:`xp-system` - Understand how streak multipliers boost XP
   - :doc:`achievements` - Discover streak-related achievements
   - :doc:`getting-started` - Begin your first streak today
   - :doc:`swiss-features` - Build Swiss optimization streaks
