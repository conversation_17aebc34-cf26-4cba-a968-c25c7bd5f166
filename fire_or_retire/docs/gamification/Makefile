# Minimal makefile for Sphinx documentation
#

# You can set these variables from the command line, and also
# from the environment for the first two.
SPHINXOPTS    ?=
SPHINXBUILD  ?= sphinx-build
SOURCEDIR    = .
BUILDDIR     = _build

# Put it first so that "make" without argument is like "make help".
help:
	@$(SPHINXBUILD) -M help "$(SOURCEDIR)" "$(BUILDDIR)" $(SPHINXOPTS) $(O)

.PHONY: help Makefile

# Catch-all target: route all unknown targets to Sphinx using the new
# "make mode" option.  $(O) is meant as a shortcut for $(SPHINXOPTS).
%: Makefile
	@$(SPHINXBUILD) -M $@ "$(SOURCEDIR)" "$(BUILDDIR)" $(SPHINXOPTS) $(O)

# Custom targets for Swiss Budget Pro Gamification Documentation

# Clean build directory
clean:
	rm -rf $(BUILDDIR)/*

# Build HTML documentation
html:
	@$(SPHINXBUILD) -b html "$(SOURCEDIR)" "$(BUILDDIR)/html" $(SPHINXOPTS) $(O)
	@echo
	@echo "Build finished. The HTML pages are in $(BUILDDIR)/html."

# Build HTML documentation with live reload for development
livehtml:
	sphinx-autobuild -b html "$(SOURCEDIR)" "$(BUILDDIR)/html" $(SPHINXOPTS) $(O)

# Build PDF documentation
pdf:
	@$(SPHINXBUILD) -b latex "$(SOURCEDIR)" "$(BUILDDIR)/latex" $(SPHINXOPTS) $(O)
	@echo "Running LaTeX files through pdflatex..."
	$(MAKE) -C $(BUILDDIR)/latex all-pdf
	@echo "pdflatex finished; the PDF files are in $(BUILDDIR)/latex."

# Build EPUB documentation
epub:
	@$(SPHINXBUILD) -b epub "$(SOURCEDIR)" "$(BUILDDIR)/epub" $(SPHINXOPTS) $(O)
	@echo
	@echo "Build finished. The epub file is in $(BUILDDIR)/epub."

# Check for broken links
linkcheck:
	@$(SPHINXBUILD) -b linkcheck "$(SOURCEDIR)" "$(BUILDDIR)/linkcheck" $(SPHINXOPTS) $(O)
	@echo
	@echo "Link check complete; look for any errors in the above output " \
	      "or in $(BUILDDIR)/linkcheck/output.txt."

# Check spelling
spelling:
	@$(SPHINXBUILD) -b spelling "$(SOURCEDIR)" "$(BUILDDIR)/spelling" $(SPHINXOPTS) $(O)
	@echo
	@echo "Spelling check complete; look for any errors in the above output " \
	      "or in $(BUILDDIR)/spelling/output.txt."

# Build documentation and open in browser
view: html
	@python -c "import webbrowser; webbrowser.open('file://$(PWD)/$(BUILDDIR)/html/index.html')"

# Quick development build (faster, less thorough)
quickhtml:
	@$(SPHINXBUILD) -b html -E "$(SOURCEDIR)" "$(BUILDDIR)/html" $(SPHINXOPTS) $(O)
	@echo
	@echo "Quick build finished. The HTML pages are in $(BUILDDIR)/html."

# Build all formats
all: html pdf epub
	@echo
	@echo "All documentation formats built successfully."

# Install dependencies
install:
	pip install -r requirements.txt

# Development setup
dev-setup: install
	pip install sphinx-autobuild
	@echo "Development environment ready. Use 'make livehtml' for live reload."

# Check documentation quality
quality-check: linkcheck spelling
	@echo "Quality checks completed."

# Deploy to GitHub Pages (if configured)
deploy: html
	@echo "Deploying to GitHub Pages..."
	@if [ -d "$(BUILDDIR)/html/.git" ]; then \
		cd $(BUILDDIR)/html && \
		git add -A && \
		git commit -m "Update documentation" && \
		git push origin gh-pages; \
	else \
		echo "GitHub Pages not configured. Run 'make setup-deploy' first."; \
	fi

# Setup GitHub Pages deployment
setup-deploy:
	@echo "Setting up GitHub Pages deployment..."
	@if [ ! -d "$(BUILDDIR)/html/.git" ]; then \
		cd $(BUILDDIR)/html && \
		git init && \
		git remote add origin https://github.com/swissbudgetpro/gamification-docs.git && \
		git checkout -b gh-pages; \
	fi

# Show build statistics
stats:
	@echo "Documentation Statistics:"
	@echo "========================="
	@find . -name "*.rst" -type f | wc -l | awk '{print "RST files: " $$1}'
	@find . -name "*.md" -type f | wc -l | awk '{print "Markdown files: " $$1}'
	@find . -name "*.py" -type f | wc -l | awk '{print "Python files: " $$1}'
	@find . -name "*.png" -o -name "*.jpg" -o -name "*.gif" -o -name "*.svg" | wc -l | awk '{print "Images: " $$1}'
	@if [ -d "$(BUILDDIR)/html" ]; then \
		find $(BUILDDIR)/html -name "*.html" | wc -l | awk '{print "Generated HTML pages: " $$1}'; \
	fi

# Validate documentation structure
validate:
	@echo "Validating documentation structure..."
	@python -c "
import os
import sys

required_files = [
    'index.rst',
    'getting-started.rst',
    'xp-system.rst',
    'achievements.rst',
    'swiss-features.rst',
    'faq.rst',
    'conf.py'
]

missing_files = []
for file in required_files:
    if not os.path.exists(file):
        missing_files.append(file)

if missing_files:
    print('Missing required files:')
    for file in missing_files:
        print(f'  - {file}')
    sys.exit(1)
else:
    print('All required files present.')
"

# Create requirements.txt if it doesn't exist
requirements.txt:
	@echo "Creating requirements.txt..."
	@echo "sphinx>=5.0.0" > requirements.txt
	@echo "sphinx-rtd-theme>=1.0.0" >> requirements.txt
	@echo "sphinx-copybutton>=0.5.0" >> requirements.txt
	@echo "sphinx-tabs>=3.4.0" >> requirements.txt
	@echo "myst-parser>=0.18.0" >> requirements.txt
	@echo "sphinx-autobuild>=2021.3.14" >> requirements.txt

# Show help for custom targets
help-custom:
	@echo "Swiss Budget Pro Gamification Documentation"
	@echo "==========================================="
	@echo ""
	@echo "Custom targets:"
	@echo "  clean          Remove build directory"
	@echo "  html           Build HTML documentation"
	@echo "  livehtml       Build HTML with live reload"
	@echo "  pdf            Build PDF documentation"
	@echo "  epub           Build EPUB documentation"
	@echo "  linkcheck      Check for broken links"
	@echo "  spelling       Check spelling"
	@echo "  view           Build HTML and open in browser"
	@echo "  quickhtml      Fast HTML build"
	@echo "  all            Build all formats"
	@echo "  install        Install dependencies"
	@echo "  dev-setup      Setup development environment"
	@echo "  quality-check  Run quality checks"
	@echo "  deploy         Deploy to GitHub Pages"
	@echo "  setup-deploy   Setup GitHub Pages"
	@echo "  stats          Show documentation statistics"
	@echo "  validate       Validate documentation structure"
	@echo ""
	@echo "For development:"
	@echo "  make dev-setup    # One-time setup"
	@echo "  make livehtml     # Live reload during editing"
	@echo "  make quality-check # Before committing"
	@echo ""
