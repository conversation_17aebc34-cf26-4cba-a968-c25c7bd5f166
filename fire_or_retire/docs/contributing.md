# Contributing to Swiss Budget Pro

Thank you for your interest in contributing to Swiss Budget Pro! This guide will help you get started with contributing to the ultimate Swiss FIRE planning tool.

## Overview

Swiss Budget Pro is an open-source project dedicated to helping Swiss residents achieve Financial Independence and Early Retirement. We welcome contributions from developers, financial experts, designers, and users.

```{admonition} 🤝 Ways to Contribute
:class: tip

**Code Contributions:**
- Bug fixes and improvements
- New features and enhancements
- Performance optimizations
- Test coverage improvements

**Non-Code Contributions:**
- Documentation improvements
- Translation and localization
- User experience feedback
- Financial calculation validation
- Swiss tax law updates
```

## Getting Started

### Prerequisites

Before contributing, ensure you have:

- **Node.js 18+** and **npm 9+**
- **Git** for version control
- **Basic knowledge** of React, TypeScript, and financial concepts
- **Understanding** of Swiss financial system (helpful but not required)

### Development Setup

1. **Fork the Repository**

   ```bash
   # Fork on GitHub, then clone your fork
   git clone https://github.com/YOUR_USERNAME/swiss-budget-pro.git
   cd swiss-budget-pro
   ```

2. **Install Dependencies**

   ```bash
   npm install
   ```

3. **Start Development Server**

   ```bash
   npm run dev
   ```

4. **Run Tests**
   ```bash
   npm run test
   npm run test:e2e
   ```

## Contribution Guidelines

### Code Standards

#### TypeScript Guidelines

```typescript
// ✅ Good: Explicit types and interfaces
interface UserProfile {
  name: string;
  age: number;
  canton: CantonCode;
  income: Decimal;
}

function calculateTax(profile: UserProfile): TaxResult {
  // Implementation
}

// ❌ Avoid: Any types and implicit returns
function calculate(data: any) {
  // Implementation without return type
}
```

#### React Component Guidelines

```typescript
// ✅ Good: Functional components with proper typing
interface CalculatorProps {
  initialValue: number;
  onCalculate: (result: number) => void;
}

const Calculator: React.FC<CalculatorProps> = ({
  initialValue,
  onCalculate
}) => {
  const [value, setValue] = useState(initialValue);

  const handleCalculate = useCallback(() => {
    const result = performCalculation(value);
    onCalculate(result);
  }, [value, onCalculate]);

  return (
    <div>
      {/* Component JSX */}
    </div>
  );
};

// ❌ Avoid: Class components and inline functions
class Calculator extends React.Component {
  render() {
    return (
      <button onClick={() => this.calculate()}>
        Calculate
      </button>
    );
  }
}
```

#### Financial Calculation Guidelines

```typescript
// ✅ Good: Use Decimal.js for financial calculations
import { Decimal } from 'decimal.js';

function calculateCompoundInterest(
  principal: Decimal,
  rate: Decimal,
  time: number
): Decimal {
  return principal.mul(rate.plus(1).pow(time));
}

// ❌ Avoid: JavaScript numbers for financial calculations
function calculateInterest(principal: number, rate: number): number {
  return principal * Math.pow(1 + rate, time); // Floating point errors!
}
```

### Testing Requirements

#### Unit Tests

All new functions must include unit tests:

```typescript
import { describe, it, expect } from 'vitest';
import { calculateSavingsRate } from '../utils/calculations';

describe('calculateSavingsRate', () => {
  it('calculates savings rate correctly', () => {
    expect(calculateSavingsRate(10000, 7000)).toBe(0.3);
  });

  it('handles edge cases', () => {
    expect(calculateSavingsRate(0, 0)).toBe(0);
    expect(calculateSavingsRate(1000, 1500)).toBe(-0.5);
  });

  it('validates input types', () => {
    expect(() => calculateSavingsRate(-1000, 500)).toThrow();
  });
});
```

#### Integration Tests

Test component interactions:

```typescript
import { render, screen, fireEvent } from '@testing-library/react';
import { IncomeForm } from '../components/IncomeForm';

describe('IncomeForm', () => {
  it('updates calculations when income changes', async () => {
    const onUpdate = vi.fn();
    render(<IncomeForm onUpdate={onUpdate} />);

    const input = screen.getByLabelText('Monthly Income');
    fireEvent.change(input, { target: { value: '8000' } });

    expect(onUpdate).toHaveBeenCalledWith(8000);
  });
});
```

#### End-to-End Tests

Critical user workflows must have E2E tests:

```typescript
import { test, expect } from '@playwright/test';

test('complete FIRE calculation workflow', async ({ page }) => {
  await page.goto('/');

  // Fill basic information
  await page.fill('[data-testid="income"]', '120000');
  await page.fill('[data-testid="expenses"]', '80000');

  // Check FIRE calculation
  await expect(page.locator('[data-testid="fire-age"]')).toContainText('55');
});
```

### Documentation Standards

#### Code Documentation

````typescript
/**
 * Calculates Swiss federal tax based on 2024 tax brackets
 *
 * @param taxableIncome - Annual taxable income in CHF
 * @param deductions - Array of applicable tax deductions
 * @returns Tax calculation result with amount and effective rate
 *
 * @example
 * ```typescript
 * const result = calculateFederalTax(
 *   new Decimal(100000),
 *   [{ type: 'pillar3a', amount: new Decimal(7056) }]
 * );
 * console.log(result.amount); // Tax amount in CHF
 * ```
 */
function calculateFederalTax(
  taxableIncome: Decimal,
  deductions: TaxDeduction[] = []
): TaxResult {
  // Implementation
}
````

#### Component Documentation

```typescript
/**
 * WealthProjectionChart displays interactive wealth accumulation projections
 *
 * Features:
 * - Interactive tooltips with detailed breakdowns
 * - Multiple scenario comparison
 * - Responsive design for mobile and desktop
 * - Accessibility support with ARIA labels
 *
 * @param data - Array of wealth projection data points
 * @param scenarios - Optional scenario comparisons
 * @param onDataPointClick - Callback for data point interactions
 */
interface WealthProjectionChartProps {
  data: WealthProjection[];
  scenarios?: ScenarioData[];
  onDataPointClick?: (point: WealthProjection) => void;
}
```

## Contribution Process

### 1. Issue Creation

Before starting work, create or find an issue:

```markdown
## Bug Report / Feature Request

**Description:**
Clear description of the bug or feature

**Steps to Reproduce:** (for bugs)

1. Go to...
2. Click on...
3. See error

**Expected Behavior:**
What should happen

**Actual Behavior:** (for bugs)
What actually happens

**Swiss Context:** (if applicable)
How this relates to Swiss financial planning

**Additional Context:**
Screenshots, error logs, etc.
```

### 2. Branch Creation

Create a descriptive branch name:

```bash
# Feature branches
git checkout -b feature/pillar-3a-optimization
git checkout -b feature/cantonal-tax-comparison

# Bug fix branches
git checkout -b fix/calculation-precision-error
git checkout -b fix/chart-rendering-issue

# Documentation branches
git checkout -b docs/api-reference-update
git checkout -b docs/user-guide-improvements
```

### 3. Development Workflow

1. **Write Tests First** (TDD approach recommended)
2. **Implement Feature/Fix**
3. **Update Documentation**
4. **Run Full Test Suite**
5. **Check Code Quality**

```bash
# Run all checks before committing
npm run test
npm run test:e2e
npm run lint
npm run type-check
npm run build
```

### 4. Commit Guidelines

Use conventional commit format:

```bash
# Feature commits
git commit -m "feat(calculations): add Pillar 3a optimization engine"
git commit -m "feat(ui): implement cantonal comparison chart"

# Bug fix commits
git commit -m "fix(tax): correct federal tax bracket calculation"
git commit -m "fix(charts): resolve D3.js memory leak"

# Documentation commits
git commit -m "docs(api): update calculation engine documentation"
git commit -m "docs(guide): add Pillar 3a withdrawal strategies"

# Other types
git commit -m "test(calculations): add comprehensive tax calculation tests"
git commit -m "refactor(components): simplify chart component structure"
git commit -m "perf(calculations): optimize projection calculations"
```

### 5. Pull Request Process

#### PR Template

```markdown
## Description

Brief description of changes

## Type of Change

- [ ] Bug fix
- [ ] New feature
- [ ] Documentation update
- [ ] Performance improvement
- [ ] Refactoring

## Swiss Financial Context

How this change relates to Swiss FIRE planning

## Testing

- [ ] Unit tests added/updated
- [ ] Integration tests added/updated
- [ ] E2E tests added/updated
- [ ] Manual testing completed

## Checklist

- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] No breaking changes (or documented)
- [ ] Tests pass locally

## Screenshots (if applicable)

Before/after screenshots for UI changes
```

#### Review Process

1. **Automated Checks**: CI/CD pipeline runs tests and quality checks
2. **Code Review**: Maintainers review code quality and design
3. **Financial Review**: Swiss financial experts validate calculations
4. **Testing**: QA testing for critical features
5. **Approval**: Two approvals required for merge

## Specialized Contributions

### Financial Calculations

When contributing financial calculations:

1. **Research Swiss Standards**: Ensure compliance with Swiss financial regulations
2. **Validate Formulas**: Cross-reference with official sources
3. **Test Edge Cases**: Include comprehensive test coverage
4. **Document Sources**: Cite official Swiss government sources

```typescript
/**
 * Swiss federal tax calculation based on:
 * - Federal Tax Administration (FTA) tax tables 2024
 * - Source: https://www.estv.admin.ch/estv/de/home/<USER>/direkte-bundessteuer-natuerliche-personen/tarife.html
 */
function calculateFederalTax(income: Decimal): TaxResult {
  // Implementation with official tax brackets
}
```

### Internationalization

For translation contributions:

1. **Use i18next Keys**: Follow existing key structure
2. **Swiss Context**: Understand Swiss financial terminology
3. **Cultural Adaptation**: Adapt content for Swiss culture

```json
{
  "calculations": {
    "pillar3a": {
      "title": "Säule 3a Optimierung",
      "description": "Optimieren Sie Ihre Säule 3a Beiträge für maximale Steuerersparnis",
      "maxContribution": "Maximaler Beitrag: CHF {{amount}}"
    }
  }
}
```

### UI/UX Improvements

For design contributions:

1. **Swiss Design Principles**: Follow Swiss design aesthetics
2. **Accessibility**: Ensure WCAG 2.1 AA compliance
3. **Mobile First**: Design for mobile devices first
4. **Financial Context**: Understand financial planning workflows

## Community Guidelines

### Code of Conduct

We are committed to providing a welcoming and inclusive environment:

- **Be Respectful**: Treat all contributors with respect
- **Be Constructive**: Provide helpful feedback and suggestions
- **Be Patient**: Remember that contributors have varying experience levels
- **Be Collaborative**: Work together toward common goals

### Communication Channels

- **GitHub Issues**: Bug reports and feature requests
- **GitHub Discussions**: General questions and community discussions
- **Pull Request Reviews**: Code-specific discussions
- **Documentation**: Comprehensive guides and references

### Recognition

Contributors are recognized through:

- **Contributor List**: Listed in project documentation
- **Release Notes**: Contributions highlighted in releases
- **Community Spotlight**: Featured contributions in discussions
- **Maintainer Opportunities**: Active contributors invited to become maintainers

## Getting Help

### Resources

- **Documentation**: Comprehensive guides in `/docs`
- **Code Examples**: Reference implementations in codebase
- **Test Examples**: Existing tests show expected patterns
- **Swiss Financial Resources**: Links to official Swiss sources

### Support Channels

1. **GitHub Discussions**: General questions and help
2. **Issue Comments**: Specific technical questions
3. **Pull Request Reviews**: Code-specific guidance
4. **Documentation**: Self-service help and guides

### Mentorship

New contributors can request mentorship:

- **Pair Programming**: Work with experienced contributors
- **Code Reviews**: Detailed feedback on contributions
- **Swiss Financial Guidance**: Help understanding Swiss financial concepts
- **Technical Guidance**: Help with React, TypeScript, and testing

---

_Thank you for contributing to Swiss Budget Pro! Together, we're building the ultimate tool for Swiss FIRE planning._
