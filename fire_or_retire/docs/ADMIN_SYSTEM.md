# Swiss Budget Pro Admin System

## 🎯 Overview

The Swiss Budget Pro Admin System provides a comprehensive interface for configuring and managing all system-wide settings including inflation rates, market data, tax configurations, healthcare parameters, and system defaults.

## 🚀 Quick Start

### Accessing the Admin Panel

1. **Navigate to Admin**: Visit `/admin` in your browser
2. **Login**: Use one of the demo credentials:
   - **Admin**: `admin` / `swiss2024!`
   - **Viewer**: `viewer` / `view2024!`
   - **Demo**: `demo` / `demo`

### Admin Interface Overview

The admin panel consists of 7 main sections:

- **📈 Inflation**: Configure inflation rates and economic assumptions
- **📊 Market Data**: Manage market indices and expected returns
- **💰 Tax**: Set up Swiss federal and cantonal tax rates
- **🏥 Healthcare**: Configure healthcare premiums and deductibles
- **⚙️ System**: Manage system defaults and feature toggles
- **📁 Import/Export**: Backup and restore configurations
- **📋 Audit Log**: Track all configuration changes

## 📈 Inflation Configuration

### Current Inflation Rates

- **Current CPI**: Overall consumer price inflation
- **Core CPI**: Inflation excluding volatile items
- **Historical Average**: Long-term average inflation rate
- **SNB Target**: Swiss National Bank inflation target

### Sector-Specific Inflation

- **Housing Costs**: Rent and housing-related inflation
- **Healthcare Costs**: Medical services and insurance inflation
- **Energy Costs**: Electricity, gas, and fuel inflation
- **Food Costs**: Food and beverage inflation

### Inflation Forecasts

- **12-Month Forecast**: Expected inflation in next 12 months
- **24-Month Forecast**: Expected inflation in next 24 months

### Data Sources

- Swiss National Bank (SNB)
- Federal Statistical Office (FSO)
- OECD
- Manual Entry

## 📊 Market Data Configuration

### Swiss Market Indices

- **SMI Index**: Swiss Market Index current level and daily change
- **SPI Index**: Swiss Performance Index level and YTD performance

### Bond Market

- **10-Year Bond Yield**: Swiss 10-year government bond yield
- **2-Year Bond Yield**: Swiss 2-year government bond yield
- **Volatility Index**: Market volatility indicator

### Expected Returns

Configure expected annual returns for:

- **Stocks**: Expected annual stock market returns
- **Bonds**: Expected annual bond returns
- **Cash**: Expected cash and money market returns
- **Real Estate**: Expected real estate returns
- **Commodities**: Expected commodity returns

### Risk Premiums

- **Equity Risk Premium**: Additional return for equity risk
- **Credit Risk Premium**: Additional return for credit risk
- **Liquidity Risk Premium**: Additional return for liquidity risk

## 💰 Tax Configuration

### Federal Tax Brackets

Configure progressive federal tax brackets with:

- Minimum income threshold
- Maximum income threshold
- Tax rate for each bracket

### Cantonal Tax Rates

For each Swiss canton:

- **Cantonal Tax Rate**: Base cantonal tax rate
- **Wealth Tax Rate**: Wealth tax rate (where applicable)
- **Wealth Tax Threshold**: Minimum wealth subject to tax

### Social Insurance Contributions

- **AHV/IV/EO**: Old Age & Disability Insurance rates and limits
- **ALV**: Unemployment insurance rates and thresholds
- **NBV**: Accident insurance rates

### Pillar 3a Settings

- **Max Annual Contribution**: Maximum contribution for employees
- **Max Self-Employed Contribution**: Maximum for self-employed
- **Withdrawal Age**: Minimum age for withdrawal
- **Early Withdrawal Penalty**: Penalty for early withdrawal

## 🏥 Healthcare Configuration

### Deductible Options

Configure available healthcare deductibles (300-2500 CHF)

### Cantonal Premiums

For each canton:

- **Average Premium**: Average monthly premium
- **Premium Range**: Minimum and maximum premiums

### Subsidy Thresholds

Income thresholds for premium subsidies by household type:

- **Single Person**: Income threshold for singles
- **Couple**: Income threshold for couples
- **Family**: Income threshold for families

### Healthcare Inflation

- **Annual Healthcare Inflation**: Healthcare cost inflation rate

## ⚙️ System Configuration

### Calculation Defaults

- **Standard Withdrawal Rate**: Default FIRE withdrawal rate (4% rule)
- **Safe Withdrawal Rate**: Conservative withdrawal rate
- **Conservative Withdrawal Rate**: Very conservative rate
- **Expected Investment Return**: Default expected annual return
- **Expected Bond Return**: Default bond return
- **Expected Cash Return**: Default cash return

### User Interface Settings

- **Default Language**: System default language (DE/FR/IT/EN)
- **Default Currency**: System currency (CHF/EUR/USD)
- **Number Format**: Decimal and thousands separators
- **Date Format**: Date display format

### Feature Toggles

Enable/disable system features:

- **Advanced Calculations**: Complex financial modeling
- **Tax Optimization**: Tax optimization recommendations
- **Healthcare Optimization**: Healthcare deductible optimization
- **Canton Comparison**: Cross-canton comparison tools
- **Export Features**: PDF export and data download

### Performance Settings

- **Auto-Save Interval**: How often to save user data (seconds)
- **Calculation Timeout**: Maximum calculation time (seconds)
- **Max History Entries**: Maximum calculation history entries

## 📁 Import/Export

### Export Configuration

- Download current configuration as JSON file
- Includes all settings with metadata
- Suitable for backup or system transfer

### Import Configuration

- Upload JSON configuration file
- Validates format before import
- Merges with existing configuration
- Missing fields use default values

### Guidelines

- **Always backup** before importing new configuration
- **Validate source** - only import from trusted sources
- **Version compatibility** - newer versions support older configs
- **Audit trail** - all imports are logged

## 📋 Audit Log

### Tracking

All configuration changes are automatically logged:

- **Timestamp**: When the change occurred
- **Action**: Type of change (create/update/delete/reset/import/export)
- **Section**: Which configuration section was modified
- **Field**: Specific field that changed (if applicable)
- **User**: Who made the change
- **Description**: Human-readable description
- **Old/New Values**: Before and after values (where applicable)

### Filtering and Search

- Filter by action type
- Filter by configuration section
- Search descriptions, users, and sections
- Pagination for large logs

### Export

- Export audit log as CSV
- Includes all filtered entries
- Suitable for compliance reporting

## 🔒 Security Features

### Authentication

- Username/password authentication
- Session management (24-hour sessions)
- Role-based access (admin/viewer)
- Secure credential storage

### Authorization

- **Admin Role**: Full read/write access to all configurations
- **Viewer Role**: Read-only access to configurations
- **Permission Checks**: All actions validate user permissions

### Audit Trail

- Complete audit log of all changes
- User attribution for all actions
- Immutable log entries
- Export capabilities for compliance

## 🛠️ Technical Implementation

### Architecture

- **React Context**: State management with AdminConfigContext
- **TypeScript**: Full type safety for all configurations
- **Local Storage**: Persistent configuration storage
- **Validation**: Comprehensive input validation
- **Error Handling**: Graceful error recovery

### Data Structure

```typescript
interface AdminConfig {
  inflation: InflationConfig;
  marketData: MarketDataConfig;
  tax: TaxConfig;
  healthcare: HealthcareConfig;
  system: SystemConfig;
  version: string;
  lastModified: string;
  modifiedBy: string;
}
```

### Validation Rules

- **Inflation Rates**: -10% to 20%
- **Tax Rates**: 0% to 100%
- **Income Amounts**: 0 to 10,000,000 CHF
- **Timeouts**: 1 to 300 seconds
- **Intervals**: 5 to 300 seconds

## 🌐 Internationalization

### Supported Languages

- **German (Switzerland)**: de-CH
- **English (Switzerland)**: en-CH
- **French (Switzerland)**: fr-CH (planned)
- **Italian (Switzerland)**: it-CH (planned)

### Translation Keys

All admin interface text is translatable with keys under `admin.*` namespace.

## 📱 Responsive Design

### Mobile Support

- Responsive layout for all screen sizes
- Touch-friendly interface elements
- Optimized navigation for mobile devices
- Accessible on tablets and smartphones

### Accessibility

- WCAG 2.1 AA compliance
- Screen reader support
- Keyboard navigation
- High contrast support
- Focus indicators

## 🚀 Development

### Adding New Configuration Sections

1. **Update AdminConfig interface** in `AdminConfigContext.tsx`
2. **Create panel component** in `components/admin/`
3. **Add to AdminPage** routing and navigation
4. **Add translations** to i18n configuration
5. **Update validation rules** as needed

### Testing

- Unit tests for all configuration logic
- Integration tests for admin workflows
- E2E tests for critical admin functions
- BDD scenarios for business requirements

## 📊 Monitoring and Analytics

### Performance Metrics

- Configuration load times
- Save operation performance
- User session analytics
- Error rate monitoring

### Usage Analytics

- Most frequently modified settings
- User activity patterns
- Feature usage statistics
- Configuration change frequency

## 🔄 Backup and Recovery

### Automatic Backups

- Configuration automatically saved to localStorage
- Session persistence across browser restarts
- Audit log preservation

### Manual Backups

- Export configuration as JSON
- Import from backup files
- Version control integration (planned)

## 🎯 Future Enhancements

### Planned Features

- **Real-time Data Integration**: Automatic updates from external APIs
- **Multi-tenant Support**: Separate configurations per organization
- **Advanced Analytics**: Configuration impact analysis
- **Workflow Management**: Approval processes for changes
- **API Access**: RESTful API for programmatic configuration
- **Database Backend**: Persistent storage with PostgreSQL
- **User Management**: Advanced user roles and permissions

### Integration Roadmap

- Swiss National Bank API integration
- SIX Swiss Exchange data feeds
- Federal Tax Administration updates
- Healthcare premium data automation

---

## 📞 Support

For technical support or questions about the admin system:

- Check the audit log for recent changes
- Export configuration for troubleshooting
- Review validation errors in the interface
- Contact system administrators for access issues

The Swiss Budget Pro Admin System provides comprehensive control over all financial calculation parameters, ensuring accurate and up-to-date Swiss financial planning capabilities.
