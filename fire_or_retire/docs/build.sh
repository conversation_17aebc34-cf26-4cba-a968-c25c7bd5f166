#!/bin/bash

# Swiss Budget Pro Documentation Build Script
# This script automates the documentation build process

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check Python version
check_python() {
    if command_exists python3; then
        PYTHON_CMD="python3"
    elif command_exists python; then
        PYTHON_CMD="python"
    else
        print_error "Python is not installed. Please install Python 3.8 or higher."
        exit 1
    fi

    # Check Python version
    PYTHON_VERSION=$($PYTHON_CMD --version 2>&1 | cut -d' ' -f2)
    PYTHON_MAJOR=$(echo $PYTHON_VERSION | cut -d'.' -f1)
    PYTHON_MINOR=$(echo $PYTHON_VERSION | cut -d'.' -f2)

    if [ "$PYTHON_MAJOR" -lt 3 ] || ([ "$PYTHON_MAJOR" -eq 3 ] && [ "$PYTHON_MINOR" -lt 8 ]); then
        print_error "Python 3.8 or higher is required. Found: $PYTHON_VERSION"
        exit 1
    fi

    print_success "Python $PYTHON_VERSION detected"
}

# Function to check if pip is available
check_pip() {
    if command_exists pip3; then
        PIP_CMD="pip3"
    elif command_exists pip; then
        PIP_CMD="pip"
    else
        print_error "pip is not installed. Please install pip."
        exit 1
    fi
    print_success "pip detected"
}

# Function to install dependencies
install_dependencies() {
    print_status "Installing documentation dependencies..."
    
    if [ -f "requirements.txt" ]; then
        $PIP_CMD install -r requirements.txt
        print_success "Dependencies installed successfully"
    else
        print_error "requirements.txt not found"
        exit 1
    fi
}

# Function to clean build directory
clean_build() {
    print_status "Cleaning build directory..."
    if [ -d "_build" ]; then
        rm -rf _build
        print_success "Build directory cleaned"
    else
        print_status "Build directory already clean"
    fi
}

# Function to build HTML documentation
build_html() {
    print_status "Building HTML documentation..."
    
    if command_exists sphinx-build; then
        sphinx-build -b html . _build/html
        print_success "HTML documentation built successfully"
        print_status "Documentation available at: _build/html/index.html"
    else
        print_error "sphinx-build not found. Please install Sphinx."
        exit 1
    fi
}

# Function to build PDF documentation
build_pdf() {
    print_status "Building PDF documentation..."
    
    if command_exists sphinx-build; then
        sphinx-build -b latex . _build/latex
        
        if command_exists pdflatex; then
            cd _build/latex
            make all-pdf
            cd ../..
            print_success "PDF documentation built successfully"
            print_status "PDF available at: _build/latex/SwissBudgetPro.pdf"
        else
            print_warning "pdflatex not found. LaTeX files generated but PDF not compiled."
            print_status "LaTeX files available at: _build/latex/"
        fi
    else
        print_error "sphinx-build not found. Please install Sphinx."
        exit 1
    fi
}

# Function to start development server
serve_docs() {
    print_status "Starting development server..."
    
    if command_exists sphinx-autobuild; then
        print_status "Documentation server will start at http://localhost:8000"
        print_status "Press Ctrl+C to stop the server"
        sphinx-autobuild . _build/html \
            --host 0.0.0.0 \
            --port 8000 \
            --open-browser \
            --watch ../retire.tsx \
            --watch ../src/ \
            --ignore "_build" \
            --ignore "**/node_modules/**" \
            --ignore "**/.git/**"
    else
        print_error "sphinx-autobuild not found. Please install it with: pip install sphinx-autobuild"
        exit 1
    fi
}

# Function to check documentation quality
check_docs() {
    print_status "Checking documentation quality..."
    
    # Check for broken links
    if command_exists sphinx-build; then
        print_status "Checking for broken links..."
        sphinx-build -b linkcheck . _build/linkcheck
    fi
    
    # Check documentation syntax (if doc8 is available)
    if command_exists doc8; then
        print_status "Checking documentation syntax..."
        doc8 --max-line-length 100 .
        print_success "Documentation syntax check passed"
    else
        print_warning "doc8 not found. Skipping syntax check."
    fi
    
    # Check reStructuredText files (if rstcheck is available)
    if command_exists rstcheck; then
        print_status "Checking reStructuredText files..."
        find . -name "*.rst" -exec rstcheck {} \;
        print_success "reStructuredText check passed"
    else
        print_warning "rstcheck not found. Skipping RST check."
    fi
}

# Function to show help
show_help() {
    echo "Swiss Budget Pro Documentation Build Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  install     Install documentation dependencies"
    echo "  clean       Clean build directory"
    echo "  html        Build HTML documentation"
    echo "  pdf         Build PDF documentation"
    echo "  serve       Start development server with auto-reload"
    echo "  check       Check documentation quality"
    echo "  all         Clean, install dependencies, and build HTML"
    echo "  help        Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 install     # Install dependencies"
    echo "  $0 html        # Build HTML docs"
    echo "  $0 serve       # Start dev server"
    echo "  $0 all         # Full build process"
    echo ""
}

# Main script logic
main() {
    # Change to script directory
    cd "$(dirname "$0")"
    
    # Check if no arguments provided
    if [ $# -eq 0 ]; then
        show_help
        exit 0
    fi
    
    # Process command line arguments
    case "$1" in
        "install")
            check_python
            check_pip
            install_dependencies
            ;;
        "clean")
            clean_build
            ;;
        "html")
            check_python
            build_html
            ;;
        "pdf")
            check_python
            build_pdf
            ;;
        "serve")
            check_python
            serve_docs
            ;;
        "check")
            check_python
            check_docs
            ;;
        "all")
            check_python
            check_pip
            clean_build
            install_dependencies
            build_html
            print_success "Documentation build complete!"
            print_status "Open _build/html/index.html in your browser to view the documentation."
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            print_error "Unknown command: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
