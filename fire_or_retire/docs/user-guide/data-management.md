# Data Management Guide

Swiss Budget Pro provides comprehensive data management features to ensure your financial plans are always safe, accessible, and well-organized. This guide covers everything from basic data entry to advanced scenario management.

## Overview

Effective data management is crucial for successful FIRE planning. Swiss Budget Pro offers:

- **Automatic saving** every 30 seconds
- **Multiple scenario management** for strategy comparison
- **Historical tracking** to monitor progress over time
- **Export/import capabilities** for data portability
- **Data validation** to ensure accuracy

```{admonition} 🔒 Data Security
:class: tip

**Your Data is Safe:**
- All data stored locally in your browser
- No personal information sent to external servers
- Automatic backups with every change
- Export capabilities for external backup
- Privacy-first design principles
```

## Data Storage Architecture

### Local Storage System

Swiss Budget Pro uses browser localStorage for data persistence:

```{mermaid}
graph TD
    A[User Input] --> B[Validation Layer]
    B --> C[State Management]
    C --> D[Auto-Save Engine]
    D --> E[localStorage]
    E --> F[Data Retrieval]
    F --> G[State Restoration]
    
    H[Export Function] --> E
    I[Import Function] --> B
```

### Data Structure

```{code-block} json
{
  "userProfile": {
    "personalInfo": {...},
    "financialGoals": {...},
    "preferences": {...}
  },
  "scenarios": {
    "default": {...},
    "conservative": {...},
    "aggressive": {...}
  },
  "historicalData": {
    "monthly": [...],
    "yearly": [...]
  },
  "settings": {
    "language": "en",
    "currency": "CHF",
    "theme": "light"
  }
}
```

## Basic Data Management

### Initial Setup

1. **Personal Information**
   - Name and age
   - Canton and municipality
   - Marital status and dependents
   - Employment status

2. **Financial Profile**
   - Current income and expenses
   - Assets and liabilities
   - Investment accounts
   - Retirement savings (Pillar 2, 3a)

3. **Goals and Preferences**
   - FIRE target amount
   - Retirement age goal
   - Risk tolerance
   - Investment preferences

### Data Entry Best Practices

```{grid} 1 2 2 2
:gutter: 3

```{grid-item-card} 📊 Accuracy
**Be Precise**
- Use actual bank statements
- Include all income sources
- Account for all expenses
- Update regularly
```

```{grid-item-card} 🔄 Consistency
**Stay Organized**
- Use consistent categories
- Maintain regular updates
- Follow naming conventions
- Document assumptions
```

```{grid-item-card} 📅 Timeliness
**Keep Current**
- Monthly data reviews
- Quarterly deep updates
- Annual goal reassessment
- Real-time adjustments
```

```{grid-item-card} 🎯 Completeness
**Include Everything**
- All income streams
- Every expense category
- All assets and debts
- Future commitments
```

## Scenario Management

### Creating Scenarios

Scenarios allow you to test different financial strategies:

```{list-table} Common Scenario Types
:header-rows: 1
:widths: 25 35 40

* - Scenario Type
  - Description
  - Use Case
* - Conservative
  - Lower returns, higher expenses
  - Risk assessment, worst-case planning
* - Aggressive
  - Higher returns, lower expenses
  - Optimistic projections, best-case
* - Realistic
  - Moderate assumptions
  - Primary planning scenario
* - Career Change
  - Different income trajectory
  - Job transition planning
* - Relocation
  - Different canton/costs
  - Geographic optimization
* - Family Planning
  - Additional dependents
  - Life change preparation
```

### Scenario Comparison

```{admonition} 🔍 Scenario Analysis Features
:class: tip

**Compare Across Scenarios:**
- FIRE timeline differences
- Total wealth accumulation
- Risk exposure levels
- Tax optimization impact
- Sensitivity to market changes

**Visual Comparisons:**
- Side-by-side charts
- Difference highlighting
- Probability distributions
- Stress test results
```

### Managing Multiple Scenarios

1. **Naming Convention**
   ```
   [Strategy]_[Assumptions]_[Date]
   Examples:
   - Conservative_3pct_2024
   - Aggressive_Zug_Move_2024
   - Base_Case_Current_2024
   ```

2. **Documentation**
   - Record key assumptions
   - Note scenario purpose
   - Track creation date
   - Document changes

3. **Regular Review**
   - Monthly scenario updates
   - Quarterly assumption review
   - Annual strategy reassessment
   - Event-driven updates

## Historical Tracking

### Automatic Data Collection

Swiss Budget Pro automatically tracks:

```{grid} 1 2 2 2
:gutter: 3

```{grid-item-card} 💰 Financial Metrics
- Net worth progression
- Savings rate trends
- Investment performance
- Expense patterns
```

```{grid-item-card} 🎯 FIRE Progress
- Distance to FIRE goal
- Timeline adjustments
- Milestone achievements
- Progress acceleration
```

```{grid-item-card} 📊 Market Data
- Return assumptions
- Inflation adjustments
- Economic indicators
- Market volatility
```

```{grid-item-card} 🔄 Behavioral Patterns
- Spending habits
- Saving consistency
- Goal adjustments
- Strategy changes
```

### Progress Visualization

Historical data is displayed through:

1. **Trend Charts**
   - Net worth over time
   - Savings rate progression
   - FIRE timeline evolution

2. **Milestone Tracking**
   - First CHF 100K
   - Half-way to FIRE
   - Coast FIRE achievement

3. **Performance Analytics**
   - Actual vs. projected
   - Variance analysis
   - Goal achievement rates

## Data Export and Import

### Export Options

```{list-table} Export Formats
:header-rows: 1
:widths: 20 30 25 25

* - Format
  - Use Case
  - Data Included
  - Compatibility
* - JSON
  - Complete backup
  - All data
  - Swiss Budget Pro
* - CSV
  - Spreadsheet analysis
  - Tabular data
  - Excel, Google Sheets
* - PDF
  - Reports/sharing
  - Formatted summaries
  - Universal viewing
* - Excel
  - Advanced analysis
  - Structured data
  - Microsoft Excel
```

### Export Process

1. **Navigate to Data & History tab**
2. **Select Export Data**
3. **Choose format and scope**
4. **Download file**
5. **Store securely**

### Import Capabilities

```{admonition} 📥 Import Sources
:class: note

**Supported Imports:**
- Previous Swiss Budget Pro exports
- Bank statement CSV files
- Investment account data
- Manual data entry templates
- Third-party financial tools

**Import Validation:**
- Data format checking
- Range validation
- Consistency verification
- Error reporting
```

## Data Validation and Quality

### Automatic Validation

Swiss Budget Pro includes comprehensive validation:

```{grid} 1 2 2 2
:gutter: 3

```{grid-item-card} 🔢 Numerical Validation
- Range checking
- Format validation
- Currency consistency
- Calculation verification
```

```{grid-item-card} 📅 Date Validation
- Chronological order
- Future date limits
- Historical consistency
- Timeline logic
```

```{grid-item-card} 🏦 Financial Validation
- Account balance logic
- Income/expense ratios
- Asset/liability matching
- Tax calculation accuracy
```

```{grid-item-card} ⚠️ Warning System
- Unusual value alerts
- Inconsistency detection
- Missing data warnings
- Calculation errors
```

### Data Quality Checklist

#### Monthly Review

- [ ] Update all account balances
- [ ] Record actual income and expenses
- [ ] Verify investment performance
- [ ] Check for data entry errors
- [ ] Review and adjust projections

#### Quarterly Deep Dive

- [ ] Comprehensive data audit
- [ ] Assumption validation
- [ ] Scenario relevance review
- [ ] Historical trend analysis
- [ ] Goal progress assessment

#### Annual Overhaul

- [ ] Complete data verification
- [ ] Strategy reassessment
- [ ] Scenario restructuring
- [ ] Historical data cleanup
- [ ] Backup and archive

## Privacy and Security

### Data Protection Principles

```{admonition} 🛡️ Privacy First
:class: tip

**Local Storage Only:**
- No cloud synchronization
- No external data transmission
- No user tracking
- No analytics collection

**User Control:**
- Complete data ownership
- Export capabilities
- Deletion options
- Transparency in data use
```

### Security Best Practices

1. **Browser Security**
   - Use updated browsers
   - Enable security features
   - Regular browser updates
   - Secure browsing habits

2. **Device Security**
   - Password protection
   - Encryption enabled
   - Regular backups
   - Secure storage

3. **Data Backup**
   - Regular exports
   - Multiple backup locations
   - Version control
   - Recovery testing

## Troubleshooting Data Issues

### Common Problems

```{list-table} Data Issue Resolution
:header-rows: 1
:widths: 30 35 35

* - Problem
  - Cause
  - Solution
* - Data not saving
  - Browser storage full
  - Clear cache, export data
* - Calculations incorrect
  - Invalid input data
  - Validate and correct inputs
* - Scenarios missing
  - Browser data cleared
  - Restore from backup
* - Export failing
  - Browser compatibility
  - Try different browser
* - Import errors
  - Format mismatch
  - Check file format
```

### Recovery Procedures

1. **Data Loss Recovery**
   - Check browser history
   - Look for auto-exports
   - Restore from backups
   - Manual re-entry if needed

2. **Corruption Recovery**
   - Validate data integrity
   - Identify corrupted sections
   - Restore from clean backup
   - Rebuild affected scenarios

## Advanced Data Features

### Bulk Operations

- **Mass Updates**: Update multiple scenarios simultaneously
- **Batch Imports**: Import multiple data sources
- **Global Changes**: Apply changes across all scenarios
- **Template Application**: Use predefined data templates

### Data Analytics

```{grid} 1 2 2 2
:gutter: 3

```{grid-item-card} 📈 Trend Analysis
- Long-term patterns
- Seasonal variations
- Growth trajectories
- Volatility measures
```

```{grid-item-card} 🎯 Goal Tracking
- Progress metrics
- Timeline analysis
- Milestone identification
- Achievement rates
```

```{grid-item-card} 🔍 Variance Analysis
- Actual vs. planned
- Deviation patterns
- Accuracy assessment
- Forecast improvement
```

```{grid-item-card} 🚀 Optimization
- Efficiency metrics
- Improvement opportunities
- Strategy effectiveness
- Resource allocation
```

## Data Management Checklist

### Daily Tasks
- [ ] Review any new transactions
- [ ] Check for calculation updates
- [ ] Verify auto-save functionality

### Weekly Tasks
- [ ] Update investment values
- [ ] Review expense categories
- [ ] Check scenario relevance

### Monthly Tasks
- [ ] Complete data review
- [ ] Export backup
- [ ] Update projections
- [ ] Validate calculations

### Quarterly Tasks
- [ ] Deep data audit
- [ ] Scenario comparison
- [ ] Historical analysis
- [ ] Strategy adjustment

### Annual Tasks
- [ ] Complete data overhaul
- [ ] Archive old scenarios
- [ ] Update all assumptions
- [ ] Plan next year's strategy

---

*Effective data management is the foundation of successful FIRE planning. Regular maintenance and validation ensure your financial projections remain accurate and actionable.*
