# Troubleshooting Guide

This guide helps you resolve common issues with Swiss Budget Pro and provides solutions for technical problems you might encounter. Start with the Quick Fixes section for immediate solutions to the most common problems.

## 🚨 Quick Fixes (Try These First!)

### "My calculations seem wrong or unrealistic"

**Most Common Causes & Quick Fixes:**

1. **Income Entry Error** → Check you're using NET monthly income, not gross
2. **Missing Expenses** → Review all categories: housing, food, transport, healthcare, entertainment
3. **Wrong Canton** → Verify canton selection (affects tax calculations significantly)
4. **Unrealistic Assumptions** → Use conservative estimates: 5-6% returns, 2-3% inflation

**Quick Verification Checklist:**

```
✓ Monthly Net Income = Annual Net Income ÷ 12
✓ Total Monthly Expenses = 60-80% of net income (for good savings rate)
✓ FIRE Number = Annual Expenses × 25 (4% rule)
✓ Canton matches your actual residence
```

### "The app is slow or frozen"

**Immediate Solutions:**

1. **Hard Refresh**: Ctrl+F5 (Windows) or Cmd+Shift+R (Mac)
2. **Close Other Tabs**: Free up browser memory
3. **Clear Cache**: Browser Settings > Privacy > Clear browsing data
4. **Try Different Browser**: Test in Chrome, Firefox, or Safari

### "My data disappeared"

**Recovery Steps:**

1. **Check Downloads Folder**: Look for recent data exports (JSON files)
2. **Try Different Browser**: Data might be in another browser you used
3. **Check Browser Storage**: Data is stored locally, not in the cloud
4. **Look for Auto-Backups**: Some browsers create automatic backups

**Prevention**: Export your data monthly as backup!

### "Charts aren't showing"

**Quick Solutions:**

1. **Enable JavaScript**: Ensure JavaScript is enabled in browser settings
2. **Disable Ad Blockers**: Temporarily disable ad blockers for the site
3. **Update Browser**: Use Chrome 90+, Firefox 88+, Safari 14+, or Edge 90+
4. **Check Zoom Level**: Reset browser zoom to 100%

### "Mobile version looks broken"

**Mobile Fixes:**

1. **Rotate Device**: Try landscape orientation for better layout
2. **Reset Zoom**: Pinch to reset zoom level
3. **Clear Mobile Cache**: Browser settings > Clear data
4. **Update Browser App**: Ensure you have the latest version

## Common Issues

### Data and Calculations

#### Issue: Calculations Seem Incorrect

**Symptoms:**

- FIRE projections don't match expectations
- Tax calculations appear wrong
- Savings rate calculations are off

**Solutions:**

1. **Verify Input Data**

   ```
   ✓ Check all income sources are included
   ✓ Ensure all expenses are accounted for
   ✓ Verify asset values are current
   ✓ Confirm canton and municipality settings
   ```

2. **Review Assumptions**

   ```
   ✓ Expected return rate (typical: 5-7%)
   ✓ Inflation rate (typical: 1-3%)
   ✓ Withdrawal rate (typical: 3.5-4%)
   ✓ Tax assumptions match your situation
   ```

3. **Check Swiss-Specific Settings**
   ```
   ✓ Correct canton selected
   ✓ Marital status accurate
   ✓ Number of children correct
   ✓ Employment type (employed/self-employed)
   ```

#### Issue: Data Not Saving

**Symptoms:**

- Changes disappear after refresh
- Auto-save indicator not working
- Export function fails

**Solutions:**

1. **Browser Storage Check**

   ```javascript
   // Check if localStorage is available
   if (typeof Storage !== 'undefined') {
     console.log('localStorage is supported');
   } else {
     console.log('localStorage is not supported');
   }
   ```

2. **Clear Browser Cache**

   ```
   Chrome: Settings > Privacy > Clear browsing data
   Firefox: Settings > Privacy > Clear Data
   Safari: Develop > Empty Caches
   ```

3. **Check Storage Space**
   ```
   - Clear unnecessary browser data
   - Close other tabs using storage
   - Try incognito/private mode
   ```

### User Interface Issues

#### Issue: Charts Not Displaying

**Symptoms:**

- Blank chart areas
- Loading indicators stuck
- Chart data missing

**Solutions:**

1. **Browser Compatibility**

   ```
   Supported Browsers:
   ✓ Chrome 90+
   ✓ Firefox 88+
   ✓ Safari 14+
   ✓ Edge 90+
   ```

2. **JavaScript Enabled**

   ```
   - Ensure JavaScript is enabled
   - Check for script blockers
   - Disable ad blockers temporarily
   ```

3. **Clear Browser Cache**
   ```
   - Hard refresh: Ctrl+F5 (Windows) or Cmd+Shift+R (Mac)
   - Clear cache and reload
   ```

#### Issue: Responsive Design Problems

**Symptoms:**

- Layout broken on mobile
- Text too small or large
- Buttons not clickable

**Solutions:**

1. **Zoom Level**

   ```
   - Reset browser zoom to 100%
   - Check device zoom settings
   - Try different zoom levels
   ```

2. **Screen Resolution**
   ```
   Supported Resolutions:
   ✓ Mobile: 320px+ width
   ✓ Tablet: 768px+ width
   ✓ Desktop: 1024px+ width
   ```

### Performance Issues

#### Issue: Slow Loading or Calculations

**Symptoms:**

- Long loading times
- Delayed calculation updates
- Browser becomes unresponsive

**Solutions:**

1. **Browser Performance**

   ```
   - Close unnecessary tabs
   - Restart browser
   - Clear cache and cookies
   - Update browser to latest version
   ```

2. **Device Performance**

   ```
   - Close other applications
   - Check available RAM
   - Restart device if necessary
   ```

3. **Data Optimization**
   ```
   - Reduce number of scenarios
   - Limit historical data range
   - Export and clear old data
   ```

## Error Messages

### "Calculation Error: Invalid Input"

**Cause:** Invalid or missing data in calculation inputs

**Solution:**

1. Check all required fields are filled
2. Ensure numerical values are positive
3. Verify date formats are correct
4. Remove any special characters from number fields

### "Storage Error: Quota Exceeded"

**Cause:** Browser storage limit reached

**Solution:**

1. Export your data as backup
2. Clear browser storage for other sites
3. Delete old scenarios you no longer need
4. Use export/import instead of keeping all data locally

### "Network Error: Unable to Load Data"

**Cause:** Connection issues with external data sources

**Solution:**

1. Check internet connection
2. Try refreshing the page
3. Wait a few minutes and try again
4. Use offline mode if available

### "Validation Error: Swiss Tax Data"

**Cause:** Issues with Swiss tax calculation parameters

**Solution:**

1. Verify canton code is correct (e.g., "ZH" for Zurich)
2. Check municipality name spelling
3. Ensure income values are reasonable
4. Verify marital status and dependents

## Browser-Specific Issues

### Chrome Issues

**Common Problems:**

- Memory usage warnings
- Extension conflicts
- Security restrictions

**Solutions:**

```
1. Disable extensions temporarily
2. Clear Chrome cache: chrome://settings/clearBrowserData
3. Reset Chrome settings if necessary
4. Try Chrome Incognito mode
```

### Firefox Issues

**Common Problems:**

- Tracking protection blocking features
- Add-on conflicts
- Storage limitations

**Solutions:**

```
1. Disable tracking protection for the site
2. Check add-on compatibility
3. Clear Firefox data: about:preferences#privacy
4. Try Firefox Private mode
```

### Safari Issues

**Common Problems:**

- Intelligent tracking prevention
- Cross-site scripting restrictions
- Storage limitations

**Solutions:**

```
1. Disable Intelligent Tracking Prevention
2. Allow cross-site tracking for the site
3. Clear Safari cache: Safari > Clear History
4. Try Safari Private mode
```

## Mobile-Specific Issues

### iOS Safari

**Common Problems:**

- Touch events not working
- Zoom behavior issues
- Storage limitations

**Solutions:**

```
1. Update iOS to latest version
2. Clear Safari cache
3. Restart Safari app
4. Try landscape orientation
```

### Android Chrome

**Common Problems:**

- Performance on older devices
- Storage limitations
- Touch sensitivity

**Solutions:**

```
1. Update Chrome app
2. Clear app cache
3. Restart device
4. Close background apps
```

## Data Recovery

### Lost Data Recovery

If you've lost your data, try these recovery methods:

1. **Browser History Recovery**

   ```
   - Check browser history for recent sessions
   - Look for auto-saved versions
   - Check if data exists in other browser profiles
   ```

2. **Export File Recovery**

   ```
   - Check Downloads folder for recent exports
   - Look in cloud storage (Dropbox, Google Drive)
   - Check email attachments if you shared data
   ```

3. **Device Backup Recovery**
   ```
   - Check device backups (Time Machine, etc.)
   - Look for browser profile backups
   - Check cloud sync services
   ```

### Corrupted Data Recovery

If your data appears corrupted:

1. **Validation and Repair**

   ```
   - Export data to check for corruption
   - Try importing a previous export
   - Reset to default values if necessary
   ```

2. **Partial Recovery**
   ```
   - Save what data is still valid
   - Re-enter corrupted sections manually
   - Use backup data where available
   ```

## Getting Additional Help

### Self-Help Resources

1. **Documentation**

   - [User Guide](index.md) - Comprehensive usage guide
   - [FAQ](faq.md) - Frequently asked questions
   - [Getting Started](../getting-started.md) - Basic setup guide

2. **Video Tutorials** (Planned)
   - Basic setup walkthrough
   - Advanced feature demonstrations
   - Troubleshooting common issues

### Community Support

1. **GitHub Discussions**

   - Ask questions to the community
   - Share solutions with other users
   - Request new features

2. **Issue Reporting**
   - Report bugs on GitHub Issues
   - Provide detailed error descriptions
   - Include browser and device information

### Professional Support

For complex Swiss financial planning questions:

1. **Swiss Financial Advisors**

   - Consult certified financial planners
   - Get personalized advice for your situation
   - Validate calculations with professionals

2. **Swiss Tax Professionals**
   - Verify tax calculations
   - Get advice on optimization strategies
   - Ensure compliance with Swiss tax law

## Reporting Issues

### Bug Report Template

When reporting issues, please include:

```markdown
**Bug Description:**
Clear description of what's wrong

**Steps to Reproduce:**

1. Go to...
2. Click on...
3. See error

**Expected Behavior:**
What should happen

**Actual Behavior:**
What actually happens

**Environment:**

- Browser: Chrome 120.0
- Operating System: macOS 14.0
- Device: MacBook Pro
- Screen Resolution: 1920x1080

**Additional Context:**

- Screenshots
- Error messages
- Console logs (F12 > Console)
```

### Feature Request Template

```markdown
**Feature Description:**
What feature would you like to see?

**Use Case:**
How would this feature help your FIRE planning?

**Swiss Context:**
How does this relate to Swiss financial planning?

**Alternatives:**
What workarounds do you currently use?

**Additional Context:**
Any other relevant information
```

## Prevention Tips

### Regular Maintenance

1. **Weekly Tasks**

   ```
   - Export data backup
   - Clear browser cache
   - Update input data
   ```

2. **Monthly Tasks**

   ```
   - Review and clean scenarios
   - Check for browser updates
   - Validate calculations
   ```

3. **Quarterly Tasks**
   ```
   - Full data export and backup
   - Review all assumptions
   - Clean up old data
   ```

### Best Practices

1. **Data Management**

   - Regular exports for backup
   - Meaningful scenario names
   - Document assumptions

2. **Browser Hygiene**

   - Keep browser updated
   - Regular cache clearing
   - Monitor storage usage

3. **Security**
   - Use secure networks
   - Keep devices updated
   - Regular security scans

---

_If you continue to experience issues after trying these solutions, please don't hesitate to reach out to our community for help._
