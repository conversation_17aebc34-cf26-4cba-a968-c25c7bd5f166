# Frequently Asked Questions

Common questions about Swiss Budget Pro and Swiss FIRE planning, answered by our community and financial experts. This FAQ is organized by topic and includes real-world examples and practical solutions.

## 🚀 Getting Started Questions

### "I'm completely new to FIRE. Where should I start?"

**Quick Answer**: Start with our [Complete Getting Started Guide](getting-started-complete.md) and [Step-by-Step Tutorial](step-by-step-tutorial.md).

**Detailed Answer**:

1. **Learn the Basics**: Understand what FIRE means and why it works
2. **Assess Your Situation**: Complete the basic financial assessment
3. **Set Realistic Goals**: Start with achievable targets and adjust over time
4. **Take Action**: Begin with small changes that compound over time

**Recommended Reading Order**:

1. [Swiss FIRE Strategies](swiss-fire-strategies.md)
2. [Complete Getting Started Guide](getting-started-complete.md)
3. [Step-by-Step Tutorial](step-by-step-tutorial.md)
4. [Swiss Features Overview](swiss-features.md)

### "How accurate are the calculations? Can I trust the numbers?"

**Quick Answer**: The calculations use proven financial formulas and Swiss tax data, but they're projections based on your assumptions.

**Detailed Answer**:

- **Tax Calculations**: Based on official Swiss cantonal tax tables (updated annually)
- **Investment Returns**: Use historical market data but future returns may vary
- **Inflation Adjustments**: Based on Swiss National Bank historical data
- **Compound Interest**: Uses standard financial mathematics

**Best Practices**:

- Use conservative assumptions for returns and inflation
- Create multiple scenarios (optimistic, realistic, pessimistic)
- Review and update your assumptions quarterly
- Focus on trends rather than exact numbers

### "I don't know my exact expenses. How can I get started?"

**Quick Answer**: Start with estimates and refine over time. It's better to begin with approximations than to delay getting started.

**Practical Steps**:

1. **Use Bank Statements**: Review last 3 months of transactions
2. **Start with Categories**: Housing, food, transport, insurance, entertainment
3. **Use Swiss Averages**: Reference typical Swiss household expenses
4. **Track for 2-3 Months**: Get accurate data for refinement
5. **Use Apps**: Connect banking apps or use expense tracking tools

**Swiss Expense Benchmarks** (monthly averages):

- **Housing**: CHF 1,200-2,500 (varies by canton and city)
- **Food**: CHF 400-800 per person
- **Transport**: CHF 200-500 (public transport vs car)
- **Healthcare**: CHF 300-500 per person
- **Entertainment**: CHF 200-600 (varies by lifestyle)

### "My FIRE timeline seems too long/short. Is this realistic?"

**Quick Answer**: FIRE timelines depend heavily on your savings rate. Small changes in savings rate have dramatic impacts on timeline.

**Savings Rate Impact Examples**:

- **20% savings rate**: ~37 years to FIRE
- **30% savings rate**: ~28 years to FIRE
- **50% savings rate**: ~17 years to FIRE
- **70% savings rate**: ~8.5 years to FIRE

**If timeline seems too long**:

- Increase income through career advancement or side hustles
- Reduce expenses by optimizing major categories
- Consider geographic arbitrage (move to lower-cost canton)
- Adjust retirement lifestyle expectations

**If timeline seems too short**:

- Check your assumptions for realism
- Consider market volatility and sequence of returns risk
- Plan for unexpected expenses and life changes
- Build larger safety margins

### "Should I pay off my mortgage or invest for FIRE?"

**Quick Answer**: It depends on your mortgage rate vs expected investment returns, but also consider risk tolerance and tax implications.

**Mathematical Approach**:

- **If mortgage rate < expected investment return**: Invest instead
- **If mortgage rate > expected investment return**: Pay off mortgage
- **Consider taxes**: Mortgage interest is tax-deductible in Switzerland

**Risk Considerations**:

- **Guaranteed return**: Paying off mortgage provides guaranteed return equal to interest rate
- **Market risk**: Investments may underperform, especially short-term
- **Liquidity**: Investments are more liquid than home equity
- **Peace of mind**: Some prefer the security of owning their home outright

**Swiss-Specific Factors**:

- Mortgage interest tax deduction
- Imputed rental value taxation
- Wealth tax implications
- Pillar 3a withdrawal for home purchase

## General Questions

### What is Swiss Budget Pro?

Swiss Budget Pro is a comprehensive financial planning tool specifically designed for Swiss residents pursuing Financial Independence and Early Retirement (FIRE). It includes Swiss-specific features like cantonal tax optimization, Pillar 3a planning, and real-time economic data integration.

### Is Swiss Budget Pro free to use?

Yes, Swiss Budget Pro is completely free and open-source. There are no subscription fees, premium features, or hidden costs. The tool is funded by the community and maintained by volunteers.

### How is my data protected?

Your financial data is stored locally in your browser and never sent to external servers. We follow a privacy-first approach:

- All data remains on your device
- No user tracking or analytics
- No cloud synchronization
- Complete data ownership and control

### Can I use Swiss Budget Pro if I don't live in Switzerland?

While Swiss Budget Pro is optimized for Swiss residents, the core FIRE planning features work for anyone. However, tax calculations and Swiss-specific features (Pillar 3a, cantonal comparisons) are designed for the Swiss financial system.

## Swiss Financial System

### What are the Swiss retirement pillars?

Switzerland has a three-pillar retirement system:

1. **Pillar 1 (AHV/IV)**: State pension - mandatory for all residents
2. **Pillar 2 (BVG/LPP)**: Occupational pension - mandatory for employees
3. **Pillar 3a**: Private pension - voluntary but tax-advantaged

Swiss Budget Pro helps optimize Pillar 3a contributions and coordinate all three pillars for FIRE planning.

### How accurate are the tax calculations?

Our tax calculations are based on official Swiss Federal Tax Administration data and are updated annually. However:

- Tax laws can change during the year
- Individual situations may have unique factors
- Always consult with tax professionals for major decisions
- Use our calculations as estimates, not definitive advice

### Which canton should I choose for FIRE?

The best canton depends on your specific situation, but generally:

**Low-tax cantons**: Zug, Schwyz, Nidwalden
**Considerations**: Cost of living, job market, quality of life, family needs

Use our cantonal comparison tool to analyze the trade-offs for your situation.

## FIRE Planning

### What is the 4% rule and does it apply in Switzerland?

The 4% rule suggests you can withdraw 4% of your portfolio annually in retirement. For Switzerland, consider:

- **Conservative approach**: 3.5% due to lower expected returns
- **Swiss market conditions**: Different from US historical data
- **Currency considerations**: CHF strength and inflation
- **Tax implications**: Withdrawal taxes and wealth taxes

### How much do I need for FIRE in Switzerland?

This depends on your lifestyle, location, and **continuing income**. Traditional ranges assume no continuing income:

- **Lean FIRE**: CHF 1-1.5 million (CHF 40,000-60,000 annual expenses)
- **Standard FIRE**: CHF 1.5-2.5 million (CHF 60,000-100,000 annual expenses)
- **Fat FIRE**: CHF 2.5+ million (CHF 100,000+ annual expenses)

**However, continuing income dramatically reduces these requirements:**

- With CHF 2,000/month continuing income: Reduce FIRE number by CHF 600,000
- With CHF 3,000/month continuing income: Reduce FIRE number by CHF 900,000

Use Swiss Budget Pro to calculate your specific FIRE number based on your expenses, goals, and continuing income streams.

### What's the difference between employment income and company income?

**Employment Income** (stops at retirement):

- Salary from your job
- Contract work (HSLU, RUAG)
- Professional services requiring active work
- Stops when you retire from employment

**Company Income** (continues after retirement):

- Business profits from your own company
- Rental income from properties
- Dividends and investment income
- Freelance work you control

**Why this matters for FIRE**: Company income continues after retirement, reducing the amount you need to withdraw from investments. This can cut your FIRE number by hundreds of thousands of CHF!

### At what age can I access my Pillar 3a?

Pillar 3a can be withdrawn:

- **Normal retirement**: Age 65 (men), 64 (women, changing to 65)
- **Early withdrawal**: From age 60 (with conditions)
- **Special circumstances**: Home purchase, emigration, self-employment

Plan your FIRE timeline considering these withdrawal restrictions.

## Using Swiss Budget Pro

### How do I get started?

1. **Basic Setup**: Enter your income, expenses, and basic information
2. **Swiss Settings**: Select your canton and configure tax settings
3. **Goals**: Set your FIRE target and retirement age
4. **Review**: Check calculations and projections
5. **Optimize**: Use recommendations to improve your plan

See our [Getting Started Guide](../getting-started.md) for detailed instructions.

### Can I compare different scenarios?

Yes! Swiss Budget Pro supports multiple scenarios:

- **Conservative vs. Aggressive**: Different return assumptions
- **Career Changes**: Impact of job transitions
- **Relocation**: Moving to different cantons
- **Family Planning**: Adding dependents
- **Investment Strategies**: Different asset allocations

### How often should I update my data?

**Monthly**: Update account balances and track progress
**Quarterly**: Review assumptions and adjust projections
**Annually**: Complete review of goals and strategies
**As needed**: Major life changes, job changes, moves

### What if I make a mistake in my data?

Don't worry! You can:

- Edit any input field at any time
- Use the undo function for recent changes
- Reset to default values if needed
- Import previous exports to restore data
- Create new scenarios to test corrections

## Technical Questions

### Which browsers are supported?

Swiss Budget Pro works on modern browsers:

- **Chrome**: Version 90+
- **Firefox**: Version 88+
- **Safari**: Version 14+
- **Edge**: Version 90+

Mobile browsers are also supported with responsive design.

### Why are my calculations slow?

Slow calculations can be caused by:

- **Large datasets**: Many scenarios or long time horizons
- **Browser performance**: Too many open tabs or low memory
- **Device limitations**: Older devices may be slower
- **Complex projections**: Monte Carlo simulations take more time

Try reducing the number of scenarios or clearing browser cache.

### Can I export my data?

Yes, you can export data in multiple formats:

- **JSON**: Complete backup for Swiss Budget Pro
- **CSV**: For spreadsheet analysis
- **PDF**: For reports and sharing
- **Excel**: For advanced analysis

Regular exports are recommended for backup purposes.

### How do I import data from other tools?

Swiss Budget Pro can import:

- **Previous exports**: From Swiss Budget Pro
- **CSV files**: From spreadsheets or other tools
- **Bank statements**: Some formats supported
- **Manual entry**: Copy and paste from other sources

Check our [Data Management Guide](data-management.md) for detailed instructions.

## Swiss-Specific Questions

### How does Pillar 3a optimization work?

Swiss Budget Pro optimizes Pillar 3a by:

- **Maximizing contributions**: Up to annual limits
- **Tax savings calculation**: Based on your marginal rate
- **Withdrawal planning**: Multi-account strategy for tax efficiency
- **Timeline coordination**: Aligning with FIRE goals

### What about wealth tax optimization?

Wealth tax strategies include:

- **Asset structure**: Optimizing how wealth is held
- **Cantonal differences**: Comparing wealth tax rates
- **Timing strategies**: When to realize gains/losses
- **Deduction maximization**: Using available deductions

### How accurate is the cantonal comparison?

Our cantonal comparison includes:

- **Tax rates**: Based on official 2024 data
- **Cost of living**: Regularly updated indices
- **Quality of life**: Standardized metrics
- **Economic factors**: Job market and growth data

Data is updated annually or when significant changes occur.

## Investment Questions

### What investment strategy should I use?

Common Swiss FIRE strategies:

- **Passive indexing**: Low-cost ETFs tracking global markets
- **Swiss bias**: Overweight Swiss stocks for currency matching
- **Real estate**: Direct property or REITs
- **Pillar 3a funds**: Tax-advantaged investment accounts

Swiss Budget Pro can model different strategies to compare outcomes.

### Should I invest in Swiss or global markets?

Consider both:

- **Swiss markets**: Currency matching, local knowledge
- **Global markets**: Diversification, growth opportunities
- **Currency hedging**: Reducing exchange rate risk
- **Tax efficiency**: Withholding tax considerations

A balanced approach often works best for Swiss investors.

### How do I handle currency risk?

Currency risk strategies:

- **CHF-hedged funds**: Reduce currency volatility
- **Natural hedging**: Match expenses to investment currencies
- **Diversification**: Spread across multiple currencies
- **Long-term perspective**: Currency fluctuations smooth over time

## Troubleshooting

### My calculations don't match other tools

Differences can occur due to:

- **Different assumptions**: Return rates, inflation, taxes
- **Calculation methods**: Various formulas and approaches
- **Swiss specifics**: Our tool includes Swiss tax and regulatory factors
- **Timing differences**: When calculations are performed

Compare assumptions first, then calculation methods.

### I lost my data, can I recover it?

Recovery options:

- **Browser cache**: May contain recent data
- **Previous exports**: Check downloads folder
- **Device backups**: Time Machine, cloud backups
- **Manual reconstruction**: Re-enter from memory or records

Regular exports prevent data loss.

### The interface looks broken

Try these solutions:

- **Hard refresh**: Ctrl+F5 or Cmd+Shift+R
- **Clear cache**: Browser settings > Clear data
- **Different browser**: Test in another browser
- **Disable extensions**: Ad blockers may interfere
- **Check zoom level**: Reset to 100%

## Getting Help

### Where can I get more help?

**Community Support:**

- GitHub Discussions for general questions
- User community for tips and strategies
- Documentation for detailed guides

**Professional Support:**

- Swiss financial advisors for personalized advice
- Tax professionals for complex tax situations
- Estate planning attorneys for advanced strategies

### How can I contribute?

You can help by:

- **Reporting bugs**: GitHub Issues for technical problems
- **Suggesting features**: Community discussions for ideas
- **Contributing code**: Pull requests for developers
- **Improving documentation**: Help make guides clearer
- **Sharing knowledge**: Help other users in discussions

### Is there a mobile app?

Currently, Swiss Budget Pro is a web application that works on mobile browsers. A dedicated mobile app is planned for future development.

---

_Don't see your question here? Ask in our [GitHub Discussions](https://github.com/swiss-budget-pro/discussions) and help us improve this FAQ!_
