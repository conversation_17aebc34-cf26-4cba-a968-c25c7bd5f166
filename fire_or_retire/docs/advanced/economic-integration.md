# Economic Data Integration

Swiss Budget Pro integrates real-time Swiss economic data to provide dynamic, market-aware financial projections. This guide covers the technical implementation and usage of economic data integration.

## Overview

The economic data integration system connects Swiss Budget Pro with authoritative Swiss financial data sources to ensure projections reflect current market conditions and economic realities.

```{admonition} 🏦 Data Sources
:class: tip

**Primary Sources:**
- Swiss National Bank (SNB) - Policy rates and monetary data
- SIX Swiss Exchange - Market indices and performance data
- Federal Statistical Office (FSO) - Inflation and economic indicators
- Swiss Federal Tax Administration - Tax rate updates

**Update Frequency:**
- Real-time: Market data during trading hours
- Daily: Economic indicators and rates
- Monthly: Statistical data and indices
- Annually: Tax rates and regulatory changes
```

## Architecture

### Data Integration Pipeline

```{mermaid}
graph TD
    A[External Data Sources] --> B[Data Fetching Layer]
    B --> C[Validation Engine]
    C --> D[Transformation Layer]
    D --> E[Caching System]
    E --> F[Calculation Engine]
    F --> G[User Interface]
    
    H[SNB API] --> B
    I[SIX API] --> B
    J[FSO Data] --> B
    K[Tax Data] --> B
    
    E --> L[Local Storage]
    E --> M[Memory Cache]
```

### Data Flow Implementation

```typescript
interface EconomicDataService {
  // Core data fetching
  fetchSNBData(): Promise<SNBData>;
  fetchSIXData(): Promise<SIXData>;
  fetchInflationData(): Promise<InflationData>;
  fetchTaxData(): Promise<TaxData>;
  
  // Aggregated data
  getEconomicIndicators(): Promise<EconomicIndicators>;
  getMarketConditions(): Promise<MarketConditions>;
  
  // Real-time updates
  subscribeToUpdates(callback: (data: EconomicUpdate) => void): void;
  unsubscribeFromUpdates(): void;
}

class EconomicDataManager implements EconomicDataService {
  private cache = new Map<string, CachedData>();
  private updateSubscribers = new Set<Function>();
  private readonly UPDATE_INTERVAL = 15 * 60 * 1000; // 15 minutes
  
  constructor(
    private snbClient: SNBClient,
    private sixClient: SIXClient,
    private fsoClient: FSOClient,
    private taxClient: TaxClient
  ) {
    this.startPeriodicUpdates();
  }
  
  async getEconomicIndicators(): Promise<EconomicIndicators> {
    const [snbData, sixData, inflationData, taxData] = await Promise.all([
      this.fetchSNBData(),
      this.fetchSIXData(),
      this.fetchInflationData(),
      this.fetchTaxData()
    ]);
    
    return this.aggregateIndicators(snbData, sixData, inflationData, taxData);
  }
}
```

## Swiss National Bank (SNB) Integration

### Policy Rate Monitoring

```typescript
interface SNBData {
  policyRate: Decimal;
  lastUpdate: Date;
  nextMeetingDate: Date;
  rateHistory: RateHistoryPoint[];
  forwardGuidance: string;
}

class SNBClient {
  private readonly BASE_URL = 'https://data.snb.ch/api/cube';
  
  async fetchPolicyRate(): Promise<SNBData> {
    try {
      const response = await fetch(`${this.BASE_URL}/zimoma`);
      const data = await response.json();
      
      return this.transformSNBData(data);
    } catch (error) {
      throw new SNBDataError('Failed to fetch SNB policy rate', error);
    }
  }
  
  private transformSNBData(rawData: any): SNBData {
    return {
      policyRate: new Decimal(rawData.observations[0].value),
      lastUpdate: new Date(rawData.observations[0].date),
      nextMeetingDate: this.calculateNextMeeting(),
      rateHistory: this.transformRateHistory(rawData.observations),
      forwardGuidance: this.extractForwardGuidance(rawData)
    };
  }
  
  private calculateNextMeeting(): Date {
    // SNB typically meets quarterly
    const now = new Date();
    const currentQuarter = Math.floor(now.getMonth() / 3);
    const nextQuarter = (currentQuarter + 1) % 4;
    const nextYear = nextQuarter === 0 ? now.getFullYear() + 1 : now.getFullYear();
    
    // Meeting dates are typically in March, June, September, December
    const meetingMonths = [2, 5, 8, 11]; // 0-indexed
    return new Date(nextYear, meetingMonths[nextQuarter], 15); // Mid-month
  }
}
```

### Impact on Projections

```typescript
function adjustProjectionsForSNBPolicy(
  baseProjections: WealthProjection[],
  snbData: SNBData
): WealthProjection[] {
  return baseProjections.map(projection => {
    // Adjust bond returns based on policy rate
    const bondAdjustment = calculateBondAdjustment(
      snbData.policyRate,
      projection.bondAllocation
    );
    
    // Adjust equity returns based on monetary policy stance
    const equityAdjustment = calculateEquityAdjustment(
      snbData.policyRate,
      snbData.forwardGuidance,
      projection.equityAllocation
    );
    
    // Adjust currency impact
    const currencyAdjustment = calculateCurrencyAdjustment(
      snbData.policyRate,
      projection.foreignAllocation
    );
    
    return {
      ...projection,
      expectedReturn: projection.expectedReturn
        .plus(bondAdjustment)
        .plus(equityAdjustment)
        .plus(currencyAdjustment),
      adjustments: {
        snbPolicy: bondAdjustment.plus(equityAdjustment).plus(currencyAdjustment),
        bondImpact: bondAdjustment,
        equityImpact: equityAdjustment,
        currencyImpact: currencyAdjustment
      }
    };
  });
}
```

## SIX Swiss Exchange Integration

### Market Data Collection

```typescript
interface SIXData {
  smi: MarketIndex;
  spi: MarketIndex;
  bonds: BondIndex[];
  sectors: SectorPerformance[];
  volatility: VolatilityData;
  tradingVolume: TradingVolumeData;
}

interface MarketIndex {
  symbol: string;
  value: Decimal;
  change: Decimal;
  changePercent: Decimal;
  volume: number;
  timestamp: Date;
  yearHigh: Decimal;
  yearLow: Decimal;
  pe: Decimal;
  dividend: Decimal;
}

class SIXClient {
  private readonly API_KEY = process.env.SIX_API_KEY;
  private readonly BASE_URL = 'https://api.six-group.com/v1';
  
  async fetchMarketData(): Promise<SIXData> {
    const [smi, spi, bonds, sectors, volatility] = await Promise.all([
      this.fetchIndex('SMI'),
      this.fetchIndex('SPI'),
      this.fetchBondIndices(),
      this.fetchSectorData(),
      this.fetchVolatilityData()
    ]);
    
    return {
      smi,
      spi,
      bonds,
      sectors,
      volatility,
      tradingVolume: await this.fetchTradingVolume()
    };
  }
  
  private async fetchIndex(symbol: string): Promise<MarketIndex> {
    const response = await fetch(
      `${this.BASE_URL}/instruments/${symbol}/quotes`,
      {
        headers: {
          'Authorization': `Bearer ${this.API_KEY}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    if (!response.ok) {
      throw new SIXDataError(`Failed to fetch ${symbol} data`);
    }
    
    const data = await response.json();
    return this.transformIndexData(data);
  }
}
```

### Market Condition Analysis

```typescript
interface MarketConditions {
  trend: 'bullish' | 'bearish' | 'neutral';
  volatility: 'low' | 'medium' | 'high';
  momentum: Decimal;
  riskLevel: number; // 1-10 scale
  confidence: number; // 0-1 scale
}

function analyzeMarketConditions(sixData: SIXData): MarketConditions {
  // Trend analysis based on moving averages
  const trend = analyzeTrend(sixData.smi);
  
  // Volatility analysis
  const volatility = analyzeVolatility(sixData.volatility);
  
  // Momentum calculation
  const momentum = calculateMomentum(sixData.smi, sixData.spi);
  
  // Risk assessment
  const riskLevel = assessRiskLevel(sixData);
  
  // Confidence in analysis
  const confidence = calculateConfidence(sixData);
  
  return {
    trend,
    volatility,
    momentum,
    riskLevel,
    confidence
  };
}

function adjustReturnsForMarketConditions(
  baseReturn: Decimal,
  conditions: MarketConditions,
  timeHorizon: number
): Decimal {
  let adjustment = new Decimal(0);
  
  // Trend adjustment
  switch (conditions.trend) {
    case 'bullish':
      adjustment = adjustment.plus(0.005); // +0.5%
      break;
    case 'bearish':
      adjustment = adjustment.minus(0.005); // -0.5%
      break;
    // neutral: no adjustment
  }
  
  // Volatility adjustment (higher volatility = lower expected returns)
  switch (conditions.volatility) {
    case 'high':
      adjustment = adjustment.minus(0.003); // -0.3%
      break;
    case 'low':
      adjustment = adjustment.plus(0.002); // +0.2%
      break;
    // medium: no adjustment
  }
  
  // Time horizon adjustment (shorter horizon = more impact)
  const timeAdjustment = Math.max(0.1, 1 - (timeHorizon / 30));
  adjustment = adjustment.mul(timeAdjustment);
  
  return baseReturn.plus(adjustment);
}
```

## Inflation Data Integration

### Federal Statistical Office (FSO) Data

```typescript
interface InflationData {
  currentRate: Decimal;
  coreInflation: Decimal;
  forecast: InflationForecast[];
  components: InflationComponent[];
  historicalData: InflationHistoryPoint[];
}

interface InflationForecast {
  year: number;
  quarter: number;
  forecast: Decimal;
  confidence: Decimal;
  source: string;
}

class FSOClient {
  private readonly BASE_URL = 'https://www.bfs.admin.ch/bfsstatic/dam/assets';
  
  async fetchInflationData(): Promise<InflationData> {
    try {
      // FSO provides data in various formats, typically CSV or JSON
      const response = await fetch(`${this.BASE_URL}/inflation/current.json`);
      const data = await response.json();
      
      return this.transformInflationData(data);
    } catch (error) {
      // Fallback to cached data or estimates
      return this.getFallbackInflationData();
    }
  }
  
  private transformInflationData(rawData: any): InflationData {
    return {
      currentRate: new Decimal(rawData.current_rate),
      coreInflation: new Decimal(rawData.core_inflation),
      forecast: this.transformForecastData(rawData.forecasts),
      components: this.transformComponentData(rawData.components),
      historicalData: this.transformHistoricalData(rawData.historical)
    };
  }
}
```

### Dynamic Inflation Adjustments

```typescript
function adjustProjectionsForInflation(
  projections: WealthProjection[],
  inflationData: InflationData
): WealthProjection[] {
  return projections.map((projection, index) => {
    // Use forecast data for near-term, long-term average for distant future
    const inflationRate = index < inflationData.forecast.length
      ? inflationData.forecast[index].forecast
      : calculateLongTermInflationExpectation(inflationData);
    
    // Adjust nominal returns
    const realReturn = projection.nominalReturn.minus(inflationRate);
    
    // Adjust expense projections
    const adjustedExpenses = projection.expenses.mul(
      inflationRate.plus(1).pow(index)
    );
    
    return {
      ...projection,
      inflationRate,
      realReturn,
      adjustedExpenses,
      realWealth: projection.nominalWealth.div(
        inflationRate.plus(1).pow(index)
      )
    };
  });
}
```

## Tax Data Integration

### Automated Tax Rate Updates

```typescript
interface TaxData {
  federalRates: TaxBracket[];
  cantonalRates: Map<CantonCode, CantonTaxData>;
  wealthTaxRates: Map<CantonCode, WealthTaxData>;
  lastUpdate: Date;
  effectiveDate: Date;
}

class TaxDataClient {
  async fetchCurrentTaxData(): Promise<TaxData> {
    // Fetch from multiple sources for comprehensive coverage
    const [federalData, cantonalData, wealthTaxData] = await Promise.all([
      this.fetchFederalTaxData(),
      this.fetchCantonalTaxData(),
      this.fetchWealthTaxData()
    ]);
    
    return {
      federalRates: federalData,
      cantonalRates: cantonalData,
      wealthTaxRates: wealthTaxData,
      lastUpdate: new Date(),
      effectiveDate: this.determineEffectiveDate()
    };
  }
  
  private async fetchFederalTaxData(): Promise<TaxBracket[]> {
    // Federal tax data from Swiss Federal Tax Administration
    const response = await fetch(
      'https://www.estv.admin.ch/dam/estv/de/dokumente/bundessteuer/tarife-bundessteuer.json'
    );
    
    const data = await response.json();
    return this.transformFederalTaxBrackets(data);
  }
}
```

## Real-Time Updates

### WebSocket Integration

```typescript
class RealTimeEconomicData {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private readonly MAX_RECONNECT_ATTEMPTS = 5;
  
  connect(): void {
    try {
      this.ws = new WebSocket('wss://api.swiss-budget-pro.ch/economic-data');
      
      this.ws.onopen = () => {
        console.log('Connected to real-time economic data');
        this.reconnectAttempts = 0;
      };
      
      this.ws.onmessage = (event) => {
        const update = JSON.parse(event.data) as EconomicUpdate;
        this.handleEconomicUpdate(update);
      };
      
      this.ws.onclose = () => {
        this.handleDisconnection();
      };
      
      this.ws.onerror = (error) => {
        console.error('WebSocket error:', error);
      };
    } catch (error) {
      console.error('Failed to connect to real-time data:', error);
    }
  }
  
  private handleEconomicUpdate(update: EconomicUpdate): void {
    // Update local cache
    this.updateCache(update);
    
    // Notify subscribers
    this.notifySubscribers(update);
    
    // Trigger recalculation if significant change
    if (this.isSignificantChange(update)) {
      this.triggerRecalculation(update);
    }
  }
  
  private isSignificantChange(update: EconomicUpdate): boolean {
    // Define thresholds for significant changes
    const thresholds = {
      policyRate: 0.0025, // 0.25%
      inflation: 0.001,   // 0.1%
      marketIndex: 0.02   // 2%
    };
    
    return Object.entries(update.changes).some(([key, change]) => {
      const threshold = thresholds[key as keyof typeof thresholds];
      return threshold && Math.abs(change) > threshold;
    });
  }
}
```

### Caching Strategy

```typescript
interface CacheEntry<T> {
  data: T;
  timestamp: Date;
  ttl: number; // Time to live in milliseconds
  source: string;
}

class EconomicDataCache {
  private cache = new Map<string, CacheEntry<any>>();
  
  set<T>(key: string, data: T, ttl: number, source: string): void {
    this.cache.set(key, {
      data,
      timestamp: new Date(),
      ttl,
      source
    });
    
    // Schedule cleanup
    setTimeout(() => {
      this.cache.delete(key);
    }, ttl);
  }
  
  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) return null;
    
    // Check if expired
    if (Date.now() - entry.timestamp.getTime() > entry.ttl) {
      this.cache.delete(key);
      return null;
    }
    
    return entry.data as T;
  }
  
  invalidate(pattern: string): void {
    const regex = new RegExp(pattern);
    
    for (const [key] of this.cache) {
      if (regex.test(key)) {
        this.cache.delete(key);
      }
    }
  }
}
```

## Error Handling and Fallbacks

### Graceful Degradation

```typescript
class EconomicDataWithFallback {
  constructor(
    private primaryService: EconomicDataService,
    private fallbackService: EconomicDataService,
    private cache: EconomicDataCache
  ) {}
  
  async getEconomicIndicators(): Promise<EconomicIndicators> {
    try {
      // Try primary service first
      const data = await this.primaryService.getEconomicIndicators();
      this.cache.set('economic-indicators', data, 15 * 60 * 1000, 'primary');
      return data;
    } catch (primaryError) {
      console.warn('Primary economic data service failed:', primaryError);
      
      try {
        // Try fallback service
        const data = await this.fallbackService.getEconomicIndicators();
        this.cache.set('economic-indicators', data, 15 * 60 * 1000, 'fallback');
        return data;
      } catch (fallbackError) {
        console.warn('Fallback economic data service failed:', fallbackError);
        
        // Use cached data if available
        const cached = this.cache.get<EconomicIndicators>('economic-indicators');
        if (cached) {
          console.info('Using cached economic data');
          return cached;
        }
        
        // Use default values as last resort
        return this.getDefaultEconomicIndicators();
      }
    }
  }
  
  private getDefaultEconomicIndicators(): EconomicIndicators {
    return {
      snbPolicyRate: new Decimal(0.015), // 1.5% default
      inflation: new Decimal(0.02),      // 2% default
      swissMarketIndex: new Decimal(11000), // Approximate SMI level
      bondYields: this.getDefaultBondYields(),
      currencyRates: this.getDefaultCurrencyRates(),
      lastUpdate: new Date(),
      source: 'default'
    };
  }
}
```

## Performance Optimization

### Batch Updates

```typescript
class BatchedEconomicUpdates {
  private pendingUpdates = new Map<string, any>();
  private batchTimer: NodeJS.Timeout | null = null;
  private readonly BATCH_DELAY = 1000; // 1 second
  
  queueUpdate(key: string, data: any): void {
    this.pendingUpdates.set(key, data);
    
    if (this.batchTimer) {
      clearTimeout(this.batchTimer);
    }
    
    this.batchTimer = setTimeout(() => {
      this.processBatch();
    }, this.BATCH_DELAY);
  }
  
  private processBatch(): void {
    if (this.pendingUpdates.size === 0) return;
    
    const updates = new Map(this.pendingUpdates);
    this.pendingUpdates.clear();
    
    // Process all updates together
    this.applyBatchedUpdates(updates);
  }
  
  private applyBatchedUpdates(updates: Map<string, any>): void {
    // Combine all updates into single calculation cycle
    const combinedUpdate = this.combineUpdates(updates);
    
    // Trigger single recalculation
    this.triggerRecalculation(combinedUpdate);
  }
}
```

---

*Economic data integration ensures Swiss Budget Pro provides accurate, up-to-date projections that reflect current Swiss market conditions and economic realities.*
