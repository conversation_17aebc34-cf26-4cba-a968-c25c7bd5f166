# Advanced Data Persistence

Swiss Budget Pro implements a sophisticated data persistence system designed for reliability, performance, and user privacy. This guide covers the technical implementation and advanced features.

## Architecture Overview

The data persistence layer is built on browser localStorage with advanced features for data integrity, versioning, and migration.

```{mermaid}
graph TD
    A[User Interface] --> B[State Management]
    B --> C[Persistence Layer]
    C --> D[Validation Engine]
    C --> E[Compression Engine]
    C --> F[Encryption Layer]
    C --> G[Version Control]
    
    D --> H[localStorage]
    E --> H
    F --> H
    G --> H
    
    H --> I[Data Recovery]
    H --> J[Export/Import]
    H --> K[Migration System]
```

## Core Persistence Engine

### Data Storage Interface

```typescript
interface PersistenceEngine {
  save<T>(key: string, data: T): Promise<void>;
  load<T>(key: string): Promise<T | null>;
  delete(key: string): Promise<void>;
  exists(key: string): Promise<boolean>;
  clear(): Promise<void>;
  getSize(): Promise<number>;
  getKeys(): Promise<string[]>;
}

class LocalStoragePersistence implements PersistenceEngine {
  private readonly prefix = 'swiss-budget-pro:';
  private readonly compression = new CompressionEngine();
  private readonly encryption = new EncryptionEngine();
  
  async save<T>(key: string, data: T): Promise<void> {
    try {
      const serialized = JSON.stringify(data);
      const compressed = await this.compression.compress(serialized);
      const encrypted = await this.encryption.encrypt(compressed);
      
      const envelope: DataEnvelope = {
        version: CURRENT_VERSION,
        timestamp: Date.now(),
        checksum: this.calculateChecksum(serialized),
        data: encrypted
      };
      
      localStorage.setItem(
        this.prefix + key,
        JSON.stringify(envelope)
      );
    } catch (error) {
      throw new PersistenceError('Failed to save data', error);
    }
  }
  
  async load<T>(key: string): Promise<T | null> {
    try {
      const item = localStorage.getItem(this.prefix + key);
      if (!item) return null;
      
      const envelope: DataEnvelope = JSON.parse(item);
      const decrypted = await this.encryption.decrypt(envelope.data);
      const decompressed = await this.compression.decompress(decrypted);
      
      // Verify data integrity
      const checksum = this.calculateChecksum(decompressed);
      if (checksum !== envelope.checksum) {
        throw new DataCorruptionError('Checksum mismatch');
      }
      
      return JSON.parse(decompressed) as T;
    } catch (error) {
      console.error('Failed to load data:', error);
      return null;
    }
  }
}
```

### Auto-Save System

```typescript
class AutoSaveManager {
  private saveQueue = new Map<string, any>();
  private saveTimer: NodeJS.Timeout | null = null;
  private readonly SAVE_INTERVAL = 30000; // 30 seconds
  private readonly MAX_QUEUE_SIZE = 100;
  
  constructor(private persistence: PersistenceEngine) {
    this.startAutoSave();
  }
  
  queueSave<T>(key: string, data: T): void {
    this.saveQueue.set(key, data);
    
    if (this.saveQueue.size > this.MAX_QUEUE_SIZE) {
      this.flushQueue();
    }
  }
  
  private startAutoSave(): void {
    this.saveTimer = setInterval(() => {
      this.flushQueue();
    }, this.SAVE_INTERVAL);
  }
  
  private async flushQueue(): Promise<void> {
    if (this.saveQueue.size === 0) return;
    
    const saves = Array.from(this.saveQueue.entries());
    this.saveQueue.clear();
    
    try {
      await Promise.all(
        saves.map(([key, data]) => this.persistence.save(key, data))
      );
      
      this.notifySuccess(saves.length);
    } catch (error) {
      // Re-queue failed saves
      saves.forEach(([key, data]) => this.saveQueue.set(key, data));
      this.notifyError(error);
    }
  }
}
```

## Data Versioning and Migration

### Version Management

```typescript
interface DataVersion {
  major: number;
  minor: number;
  patch: number;
}

interface MigrationRule {
  from: DataVersion;
  to: DataVersion;
  migrate: (data: any) => any;
  validate: (data: any) => boolean;
}

class DataMigrationEngine {
  private migrations: MigrationRule[] = [];
  
  registerMigration(rule: MigrationRule): void {
    this.migrations.push(rule);
    this.migrations.sort((a, b) => this.compareVersions(a.from, b.from));
  }
  
  async migrateData(data: any, fromVersion: DataVersion): Promise<any> {
    let currentData = data;
    let currentVersion = fromVersion;
    
    for (const migration of this.migrations) {
      if (this.compareVersions(currentVersion, migration.from) === 0) {
        try {
          currentData = migration.migrate(currentData);
          
          if (!migration.validate(currentData)) {
            throw new MigrationError('Migration validation failed');
          }
          
          currentVersion = migration.to;
        } catch (error) {
          throw new MigrationError(
            `Migration from ${this.versionToString(migration.from)} failed`,
            error
          );
        }
      }
    }
    
    return currentData;
  }
}

// Example migration
const migrationV1ToV2: MigrationRule = {
  from: { major: 1, minor: 0, patch: 0 },
  to: { major: 1, minor: 1, patch: 0 },
  migrate: (data: any) => ({
    ...data,
    userProfile: {
      ...data.userProfile,
      preferences: {
        ...data.userProfile.preferences,
        currency: 'CHF', // Add default currency
        locale: 'de-CH'  // Add default locale
      }
    }
  }),
  validate: (data: any) => 
    data.userProfile?.preferences?.currency !== undefined
};
```

### Schema Evolution

```typescript
interface DataSchema {
  version: DataVersion;
  structure: SchemaDefinition;
  constraints: ValidationRule[];
}

interface SchemaDefinition {
  [key: string]: {
    type: 'string' | 'number' | 'boolean' | 'object' | 'array';
    required: boolean;
    default?: any;
    validation?: ValidationRule[];
  };
}

class SchemaManager {
  private schemas = new Map<string, DataSchema>();
  
  registerSchema(name: string, schema: DataSchema): void {
    this.schemas.set(name, schema);
  }
  
  validateData(schemaName: string, data: any): ValidationResult {
    const schema = this.schemas.get(schemaName);
    if (!schema) {
      throw new Error(`Schema ${schemaName} not found`);
    }
    
    return this.validateAgainstSchema(data, schema);
  }
  
  private validateAgainstSchema(
    data: any,
    schema: DataSchema
  ): ValidationResult {
    const errors: ValidationError[] = [];
    
    for (const [field, definition] of Object.entries(schema.structure)) {
      const value = data[field];
      
      // Check required fields
      if (definition.required && value === undefined) {
        errors.push({
          field,
          message: `Required field ${field} is missing`,
          code: 'REQUIRED_FIELD_MISSING'
        });
        continue;
      }
      
      // Type validation
      if (value !== undefined && !this.validateType(value, definition.type)) {
        errors.push({
          field,
          message: `Field ${field} has invalid type`,
          code: 'INVALID_TYPE'
        });
      }
      
      // Custom validation rules
      if (definition.validation) {
        for (const rule of definition.validation) {
          if (!rule.validate(value)) {
            errors.push({
              field,
              message: rule.message,
              code: rule.code
            });
          }
        }
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }
}
```

## Data Compression and Optimization

### Compression Engine

```typescript
class CompressionEngine {
  async compress(data: string): Promise<string> {
    // Use LZ-string for client-side compression
    return LZString.compress(data);
  }
  
  async decompress(compressed: string): Promise<string> {
    const decompressed = LZString.decompress(compressed);
    if (decompressed === null) {
      throw new CompressionError('Failed to decompress data');
    }
    return decompressed;
  }
  
  calculateCompressionRatio(original: string, compressed: string): number {
    return compressed.length / original.length;
  }
}

// Advanced compression with chunking for large datasets
class ChunkedCompressionEngine extends CompressionEngine {
  private readonly CHUNK_SIZE = 64 * 1024; // 64KB chunks
  
  async compress(data: string): Promise<string> {
    if (data.length <= this.CHUNK_SIZE) {
      return super.compress(data);
    }
    
    const chunks: string[] = [];
    for (let i = 0; i < data.length; i += this.CHUNK_SIZE) {
      const chunk = data.slice(i, i + this.CHUNK_SIZE);
      chunks.push(await super.compress(chunk));
    }
    
    return JSON.stringify({
      type: 'chunked',
      chunks,
      originalLength: data.length
    });
  }
  
  async decompress(compressed: string): Promise<string> {
    try {
      const parsed = JSON.parse(compressed);
      if (parsed.type === 'chunked') {
        const decompressedChunks = await Promise.all(
          parsed.chunks.map((chunk: string) => super.decompress(chunk))
        );
        return decompressedChunks.join('');
      }
    } catch {
      // Fallback to regular decompression
    }
    
    return super.decompress(compressed);
  }
}
```

### Data Deduplication

```typescript
class DeduplicationEngine {
  private hashCache = new Map<string, string>();
  
  deduplicate(data: any): DeduplicatedData {
    const references = new Map<string, string>();
    const deduplicated = this.processObject(data, references);
    
    return {
      data: deduplicated,
      references: Object.fromEntries(references)
    };
  }
  
  private processObject(obj: any, references: Map<string, string>): any {
    if (typeof obj !== 'object' || obj === null) {
      return obj;
    }
    
    const hash = this.calculateHash(obj);
    
    if (this.hashCache.has(hash)) {
      const refId = this.hashCache.get(hash)!;
      return { $ref: refId };
    }
    
    const refId = this.generateRefId();
    this.hashCache.set(hash, refId);
    references.set(refId, obj);
    
    if (Array.isArray(obj)) {
      return obj.map(item => this.processObject(item, references));
    }
    
    const result: any = {};
    for (const [key, value] of Object.entries(obj)) {
      result[key] = this.processObject(value, references);
    }
    
    return result;
  }
}
```

## Backup and Recovery

### Automatic Backup System

```typescript
class BackupManager {
  private readonly MAX_BACKUPS = 10;
  private readonly BACKUP_INTERVAL = 24 * 60 * 60 * 1000; // 24 hours
  
  async createBackup(): Promise<BackupInfo> {
    const timestamp = Date.now();
    const backupId = `backup_${timestamp}`;
    
    try {
      // Collect all user data
      const userData = await this.collectUserData();
      
      // Create backup package
      const backup: BackupPackage = {
        id: backupId,
        timestamp,
        version: CURRENT_VERSION,
        data: userData,
        checksum: this.calculateChecksum(userData)
      };
      
      // Store backup
      await this.persistence.save(`backup:${backupId}`, backup);
      
      // Cleanup old backups
      await this.cleanupOldBackups();
      
      return {
        id: backupId,
        timestamp,
        size: this.calculateSize(backup)
      };
    } catch (error) {
      throw new BackupError('Failed to create backup', error);
    }
  }
  
  async restoreBackup(backupId: string): Promise<void> {
    try {
      const backup = await this.persistence.load<BackupPackage>(
        `backup:${backupId}`
      );
      
      if (!backup) {
        throw new BackupError('Backup not found');
      }
      
      // Verify backup integrity
      const checksum = this.calculateChecksum(backup.data);
      if (checksum !== backup.checksum) {
        throw new BackupError('Backup corrupted');
      }
      
      // Restore data
      await this.restoreUserData(backup.data);
      
    } catch (error) {
      throw new BackupError('Failed to restore backup', error);
    }
  }
  
  async listBackups(): Promise<BackupInfo[]> {
    const keys = await this.persistence.getKeys();
    const backupKeys = keys.filter(key => key.startsWith('backup:'));
    
    const backups = await Promise.all(
      backupKeys.map(async key => {
        const backup = await this.persistence.load<BackupPackage>(key);
        return backup ? {
          id: backup.id,
          timestamp: backup.timestamp,
          size: this.calculateSize(backup)
        } : null;
      })
    );
    
    return backups
      .filter(backup => backup !== null)
      .sort((a, b) => b!.timestamp - a!.timestamp) as BackupInfo[];
  }
}
```

### Data Recovery

```typescript
class DataRecoveryEngine {
  async recoverCorruptedData(key: string): Promise<any> {
    // Try multiple recovery strategies
    const strategies = [
      () => this.recoverFromBackup(key),
      () => this.recoverFromCache(key),
      () => this.recoverFromHistory(key),
      () => this.recoverFromDefaults(key)
    ];
    
    for (const strategy of strategies) {
      try {
        const recovered = await strategy();
        if (recovered) {
          console.log(`Data recovered using strategy: ${strategy.name}`);
          return recovered;
        }
      } catch (error) {
        console.warn(`Recovery strategy failed: ${strategy.name}`, error);
      }
    }
    
    throw new RecoveryError('All recovery strategies failed');
  }
  
  private async recoverFromBackup(key: string): Promise<any> {
    const backups = await this.backupManager.listBackups();
    
    for (const backup of backups) {
      try {
        const backupData = await this.backupManager.getBackupData(backup.id);
        if (backupData[key]) {
          return backupData[key];
        }
      } catch (error) {
        continue; // Try next backup
      }
    }
    
    return null;
  }
  
  private async recoverFromCache(key: string): Promise<any> {
    // Check if data exists in memory cache
    return this.memoryCache.get(key);
  }
  
  private async recoverFromHistory(key: string): Promise<any> {
    // Check historical versions
    const history = await this.versionManager.getHistory(key);
    return history.length > 0 ? history[0].data : null;
  }
  
  private recoverFromDefaults(key: string): any {
    // Return default values based on key
    return this.defaultValueProvider.getDefault(key);
  }
}
```

## Performance Monitoring

### Storage Analytics

```typescript
class StorageAnalytics {
  async getStorageStats(): Promise<StorageStats> {
    const keys = await this.persistence.getKeys();
    const stats: StorageStats = {
      totalKeys: keys.length,
      totalSize: 0,
      keyStats: [],
      compressionRatio: 0,
      fragmentationLevel: 0
    };
    
    for (const key of keys) {
      const size = await this.getKeySize(key);
      const lastAccess = await this.getLastAccessTime(key);
      
      stats.keyStats.push({
        key,
        size,
        lastAccess,
        accessCount: await this.getAccessCount(key)
      });
      
      stats.totalSize += size;
    }
    
    stats.compressionRatio = await this.calculateCompressionRatio();
    stats.fragmentationLevel = await this.calculateFragmentation();
    
    return stats;
  }
  
  async optimizeStorage(): Promise<OptimizationResult> {
    const stats = await this.getStorageStats();
    const optimizations: OptimizationAction[] = [];
    
    // Remove unused keys
    const unusedKeys = stats.keyStats.filter(
      stat => Date.now() - stat.lastAccess > 30 * 24 * 60 * 60 * 1000
    );
    
    for (const stat of unusedKeys) {
      await this.persistence.delete(stat.key);
      optimizations.push({
        type: 'delete',
        key: stat.key,
        spaceSaved: stat.size
      });
    }
    
    // Compress large keys
    const largeKeys = stats.keyStats.filter(stat => stat.size > 100 * 1024);
    
    for (const stat of largeKeys) {
      const compressed = await this.compressKey(stat.key);
      if (compressed.spaceSaved > 0) {
        optimizations.push(compressed);
      }
    }
    
    return {
      optimizations,
      totalSpaceSaved: optimizations.reduce(
        (sum, opt) => sum + opt.spaceSaved,
        0
      )
    };
  }
}
```

### Performance Metrics

```typescript
class PersistenceMetrics {
  private metrics = new Map<string, PerformanceMetric>();
  
  startOperation(operation: string): string {
    const operationId = this.generateOperationId();
    const metric: PerformanceMetric = {
      operation,
      startTime: performance.now(),
      operationId
    };
    
    this.metrics.set(operationId, metric);
    return operationId;
  }
  
  endOperation(operationId: string, success: boolean = true): void {
    const metric = this.metrics.get(operationId);
    if (!metric) return;
    
    metric.endTime = performance.now();
    metric.duration = metric.endTime - metric.startTime;
    metric.success = success;
    
    this.recordMetric(metric);
    this.metrics.delete(operationId);
  }
  
  getAverageOperationTime(operation: string): number {
    const operations = this.getOperationHistory(operation);
    if (operations.length === 0) return 0;
    
    const totalTime = operations.reduce((sum, op) => sum + op.duration, 0);
    return totalTime / operations.length;
  }
  
  getSuccessRate(operation: string): number {
    const operations = this.getOperationHistory(operation);
    if (operations.length === 0) return 1;
    
    const successful = operations.filter(op => op.success).length;
    return successful / operations.length;
  }
}
```

## Security and Privacy

### Data Encryption

```typescript
class EncryptionEngine {
  private readonly algorithm = 'AES-GCM';
  private readonly keyLength = 256;
  
  async generateKey(): Promise<CryptoKey> {
    return crypto.subtle.generateKey(
      {
        name: this.algorithm,
        length: this.keyLength
      },
      true,
      ['encrypt', 'decrypt']
    );
  }
  
  async encrypt(data: string, key?: CryptoKey): Promise<string> {
    const encryptionKey = key || await this.getOrCreateKey();
    const encoder = new TextEncoder();
    const dataBuffer = encoder.encode(data);
    
    const iv = crypto.getRandomValues(new Uint8Array(12));
    
    const encrypted = await crypto.subtle.encrypt(
      {
        name: this.algorithm,
        iv
      },
      encryptionKey,
      dataBuffer
    );
    
    // Combine IV and encrypted data
    const combined = new Uint8Array(iv.length + encrypted.byteLength);
    combined.set(iv);
    combined.set(new Uint8Array(encrypted), iv.length);
    
    return this.arrayBufferToBase64(combined);
  }
  
  async decrypt(encryptedData: string, key?: CryptoKey): Promise<string> {
    const decryptionKey = key || await this.getOrCreateKey();
    const combined = this.base64ToArrayBuffer(encryptedData);
    
    const iv = combined.slice(0, 12);
    const encrypted = combined.slice(12);
    
    const decrypted = await crypto.subtle.decrypt(
      {
        name: this.algorithm,
        iv
      },
      decryptionKey,
      encrypted
    );
    
    const decoder = new TextDecoder();
    return decoder.decode(decrypted);
  }
}
```

---

*The data persistence system ensures your financial data is always safe, accessible, and private while providing enterprise-grade reliability and performance.*
