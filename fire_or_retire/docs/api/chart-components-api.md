# Chart Components API Reference

**Complete API documentation for data visualization components**

This reference provides comprehensive documentation for all chart components, hooks, utilities, and interfaces in the Swiss Budget Pro data visualization system.

## Core Components

### DataVisualizationDashboard

**Main orchestration component for the visualization system.**

```typescript
interface DataVisualizationDashboardProps {
  darkMode: boolean;
  userData: UserFinancialData;
  expenses?: ExpenseData[];
  investments?: InvestmentData[];
  savingsGoals?: SavingsGoalData[];
  className?: string;
}

const DataVisualizationDashboard: React.FC<DataVisualizationDashboardProps>
```

**Props:**

```{list-table} DataVisualizationDashboard Props
:header-rows: 1
:widths: 20 20 60

* - Prop
  - Type
  - Description
* - darkMode
  - boolean
  - Enable dark theme for all charts
* - userData
  - UserFinancialData
  - Core financial data for calculations
* - expenses
  - ExpenseData[]
  - Optional expense data for detailed analysis
* - investments
  - InvestmentData[]
  - Optional investment portfolio data
* - savingsGoals
  - SavingsGoalData[]
  - Optional savings goals for progress tracking
* - className
  - string
  - Additional CSS classes for styling
```

**Features:**
- Multi-view interface (Enhanced, Mobile, Performance)
- Real-time dashboard statistics
- Performance monitoring integration
- Settings persistence with localStorage

**Example Usage:**
```typescript
<DataVisualizationDashboard
  darkMode={isDarkMode}
  userData={{
    currentAge: 30,
    retirementAge: 60,
    currentSavings: 100000,
    monthlyIncome: 8000,
    monthlyExpenses: 5000,
    expectedReturn: 7,
    inflationRate: 2,
    safeWithdrawalRate: 4,
  }}
  expenses={expenseData}
  investments={investmentData}
  savingsGoals={savingsGoals}
  className="custom-dashboard"
/>
```

### EnhancedD3Chart

**Professional D3.js chart component with advanced features.**

```typescript
interface ChartData {
  date: Date;
  value: number;
  label?: string;
  category?: string;
}

interface ChartConfig {
  type: 'line' | 'area' | 'bar' | 'scatter';
  width?: number;
  height?: number;
  margin?: { top: number; right: number; bottom: number; left: number };
  color?: string;
  gradient?: boolean;
  animated?: boolean;
  interactive?: boolean;
  responsive?: boolean;
}

interface EnhancedD3ChartProps {
  data: ChartData[];
  config: ChartConfig;
  darkMode: boolean;
  onDataPointClick?: (data: ChartData) => void;
  onDataPointHover?: (data: ChartData | null) => void;
  className?: string;
}

const EnhancedD3Chart: React.FC<EnhancedD3ChartProps>
```

**Props:**

```{list-table} EnhancedD3Chart Props
:header-rows: 1
:widths: 20 20 60

* - Prop
  - Type
  - Description
* - data
  - ChartData[]
  - Array of data points to visualize
* - config
  - ChartConfig
  - Chart configuration and styling options
* - darkMode
  - boolean
  - Enable dark theme styling
* - onDataPointClick
  - (data: ChartData) => void
  - Callback for data point click events
* - onDataPointHover
  - (data: ChartData \| null) => void
  - Callback for data point hover events
* - className
  - string
  - Additional CSS classes
```

**Chart Configuration Options:**

```{list-table} ChartConfig Properties
:header-rows: 1
:widths: 20 20 20 40

* - Property
  - Type
  - Default
  - Description
* - type
  - 'line' \| 'area' \| 'bar' \| 'scatter'
  - 'line'
  - Chart visualization type
* - width
  - number
  - 600
  - Chart width in pixels
* - height
  - number
  - 400
  - Chart height in pixels
* - margin
  - MarginConfig
  - {top: 20, right: 30, bottom: 40, left: 50}
  - Chart margins for axes and labels
* - color
  - string
  - '#3B82F6'
  - Primary chart color
* - gradient
  - boolean
  - false
  - Enable gradient fills for area charts
* - animated
  - boolean
  - true
  - Enable smooth animations
* - interactive
  - boolean
  - true
  - Enable user interactions
* - responsive
  - boolean
  - true
  - Enable responsive sizing
```

**Example Usage:**
```typescript
const chartData = [
  { date: new Date('2024-01-01'), value: 100000, label: 'Start' },
  { date: new Date('2024-06-01'), value: 120000, label: 'Mid Year' },
  { date: new Date('2024-12-01'), value: 150000, label: 'Current' },
];

<EnhancedD3Chart
  data={chartData}
  config={{
    type: 'area',
    width: 800,
    height: 400,
    color: '#10B981',
    gradient: true,
    animated: true,
    interactive: true,
  }}
  darkMode={false}
  onDataPointClick={(data) => console.log('Clicked:', data)}
  onDataPointHover={(data) => setHoveredData(data)}
/>
```

### ChartControls

**Interactive control panel for chart customization.**

```typescript
type TimeframeOption = '1M' | '3M' | '6M' | '1Y' | '2Y' | 'ALL';
type ChartTypeOption = 'line' | 'area' | 'bar' | 'scatter';
type ExportFormat = 'png' | 'svg' | 'pdf' | 'csv' | 'json';

interface MetricDefinition {
  key: string;
  label: string;
  color: string;
  enabled: boolean;
}

interface ChartControlsProps {
  darkMode: boolean;
  selectedTimeframe: TimeframeOption;
  selectedChartType: ChartTypeOption;
  selectedMetrics: string[];
  availableMetrics: MetricDefinition[];
  onTimeframeChange: (timeframe: TimeframeOption) => void;
  onChartTypeChange: (chartType: ChartTypeOption) => void;
  onMetricToggle: (metricKey: string) => void;
  onExport?: (format: ExportFormat) => void;
  onZoomIn?: () => void;
  onZoomOut?: () => void;
  onResetZoom?: () => void;
  onFullscreen?: () => void;
  showExportOptions?: boolean;
  showZoomControls?: boolean;
  showChartTypeSelector?: boolean;
  className?: string;
}

const ChartControls: React.FC<ChartControlsProps>
```

**Example Usage:**
```typescript
const availableMetrics = [
  { key: 'netWorth', label: 'Net Worth', color: '#10B981', enabled: true },
  { key: 'savingsRate', label: 'Savings Rate', color: '#F59E0B', enabled: true },
  { key: 'fireProgress', label: 'FIRE Progress', color: '#06B6D4', enabled: true },
];

<ChartControls
  darkMode={false}
  selectedTimeframe="1Y"
  selectedChartType="line"
  selectedMetrics={['netWorth', 'savingsRate']}
  availableMetrics={availableMetrics}
  onTimeframeChange={setTimeframe}
  onChartTypeChange={setChartType}
  onMetricToggle={toggleMetric}
  onExport={handleExport}
  showExportOptions={true}
  showZoomControls={true}
/>
```

### MobileOptimizedChart

**Touch-optimized chart component for mobile devices.**

```typescript
interface TouchPoint {
  x: number;
  y: number;
  timestamp: number;
}

interface MobileChartProps {
  data: Array<{ date: Date; value: number; label?: string }>;
  darkMode: boolean;
  width?: number;
  height?: number;
  color?: string;
  onDataPointSelect?: (data: any) => void;
  className?: string;
}

const MobileOptimizedChart: React.FC<MobileChartProps>
```

**Mobile-Specific Features:**
- Touch gesture recognition (tap, swipe, pinch)
- Responsive sizing for mobile screens
- Simplified interface for touch interaction
- Performance optimization for mobile devices

**Example Usage:**
```typescript
<MobileOptimizedChart
  data={mobileChartData}
  darkMode={false}
  color="#10B981"
  onDataPointSelect={handleMobileSelection}
  className="mobile-chart"
/>
```

## Custom Hooks

### useD3Chart

**Custom React hook for D3.js chart management.**

```typescript
interface ChartDimensions {
  width: number;
  height: number;
  margin: { top: number; right: number; bottom: number; left: number };
}

interface UseD3ChartOptions {
  responsive?: boolean;
  animated?: boolean;
  darkMode?: boolean;
  onResize?: (dimensions: ChartDimensions) => void;
}

const useD3Chart = <T>(
  data: T[],
  dimensions: ChartDimensions,
  options: UseD3ChartOptions = {}
) => {
  // Hook implementation
  return {
    svgRef: RefObject<SVGSVGElement>;
    containerRef: RefObject<HTMLDivElement>;
    createSVG: () => { svg: d3.Selection; dimensions: ChartDimensions } | null;
    createTimeScale: (domain: [Date, Date], range: [number, number]) => d3.ScaleTime;
    createLinearScale: (domain: [number, number], range: [number, number], nice?: boolean) => d3.ScaleLinear;
    animatePathDraw: (path: d3.Selection, duration?: number) => void;
    animateElementsSequentially: (selection: d3.Selection, duration?: number, delay?: number) => void;
    createTooltip: () => d3.Selection<HTMLDivElement>;
    showTooltip: (tooltip: d3.Selection, content: string, event: MouseEvent) => void;
    hideTooltip: (tooltip: d3.Selection) => void;
    createXAxis: (scale: d3.ScaleTime | d3.ScaleLinear, format?: string) => d3.Axis;
    createYAxis: (scale: d3.ScaleLinear, format?: (value: number) => string) => d3.Axis;
    styleAxes: (g: d3.Selection<SVGGElement>) => void;
    formatCurrency: (value: number, compact?: boolean) => string;
    formatDate: (date: Date, format?: 'short' | 'long') => string;
    formatPercentage: (value: number, decimals?: number) => string;
    getResponsiveDimensions: () => ChartDimensions;
  };
};
```

**Hook Features:**
- D3.js scale creation and management
- Animation utilities for smooth transitions
- Swiss locale formatting functions
- Responsive dimension handling
- Tooltip system management

**Example Usage:**
```typescript
const {
  svgRef,
  containerRef,
  createSVG,
  createTimeScale,
  createLinearScale,
  formatCurrency,
  formatDate,
} = useD3Chart(data, dimensions, {
  responsive: true,
  animated: true,
  darkMode: false,
});

// Use in component
useEffect(() => {
  const result = createSVG();
  if (!result) return;
  
  const { svg, dimensions } = result;
  const xScale = createTimeScale([startDate, endDate], [0, dimensions.width]);
  const yScale = createLinearScale([minValue, maxValue], [dimensions.height, 0]);
  
  // Render chart with scales
}, [data]);
```

## Utility Functions

### Chart Export Utilities

```typescript
// Export chart as PNG image
export const exportChartAsPNG = async (
  svgElement: SVGSVGElement, 
  filename: string = 'chart.png'
) => Promise<void>

// Export chart as SVG vector
export const exportChartAsSVG = (
  svgElement: SVGSVGElement, 
  filename: string = 'chart.svg'
) => void

// Export data as CSV
export const exportDataAsCSV = (
  data: any[], 
  filename: string = 'chart-data.csv'
) => void

// Export data as JSON
export const exportDataAsJSON = (
  data: any[], 
  filename: string = 'chart-data.json'
) => void
```

### Mobile Utilities

```typescript
// Detect mobile device
export const isMobileDevice = () => boolean

// Get touch event coordinates
export const getTouchEventCoordinates = (
  event: TouchEvent, 
  element: Element
) => { x: number; y: number }

// Detect swipe direction
export const detectSwipeDirection = (
  startPoint: TouchPoint, 
  endPoint: TouchPoint
) => 'left' | 'right' | 'up' | 'down'
```

## Type Definitions

### Core Data Types

```typescript
interface UserFinancialData {
  currentAge: number;
  retirementAge: number;
  currentSavings: number;
  monthlyIncome: number;
  monthlyExpenses: number;
  expectedReturn: number;
  inflationRate: number;
  safeWithdrawalRate: number;
}

interface ExpenseData {
  id: string;
  category: string;
  amount: number;
  isActive: boolean;
  priority?: 'high' | 'medium' | 'low';
}

interface InvestmentData {
  id: string;
  name: string;
  amount: number;
  type: 'stocks' | 'bonds' | 'real-estate' | 'crypto' | 'other';
  expectedReturn?: number;
}

interface SavingsGoalData {
  id: string;
  name: string;
  targetAmount: number;
  currentAmount: number;
  targetDate?: Date;
  priority?: 'high' | 'medium' | 'low';
}
```

### Chart-Specific Types

```typescript
interface HistoricalDataPoint {
  date: string;
  age: number;
  netWorth: number;
  monthlyIncome: number;
  monthlyExpenses: number;
  monthlySavings: number;
  savingsRate: number;
  fireProgress: number;
  investmentValue: number;
  emergencyFund: number;
}

interface PerformanceMetric {
  name: string;
  value: number;
  unit: string;
  threshold: number;
  status: 'good' | 'warning' | 'critical';
  description: string;
}

interface DashboardSettings {
  showPerformanceMonitor: boolean;
  enableMobileOptimization: boolean;
  autoDetectMobile: boolean;
  defaultChartType: ChartTypeOption;
  defaultTimeframe: TimeframeOption;
  enableAnimations: boolean;
  enableInteractivity: boolean;
}
```

## Performance Considerations

### Optimization Guidelines

**Data Handling:**
- Use `React.memo` for expensive chart components
- Implement data memoization with `useMemo`
- Debounce real-time updates (300ms recommended)
- Limit data points for large datasets (max 1000 points)

**Rendering Performance:**
- Enable responsive mode only when needed
- Disable animations on slower devices
- Use canvas fallback for very large datasets
- Implement progressive loading for complex charts

**Memory Management:**
- Clean up D3.js elements on component unmount
- Remove event listeners properly
- Avoid memory leaks in animation loops
- Monitor memory usage with performance tools

### Performance Thresholds

```{list-table} Performance Requirements
:header-rows: 1
:widths: 30 20 50

* - Metric
  - Threshold
  - Recommendation
* - Initial Render
  - <2000ms
  - Optimize data processing
* - Chart Updates
  - <500ms
  - Use debounced updates
* - Memory Usage
  - <100MB
  - Implement cleanup functions
* - Frame Rate
  - >30fps
  - Reduce animation complexity
* - Interaction Latency
  - <100ms
  - Optimize event handlers
```

---

*This API reference provides complete documentation for integrating and customizing the Swiss Budget Pro data visualization system.*
