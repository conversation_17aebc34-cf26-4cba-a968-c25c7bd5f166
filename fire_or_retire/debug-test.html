<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Swiss Budget Pro - Debug Test Page</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #1e293b 0%, #3b82f6 100%);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            font-size: 2.5rem;
            margin: 0 0 10px 0;
        }
        .header p {
            opacity: 0.8;
            margin: 0;
        }
        .test-section {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .test-section h3 {
            margin-top: 0;
            color: #60a5fa;
        }
        .button {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            margin: 5px;
            transition: all 0.2s;
        }
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
        }
        .button.danger {
            background: linear-gradient(135deg, #ef4444, #dc2626);
        }
        .button.warning {
            background: linear-gradient(135deg, #f59e0b, #d97706);
        }
        .button.success {
            background: linear-gradient(135deg, #10b981, #059669);
        }
        .log-output {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 15px;
            border: 1px solid #333;
        }
        .shortcuts {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
        }
        .shortcuts h4 {
            margin-top: 0;
            color: #fbbf24;
        }
        .shortcut {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            font-family: monospace;
        }
        .error-info {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .error-info h4 {
            color: #fca5a5;
            margin-top: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🐛 Debug Test Page</h1>
            <p>Test and demonstrate the Swiss Budget Pro debugging capabilities</p>
        </div>

        <div class="error-info">
            <h4>🚨 Current Runtime Errors Detected</h4>
            <p><strong>Tailwind CSS Warning:</strong> Using Tailwind CSS CDN in production is not recommended</p>
            <p><strong>Reference Error:</strong> SwissBudgetProWithErrorBoundary is not defined</p>
            <p>Use the debug tools below to investigate and capture detailed error information.</p>
        </div>

        <div class="test-section">
            <h3>🔥 Error Simulation Tests</h3>
            <p>Click these buttons to simulate different types of runtime errors:</p>
            
            <button class="button danger" onclick="triggerReferenceError()">
                Reference Error
            </button>
            <button class="button danger" onclick="triggerTypeError()">
                Type Error
            </button>
            <button class="button warning" onclick="triggerNetworkError()">
                Network Error
            </button>
            <button class="button warning" onclick="triggerLocalStorageError()">
                Storage Error
            </button>
            <button class="button danger" onclick="triggerUnhandledPromise()">
                Promise Rejection
            </button>
        </div>

        <div class="test-section">
            <h3>📊 System Diagnostics</h3>
            <p>Run comprehensive system checks:</p>
            
            <button class="button" onclick="runDiagnostics()">
                🔍 Run Full Diagnostics
            </button>
            <button class="button" onclick="checkBrowserInfo()">
                🌐 Browser Info
            </button>
            <button class="button" onclick="checkLocalStorage()">
                💾 Storage Health
            </button>
            <button class="button" onclick="checkPerformance()">
                ⚡ Performance Check
            </button>
        </div>

        <div class="test-section">
            <h3>📋 Debug Actions</h3>
            <p>Debug panel and logging actions:</p>
            
            <button class="button success" onclick="openDebugPanel()">
                🐛 Open Debug Panel
            </button>
            <button class="button" onclick="exportLogs()">
                📥 Export Debug Logs
            </button>
            <button class="button warning" onclick="clearLogs()">
                🗑️ Clear All Logs
            </button>
            <button class="button" onclick="logTestMessage()">
                📝 Log Test Message
            </button>
        </div>

        <div class="shortcuts">
            <h4>⌨️ Keyboard Shortcuts</h4>
            <div class="shortcut">
                <span>Toggle Debug Panel:</span>
                <code>Ctrl + Shift + D</code>
            </div>
            <div class="shortcut">
                <span>Export Logs:</span>
                <code>Ctrl + Shift + L</code>
            </div>
            <div class="shortcut">
                <span>Clear Logs:</span>
                <code>Ctrl + Shift + C</code>
            </div>
        </div>

        <div class="test-section">
            <h3>📺 Console Output</h3>
            <p>Real-time debug output (also check browser console):</p>
            <div id="logOutput" class="log-output">
                Debug logger initialized. Ready for testing...<br>
                Press F12 to open browser console for full debug output.<br>
            </div>
        </div>
    </div>

    <script>
        // Simple debug logger for this test page
        const testLogger = {
            log: (level, category, message, data = {}) => {
                const timestamp = new Date().toISOString();
                const logEntry = `[${timestamp}] [${level.toUpperCase()}] [${category}] ${message}`;
                
                // Add to page output
                const output = document.getElementById('logOutput');
                output.innerHTML += logEntry + '<br>';
                output.scrollTop = output.scrollHeight;
                
                // Log to console with styling
                const styles = {
                    error: 'color: #ff6b6b; font-weight: bold;',
                    warn: 'color: #ffa726; font-weight: bold;',
                    info: 'color: #42a5f5;',
                    debug: 'color: #66bb6a;'
                };
                
                console.log(`%c${logEntry}`, styles[level] || styles.info);
                if (Object.keys(data).length > 0) {
                    console.log('Data:', data);
                }
                
                return `${level}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            }
        };

        // Error simulation functions
        function triggerReferenceError() {
            testLogger.log('info', 'test', 'Triggering reference error...');
            try {
                // This will cause a ReferenceError
                console.log(undefinedVariable);
            } catch (error) {
                testLogger.log('error', 'test', 'Reference error caught', { 
                    error: error.message,
                    stack: error.stack 
                });
            }
        }

        function triggerTypeError() {
            testLogger.log('info', 'test', 'Triggering type error...');
            try {
                // This will cause a TypeError
                null.someMethod();
            } catch (error) {
                testLogger.log('error', 'test', 'Type error caught', { 
                    error: error.message,
                    stack: error.stack 
                });
            }
        }

        function triggerNetworkError() {
            testLogger.log('info', 'test', 'Triggering network error...');
            fetch('https://nonexistent-api.example.com/data')
                .catch(error => {
                    testLogger.log('error', 'test', 'Network error caught', { 
                        error: error.message 
                    });
                });
        }

        function triggerLocalStorageError() {
            testLogger.log('info', 'test', 'Testing localStorage...');
            try {
                // Try to exceed localStorage quota
                const largeData = 'x'.repeat(10 * 1024 * 1024); // 10MB
                localStorage.setItem('test_large_data', largeData);
                testLogger.log('info', 'test', 'localStorage test passed');
            } catch (error) {
                testLogger.log('error', 'test', 'localStorage error caught', { 
                    error: error.message 
                });
            }
        }

        function triggerUnhandledPromise() {
            testLogger.log('info', 'test', 'Triggering unhandled promise rejection...');
            // This will cause an unhandled promise rejection
            Promise.reject(new Error('Test unhandled promise rejection'));
        }

        // Diagnostic functions
        function runDiagnostics() {
            testLogger.log('info', 'diagnostics', 'Running comprehensive diagnostics...');
            
            const diagnostics = {
                browser: navigator.userAgent,
                online: navigator.onLine,
                language: navigator.language,
                platform: navigator.platform,
                cookieEnabled: navigator.cookieEnabled,
                localStorage: isLocalStorageAvailable(),
                sessionStorage: isSessionStorageAvailable(),
                screen: {
                    width: screen.width,
                    height: screen.height,
                    colorDepth: screen.colorDepth
                },
                viewport: {
                    width: window.innerWidth,
                    height: window.innerHeight
                },
                memory: performance.memory ? {
                    used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024) + 'MB',
                    total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024) + 'MB',
                    limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024) + 'MB'
                } : 'Not available'
            };
            
            testLogger.log('info', 'diagnostics', 'System diagnostics completed', diagnostics);
        }

        function checkBrowserInfo() {
            testLogger.log('info', 'browser', 'Browser information', {
                userAgent: navigator.userAgent,
                vendor: navigator.vendor,
                platform: navigator.platform,
                language: navigator.language,
                languages: navigator.languages,
                onLine: navigator.onLine,
                cookieEnabled: navigator.cookieEnabled
            });
        }

        function checkLocalStorage() {
            const available = isLocalStorageAvailable();
            const usage = getLocalStorageUsage();
            
            testLogger.log('info', 'storage', 'Storage health check', {
                localStorage: available,
                sessionStorage: isSessionStorageAvailable(),
                usage: usage,
                quota: '~5MB typical limit'
            });
        }

        function checkPerformance() {
            const timing = performance.timing;
            const navigation = timing.navigationStart;
            
            testLogger.log('info', 'performance', 'Performance metrics', {
                domContentLoaded: timing.domContentLoadedEventEnd - navigation + 'ms',
                loadComplete: timing.loadEventEnd - navigation + 'ms',
                domInteractive: timing.domInteractive - navigation + 'ms',
                memory: performance.memory ? {
                    used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024) + 'MB',
                    total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024) + 'MB'
                } : 'Not available'
            });
        }

        // Debug actions
        function openDebugPanel() {
            testLogger.log('info', 'debug', 'Debug panel would open here');
            alert('Debug panel would open here.\nIn the full app, press Ctrl+Shift+D to open the debug panel.');
        }

        function exportLogs() {
            testLogger.log('info', 'debug', 'Exporting debug logs...');
            
            const logs = document.getElementById('logOutput').innerText;
            const blob = new Blob([logs], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `debug-test-logs-${Date.now()}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            testLogger.log('info', 'debug', 'Debug logs exported');
        }

        function clearLogs() {
            document.getElementById('logOutput').innerHTML = 'Logs cleared...<br>';
            testLogger.log('info', 'debug', 'Debug logs cleared');
        }

        function logTestMessage() {
            testLogger.log('info', 'test', 'Test message logged', {
                timestamp: new Date().toISOString(),
                random: Math.random(),
                testData: { foo: 'bar', nested: { value: 123 } }
            });
        }

        // Helper functions
        function isLocalStorageAvailable() {
            try {
                const test = '__localStorage_test__';
                localStorage.setItem(test, test);
                localStorage.removeItem(test);
                return true;
            } catch {
                return false;
            }
        }

        function isSessionStorageAvailable() {
            try {
                const test = '__sessionStorage_test__';
                sessionStorage.setItem(test, test);
                sessionStorage.removeItem(test);
                return true;
            } catch {
                return false;
            }
        }

        function getLocalStorageUsage() {
            try {
                let used = 0;
                for (const key in localStorage) {
                    if (localStorage.hasOwnProperty(key)) {
                        used += localStorage[key].length + key.length;
                    }
                }
                return {
                    used: Math.round(used / 1024) + 'KB',
                    percentage: Math.round((used / (5 * 1024 * 1024)) * 100) + '%'
                };
            } catch {
                return { used: 'Unknown', percentage: 'Unknown' };
            }
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', (event) => {
            if (event.ctrlKey && event.shiftKey) {
                switch (event.key) {
                    case 'D':
                        event.preventDefault();
                        openDebugPanel();
                        break;
                    case 'L':
                        event.preventDefault();
                        exportLogs();
                        break;
                    case 'C':
                        event.preventDefault();
                        clearLogs();
                        break;
                }
            }
        });

        // Initialize
        testLogger.log('info', 'init', 'Debug test page initialized');
        testLogger.log('info', 'init', 'Ready for error testing and diagnostics');
    </script>
</body>
</html>
