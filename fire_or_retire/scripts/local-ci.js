#!/usr/bin/env node

/**
 * Local CI/CD Pipeline for Swiss Budget Pro
 * Runs comprehensive checks on file changes
 */

import { execSync } from 'child_process';
import { watch } from 'fs';
import { dirname, join } from 'path';
import { fileURLToPath } from 'url';

const __dirname = dirname(fileURLToPath(import.meta.url));
const projectRoot = join(__dirname, '..');

const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
  bold: '\x1b[1m',
  dim: '\x1b[2m',
};

function log(message, color = 'reset') {
  const timestamp = new Date().toLocaleTimeString();
  console.log(
    `${colors.dim}[${timestamp}]${colors.reset} ${colors[color]}${message}${colors.reset}`
  );
}

function logHeader(title) {
  console.clear();
  log(`${'='.repeat(80)}`, 'cyan');
  log(`🚀 Swiss Budget Pro - Local CI/CD Pipeline`, 'bold');
  log(`📋 ${title}`, 'cyan');
  log(`${'='.repeat(80)}`, 'cyan');
}

class LocalCI {
  constructor() {
    this.isRunning = false;
    this.lastRun = 0;
    this.debounceMs = 2000; // 2 second debounce
    this.watchedExtensions = ['.ts', '.tsx', '.js', '.jsx', '.json'];
    this.excludePaths = [
      'node_modules',
      'dist',
      'coverage',
      '.git',
      'archived-code',
    ];
  }

  async runPipeline(trigger = 'manual') {
    if (this.isRunning) {
      log('⏳ Pipeline already running, skipping...', 'yellow');
      return;
    }

    const now = Date.now();
    if (now - this.lastRun < this.debounceMs) {
      log('⏳ Debouncing file changes...', 'dim');
      return;
    }

    this.isRunning = true;
    this.lastRun = now;

    logHeader(`Running Pipeline (${trigger})`);

    const startTime = Date.now();
    const results = [];

    try {
      // Stage 1: Quick Syntax Check
      log('\n🔍 Stage 1: Quick Syntax Check', 'blue');
      results.push(
        await this.runStage('syntax', [
          { cmd: 'npm run type-check', desc: 'TypeScript compilation' },
          {
            cmd: 'npm run lint -- --max-warnings 0',
            desc: 'ESLint validation',
          },
        ])
      );

      // Stage 2: Code Quality
      log('\n📏 Stage 2: Code Quality', 'blue');
      results.push(
        await this.runStage('quality', [
          { cmd: 'npm run format:check', desc: 'Code formatting' },
          {
            cmd: 'node scripts/detect-critical-issues.js',
            desc: 'Critical issue detection',
          },
        ])
      );

      // Stage 3: Fast Tests
      log('\n⚡ Stage 3: Fast Tests', 'blue');
      results.push(
        await this.runStage('tests', [
          {
            cmd: 'npm run test:run -- --reporter=basic --run',
            desc: 'Unit tests',
          },
        ])
      );

      // Stage 4: Build Verification
      log('\n🏗️  Stage 4: Build Verification', 'blue');
      results.push(
        await this.runStage('build', [
          { cmd: 'npm run build', desc: 'Production build' },
        ])
      );

      // Results Summary
      this.showResults(results, startTime);
    } catch (error) {
      log(`💥 Pipeline failed: ${error.message}`, 'red');
    } finally {
      this.isRunning = false;
    }
  }

  async runStage(stageName, commands) {
    const stageResults = [];

    for (const { cmd, desc } of commands) {
      const result = await this.runCommand(cmd, desc);
      stageResults.push(result);

      if (!result.success) {
        log(`❌ Stage '${stageName}' failed at: ${desc}`, 'red');
        break; // Fail fast
      }
    }

    const passed = stageResults.filter(r => r.success).length;
    const total = stageResults.length;

    if (passed === total) {
      log(`✅ Stage '${stageName}' passed (${passed}/${total})`, 'green');
    } else {
      log(`❌ Stage '${stageName}' failed (${passed}/${total})`, 'red');
    }

    return { stageName, passed, total, results: stageResults };
  }

  async runCommand(command, description) {
    try {
      log(`  ▶️  ${description}...`, 'dim');

      const startTime = Date.now();
      const output = execSync(command, {
        encoding: 'utf8',
        stdio: 'pipe',
        timeout: 120000,
        cwd: projectRoot,
      });
      const duration = Date.now() - startTime;

      log(`  ✅ ${description} (${duration}ms)`, 'green');
      return { success: true, output, duration, description };
    } catch (error) {
      const duration = Date.now() - Date.now();
      log(`  ❌ ${description} failed`, 'red');
      if (error.stdout) {
        log(`     ${error.stdout.slice(0, 200)}...`, 'red');
      }
      return {
        success: false,
        error: error.stdout || error.message,
        duration,
        description,
      };
    }
  }

  showResults(stageResults, startTime) {
    const totalDuration = Date.now() - startTime;

    log('\n📊 Pipeline Results', 'cyan');
    log('─'.repeat(60), 'dim');

    let allPassed = true;
    stageResults.forEach(stage => {
      const status = stage.passed === stage.total ? '✅' : '❌';
      log(
        `${status} ${stage.stageName}: ${stage.passed}/${stage.total}`,
        stage.passed === stage.total ? 'green' : 'red'
      );

      if (stage.passed !== stage.total) {
        allPassed = false;
      }
    });

    log('─'.repeat(60), 'dim');
    log(`⏱️  Total time: ${totalDuration}ms`, 'dim');

    if (allPassed) {
      log('🎉 All stages passed! Code is ready.', 'green');
      this.showSuccessMessage();
    } else {
      log('🔧 Some stages failed. Please fix issues.', 'red');
      this.showFailureMessage();
    }
  }

  showSuccessMessage() {
    log('\n✨ Success Checklist:', 'green');
    log('  ✅ TypeScript compilation clean', 'green');
    log('  ✅ No linting errors', 'green');
    log('  ✅ Code formatting consistent', 'green');
    log('  ✅ Tests passing', 'green');
    log('  ✅ Build successful', 'green');
    log('\n🚀 Ready for commit/deployment!', 'bold');
  }

  showFailureMessage() {
    log('\n🔧 Quick fixes:', 'yellow');
    log('  • Run: npm run lint:fix', 'yellow');
    log('  • Run: npm run format', 'yellow');
    log('  • Check: npm run type-check', 'yellow');
    log('  • Test: npm run test', 'yellow');
  }

  startWatching() {
    logHeader('File Watcher Started');
    log('👀 Watching for file changes...', 'blue');
    log('📁 Monitoring: .ts, .tsx, .js, .jsx, .json files', 'dim');
    log('🚫 Excluding: node_modules, dist, coverage', 'dim');
    log('⏹️  Press Ctrl+C to stop\n', 'dim');

    // Watch src directory
    this.watchDirectory('src');

    // Watch root config files
    this.watchFile('package.json');
    this.watchFile('tsconfig.json');
    this.watchFile('vite.config.ts');

    // Initial run
    setTimeout(() => this.runPipeline('startup'), 1000);
  }

  watchDirectory(dir) {
    try {
      watch(dir, { recursive: true }, (eventType, filename) => {
        if (!filename) return;

        const isWatchedFile = this.watchedExtensions.some(ext =>
          filename.endsWith(ext)
        );

        const isExcluded = this.excludePaths.some(path =>
          filename.includes(path)
        );

        if (isWatchedFile && !isExcluded) {
          log(`📝 File changed: ${filename}`, 'yellow');
          setTimeout(() => this.runPipeline('file-change'), 500);
        }
      });

      log(`👀 Watching directory: ${dir}`, 'dim');
    } catch (error) {
      log(`⚠️  Could not watch directory ${dir}: ${error.message}`, 'yellow');
    }
  }

  watchFile(file) {
    try {
      watch(file, eventType => {
        log(`📝 Config file changed: ${file}`, 'yellow');
        setTimeout(() => this.runPipeline('config-change'), 1000);
      });

      log(`👀 Watching file: ${file}`, 'dim');
    } catch (error) {
      log(`⚠️  Could not watch file ${file}: ${error.message}`, 'yellow');
    }
  }
}

// CLI Interface
const args = process.argv.slice(2);
const command = args[0] || 'watch';

const ci = new LocalCI();

switch (command) {
  case 'run':
    ci.runPipeline('manual');
    break;
  case 'watch':
    ci.startWatching();
    break;
  case 'quick':
    // Quick check without build
    ci.runStage('quick', [
      { cmd: 'npm run type-check', desc: 'TypeScript check' },
      { cmd: 'npm run lint', desc: 'Lint check' },
    ]);
    break;
  default:
    log('Usage: node scripts/local-ci.js [run|watch|quick]', 'cyan');
    log('  run   - Run pipeline once', 'dim');
    log('  watch - Start file watcher (default)', 'dim');
    log('  quick - Quick syntax check only', 'dim');
}

// Graceful shutdown
process.on('SIGINT', () => {
  log('\n👋 Local CI/CD stopped', 'yellow');
  process.exit(0);
});
