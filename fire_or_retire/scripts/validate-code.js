#!/usr/bin/env node

/**
 * Comprehensive Code Validation Script
 * Runs multiple checks to catch errors before they reach production
 */

import { execSync } from 'child_process';
import { existsSync, readFileSync } from 'fs';
import { join } from 'path';

const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSection(title) {
  log(`\n${colors.bold}${'='.repeat(60)}`, 'cyan');
  log(`${colors.bold}🔍 ${title}`, 'cyan');
  log(`${colors.bold}${'='.repeat(60)}`, 'cyan');
}

function runCommand(command, description) {
  try {
    log(`\n▶️  ${description}...`, 'blue');
    const output = execSync(command, { 
      encoding: 'utf8', 
      stdio: 'pipe',
      timeout: 60000 
    });
    log(`✅ ${description} passed`, 'green');
    return { success: true, output };
  } catch (error) {
    log(`❌ ${description} failed`, 'red');
    log(error.stdout || error.message, 'red');
    return { success: false, error: error.stdout || error.message };
  }
}

function checkFileExists(filePath, description) {
  if (existsSync(filePath)) {
    log(`✅ ${description} exists`, 'green');
    return true;
  } else {
    log(`❌ ${description} missing: ${filePath}`, 'red');
    return false;
  }
}

function validatePackageJson() {
  logSection('Package.json Validation');
  
  if (!checkFileExists('package.json', 'Package.json')) {
    return false;
  }

  try {
    const packageJson = JSON.parse(readFileSync('package.json', 'utf8'));
    
    // Check required scripts
    const requiredScripts = ['dev', 'build', 'test', 'lint', 'type-check'];
    let allScriptsExist = true;
    
    requiredScripts.forEach(script => {
      if (packageJson.scripts && packageJson.scripts[script]) {
        log(`✅ Script '${script}' defined`, 'green');
      } else {
        log(`❌ Script '${script}' missing`, 'red');
        allScriptsExist = false;
      }
    });

    return allScriptsExist;
  } catch (error) {
    log(`❌ Invalid package.json: ${error.message}`, 'red');
    return false;
  }
}

function validateTsConfig() {
  logSection('TypeScript Configuration');
  
  const configs = [
    { file: 'tsconfig.json', desc: 'Main TypeScript config' },
    { file: 'tsconfig.node.json', desc: 'Node TypeScript config' }
  ];

  let allValid = true;
  configs.forEach(({ file, desc }) => {
    if (!checkFileExists(file, desc)) {
      allValid = false;
      return;
    }

    try {
      JSON.parse(readFileSync(file, 'utf8'));
      log(`✅ ${desc} is valid JSON`, 'green');
    } catch (error) {
      log(`❌ ${desc} has invalid JSON: ${error.message}`, 'red');
      allValid = false;
    }
  });

  return allValid;
}

function checkForCommonIssues() {
  logSection('Common Issue Detection');
  
  const issues = [];
  
  // Check for duplicate exports
  const result = runCommand(
    'grep -r "export.*SwissBudgetPro" src/ || true',
    'Checking for duplicate exports'
  );
  
  if (result.success && result.output.trim()) {
    const lines = result.output.trim().split('\n');
    if (lines.length > 1) {
      issues.push(`Found ${lines.length} export statements for SwissBudgetPro`);
      log(`⚠️  Multiple exports detected:`, 'yellow');
      lines.forEach(line => log(`   ${line}`, 'yellow'));
    }
  }

  // Check for orphaned JSX
  const jsxCheck = runCommand(
    'grep -r "className.*=" src/ | grep -v "export" | wc -l || echo "0"',
    'Checking for orphaned JSX'
  );

  // Check for missing imports
  const importCheck = runCommand(
    'grep -r "SwissEconomicDataEngine\\|MonteCarloEngine\\|SwissTaxEngine" src/ | grep -v "import\\|export" || true',
    'Checking for missing imports'
  );

  return issues;
}

async function main() {
  log(`${colors.bold}🚀 Swiss Budget Pro - Code Validation`, 'magenta');
  log(`${colors.bold}Starting comprehensive validation...`, 'magenta');

  const results = [];

  // 1. Package.json validation
  results.push(validatePackageJson());

  // 2. TypeScript configuration
  results.push(validateTsConfig());

  // 3. TypeScript compilation check
  const typeCheck = runCommand('npm run type-check', 'TypeScript compilation');
  results.push(typeCheck.success);

  // 4. ESLint check
  const lintCheck = runCommand('npm run lint', 'ESLint validation');
  results.push(lintCheck.success);

  // 5. Prettier check
  const formatCheck = runCommand('npm run format:check', 'Code formatting');
  results.push(formatCheck.success);

  // 6. Common issues detection
  logSection('Advanced Issue Detection');
  const issues = checkForCommonIssues();
  if (issues.length > 0) {
    log(`⚠️  Found ${issues.length} potential issues:`, 'yellow');
    issues.forEach(issue => log(`   • ${issue}`, 'yellow'));
  } else {
    log('✅ No common issues detected', 'green');
  }

  // 7. Test compilation (quick check)
  const testCompile = runCommand('npm run test:run -- --run --reporter=basic', 'Test compilation');
  results.push(testCompile.success);

  // Summary
  logSection('Validation Summary');
  const passed = results.filter(Boolean).length;
  const total = results.length;
  
  if (passed === total) {
    log(`🎉 All ${total} validation checks passed!`, 'green');
    log('✅ Code is ready for commit/deployment', 'green');
    process.exit(0);
  } else {
    log(`❌ ${total - passed} out of ${total} checks failed`, 'red');
    log('🔧 Please fix the issues above before proceeding', 'red');
    process.exit(1);
  }
}

main().catch(error => {
  log(`💥 Validation script failed: ${error.message}`, 'red');
  process.exit(1);
});
