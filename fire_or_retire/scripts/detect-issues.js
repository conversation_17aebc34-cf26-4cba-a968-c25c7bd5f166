#!/usr/bin/env node

/**
 * Advanced Issue Detection for Swiss Budget Pro
 * Catches common problems that cause compilation errors
 */

import { execSync } from 'child_process';
import { readFileSync, readdirSync, statSync } from 'fs';
import { extname, join } from 'path';

const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

class IssueDetector {
  constructor() {
    this.issues = [];
    this.warnings = [];
    this.srcDir = 'src';
    this.mainFile = 'retire.tsx';
  }

  detect() {
    log('🔍 Detecting potential issues...', 'blue');

    this.checkDuplicateExports();
    this.checkOrphanedCode();
    this.checkMissingImports();
    this.checkUnusedImports();
    this.checkCircularDependencies();
    this.checkLargeFiles();
    this.checkNamingConventions();
    this.checkReactPatterns();

    this.reportResults();
    return this.issues.length === 0;
  }

  checkDuplicateExports() {
    console.log('  📤 Checking for duplicate exports...');

    try {
      const files = this.getAllTsxFiles();
      const exports = new Map();

      files.forEach(file => {
        const content = readFileSync(file, 'utf8');
        // More precise export matching
        const exportMatches =
          content.match(
            /^export\s+(?:default\s+)?(?:const\s+|function\s+|class\s+|interface\s+|type\s+|enum\s+)?(\w+)/gm
          ) || [];

        exportMatches.forEach(match => {
          // Extract the actual exported name more precisely
          const nameMatch = match.match(/(\w+)(?:\s|$)/);
          if (nameMatch) {
            const exportName = nameMatch[1];
            if (!exports.has(exportName)) {
              exports.set(exportName, []);
            }
            exports.get(exportName).push(file);
          }
        });
      });

      exports.forEach((files, exportName) => {
        if (files.length > 1) {
          this.issues.push({
            type: 'duplicate-export',
            message: `Duplicate export '${exportName}' found in ${files.length} files`,
            files: files,
            severity: 'error',
          });
        }
      });
    } catch (error) {
      this.warnings.push(`Could not check exports: ${error.message}`);
    }
  }

  checkOrphanedCode() {
    console.log('  🏚️  Checking for orphaned code...');

    try {
      const mainFilePath = join(this.srcDir, this.mainFile);
      if (!this.fileExists(mainFilePath)) return;

      const content = readFileSync(mainFilePath, 'utf8');

      // Check for code after export statements
      const exportLines = content
        .split('\n')
        .map((line, index) => ({
          line: line.trim(),
          number: index + 1,
        }))
        .filter(({ line }) => line.startsWith('export default'));

      if (exportLines.length > 1) {
        this.issues.push({
          type: 'multiple-default-exports',
          message: `Multiple default exports found in ${this.mainFile}`,
          lines: exportLines.map(l => l.number),
          severity: 'error',
        });
      }

      // Check for JSX after exports
      const lines = content.split('\n');
      let foundExport = false;
      let orphanedJSX = [];

      lines.forEach((line, index) => {
        if (line.trim().startsWith('export default')) {
          foundExport = true;
        } else if (foundExport && line.includes('className=')) {
          orphanedJSX.push(index + 1);
        }
      });

      if (orphanedJSX.length > 0) {
        this.issues.push({
          type: 'orphaned-jsx',
          message: `Orphaned JSX found after export statement`,
          lines: orphanedJSX.slice(0, 5), // Show first 5 lines
          severity: 'error',
        });
      }
    } catch (error) {
      this.warnings.push(`Could not check for orphaned code: ${error.message}`);
    }
  }

  checkMissingImports() {
    console.log('  📥 Checking for missing imports...');

    try {
      const files = this.getAllTsxFiles();

      files.forEach(file => {
        const content = readFileSync(file, 'utf8');

        // Common patterns that need imports
        const patterns = [
          {
            pattern: /SwissEconomicDataEngine/g,
            import: 'SwissEconomicDataEngine',
          },
          { pattern: /MonteCarloEngine/g, import: 'MonteCarloEngine' },
          { pattern: /SwissTaxEngine/g, import: 'SwissTaxEngine' },
          { pattern: /useLocalStorage/g, import: 'useLocalStorage' },
          { pattern: /React\.useState/g, import: 'React' },
          { pattern: /React\.useEffect/g, import: 'React' },
        ];

        patterns.forEach(({ pattern, import: importName }) => {
          const usage = content.match(pattern);
          const hasImport =
            content.includes(`import`) && content.includes(importName);

          if (usage && !hasImport) {
            this.issues.push({
              type: 'missing-import',
              message: `Missing import for '${importName}' in ${file}`,
              file: file,
              severity: 'error',
            });
          }
        });
      });
    } catch (error) {
      this.warnings.push(`Could not check imports: ${error.message}`);
    }
  }

  checkUnusedImports() {
    log('  🗑️  Checking for unused imports...', 'dim');

    try {
      // Use TypeScript compiler to check unused imports
      const result = execSync(
        'npx tsc --noEmit --noUnusedLocals --noUnusedParameters',
        {
          encoding: 'utf8',
          stdio: 'pipe',
        }
      );
    } catch (error) {
      const output = error.stdout || error.stderr || '';
      const unusedMatches = output.match(
        /'.*' is declared but its value is never read/g
      );

      if (unusedMatches && unusedMatches.length > 5) {
        this.warnings.push(
          `Found ${unusedMatches.length} unused imports/variables`
        );
      }
    }
  }

  checkCircularDependencies() {
    log('  🔄 Checking for circular dependencies...', 'dim');

    try {
      const files = this.getAllTsxFiles();
      const dependencies = new Map();

      files.forEach(file => {
        const content = readFileSync(file, 'utf8');
        const imports = content.match(/import.*from\s+['"]\.\/.*['"]/g) || [];
        dependencies.set(
          file,
          imports
            .map(imp => imp.match(/from\s+['"](.*)['"]$/)?.[1])
            .filter(Boolean)
        );
      });

      // Simple circular dependency detection
      dependencies.forEach((deps, file) => {
        deps.forEach(dep => {
          const depFile = join(this.srcDir, dep + '.tsx');
          if (dependencies.has(depFile)) {
            const depDeps = dependencies.get(depFile);
            if (depDeps.some(d => d.includes(file.replace('.tsx', '')))) {
              this.warnings.push(
                `Potential circular dependency: ${file} ↔ ${depFile}`
              );
            }
          }
        });
      });
    } catch (error) {
      this.warnings.push(
        `Could not check circular dependencies: ${error.message}`
      );
    }
  }

  checkLargeFiles() {
    log('  📏 Checking file sizes...', 'dim');

    try {
      const files = this.getAllTsxFiles();
      const maxLines = 5000;

      files.forEach(file => {
        const content = readFileSync(file, 'utf8');
        const lines = content.split('\n').length;

        if (lines > maxLines) {
          this.warnings.push(`Large file detected: ${file} (${lines} lines)`);
        }
      });
    } catch (error) {
      this.warnings.push(`Could not check file sizes: ${error.message}`);
    }
  }

  checkNamingConventions() {
    log('  📝 Checking naming conventions...', 'dim');

    try {
      const files = this.getAllTsxFiles();

      files.forEach(file => {
        const content = readFileSync(file, 'utf8');

        // Check for component naming
        const componentMatches = content.match(
          /const\s+([a-z][A-Za-z]*)\s*=.*=>/g
        );
        if (componentMatches) {
          componentMatches.forEach(match => {
            const name = match.match(/const\s+([a-z][A-Za-z]*)/)?.[1];
            if (name && name[0] === name[0].toLowerCase()) {
              this.warnings.push(
                `Component '${name}' should start with uppercase in ${file}`
              );
            }
          });
        }
      });
    } catch (error) {
      this.warnings.push(
        `Could not check naming conventions: ${error.message}`
      );
    }
  }

  checkReactPatterns() {
    log('  ⚛️  Checking React patterns...', 'dim');

    try {
      const files = this.getAllTsxFiles();

      files.forEach(file => {
        const content = readFileSync(file, 'utf8');

        // Check for missing key props in lists
        const mapMatches = content.match(/\.map\([^)]*=>\s*<[^>]*>/g);
        if (mapMatches) {
          mapMatches.forEach(match => {
            if (!match.includes('key=')) {
              this.warnings.push(
                `Missing 'key' prop in map function in ${file}`
              );
            }
          });
        }

        // Check for inline styles (should use Tailwind)
        const styleMatches = content.match(/style=\{\{[^}]*\}\}/g);
        if (styleMatches && styleMatches.length > 3) {
          this.warnings.push(
            `Consider using Tailwind classes instead of inline styles in ${file}`
          );
        }
      });
    } catch (error) {
      this.warnings.push(`Could not check React patterns: ${error.message}`);
    }
  }

  getAllTsxFiles() {
    const files = [];

    const scanDir = dir => {
      try {
        const items = readdirSync(dir);
        items.forEach(item => {
          const fullPath = join(dir, item);
          const stat = statSync(fullPath);

          if (stat.isDirectory() && !item.startsWith('.')) {
            scanDir(fullPath);
          } else if (stat.isFile() && ['.ts', '.tsx'].includes(extname(item))) {
            files.push(fullPath);
          }
        });
      } catch (error) {
        // Ignore errors for directories we can't read
      }
    };

    scanDir(this.srcDir);
    return files;
  }

  fileExists(path) {
    try {
      statSync(path);
      return true;
    } catch {
      return false;
    }
  }

  reportResults() {
    log('\n📊 Issue Detection Results', 'blue');
    log('─'.repeat(50), 'dim');

    if (this.issues.length === 0 && this.warnings.length === 0) {
      log('✅ No issues detected!', 'green');
      return;
    }

    if (this.issues.length > 0) {
      log(`❌ Found ${this.issues.length} critical issues:`, 'red');
      this.issues.forEach((issue, index) => {
        log(`  ${index + 1}. ${issue.message}`, 'red');
        if (issue.files) {
          issue.files.forEach(file => log(`     📁 ${file}`, 'dim'));
        }
        if (issue.lines) {
          log(`     📍 Lines: ${issue.lines.join(', ')}`, 'dim');
        }
      });
    }

    if (this.warnings.length > 0) {
      log(`\n⚠️  Found ${this.warnings.length} warnings:`, 'yellow');
      this.warnings.forEach((warning, index) => {
        log(`  ${index + 1}. ${warning}`, 'yellow');
      });
    }

    log('─'.repeat(50), 'dim');

    if (this.issues.length > 0) {
      log('🔧 Please fix critical issues before proceeding', 'red');
      process.exit(1);
    } else {
      log('✅ No critical issues found', 'green');
    }
  }
}

// Run detection
const detector = new IssueDetector();
detector.detect();
