#!/bin/bash

# Data Visualization Test Runner Script
# Comprehensive test execution with multiple configurations

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
TEST_DIR="tests/e2e/data-visualization"
RESULTS_DIR="test-results"
CONFIG_FILE="$TEST_DIR/playwright.config.visualization.ts"

# Default values
RUN_MODE="all"
BROWSER="all"
DEVICE="desktop"
PARALLEL="true"
HEADED="false"
DEBUG="false"
REPORT="true"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    cat << EOF
Data Visualization Test Runner

Usage: $0 [OPTIONS]

OPTIONS:
    -m, --mode MODE         Test mode: all, smoke, regression, performance, accessibility, mobile (default: all)
    -b, --browser BROWSER   Browser: chromium, firefox, webkit, all (default: all)
    -d, --device DEVICE     Device type: desktop, mobile, tablet, all (default: desktop)
    -p, --parallel          Run tests in parallel (default: true)
    -s, --serial            Run tests serially
    -h, --headed            Run tests in headed mode
    -g, --debug             Enable debug mode
    -r, --report            Generate HTML report (default: true)
    --no-report             Skip HTML report generation
    --help                  Show this help message

EXAMPLES:
    $0                                          # Run all tests
    $0 -m smoke -b chromium                     # Run smoke tests on Chromium
    $0 -m performance -d all                    # Run performance tests on all devices
    $0 -m accessibility -h                      # Run accessibility tests in headed mode
    $0 -m mobile -b webkit -d mobile            # Run mobile tests on WebKit
    $0 --debug -s                               # Run in debug mode serially

TEST MODES:
    all           - Run complete test suite
    smoke         - Quick validation tests
    regression    - Core functionality tests
    performance   - Performance and load tests
    accessibility - WCAG compliance tests
    mobile        - Mobile-specific tests

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -m|--mode)
            RUN_MODE="$2"
            shift 2
            ;;
        -b|--browser)
            BROWSER="$2"
            shift 2
            ;;
        -d|--device)
            DEVICE="$2"
            shift 2
            ;;
        -p|--parallel)
            PARALLEL="true"
            shift
            ;;
        -s|--serial)
            PARALLEL="false"
            shift
            ;;
        -h|--headed)
            HEADED="true"
            shift
            ;;
        -g|--debug)
            DEBUG="true"
            HEADED="true"
            PARALLEL="false"
            shift
            ;;
        -r|--report)
            REPORT="true"
            shift
            ;;
        --no-report)
            REPORT="false"
            shift
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate inputs
case $RUN_MODE in
    all|smoke|regression|performance|accessibility|mobile) ;;
    *)
        print_error "Invalid mode: $RUN_MODE"
        exit 1
        ;;
esac

case $BROWSER in
    chromium|firefox|webkit|all) ;;
    *)
        print_error "Invalid browser: $BROWSER"
        exit 1
        ;;
esac

case $DEVICE in
    desktop|mobile|tablet|all) ;;
    *)
        print_error "Invalid device: $DEVICE"
        exit 1
        ;;
esac

# Setup
print_status "Setting up Data Visualization Test Environment..."

# Create results directory
mkdir -p "$RESULTS_DIR"

# Check if Playwright is installed
if ! command -v npx &> /dev/null; then
    print_error "npx not found. Please install Node.js and npm."
    exit 1
fi

# Check if Playwright browsers are installed
print_status "Checking Playwright browser installation..."
if ! npx playwright --version &> /dev/null; then
    print_error "Playwright not found. Installing..."
    npm install @playwright/test
    npx playwright install
fi

# Build test command
PLAYWRIGHT_CMD="npx playwright test"

# Add configuration
PLAYWRIGHT_CMD="$PLAYWRIGHT_CMD --config=$CONFIG_FILE"

# Add browser selection
if [ "$BROWSER" != "all" ]; then
    case $BROWSER in
        chromium) PLAYWRIGHT_CMD="$PLAYWRIGHT_CMD --project=chromium-desktop" ;;
        firefox) PLAYWRIGHT_CMD="$PLAYWRIGHT_CMD --project=firefox-desktop" ;;
        webkit) PLAYWRIGHT_CMD="$PLAYWRIGHT_CMD --project=webkit-desktop" ;;
    esac
fi

# Add device selection
if [ "$DEVICE" != "desktop" ]; then
    case $DEVICE in
        mobile) PLAYWRIGHT_CMD="$PLAYWRIGHT_CMD --project=mobile-chrome --project=mobile-safari" ;;
        tablet) PLAYWRIGHT_CMD="$PLAYWRIGHT_CMD --project=tablet-chrome" ;;
        all) PLAYWRIGHT_CMD="$PLAYWRIGHT_CMD --project=mobile-chrome --project=mobile-safari --project=tablet-chrome" ;;
    esac
fi

# Add test mode selection
case $RUN_MODE in
    smoke)
        PLAYWRIGHT_CMD="$PLAYWRIGHT_CMD --grep='E2E-VIZ-001|E2E-MOBILE-VIZ-001'"
        ;;
    regression)
        PLAYWRIGHT_CMD="$PLAYWRIGHT_CMD tests/e2e/data-visualization/chart-interactions.spec.ts tests/e2e/data-visualization/chart-performance.spec.ts"
        ;;
    performance)
        PLAYWRIGHT_CMD="$PLAYWRIGHT_CMD tests/e2e/data-visualization/chart-performance.spec.ts"
        ;;
    accessibility)
        PLAYWRIGHT_CMD="$PLAYWRIGHT_CMD tests/e2e/data-visualization/chart-accessibility.spec.ts"
        ;;
    mobile)
        PLAYWRIGHT_CMD="$PLAYWRIGHT_CMD tests/e2e/data-visualization/mobile-chart-interactions.spec.ts"
        ;;
    all)
        PLAYWRIGHT_CMD="$PLAYWRIGHT_CMD $TEST_DIR"
        ;;
esac

# Add execution options
if [ "$PARALLEL" = "false" ]; then
    PLAYWRIGHT_CMD="$PLAYWRIGHT_CMD --workers=1"
fi

if [ "$HEADED" = "true" ]; then
    PLAYWRIGHT_CMD="$PLAYWRIGHT_CMD --headed"
fi

if [ "$DEBUG" = "true" ]; then
    PLAYWRIGHT_CMD="$PLAYWRIGHT_CMD --debug"
fi

# Add reporting options
if [ "$REPORT" = "true" ]; then
    PLAYWRIGHT_CMD="$PLAYWRIGHT_CMD --reporter=html,json,junit"
else
    PLAYWRIGHT_CMD="$PLAYWRIGHT_CMD --reporter=list"
fi

# Display configuration
print_status "Test Configuration:"
echo "  Mode: $RUN_MODE"
echo "  Browser: $BROWSER"
echo "  Device: $DEVICE"
echo "  Parallel: $PARALLEL"
echo "  Headed: $HEADED"
echo "  Debug: $DEBUG"
echo "  Report: $REPORT"
echo ""

# Start application if needed
print_status "Starting application..."
if ! curl -s http://localhost:3000 > /dev/null 2>&1; then
    print_warning "Application not running. Starting development server..."
    npm run dev &
    APP_PID=$!
    
    # Wait for application to start
    for i in {1..30}; do
        if curl -s http://localhost:3000 > /dev/null 2>&1; then
            print_success "Application started successfully"
            break
        fi
        sleep 2
    done
    
    if ! curl -s http://localhost:3000 > /dev/null 2>&1; then
        print_error "Failed to start application"
        exit 1
    fi
else
    print_success "Application already running"
    APP_PID=""
fi

# Run tests
print_status "Running Data Visualization Tests..."
echo "Command: $PLAYWRIGHT_CMD"
echo ""

START_TIME=$(date +%s)

if eval "$PLAYWRIGHT_CMD"; then
    END_TIME=$(date +%s)
    DURATION=$((END_TIME - START_TIME))
    print_success "Tests completed successfully in ${DURATION}s"
    EXIT_CODE=0
else
    END_TIME=$(date +%s)
    DURATION=$((END_TIME - START_TIME))
    print_error "Tests failed after ${DURATION}s"
    EXIT_CODE=1
fi

# Cleanup
if [ -n "$APP_PID" ]; then
    print_status "Stopping application..."
    kill $APP_PID 2>/dev/null || true
fi

# Generate summary report
if [ "$REPORT" = "true" ] && [ -f "$RESULTS_DIR/visualization-results.json" ]; then
    print_status "Generating test summary..."
    
    # Extract key metrics from results
    TOTAL_TESTS=$(jq '.stats.total // 0' "$RESULTS_DIR/visualization-results.json" 2>/dev/null || echo "0")
    PASSED_TESTS=$(jq '.stats.passed // 0' "$RESULTS_DIR/visualization-results.json" 2>/dev/null || echo "0")
    FAILED_TESTS=$(jq '.stats.failed // 0' "$RESULTS_DIR/visualization-results.json" 2>/dev/null || echo "0")
    
    echo ""
    print_status "Test Summary:"
    echo "  Total Tests: $TOTAL_TESTS"
    echo "  Passed: $PASSED_TESTS"
    echo "  Failed: $FAILED_TESTS"
    echo "  Duration: ${DURATION}s"
    echo "  Results: $RESULTS_DIR/visualization-report/index.html"
    echo ""
fi

# Final status
if [ $EXIT_CODE -eq 0 ]; then
    print_success "Data Visualization Tests Completed Successfully! 🎉"
else
    print_error "Data Visualization Tests Failed! ❌"
fi

exit $EXIT_CODE
