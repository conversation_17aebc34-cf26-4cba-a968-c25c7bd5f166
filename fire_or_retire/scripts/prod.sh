#!/bin/bash

# Fire or Retire Calculator - Production Script
# This script manages the production environment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${PURPLE}🔥 Fire or Retire Calculator - Production Mode${NC}"
    echo -e "${CYAN}===================================================${NC}"
}

# Function to check if <PERSON><PERSON> is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
}

# Function to start production environment
start_prod() {
    print_header
    print_status "Starting production environment..."
    
    # Stop any existing containers
    docker compose down 2>/dev/null || true
    
    # Build and start production container
    print_status "Building production container..."
    docker compose up --build -d
    
    print_success "Production environment started!"
    print_status "🌐 Application URLs:"
    echo -e "  ${CYAN}Production Server:${NC} http://localhost:4173"
    echo -e "  ${CYAN}Traefik URL:${NC}       http://fire.docker.localhost"
    
    print_status "📝 Useful commands:"
    echo -e "  ${YELLOW}View logs:${NC}     docker compose logs -f"
    echo -e "  ${YELLOW}Stop prod:${NC}     docker compose down"
    echo -e "  ${YELLOW}Restart:${NC}       docker compose restart"
    echo -e "  ${YELLOW}Shell access:${NC}  docker compose exec fire-or-retire sh"
}

# Function to stop production environment
stop_prod() {
    print_status "Stopping production environment..."
    docker compose down
    print_success "Production environment stopped!"
}

# Function to show logs
show_logs() {
    print_status "Showing production logs (Ctrl+C to exit)..."
    docker compose logs -f
}

# Function to restart production environment
restart_prod() {
    print_status "Restarting production environment..."
    docker compose restart
    print_success "Production environment restarted!"
}

# Function to open shell in container
shell_access() {
    print_status "Opening shell in production container..."
    docker compose exec fire-or-retire sh
}

# Function to show status
show_status() {
    print_header
    print_status "Production Environment Status:"
    docker compose ps
    echo ""
    print_status "Container Health:"
    docker compose exec fire-or-retire wget --spider -q http://localhost:4173 && \
        print_success "Production server is healthy" || \
        print_warning "Production server is not responding"
}

# Function to deploy (rebuild and restart)
deploy() {
    print_header
    print_status "Deploying latest changes to production..."
    
    # Stop existing containers
    docker compose down
    
    # Rebuild and start
    print_status "Building and starting production container..."
    docker compose up --build -d
    
    print_success "Deployment completed!"
    show_status
}

# Main script logic
case "${1:-start}" in
    "start")
        check_docker
        start_prod
        ;;
    "stop")
        stop_prod
        ;;
    "restart")
        check_docker
        restart_prod
        ;;
    "logs")
        show_logs
        ;;
    "shell")
        shell_access
        ;;
    "status")
        show_status
        ;;
    "deploy")
        check_docker
        deploy
        ;;
    "help"|"-h"|"--help")
        print_header
        echo -e "${CYAN}Usage:${NC} $0 [command]"
        echo ""
        echo -e "${CYAN}Commands:${NC}"
        echo -e "  ${YELLOW}start${NC}    Start production environment (default)"
        echo -e "  ${YELLOW}stop${NC}     Stop production environment"
        echo -e "  ${YELLOW}restart${NC}  Restart production environment"
        echo -e "  ${YELLOW}logs${NC}     Show production logs"
        echo -e "  ${YELLOW}shell${NC}    Open shell in production container"
        echo -e "  ${YELLOW}status${NC}   Show production environment status"
        echo -e "  ${YELLOW}deploy${NC}   Deploy latest changes (rebuild and restart)"
        echo -e "  ${YELLOW}help${NC}     Show this help message"
        ;;
    *)
        print_error "Unknown command: $1"
        print_status "Use '$0 help' to see available commands"
        exit 1
        ;;
esac
