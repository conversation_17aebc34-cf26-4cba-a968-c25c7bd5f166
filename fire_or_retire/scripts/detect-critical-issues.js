#!/usr/bin/env node

/**
 * Critical Issue Detection for Swiss Budget Pro
 * Focuses only on issues that cause compilation errors
 */

import { readFileSync, existsSync } from 'fs';

const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

class CriticalIssueDetector {
  constructor() {
    this.issues = [];
    this.mainFile = 'retire.tsx';
  }

  detect() {
    log('🔍 Checking for critical compilation issues...', 'blue');
    
    this.checkMainFile();
    this.checkOrphanedJSX();
    this.checkDuplicateDefaultExports();
    
    this.reportResults();
    return this.issues.length === 0;
  }

  checkMainFile() {
    console.log('  📄 Checking main file structure...');
    
    if (!existsSync(this.mainFile)) {
      this.issues.push({
        type: 'missing-main-file',
        message: `Main file ${this.mainFile} not found`,
        severity: 'error'
      });
      return;
    }

    try {
      const content = readFileSync(this.mainFile, 'utf8');
      
      // Check for basic syntax issues
      if (!content.includes('export default')) {
        this.issues.push({
          type: 'no-default-export',
          message: `No default export found in ${this.mainFile}`,
          severity: 'error'
        });
      }
      
    } catch (error) {
      this.issues.push({
        type: 'file-read-error',
        message: `Cannot read ${this.mainFile}: ${error.message}`,
        severity: 'error'
      });
    }
  }

  checkOrphanedJSX() {
    console.log('  🏚️  Checking for orphaned JSX...');
    
    try {
      const content = readFileSync(this.mainFile, 'utf8');
      const lines = content.split('\n');
      
      let foundExport = false;
      let orphanedLines = [];
      
      lines.forEach((line, index) => {
        const trimmed = line.trim();
        
        // Check for export default
        if (trimmed.startsWith('export default')) {
          foundExport = true;
        }
        
        // After export, check for JSX or component code
        if (foundExport && (
          trimmed.includes('className=') ||
          trimmed.includes('<div') ||
          trimmed.includes('<span') ||
          trimmed.includes('onClick=') ||
          trimmed.includes('useState(') ||
          trimmed.includes('useEffect(')
        )) {
          orphanedLines.push(index + 1);
        }
      });
      
      if (orphanedLines.length > 0) {
        this.issues.push({
          type: 'orphaned-jsx',
          message: `Found ${orphanedLines.length} lines of orphaned JSX/React code after export statement`,
          lines: orphanedLines.slice(0, 5), // Show first 5 lines
          severity: 'error'
        });
      }
      
    } catch (error) {
      console.log(`    ⚠️  Could not check for orphaned JSX: ${error.message}`);
    }
  }

  checkDuplicateDefaultExports() {
    console.log('  📤 Checking for duplicate default exports...');
    
    try {
      const content = readFileSync(this.mainFile, 'utf8');
      const exportMatches = content.match(/^export default/gm) || [];
      
      if (exportMatches.length > 1) {
        this.issues.push({
          type: 'duplicate-default-exports',
          message: `Found ${exportMatches.length} default export statements`,
          severity: 'error'
        });
      }
      
      // Check for duplicate component definitions
      const componentMatches = content.match(/const SwissBudgetProWithErrorBoundary/g) || [];
      if (componentMatches.length > 1) {
        this.issues.push({
          type: 'duplicate-components',
          message: `Found ${componentMatches.length} SwissBudgetProWithErrorBoundary definitions`,
          severity: 'error'
        });
      }
      
    } catch (error) {
      console.log(`    ⚠️  Could not check exports: ${error.message}`);
    }
  }

  reportResults() {
    log('\n📊 Critical Issue Detection Results', 'blue');
    log('─'.repeat(50));
    
    if (this.issues.length === 0) {
      log('✅ No critical issues detected!', 'green');
      return true;
    }
    
    log(`❌ Found ${this.issues.length} critical issues:`, 'red');
    this.issues.forEach((issue, index) => {
      log(`  ${index + 1}. ${issue.message}`, 'red');
      if (issue.lines) {
        log(`     📍 Lines: ${issue.lines.join(', ')}`, 'yellow');
      }
    });
    
    log('─'.repeat(50));
    log('🔧 Please fix these issues before proceeding', 'red');
    
    return false;
  }
}

// Run detection
const detector = new CriticalIssueDetector();
const success = detector.detect();
process.exit(success ? 0 : 1);
