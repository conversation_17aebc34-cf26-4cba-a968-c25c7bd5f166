services:
  fire-or-retire:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: fire-or-retire-app
    restart: unless-stopped
    
    # Environment variables
    environment:
      - NODE_ENV=production
      - VITE_APP_NAME=Swiss Budget Pro
      - VITE_APP_VERSION=1.0.0
    
    # Port mapping for direct access (alternative to Traefik)
    ports:
      - "4173:4173"

    # Health check
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:4173/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # Traefik labels for routing
    labels:
      - "traefik.enable=true"
      - "traefik.docker.network=traefik_network"
      
      # HTTP router
      - "traefik.http.routers.fire-or-retire.rule=Host(`fire.localhost`)"
      - "traefik.http.routers.fire-or-retire.entrypoints=web"
      - "traefik.http.routers.fire-or-retire.service=fire-or-retire-service"
      
      # Service configuration
      - "traefik.http.services.fire-or-retire-service.loadbalancer.server.port=4173"
      - "traefik.http.services.fire-or-retire-service.loadbalancer.server.scheme=http"
      
      # Optional: HTTPS redirect (uncomment if you have HTTPS setup)
      # - "traefik.http.routers.fire-or-retire.middlewares=redirect-to-https"
      # - "traefik.http.middlewares.redirect-to-https.redirectscheme.scheme=https"
      
      # Optional: HTTPS router (uncomment if you have HTTPS setup)
      # - "traefik.http.routers.fire-or-retire-secure.rule=Host(`fire.docker.localhost`)"
      # - "traefik.http.routers.fire-or-retire-secure.entrypoints=websecure"
      # - "traefik.http.routers.fire-or-retire-secure.tls=true"
      # - "traefik.http.routers.fire-or-retire-secure.service=fire-or-retire-service"
      
      # Container metadata
      - "com.docker.compose.project=fire-or-retire"
      - "com.docker.compose.service=fire-or-retire"
    
    # Networks
    networks:
      - traefik_network
      - internal
    
    # Logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

# Networks
networks:
  traefik_network:
    external: true
    name: traefik_network
  internal:
    driver: bridge
    name: fire-or-retire-internal

# Optional: Volumes for persistent data (if needed in future)
# volumes:
#   app-data:
#     driver: local
