services:
  fire-or-retire:
    build:
      context: .
      dockerfile: Dockerfile.dev
      target: development
    container_name: fire-or-retire-dev
    restart: unless-stopped
    
    # Environment variables for development
    environment:
      - NODE_ENV=development
      - VITE_APP_NAME=Swiss Budget Pro (Dev)
      - VITE_APP_VERSION=1.0.0-dev
      - CHOKIDAR_USEPOLLING=true  # For file watching in Docker
      - WATCHPACK_POLLING=true    # For webpack file watching
    
    # Volume mapping for live development
    volumes:
      - .:/app                    # Map entire project directory
      - /app/node_modules         # Exclude node_modules (use container's version)
      - /app/dist                 # Exclude dist (generated files)
    
    # Port mapping for development server
    ports:
      - "5173:5173"  # Vite dev server
      - "4173:4173"  # Preview server (if needed)
    
    # Working directory
    working_dir: /app
    
    # Command to run development server
    command: npm run dev -- --host 0.0.0.0 --port 5173
    
    # Health check for dev server
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:5173/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # Traefik labels for development routing
    labels:
      - "traefik.enable=true"
      - "traefik.docker.network=traefik_network"
      
      # HTTP router for development
      - "traefik.http.routers.fire-or-retire-dev.rule=Host(`fire-dev.docker.localhost`)"
      - "traefik.http.routers.fire-or-retire-dev.entrypoints=web"
      - "traefik.http.routers.fire-or-retire-dev.service=fire-or-retire-dev-service"
      
      # Service configuration for dev server
      - "traefik.http.services.fire-or-retire-dev-service.loadbalancer.server.port=5173"
      - "traefik.http.services.fire-or-retire-dev-service.loadbalancer.server.scheme=http"
      
      # Container metadata
      - "com.docker.compose.project=fire-or-retire-dev"
      - "com.docker.compose.service=fire-or-retire-dev"
    
    # Networks
    networks:
      - traefik_network
      - internal
    
    # Logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

# Networks
networks:
  traefik_network:
    external: true
    name: traefik_network
  internal:
    driver: bridge
    name: fire-or-retire-internal

# Development volumes
volumes:
  node_modules:
    driver: local
