# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
dist/
build/
.next/
out/

# Development files
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Development container
.devcontainer/

# Testing
coverage/
.nyc_output/
test-results/
playwright-report/
.tsbuildinfo

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Documentation
docs/
*.md
README*
CHANGELOG*
LICENSE*

# Archived and backup files
archived-code/
*.backup
*.bak

# Temporary files
tmp/
temp/
.tmp/

# Cache directories
.cache/
.parcel-cache/
.eslintcache

# Debug files
debug-*.png
debug-*.html
