<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Swiss Budget Pro Debug</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .error-details {
            background: #2d1b1b;
            border: 1px solid #ff6b6b;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .success {
            background: #1b2d1b;
            border: 1px solid #6bff6b;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        pre {
            background: #333;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        button {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a9e;
        }
        .diagnostic-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #444;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <h1>🇨🇭 Swiss Budget Pro - Debug Console</h1>
    
    <div class="diagnostic-section">
        <h2>🔍 Application Diagnostics</h2>
        <button onclick="runDiagnostics()">Run Full Diagnostics</button>
        <button onclick="clearAllData()">Clear All Data</button>
        <button onclick="checkLocalStorage()">Check LocalStorage</button>
        <button onclick="testCalculations()">Test Calculations</button>
        <div id="diagnostics-output"></div>
    </div>

    <div class="diagnostic-section">
        <h2>📊 Error Analysis</h2>
        <button onclick="checkErrors()">Check Recent Errors</button>
        <button onclick="simulateError()">Simulate Error (Test)</button>
        <div id="error-output"></div>
    </div>

    <div class="diagnostic-section">
        <h2>🛠️ Quick Fixes</h2>
        <button onclick="resetToDefaults()">Reset to Defaults</button>
        <button onclick="repairData()">Repair Corrupted Data</button>
        <button onclick="validateData()">Validate Current Data</button>
        <div id="fixes-output"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error-details' : type === 'success' ? 'success' : '';
            return `<div class="${className}"><strong>[${timestamp}]</strong> ${message}</div>`;
        }

        function runDiagnostics() {
            const output = document.getElementById('diagnostics-output');
            let results = '<h3>Diagnostic Results:</h3>';
            
            try {
                // Check browser compatibility
                results += log('✅ Browser: ' + navigator.userAgent.split(' ').pop(), 'success');
                
                // Check localStorage
                const storageTest = 'test';
                localStorage.setItem(storageTest, 'test');
                localStorage.removeItem(storageTest);
                results += log('✅ LocalStorage: Working', 'success');
                
                // Check for Swiss Budget Pro data
                const keys = Object.keys(localStorage).filter(key => key.startsWith('swissBudgetPro_'));
                results += log(`📊 Found ${keys.length} Swiss Budget Pro data entries`, 'info');
                
                // Check for errors
                const errorKeys = keys.filter(key => key.includes('error_'));
                if (errorKeys.length > 0) {
                    results += log(`⚠️ Found ${errorKeys.length} stored errors`, 'error');
                    errorKeys.forEach(key => {
                        try {
                            const errorData = JSON.parse(localStorage.getItem(key));
                            results += log(`Error: ${errorData.error?.message || 'Unknown error'}`, 'error');
                        } catch (e) {
                            results += log(`Corrupted error data in ${key}`, 'error');
                        }
                    });
                } else {
                    results += log('✅ No stored errors found', 'success');
                }
                
                // Check data integrity
                const mainDataKey = 'swissBudgetPro_default';
                const mainData = localStorage.getItem(mainDataKey);
                if (mainData) {
                    try {
                        const parsed = JSON.parse(mainData);
                        results += log('✅ Main data structure is valid JSON', 'success');
                        results += log(`Data timestamp: ${parsed.timestamp || 'Not set'}`, 'info');
                    } catch (e) {
                        results += log('❌ Main data is corrupted JSON', 'error');
                    }
                } else {
                    results += log('ℹ️ No main data found (fresh install)', 'info');
                }
                
            } catch (error) {
                results += log('❌ Diagnostic failed: ' + error.message, 'error');
            }
            
            output.innerHTML = results;
        }

        function clearAllData() {
            const output = document.getElementById('diagnostics-output');
            try {
                const keys = Object.keys(localStorage).filter(key => key.startsWith('swissBudgetPro_'));
                keys.forEach(key => localStorage.removeItem(key));
                output.innerHTML = log(`✅ Cleared ${keys.length} data entries`, 'success');
            } catch (error) {
                output.innerHTML = log('❌ Failed to clear data: ' + error.message, 'error');
            }
        }

        function checkLocalStorage() {
            const output = document.getElementById('diagnostics-output');
            let results = '<h3>LocalStorage Contents:</h3>';
            
            try {
                const keys = Object.keys(localStorage).filter(key => key.startsWith('swissBudgetPro_'));
                
                if (keys.length === 0) {
                    results += log('No Swiss Budget Pro data found', 'info');
                } else {
                    keys.forEach(key => {
                        const value = localStorage.getItem(key);
                        const size = new Blob([value]).size;
                        results += log(`${key}: ${size} bytes`, 'info');
                        
                        if (key.includes('error_')) {
                            try {
                                const errorData = JSON.parse(value);
                                results += log(`  Error: ${errorData.error?.message}`, 'error');
                            } catch (e) {
                                results += log(`  Corrupted error data`, 'error');
                            }
                        }
                    });
                }
                
                // Check total storage usage
                const totalSize = keys.reduce((total, key) => {
                    return total + new Blob([localStorage.getItem(key)]).size;
                }, 0);
                results += log(`Total storage used: ${(totalSize / 1024).toFixed(2)} KB`, 'info');
                
            } catch (error) {
                results += log('❌ Failed to check localStorage: ' + error.message, 'error');
            }
            
            output.innerHTML = results;
        }

        function testCalculations() {
            const output = document.getElementById('diagnostics-output');
            let results = '<h3>Calculation Tests:</h3>';
            
            try {
                // Test basic math
                const testResult = 100 * 1.07;
                results += log(`✅ Basic math: 100 * 1.07 = ${testResult}`, 'success');
                
                // Test JSON parsing
                const testData = { income: 5000, expenses: 3000 };
                const jsonString = JSON.stringify(testData);
                const parsed = JSON.parse(jsonString);
                results += log(`✅ JSON operations working`, 'success');
                
                // Test date operations
                const now = new Date();
                const future = new Date(now.getTime() + 365 * 24 * 60 * 60 * 1000);
                results += log(`✅ Date operations: ${now.getFullYear()} to ${future.getFullYear()}`, 'success');
                
                // Test array operations
                const testArray = [1, 2, 3, 4, 5];
                const sum = testArray.reduce((a, b) => a + b, 0);
                results += log(`✅ Array operations: sum of [1,2,3,4,5] = ${sum}`, 'success');
                
            } catch (error) {
                results += log('❌ Calculation test failed: ' + error.message, 'error');
            }
            
            output.innerHTML = results;
        }

        function checkErrors() {
            const output = document.getElementById('error-output');
            let results = '<h3>Recent Errors:</h3>';
            
            try {
                const errorKeys = Object.keys(localStorage)
                    .filter(key => key.startsWith('swissBudgetPro_error_'))
                    .sort()
                    .reverse()
                    .slice(0, 5); // Last 5 errors
                
                if (errorKeys.length === 0) {
                    results += log('✅ No recent errors found', 'success');
                } else {
                    errorKeys.forEach(key => {
                        try {
                            const errorData = JSON.parse(localStorage.getItem(key));
                            results += log(`<strong>Error ID:</strong> ${errorData.errorId}`, 'error');
                            results += log(`<strong>Time:</strong> ${errorData.timestamp}`, 'error');
                            results += log(`<strong>Message:</strong> ${errorData.error?.message}`, 'error');
                            results += log(`<strong>Component:</strong> ${errorData.errorInfo?.componentStack?.split('\n')[1]?.trim()}`, 'error');
                            results += '<hr>';
                        } catch (e) {
                            results += log(`Corrupted error data in ${key}`, 'error');
                        }
                    });
                }
            } catch (error) {
                results += log('❌ Failed to check errors: ' + error.message, 'error');
            }
            
            output.innerHTML = results;
        }

        function simulateError() {
            const output = document.getElementById('error-output');
            try {
                // This will throw an error to test error handling
                throw new Error('Test error for debugging purposes');
            } catch (error) {
                output.innerHTML = log('✅ Error simulation successful: ' + error.message, 'success');
            }
        }

        function resetToDefaults() {
            const output = document.getElementById('fixes-output');
            try {
                // Clear all Swiss Budget Pro data
                const keys = Object.keys(localStorage).filter(key => key.startsWith('swissBudgetPro_'));
                keys.forEach(key => localStorage.removeItem(key));
                
                // Set default values
                const defaultData = {
                    timestamp: new Date().toISOString(),
                    version: '1.0',
                    currentAge: '30',
                    retirementAge: '65',
                    selectedCanton: 'ZH',
                    civilStatus: 'single',
                    primaryIncome: '5000',
                    totalExpenses: '3000'
                };
                
                localStorage.setItem('swissBudgetPro_default', JSON.stringify(defaultData));
                output.innerHTML = log('✅ Reset to default values completed', 'success');
            } catch (error) {
                output.innerHTML = log('❌ Reset failed: ' + error.message, 'error');
            }
        }

        function repairData() {
            const output = document.getElementById('fixes-output');
            let results = '<h3>Data Repair Results:</h3>';
            
            try {
                const mainDataKey = 'swissBudgetPro_default';
                const mainData = localStorage.getItem(mainDataKey);
                
                if (!mainData) {
                    results += log('No data to repair', 'info');
                } else {
                    try {
                        const parsed = JSON.parse(mainData);
                        
                        // Check and fix common issues
                        let fixed = false;
                        
                        // Ensure required fields exist
                        const requiredFields = ['currentAge', 'retirementAge', 'selectedCanton', 'civilStatus'];
                        requiredFields.forEach(field => {
                            if (!parsed[field]) {
                                parsed[field] = field === 'currentAge' ? '30' : 
                                               field === 'retirementAge' ? '65' :
                                               field === 'selectedCanton' ? 'ZH' : 'single';
                                fixed = true;
                            }
                        });
                        
                        // Ensure numeric fields are valid
                        const numericFields = ['primaryIncome', 'totalExpenses', 'currentSavings'];
                        numericFields.forEach(field => {
                            if (parsed[field] && isNaN(parseFloat(parsed[field]))) {
                                parsed[field] = '0';
                                fixed = true;
                            }
                        });
                        
                        if (fixed) {
                            localStorage.setItem(mainDataKey, JSON.stringify(parsed));
                            results += log('✅ Data repaired successfully', 'success');
                        } else {
                            results += log('✅ No repairs needed', 'success');
                        }
                        
                    } catch (e) {
                        results += log('❌ Data is corrupted beyond repair', 'error');
                        localStorage.removeItem(mainDataKey);
                        results += log('🗑️ Corrupted data removed', 'info');
                    }
                }
            } catch (error) {
                results += log('❌ Repair failed: ' + error.message, 'error');
            }
            
            output.innerHTML = results;
        }

        function validateData() {
            const output = document.getElementById('fixes-output');
            let results = '<h3>Data Validation:</h3>';
            
            try {
                const mainDataKey = 'swissBudgetPro_default';
                const mainData = localStorage.getItem(mainDataKey);
                
                if (!mainData) {
                    results += log('ℹ️ No data to validate', 'info');
                } else {
                    const parsed = JSON.parse(mainData);
                    let isValid = true;
                    
                    // Validate required fields
                    const requiredFields = ['currentAge', 'retirementAge', 'selectedCanton'];
                    requiredFields.forEach(field => {
                        if (!parsed[field]) {
                            results += log(`❌ Missing required field: ${field}`, 'error');
                            isValid = false;
                        } else {
                            results += log(`✅ ${field}: ${parsed[field]}`, 'success');
                        }
                    });
                    
                    // Validate numeric ranges
                    if (parsed.currentAge && (parseInt(parsed.currentAge) < 18 || parseInt(parsed.currentAge) > 100)) {
                        results += log(`❌ Invalid age: ${parsed.currentAge}`, 'error');
                        isValid = false;
                    }
                    
                    if (parsed.retirementAge && (parseInt(parsed.retirementAge) < parseInt(parsed.currentAge))) {
                        results += log(`❌ Retirement age before current age`, 'error');
                        isValid = false;
                    }
                    
                    if (isValid) {
                        results += log('✅ All data validation passed', 'success');
                    } else {
                        results += log('❌ Data validation failed - consider repairing', 'error');
                    }
                }
            } catch (error) {
                results += log('❌ Validation failed: ' + error.message, 'error');
            }
            
            output.innerHTML = results;
        }

        // Auto-run diagnostics on page load
        window.onload = function() {
            runDiagnostics();
        };
    </script>
</body>
</html>
