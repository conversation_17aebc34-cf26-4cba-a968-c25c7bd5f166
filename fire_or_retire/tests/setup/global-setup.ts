/**
 * Global Setup for Swiss Budget Pro E2E Tests
 * Initializes test environment and prepares Swiss-specific test data
 */

import { chromium, FullConfig } from '@playwright/test';
import fs from 'fs';
import path from 'path';

async function globalSetup(config: FullConfig) {
  console.log('🇨🇭 Setting up Swiss Budget Pro E2E Test Environment...');
  
  // Create test results directories
  const testResultsDir = 'test-results';
  const directories = [
    testResultsDir,
    `${testResultsDir}/screenshots`,
    `${testResultsDir}/videos`,
    `${testResultsDir}/traces`,
    `${testResultsDir}/reports`,
  ];

  directories.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      console.log(`📁 Created directory: ${dir}`);
    }
  });

  // Initialize Swiss test data
  await initializeSwissTestData();
  
  // Verify application is accessible
  await verifyApplicationAccess(config);
  
  // Setup Swiss financial calculation test data
  await setupSwissFinancialData();
  
  console.log('✅ Swiss Budget Pro E2E Test Environment Ready');
}

/**
 * Initialize Swiss-specific test data
 */
async function initializeSwissTestData() {
  console.log('📊 Initializing Swiss test data...');
  
  const swissTestData = {
    cantons: [
      { code: 'ZH', name: 'Zurich', taxRate: 0.12 },
      { code: 'GE', name: 'Geneva', taxRate: 0.15 },
      { code: 'VD', name: 'Vaud', taxRate: 0.14 },
      { code: 'BE', name: 'Bern', taxRate: 0.13 },
      { code: 'ZG', name: 'Zug', taxRate: 0.08 },
      { code: 'BS', name: 'Basel-Stadt', taxRate: 0.16 },
      { code: 'BL', name: 'Basel-Landschaft', taxRate: 0.11 },
      { code: 'AG', name: 'Aargau', taxRate: 0.10 },
    ],
    
    userProfiles: [
      {
        id: 'young-professional',
        age: 28,
        income: 85000,
        expenses: 60000,
        savings: 25000,
        canton: 'ZH',
        goals: 'FIRE by 50',
      },
      {
        id: 'mid-career-family',
        age: 42,
        income: 180000,
        expenses: 120000,
        savings: 350000,
        canton: 'GE',
        goals: 'Traditional retirement',
        married: true,
        children: 2,
      },
      {
        id: 'high-earner',
        age: 35,
        income: 250000,
        expenses: 100000,
        savings: 800000,
        canton: 'ZG',
        goals: 'Early retirement optimization',
      },
    ],
    
    healthcareProfiles: [
      {
        id: 'low-risk',
        age: 30,
        healthStatus: 'excellent',
        annualCosts: 1200,
        recommendedDeductible: 2500,
      },
      {
        id: 'medium-risk',
        age: 45,
        healthStatus: 'good',
        annualCosts: 2800,
        recommendedDeductible: 1500,
      },
      {
        id: 'high-risk',
        age: 60,
        healthStatus: 'fair',
        annualCosts: 4500,
        recommendedDeductible: 300,
      },
    ],
    
    timestamp: new Date().toISOString(),
  };

  // Save test data to file
  const testDataPath = path.join('tests', 'fixtures', 'swiss-test-data.json');
  fs.mkdirSync(path.dirname(testDataPath), { recursive: true });
  fs.writeFileSync(testDataPath, JSON.stringify(swissTestData, null, 2));
  
  console.log(`✅ Swiss test data saved to ${testDataPath}`);
}

/**
 * Verify that the application is accessible
 */
async function verifyApplicationAccess(config: FullConfig) {
  console.log('🔍 Verifying application access...');
  
  const baseURL = config.projects[0].use.baseURL || 'http://localhost:5173';
  
  try {
    const browser = await chromium.launch();
    const context = await browser.newContext();
    const page = await context.newPage();
    
    // Navigate to the application
    await page.goto(baseURL, { waitUntil: 'networkidle' });
    
    // Check if the application loads
    const title = await page.title();
    console.log(`📄 Application title: ${title}`);
    
    // Check for Swiss Budget Pro specific elements
    const hasSwissElements = await page.locator('text=Swiss').count() > 0;
    if (hasSwissElements) {
      console.log('✅ Swiss Budget Pro elements detected');
    } else {
      console.log('⚠️  Swiss Budget Pro elements not found - may be loading');
    }
    
    await browser.close();
    console.log('✅ Application access verified');
    
  } catch (error) {
    console.error('❌ Failed to verify application access:', error);
    throw new Error(`Application not accessible at ${baseURL}`);
  }
}

/**
 * Setup Swiss financial calculation test data
 */
async function setupSwissFinancialData() {
  console.log('💰 Setting up Swiss financial calculation data...');
  
  const financialData = {
    taxRates: {
      federal: {
        brackets: [
          { min: 0, max: 14500, rate: 0 },
          { min: 14500, max: 31600, rate: 0.077 },
          { min: 31600, max: 41400, rate: 0.088 },
          { min: 41400, max: 55200, rate: 0.11 },
          { min: 55200, max: 72500, rate: 0.132 },
          { min: 72500, max: 78100, rate: 0.154 },
          { min: 78100, max: Infinity, rate: 0.165 },
        ],
      },
      cantonal: {
        ZH: 0.12,
        GE: 0.15,
        VD: 0.14,
        BE: 0.13,
        ZG: 0.08,
        BS: 0.16,
        BL: 0.11,
        AG: 0.10,
      },
    },
    
    pillar3a: {
      maxContribution2024: 7056,
      maxContributionSelfEmployed2024: 35280,
      taxDeductible: true,
    },
    
    healthcareDeductibles: [300, 500, 1000, 1500, 2000, 2500],
    
    fireCalculations: {
      withdrawalRate: 0.04,
      safeWithdrawalRate: 0.035,
      conservativeWithdrawalRate: 0.03,
    },
    
    marketAssumptions: {
      stockReturn: 0.07,
      bondReturn: 0.03,
      inflation: 0.02,
      volatility: 0.15,
    },
  };

  // Save financial data
  const financialDataPath = path.join('tests', 'fixtures', 'swiss-financial-data.json');
  fs.writeFileSync(financialDataPath, JSON.stringify(financialData, null, 2));
  
  console.log(`✅ Swiss financial data saved to ${financialDataPath}`);
}

export default globalSetup;
