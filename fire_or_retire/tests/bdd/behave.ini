[behave]
# Default output format
default_format = pretty

# Default tags to exclude work-in-progress tests
default_tags = -@wip -@skip

# JUnit XML output for CI/CD integration
junit = true
junit_directory = reports/junit

# Logging configuration
logging_level = INFO
log_capture = false
stdout_capture = false
stderr_capture = false

# Show skipped tests
show_skipped = true

# Show multiline text and tables
show_multiline = true

# Summary output
summary = true

# Stop on first failure (useful for debugging)
# stop = true

# Dry run to validate scenarios without execution
# dry_run = true

# User data directory for custom configurations
userdata_defines = 
    browser=chromium
    headless=false
    base_url=http://localhost:5173
    timeout=30
    swiss_locale=de-CH
    test_env=local

# Format options for different output types
format = 
    pretty
    json:reports/bdd-results.json
    html:reports/bdd-report.html
    junit:reports/junit/bdd-junit.xml

# Include source code in failure reports
include_source = true

# Language for Gherkin keywords (Swiss German support)
lang = en

# Paths configuration
paths = features/

# Step definitions search paths
steps_dir = steps/

# Tags for different test categories
# Usage: behave --tags=@critical
# Available tags:
# @critical - Critical business flows
# @swiss - Swiss-specific features  
# @fire - FIRE calculation tests
# @tax - Tax optimization tests
# @healthcare - Healthcare optimization tests
# @mobile - Mobile responsiveness tests
# @accessibility - Accessibility compliance tests
# @performance - Performance tests
# @ux - User experience tests
# @regression - Regression test suite
# @smoke - Smoke tests for quick validation
# @integration - Integration tests
# @edge-case - Edge case scenarios
# @wip - Work in progress (excluded by default)
# @skip - Skip these tests (excluded by default)
