# Swiss Budget Pro - Behavior Driven Development (BDD) Testing

A comprehensive BDD testing framework using Behave for Swiss Budget Pro, focusing on business-readable scenarios and Swiss-specific financial planning behaviors.

## 🎯 Overview

This BDD testing suite provides:
- **Business-readable scenarios** in Gherkin syntax
- **Swiss financial planning behaviors** validation
- **User story-driven testing** approach
- **Stakeholder-friendly test reports**
- **Integration with <PERSON>wright** for browser automation
- **Swiss regulatory compliance** verification

## 📁 Structure

```
tests/bdd/
├── features/                    # Gherkin feature files
│   ├── fire_calculation/
│   │   ├── basic_fire.feature
│   │   ├── advanced_fire.feature
│   │   └── edge_cases.feature
│   ├── swiss_tax/
│   │   ├── canton_comparison.feature
│   │   ├── pillar3a_optimization.feature
│   │   └── tax_scenarios.feature
│   ├── healthcare/
│   │   ├── deductible_optimization.feature
│   │   ├── premium_comparison.feature
│   │   └── family_planning.feature
│   ├── user_experience/
│   │   ├── onboarding.feature
│   │   ├── data_management.feature
│   │   └── accessibility.feature
│   └── mobile/
│       ├── responsive_design.feature
│       └── touch_interactions.feature
├── steps/                       # Step definitions
│   ├── fire_calculation_steps.py
│   ├── swiss_tax_steps.py
│   ├── healthcare_steps.py
│   ├── user_interface_steps.py
│   └── common_steps.py
├── pages/                       # Page objects for BDD
│   ├── base_page.py
│   ├── dashboard_page.py
│   ├── fire_calculator_page.py
│   ├── tax_optimizer_page.py
│   └── healthcare_optimizer_page.py
├── fixtures/                    # Test data
│   ├── swiss_personas.json
│   ├── financial_scenarios.json
│   └── test_data.json
├── utils/                       # Utilities
│   ├── browser_manager.py
│   ├── swiss_calculations.py
│   ├── data_helpers.py
│   └── reporting.py
├── environment.py               # Behave configuration
├── behave.ini                   # Behave settings
└── requirements.txt             # Python dependencies
```

## 🚀 Quick Start

### Installation

```bash
# Install Python dependencies
pip install -r tests/bdd/requirements.txt

# Install Playwright for Python
playwright install
```

### Running BDD Tests

```bash
# Run all BDD tests
behave tests/bdd/features/

# Run specific feature
behave tests/bdd/features/fire_calculation/

# Run with specific tags
behave tests/bdd/features/ --tags=@critical

# Run with HTML report
behave tests/bdd/features/ --format=html --outfile=reports/bdd-report.html

# Run with JSON report for CI/CD
behave tests/bdd/features/ --format=json --outfile=reports/bdd-results.json
```

## 🎭 Test Categories

### Critical Financial Flows (@critical)
- FIRE calculation scenarios
- Swiss tax optimization
- Healthcare cost optimization
- Multi-canton comparisons

### User Experience (@ux)
- Onboarding workflows
- Data input and validation
- Error handling and recovery
- Accessibility compliance

### Swiss Compliance (@swiss)
- Regulatory compliance
- Canton-specific features
- Swiss financial regulations
- Localization testing

### Mobile Experience (@mobile)
- Responsive design
- Touch interactions
- Mobile performance
- Cross-device compatibility

## 📝 Writing BDD Tests

### Feature File Example

```gherkin
Feature: Swiss FIRE Calculation
  As a Swiss resident planning for financial independence
  I want to calculate my FIRE timeline
  So that I can plan my early retirement strategy

  Background:
    Given I am on the Swiss Budget Pro homepage
    And the application has loaded successfully

  @critical @fire
  Scenario: Young professional FIRE calculation
    Given I am a 28-year-old professional in Zurich
    When I enter my monthly income of 8500 CHF
    And I enter my monthly expenses of 5500 CHF
    And I enter my current savings of 50000 CHF
    And I select "Zurich" as my canton
    And I click the calculate button
    Then I should see my FIRE timeline is approximately 18 years
    And I should see my FIRE number is approximately 1650000 CHF
    And I should see my savings rate is approximately 35%

  @critical @fire @edge-case
  Scenario: High earner in low-tax canton
    Given I am a 35-year-old high earner in Zug
    When I enter my monthly income of 20000 CHF
    And I enter my monthly expenses of 8000 CHF
    And I enter my current savings of 500000 CHF
    And I select "Zug" as my canton
    And I click the calculate button
    Then I should see my FIRE timeline is less than 10 years
    And I should see tax optimization recommendations
    And I should see Pillar 3a optimization suggestions
```

### Step Definition Example

```python
from behave import given, when, then
from pages.fire_calculator_page import FireCalculatorPage

@given('I am a {age:d}-year-old {profession} in {canton}')
def step_user_profile(context, age, profession, canton):
    context.user_profile = {
        'age': age,
        'profession': profession,
        'canton': canton
    }

@when('I enter my monthly income of {amount:d} CHF')
def step_enter_income(context, amount):
    fire_page = FireCalculatorPage(context.browser)
    fire_page.enter_monthly_income(amount)

@then('I should see my FIRE timeline is approximately {years:d} years')
def step_verify_fire_timeline(context, years):
    fire_page = FireCalculatorPage(context.browser)
    actual_years = fire_page.get_fire_years()
    assert abs(actual_years - years) <= 1, f"Expected ~{years} years, got {actual_years}"
```

## 🎯 Swiss-Specific Scenarios

### Canton Tax Comparison

```gherkin
Feature: Swiss Canton Tax Comparison
  As a Swiss resident considering relocation
  I want to compare tax implications across cantons
  So that I can optimize my tax burden

  @swiss @tax @comparison
  Scenario Outline: Tax comparison across major cantons
    Given I have an annual income of <income> CHF
    When I compare taxes in <canton1> and <canton2>
    Then I should see the tax difference
    And I should see relocation recommendations if beneficial

    Examples:
      | income | canton1 | canton2 |
      | 120000 | Zurich  | Zug     |
      | 150000 | Geneva  | Vaud    |
      | 200000 | Basel   | Aargau  |
```

### Healthcare Optimization

```gherkin
Feature: Swiss Healthcare Deductible Optimization
  As a Swiss resident with health insurance
  I want to optimize my deductible choice
  So that I can minimize my total healthcare costs

  @swiss @healthcare @optimization
  Scenario: Low-risk profile deductible optimization
    Given I am a healthy 30-year-old with minimal medical needs
    When I analyze deductible options from 300 to 2500 CHF
    Then I should see 2500 CHF deductible recommended
    And I should see potential annual savings
    And I should see risk assessment warnings
```

## 📊 Reporting and Analytics

### HTML Reports
- Business-readable test results
- Scenario pass/fail status
- Step-by-step execution details
- Screenshots on failures

### JSON Reports
- Machine-readable results for CI/CD
- Integration with test dashboards
- Trend analysis over time
- Performance metrics

### Swiss-Specific Reports
- Financial calculation accuracy
- Regulatory compliance status
- Canton-specific test coverage
- Localization validation

## 🔧 Configuration

### behave.ini
```ini
[behave]
default_format = pretty
default_tags = -@wip
junit = true
junit_directory = reports/junit
logging_level = INFO
log_capture = false
stdout_capture = false
stderr_capture = false
```

### environment.py
```python
from selenium import webdriver
from playwright.sync_api import sync_playwright
import json

def before_all(context):
    # Load test data
    with open('fixtures/swiss_personas.json') as f:
        context.personas = json.load(f)
    
    # Initialize browser
    context.playwright = sync_playwright().start()
    context.browser = context.playwright.chromium.launch(headless=False)
    context.page = context.browser.new_page()

def after_all(context):
    context.browser.close()
    context.playwright.stop()
```

## 🎭 Test Data Management

### Swiss Personas
```json
{
  "young_professional": {
    "age": 28,
    "income": 85000,
    "expenses": 60000,
    "savings": 25000,
    "canton": "ZH",
    "risk_tolerance": "high"
  },
  "family_zurich": {
    "age": 42,
    "income": 180000,
    "expenses": 120000,
    "savings": 350000,
    "canton": "ZH",
    "married": true,
    "children": 2
  }
}
```

## 🚀 Integration with CI/CD

### GitHub Actions
```yaml
name: BDD Tests
on: [push, pull_request]
jobs:
  bdd-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-python@v4
        with:
          python-version: '3.11'
      - run: pip install -r tests/bdd/requirements.txt
      - run: playwright install
      - run: behave tests/bdd/features/ --format=json --outfile=bdd-results.json
```

## 📈 Benefits of BDD Approach

### Business Value
- **Stakeholder Communication**: Tests written in business language
- **Requirements Validation**: Scenarios validate business requirements
- **Documentation**: Living documentation of system behavior
- **Collaboration**: Bridge between business and technical teams

### Technical Benefits
- **Behavior Focus**: Tests focus on user behavior, not implementation
- **Reusable Steps**: Step definitions can be reused across scenarios
- **Data-Driven**: Easy to test multiple scenarios with examples
- **Maintainable**: Clear separation of test logic and test data

### Swiss-Specific Benefits
- **Regulatory Compliance**: Scenarios validate Swiss financial regulations
- **Canton Coverage**: Systematic testing across all Swiss cantons
- **Localization**: Multi-language scenario validation
- **Financial Accuracy**: Business-readable validation of complex calculations

---

**Swiss Budget Pro BDD Testing Framework v1.0.0**  
*Ensuring reliable Swiss financial planning through behavior-driven testing*
