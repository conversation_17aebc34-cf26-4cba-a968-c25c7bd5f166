"""
Swiss Financial Calculations for BDD Test Validation
Provides accurate Swiss financial calculations for test verification
"""

import logging
from decimal import Decimal, ROUND_HALF_UP
from typing import Dict, List, Optional, Tuple
import json
from pathlib import Path

logger = logging.getLogger(__name__)

class SwissCalculations:
    """
    Swiss-specific financial calculations for test validation
    """
    
    def __init__(self):
        self.load_swiss_data()
    
    def load_swiss_data(self):
        """Load Swiss financial data for calculations"""
        try:
            # Load Swiss financial data
            data_path = Path(__file__).parent.parent / 'fixtures' / 'test_data.json'
            if data_path.exists():
                with open(data_path, 'r', encoding='utf-8') as f:
                    self.swiss_data = json.load(f)
            else:
                # Fallback data if file doesn't exist
                self.swiss_data = self._get_default_swiss_data()
            
            logger.info("✅ Swiss financial data loaded")
            
        except Exception as e:
            logger.warning(f"⚠️ Failed to load Swiss data, using defaults: {e}")
            self.swiss_data = self._get_default_swiss_data()
    
    def _get_default_swiss_data(self) -> Dict:
        """Default Swiss financial data"""
        return {
            "tax_rates": {
                "federal": {
                    "brackets": [
                        {"min": 0, "max": 14500, "rate": 0},
                        {"min": 14500, "max": 31600, "rate": 0.077},
                        {"min": 31600, "max": 41400, "rate": 0.088},
                        {"min": 41400, "max": 55200, "rate": 0.11},
                        {"min": 55200, "max": 72500, "rate": 0.132},
                        {"min": 72500, "max": 78100, "rate": 0.154},
                        {"min": 78100, "max": *********, "rate": 0.165}
                    ]
                },
                "cantonal": {
                    "ZH": 0.12, "GE": 0.15, "VD": 0.14, "BE": 0.13,
                    "ZG": 0.08, "BS": 0.16, "BL": 0.11, "AG": 0.10
                }
            },
            "social_insurance": {
                "ahv": {"rate": 0.0525, "max_income": 88200},
                "alv": {"rate": 0.011, "max_income": 148200}
            },
            "pillar3a": {
                "max_contribution_2024": 7056,
                "max_contribution_self_employed_2024": 35280
            },
            "fire_calculations": {
                "withdrawal_rate": 0.04,
                "safe_withdrawal_rate": 0.035,
                "conservative_withdrawal_rate": 0.03
            },
            "healthcare_deductibles": [300, 500, 1000, 1500, 2000, 2500]
        }
    
    def calculate_fire_timeline(self, 
                               annual_income: float, 
                               annual_expenses: float, 
                               current_savings: float,
                               canton: str = "ZH",
                               withdrawal_rate: float = 0.04,
                               investment_return: float = 0.07) -> Dict:
        """
        Calculate FIRE timeline with Swiss tax considerations
        """
        try:
            # Calculate net income after taxes
            net_income = self.calculate_net_income(annual_income, canton)
            
            # Calculate annual savings
            annual_savings = net_income - annual_expenses
            
            if annual_savings <= 0:
                return {
                    "fire_possible": False,
                    "fire_years": None,
                    "fire_number": None,
                    "savings_rate": 0,
                    "monthly_savings": 0,
                    "error": "Expenses exceed net income - FIRE not possible"
                }
            
            # Calculate FIRE number (25x annual expenses for 4% rule)
            fire_number = annual_expenses / withdrawal_rate
            
            # Calculate years to FIRE with compound interest
            if annual_savings > 0:
                # Using compound interest formula: FV = PV(1+r)^n + PMT[((1+r)^n - 1)/r]
                # Solving for n when FV = fire_number
                if investment_return > 0:
                    # Iterative calculation for years to FIRE
                    years = 0
                    current_value = current_savings
                    
                    while current_value < fire_number and years < 100:  # Max 100 years
                        current_value = current_value * (1 + investment_return) + annual_savings
                        years += 1
                    
                    fire_years = years if current_value >= fire_number else None
                else:
                    # Simple calculation without investment returns
                    fire_years = (fire_number - current_savings) / annual_savings
            else:
                fire_years = None
            
            # Calculate savings rate
            savings_rate = annual_savings / net_income if net_income > 0 else 0
            
            result = {
                "fire_possible": fire_years is not None and fire_years > 0,
                "fire_years": round(fire_years, 1) if fire_years else None,
                "fire_number": round(fire_number, 0),
                "savings_rate": round(savings_rate * 100, 1),  # As percentage
                "monthly_savings": round(annual_savings / 12, 0),
                "net_income": round(net_income, 0),
                "annual_savings": round(annual_savings, 0),
                "withdrawal_rate": withdrawal_rate * 100,  # As percentage
                "canton": canton
            }
            
            logger.info(f"💰 FIRE calculation completed: {result}")
            return result
            
        except Exception as e:
            logger.error(f"❌ FIRE calculation failed: {e}")
            return {"error": str(e)}
    
    def calculate_net_income(self, gross_income: float, canton: str = "ZH") -> float:
        """
        Calculate net income after Swiss taxes and social insurance
        """
        try:
            # Federal tax
            federal_tax = self.calculate_federal_tax(gross_income)
            
            # Cantonal tax
            cantonal_rate = self.swiss_data["tax_rates"]["cantonal"].get(canton, 0.12)
            cantonal_tax = gross_income * cantonal_rate
            
            # Social insurance contributions
            ahv_rate = self.swiss_data["social_insurance"]["ahv"]["rate"]
            ahv_max = self.swiss_data["social_insurance"]["ahv"]["max_income"]
            ahv_contribution = min(gross_income, ahv_max) * ahv_rate
            
            alv_rate = self.swiss_data["social_insurance"]["alv"]["rate"]
            alv_max = self.swiss_data["social_insurance"]["alv"]["max_income"]
            alv_contribution = min(gross_income, alv_max) * alv_rate
            
            # Calculate net income
            total_deductions = federal_tax + cantonal_tax + ahv_contribution + alv_contribution
            net_income = gross_income - total_deductions
            
            logger.debug(f"💸 Tax calculation: Gross: {gross_income}, Net: {net_income}, Deductions: {total_deductions}")
            
            return max(0, net_income)
            
        except Exception as e:
            logger.error(f"❌ Net income calculation failed: {e}")
            return gross_income * 0.7  # Fallback: assume 30% total tax rate
    
    def calculate_federal_tax(self, income: float) -> float:
        """
        Calculate Swiss federal income tax using progressive brackets
        """
        try:
            brackets = self.swiss_data["tax_rates"]["federal"]["brackets"]
            total_tax = 0
            
            for bracket in brackets:
                if income > bracket["min"]:
                    taxable_in_bracket = min(income, bracket["max"]) - bracket["min"]
                    tax_in_bracket = taxable_in_bracket * bracket["rate"]
                    total_tax += tax_in_bracket
                    
                    if income <= bracket["max"]:
                        break
            
            return total_tax
            
        except Exception as e:
            logger.error(f"❌ Federal tax calculation failed: {e}")
            return income * 0.1  # Fallback: 10% federal tax
    
    def calculate_pillar3a_optimization(self, income: float, canton: str = "ZH") -> Dict:
        """
        Calculate Pillar 3a tax optimization benefits
        """
        try:
            max_contribution = self.swiss_data["pillar3a"]["max_contribution_2024"]
            
            # Calculate marginal tax rate
            marginal_rate = self.get_marginal_tax_rate(income, canton)
            
            # Calculate tax savings from maximum Pillar 3a contribution
            tax_savings = max_contribution * marginal_rate
            
            result = {
                "max_contribution": max_contribution,
                "tax_savings": round(tax_savings, 0),
                "marginal_tax_rate": round(marginal_rate * 100, 1),
                "net_cost": max_contribution - tax_savings,
                "canton": canton
            }
            
            logger.info(f"🏛️ Pillar 3a optimization: {result}")
            return result
            
        except Exception as e:
            logger.error(f"❌ Pillar 3a calculation failed: {e}")
            return {"error": str(e)}
    
    def get_marginal_tax_rate(self, income: float, canton: str = "ZH") -> float:
        """
        Calculate marginal tax rate for given income and canton
        """
        try:
            # Federal marginal rate
            federal_brackets = self.swiss_data["tax_rates"]["federal"]["brackets"]
            federal_marginal = 0
            
            for bracket in federal_brackets:
                if income > bracket["min"]:
                    federal_marginal = bracket["rate"]
                    if income <= bracket["max"]:
                        break
            
            # Cantonal rate
            cantonal_rate = self.swiss_data["tax_rates"]["cantonal"].get(canton, 0.12)
            
            # Social insurance rates
            ahv_rate = self.swiss_data["social_insurance"]["ahv"]["rate"]
            alv_rate = self.swiss_data["social_insurance"]["alv"]["rate"]
            
            # Total marginal rate
            total_marginal = federal_marginal + cantonal_rate + ahv_rate + alv_rate
            
            return min(total_marginal, 0.5)  # Cap at 50%
            
        except Exception as e:
            logger.error(f"❌ Marginal tax rate calculation failed: {e}")
            return 0.3  # Fallback: 30% marginal rate
    
    def calculate_healthcare_optimization(self, 
                                        age: int,
                                        annual_health_costs: float,
                                        risk_profile: str = "medium") -> Dict:
        """
        Calculate optimal healthcare deductible
        """
        try:
            deductibles = self.swiss_data["healthcare_deductibles"]
            
            # Estimate premium savings for higher deductibles
            # Higher deductible = lower premium
            base_premium = 400  # Base monthly premium
            
            optimization_results = []
            
            for deductible in deductibles:
                # Calculate premium discount for higher deductible
                premium_discount = (deductible - 300) * 0.1  # Rough estimate
                monthly_premium = base_premium - premium_discount
                annual_premium = monthly_premium * 12
                
                # Calculate total annual cost
                if annual_health_costs > deductible:
                    # Pay deductible + 10% of remaining costs
                    out_of_pocket = deductible + (annual_health_costs - deductible) * 0.1
                else:
                    # Pay only actual costs (less than deductible)
                    out_of_pocket = annual_health_costs
                
                total_annual_cost = annual_premium + out_of_pocket
                
                optimization_results.append({
                    "deductible": deductible,
                    "monthly_premium": round(monthly_premium, 0),
                    "annual_premium": round(annual_premium, 0),
                    "out_of_pocket": round(out_of_pocket, 0),
                    "total_annual_cost": round(total_annual_cost, 0)
                })
            
            # Find optimal deductible (lowest total cost)
            optimal = min(optimization_results, key=lambda x: x["total_annual_cost"])
            
            # Risk-based recommendations
            risk_recommendations = {
                "low": 2500,    # Healthy, high deductible
                "medium": 1500, # Moderate health needs
                "high": 300     # Chronic conditions, low deductible
            }
            
            recommended_deductible = risk_recommendations.get(risk_profile, 1500)
            
            result = {
                "optimal_deductible": optimal["deductible"],
                "recommended_deductible": recommended_deductible,
                "optimal_total_cost": optimal["total_annual_cost"],
                "all_options": optimization_results,
                "risk_profile": risk_profile,
                "annual_health_costs": annual_health_costs
            }
            
            logger.info(f"🏥 Healthcare optimization: Optimal deductible {optimal['deductible']} CHF")
            return result
            
        except Exception as e:
            logger.error(f"❌ Healthcare optimization failed: {e}")
            return {"error": str(e)}
    
    def compare_cantons(self, income: float, expenses: float, savings: float) -> Dict:
        """
        Compare FIRE timeline across different Swiss cantons
        """
        try:
            canton_results = {}
            
            for canton in self.swiss_data["tax_rates"]["cantonal"].keys():
                fire_result = self.calculate_fire_timeline(income, expenses, savings, canton)
                canton_results[canton] = fire_result
            
            # Find best canton for FIRE
            valid_results = {k: v for k, v in canton_results.items() 
                           if v.get("fire_possible", False)}
            
            if valid_results:
                best_canton = min(valid_results.keys(), 
                                key=lambda x: valid_results[x]["fire_years"])
                worst_canton = max(valid_results.keys(), 
                                 key=lambda x: valid_results[x]["fire_years"])
            else:
                best_canton = worst_canton = None
            
            result = {
                "canton_results": canton_results,
                "best_canton": best_canton,
                "worst_canton": worst_canton,
                "comparison_summary": {
                    "income": income,
                    "expenses": expenses,
                    "savings": savings
                }
            }
            
            logger.info(f"🇨🇭 Canton comparison: Best: {best_canton}, Worst: {worst_canton}")
            return result
            
        except Exception as e:
            logger.error(f"❌ Canton comparison failed: {e}")
            return {"error": str(e)}
    
    def validate_calculation_accuracy(self, 
                                    expected: Dict, 
                                    actual: Dict, 
                                    tolerance: float = 0.1) -> Dict:
        """
        Validate calculation accuracy for BDD tests
        """
        try:
            validation_results = {
                "passed": True,
                "errors": [],
                "warnings": []
            }
            
            # Check FIRE years
            if "fire_years" in expected and "fire_years" in actual:
                if expected["fire_years"] and actual["fire_years"]:
                    diff = abs(expected["fire_years"] - actual["fire_years"])
                    if diff > tolerance:
                        validation_results["passed"] = False
                        validation_results["errors"].append(
                            f"FIRE years mismatch: expected {expected['fire_years']}, got {actual['fire_years']}"
                        )
            
            # Check FIRE number
            if "fire_number" in expected and "fire_number" in actual:
                if expected["fire_number"] and actual["fire_number"]:
                    diff_percent = abs(expected["fire_number"] - actual["fire_number"]) / expected["fire_number"]
                    if diff_percent > tolerance:
                        validation_results["passed"] = False
                        validation_results["errors"].append(
                            f"FIRE number mismatch: expected {expected['fire_number']}, got {actual['fire_number']}"
                        )
            
            # Check savings rate
            if "savings_rate" in expected and "savings_rate" in actual:
                diff = abs(expected["savings_rate"] - actual["savings_rate"])
                if diff > tolerance * 100:  # Tolerance in percentage points
                    validation_results["warnings"].append(
                        f"Savings rate difference: expected {expected['savings_rate']}%, got {actual['savings_rate']}%"
                    )
            
            logger.info(f"✅ Calculation validation: {'PASSED' if validation_results['passed'] else 'FAILED'}")
            return validation_results
            
        except Exception as e:
            logger.error(f"❌ Calculation validation failed: {e}")
            return {"passed": False, "errors": [str(e)], "warnings": []}
