@premium @swiss @tax-planning
Feature: Swiss Tax Planning Dashboard
  As a Swiss resident planning for FIRE
  I want to access comprehensive tax planning tools
  So that I can optimize my tax situation and maximize savings

  Background:
    Given I am on the Swiss FIRE Calculator
    And I have entered my basic financial information
    And I navigate to the "Analysis" tab
    And I select the "Tax Optimization" secondary tab

  @smoke
  Scenario: Display Swiss Tax Planning Dashboard
    When the Swiss Tax Planning Dashboard loads
    Then I should see the dashboard header "Swiss Tax Planning Dashboard"
    And I should see the description "Comprehensive Swiss tax planning with social insurance, deductions, and optimization strategies"
    And I should see 4 navigation tabs: "Overview", "Optimization", "Deductions", "Scenarios"
    And the "Overview" tab should be selected by default

  @smoke
  Scenario: Navigate between tax planning tabs
    Given the Swiss Tax Planning Dashboard is loaded
    When I click on the "Optimization" tab
    Then I should see the Swiss Tax Optimization Engine
    When I click on the "Deductions" tab
    Then I should see the Swiss Tax Deduction Optimizer
    When I click on the "Scenarios" tab
    Then I should see the tax planning scenarios
    When I click on the "Overview" tab
    Then I should see the current tax situation overview

  Scenario: View current tax situation overview
    Given I am on the "Overview" tab of the tax planning dashboard
    When the tax analysis completes
    Then I should see my "Gross Annual Income" displayed in CHF
    And I should see my "Social Insurance" contributions displayed
    And I should see my "Income + Wealth Tax" displayed
    And I should see my "Net Annual Income" displayed
    And I should see my "Total Tax Burden" with effective rate percentage
    And I should see a tax breakdown showing percentages for social insurance, income tax, and wealth tax

  Scenario: View social insurance breakdown
    Given I am on the "Overview" tab of the tax planning dashboard
    When I scroll to the social insurance section
    Then I should see "AHV/IV/EO" contributions with "4.35% rate"
    And I should see "ALV" contributions with "1.1% rate"
    And I should see "NBU" contributions with "~1.0% rate"
    And I should see "Pension Fund" contributions with "Age-based rate"
    And all contribution amounts should be displayed in CHF format

  Scenario: Access tax optimization strategies
    Given I am on the "Optimization" tab of the tax planning dashboard
    When the optimization analysis completes
    Then I should see "Tax Optimization Strategies" section
    And I should see strategies with annual savings amounts in CHF
    And each strategy should show risk level (Low/Medium/High)
    And each strategy should have implementation steps
    And I should see time to implement estimates
    And I should see legal compliance indicators

  Scenario: Use tax deduction optimizer
    Given I am on the "Deductions" tab of the tax planning dashboard
    When the deduction optimizer loads
    Then I should see "Optimization Potential" summary
    And I should see "Current Deductions", "Potential Additional Deductions", and "Annual Tax Savings"
    And I should see deduction category tabs with potential savings indicators
    And I can select different deduction categories to view details
    And each category should show requirements, tips, and documentation needed

  Scenario: Compare tax planning scenarios
    Given I am on the "Scenarios" tab of the tax planning dashboard
    When the scenarios load
    Then I should see scenario buttons: "Current Situation", "Optimized (Same Canton)", "Best Canton"
    And each scenario button should show potential savings if applicable
    When I select a scenario
    Then I should see detailed scenario information including gross income, total tax burden, and net income
    And scenarios with savings should highlight the annual tax savings amount

  Scenario: Pillar 3a optimization analysis
    Given I am on the "Deductions" tab
    And I select the "Pillar 3a Contributions" category
    When the category details load
    Then I should see the maximum amount "CHF 7,056" for employees
    And I should see my current contribution amount
    And I should see potential tax savings from maximizing contributions
    And I should see implementation tips including "Contribute early in the year" and "Choose investment-based Pillar 3a"
    And I should see required documentation list

  Scenario: Professional expenses optimization
    Given I am on the "Deductions" tab
    And I select the "Professional Expenses" category
    When the category details load
    Then I should see the maximum allowed amount (20% of income up to CHF 4,000)
    And I should see my current professional expenses
    And I should see potential additional deductions available
    And I should see tips for "Document all work-related expenses" and "Claim home office expenses"
    And I should see "Low" risk level indicator

  Scenario: Canton tax comparison analysis
    Given I am on the "Optimization" tab
    When I scroll to the "Canton Tax Comparison" section
    Then I should see "Top 10 Lowest Tax" canton comparison table
    And I should see columns for Canton, Income Tax, Wealth Tax, Total Tax, Effective Rate, and vs Current
    And my current canton should be highlighted with "Current" indicator
    And I should see potential savings or additional costs for each canton
    And cantons should be sorted by total tax burden (lowest first)

  Scenario: Tax planning recommendations by timeframe
    Given I am on the "Optimization" tab
    When I scroll to the "Tax Planning Recommendations" section
    Then I should see "Immediate Actions (0-3 months)" with blue styling
    And I should see "Medium-term Planning (3-12 months)" with yellow styling
    And I should see "Long-term Strategy (1+ years)" with green styling
    And each timeframe should have specific actionable recommendations
    And recommendations should include Pillar 3a, professional expenses, and strategic planning

  Scenario: Real-time tax calculation updates
    Given I am on the tax planning dashboard
    When I modify my income or deduction inputs
    Then all tax calculations should update in real-time
    And the effective tax rate should recalculate automatically
    And potential savings should adjust based on new inputs
    And all CHF amounts should maintain Swiss formatting

  Scenario: Mobile responsiveness validation
    Given I am using a mobile device
    When I access the Swiss Tax Planning Dashboard
    Then all tabs should be accessible and touch-friendly
    And tables should scroll horizontally on small screens
    And all buttons should be appropriately sized for touch interaction
    And text should remain readable at mobile viewport sizes
    And the interface should maintain functionality across all tabs

  Scenario: Dark mode compatibility
    Given I have dark mode enabled
    When I use the Swiss Tax Planning Dashboard
    Then all tabs should display correctly in dark theme
    And text contrast should remain readable
    And color coding for recommendations should be visible
    And interactive elements should maintain proper styling
    And the dashboard should provide consistent dark mode experience

  Scenario: Error handling and edge cases
    Given I am on the tax planning dashboard
    When I enter invalid or extreme values
    Then the system should handle errors gracefully
    And I should see appropriate error messages or warnings
    And calculations should not break or show undefined values
    And the interface should remain functional despite invalid inputs

  Scenario: Performance validation for complex calculations
    Given I am on the tax planning dashboard with high income values
    When complex tax calculations are performed
    Then all calculations should complete within 3 seconds
    And the interface should remain responsive during calculations
    And loading indicators should be shown for longer operations
    And the user experience should remain smooth throughout

  Scenario: Swiss formatting and localization
    Given I am using the tax planning dashboard
    When viewing financial amounts and dates
    Then all currency should be displayed in CHF format with Swiss number formatting
    And dates should use DD.MM.YYYY Swiss format where applicable
    And tax rates should be displayed as percentages with appropriate precision
    And the interface should maintain Swiss financial conventions throughout

  Scenario: Integration with existing FIRE calculator data
    Given I have entered financial data in the main FIRE calculator
    When I access the tax planning dashboard
    Then my income, savings, and canton information should be automatically populated
    And changes in the tax dashboard should reflect in the main calculator
    And the data should remain synchronized across all components
    And I should not need to re-enter information already provided
