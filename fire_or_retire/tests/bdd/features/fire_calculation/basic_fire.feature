Feature: Basic FIRE Calculation
  As a Swiss resident planning for financial independence
  I want to calculate my FIRE timeline accurately
  So that I can plan my early retirement strategy effectively

  Background:
    Given I am on the Swiss Budget Pro homepage
    And the application has loaded successfully
    And I can see the main calculator interface

  @critical @fire @smoke
  Scenario: Young professional FIRE calculation in Zurich
    Given I am a 28-year-old software engineer in Zurich
    When I enter the following financial information:
      | Field                | Value  |
      | Monthly Income       | 8500   |
      | Monthly Expenses     | 5500   |
      | Current Savings      | 50000  |
      | Canton               | Zurich |
      | Age                  | 28     |
      | Target Retirement Age| 50     |
    And I click the calculate FIRE button
    Then I should see my FIRE timeline is approximately 18 years
    And I should see my FIRE number is approximately 1650000 CHF
    And I should see my savings rate is approximately 35%
    And I should see my monthly savings is approximately 2400 CHF
    And the calculation should complete within 3 seconds

  @critical @fire
  Scenario: Mid-career professional with family in Geneva
    Given I am a 42-year-old manager with a family in Geneva
    When I enter the following financial information:
      | Field                | Value  |
      | Monthly Income       | 15000  |
      | Monthly Expenses     | 10000  |
      | Current Savings      | 350000 |
      | Canton               | Geneva |
      | Age                  | 42     |
      | Target Retirement Age| 65     |
    And I click the calculate FIRE button
    Then I should see my FIRE timeline is approximately 15 years
    And I should see my FIRE number is approximately 3000000 CHF
    And I should see my savings rate is approximately 40%
    And I should see tax optimization recommendations
    And I should see Pillar 3a contribution suggestions

  @critical @fire @high-earner
  Scenario: High earner optimization in low-tax Zug
    Given I am a 35-year-old executive in Zug
    When I enter the following financial information:
      | Field                | Value  |
      | Monthly Income       | 25000  |
      | Monthly Expenses     | 10000  |
      | Current Savings      | 800000 |
      | Canton               | Zug    |
      | Age                  | 35     |
      | Target Retirement Age| 50     |
    And I click the calculate FIRE button
    Then I should see my FIRE timeline is less than 8 years
    And I should see my FIRE number is approximately 3000000 CHF
    And I should see my savings rate is greater than 60%
    And I should see significant tax optimization opportunities
    And I should see wealth tax considerations
    And I should see recommendations for investment diversification

  @fire @conservative
  Scenario: Conservative FIRE approach with lower withdrawal rate
    Given I am a 45-year-old conservative investor in Bern
    When I enter the following financial information:
      | Field                | Value  |
      | Monthly Income       | 12000  |
      | Monthly Expenses     | 8000   |
      | Current Savings      | 600000 |
      | Canton               | Bern   |
      | Age                  | 45     |
      | Target Retirement Age| 60     |
    And I select a conservative withdrawal rate of 3%
    And I click the calculate FIRE button
    Then I should see my FIRE number is approximately 3200000 CHF
    And I should see my FIRE timeline is approximately 12 years
    And I should see risk assessment warnings about market volatility
    And I should see recommendations for bond allocation

  @fire @edge-case
  Scenario: Minimal savings capacity scenario
    Given I am a 30-year-old with limited income in Aargau
    When I enter the following financial information:
      | Field                | Value  |
      | Monthly Income       | 4500   |
      | Monthly Expenses     | 4200   |
      | Current Savings      | 10000  |
      | Canton               | Aargau |
      | Age                  | 30     |
      | Target Retirement Age| 65     |
    And I click the calculate FIRE button
    Then I should see my FIRE timeline is approximately 45 years
    And I should see my savings rate is approximately 7%
    And I should see recommendations to increase income
    And I should see expense optimization suggestions
    And I should see warnings about the long timeline

  @fire @edge-case @negative
  Scenario: Impossible FIRE scenario - expenses exceed income
    Given I am a person with high expenses relative to income
    When I enter the following financial information:
      | Field                | Value  |
      | Monthly Income       | 6000   |
      | Monthly Expenses     | 6500   |
      | Current Savings      | 20000  |
      | Canton               | Geneva |
      | Age                  | 35     |
      | Target Retirement Age| 65     |
    And I click the calculate FIRE button
    Then I should see an error message "FIRE not possible with current income and expenses"
    And I should see recommendations to reduce expenses
    And I should see suggestions to increase income
    And I should not see a FIRE timeline
    And I should not see a FIRE number

  @fire @validation
  Scenario: Input validation for FIRE calculation
    Given I am on the FIRE calculator
    When I enter invalid financial information:
      | Field                | Value    |
      | Monthly Income       | -5000    |
      | Monthly Expenses     | abc      |
      | Current Savings      | 1000000000 |
      | Canton               | Invalid  |
    And I attempt to calculate FIRE
    Then I should see validation errors for each invalid field
    And I should see "Income must be positive" error
    And I should see "Expenses must be a valid number" error
    And I should see "Savings amount seems unrealistic" warning
    And I should see "Please select a valid Swiss canton" error
    And the calculate button should remain disabled until errors are fixed

  @fire @real-time
  Scenario: Real-time FIRE calculation updates
    Given I am on the FIRE calculator
    When I enter my monthly income of 10000 CHF
    And I enter my monthly expenses of 6000 CHF
    And I select "Zurich" as my canton
    Then I should see the FIRE calculation update automatically
    And I should see the savings rate update to approximately 32%
    When I change my monthly expenses to 5000 CHF
    Then I should see the FIRE timeline decrease
    And I should see the savings rate increase to approximately 42%
    And the updates should happen within 1 second of input change

  @fire @mobile
  Scenario: FIRE calculation on mobile device
    Given I am using a mobile device
    And I am on the Swiss Budget Pro mobile interface
    When I enter my financial information using touch inputs:
      | Field                | Value  |
      | Monthly Income       | 7500   |
      | Monthly Expenses     | 5000   |
      | Current Savings      | 100000 |
      | Canton               | Vaud   |
    And I tap the calculate button
    Then I should see my FIRE results displayed clearly on mobile
    And the interface should be responsive and touch-friendly
    And I should be able to scroll through all results
    And the calculation should complete within 5 seconds on mobile

  @fire @accessibility
  Scenario: FIRE calculation with screen reader
    Given I am using a screen reader
    And I am on the FIRE calculator
    When I navigate through the form using keyboard only
    And I enter financial information using keyboard input
    And I activate the calculate button with Enter key
    Then I should hear the FIRE results announced clearly
    And all form fields should have proper labels
    And the results should be accessible via screen reader
    And error messages should be announced immediately

  @fire @data-persistence
  Scenario: FIRE calculation data persistence
    Given I have entered my financial information
    When I calculate my FIRE timeline
    And I refresh the browser page
    Then my previously entered data should be restored
    And my calculation results should be preserved
    When I close and reopen the browser
    Then my data should still be available
    And I should see a message about restored data

  @fire @export
  Scenario: Export FIRE calculation results
    Given I have completed a FIRE calculation
    When I click the export results button
    Then I should be able to download a PDF report
    And the PDF should contain my FIRE timeline
    And the PDF should include my financial assumptions
    And the PDF should show Swiss tax considerations
    And the PDF should include recommendations
    And the export should complete within 10 seconds
