Feature: Healthcare Cost Optimizer
  As a Swiss resident planning for FIRE
  I want to optimize my healthcare costs and deductibles
  So that I can reduce my healthcare expenses while maintaining adequate coverage

  Background:
    Given I am on the Swiss FIRE Calculator homepage
    And I have entered my basic information:
      | Field           | Value |
      | Age             | 35    |
      | Canton          | ZH    |
      | Income          | 80000 |
      | Family Size     | 2     |
    And I navigate to the Premium Features tab

  @smoke @premium @healthcare
  Scenario: Healthcare Cost Optimizer displays comprehensive analysis
    When I access the Healthcare Cost Optimizer section
    Then I should see the Healthcare Cost Optimizer component
    And I should see "Swiss healthcare deductible and premium optimization"
    And I should see current healthcare configuration
    And the system should automatically analyze my healthcare costs
    And I should see a loading message "Analyzing healthcare optimization opportunities"
    When the healthcare analysis completes
    Then I should see "Current Healthcare Analysis" section
    And I should see "Optimization Recommendations" section
    And I should see potential annual savings displayed

  @premium @healthcare @deductible-analysis
  Scenario: Deductible optimization analysis is comprehensive
    Given the Healthcare Cost Optimizer has completed analysis
    When I view the deductible analysis
    Then I should see analysis for all Swiss deductible levels:
      | Deductible | Type        |
      | CHF 300    | Minimum     |
      | CHF 500    | Low         |
      | CHF 1000   | Medium      |
      | CHF 1500   | Medium-High |
      | CHF 2000   | High        |
      | CHF 2500   | Maximum     |
    And each deductible should show annual premium savings
    And each deductible should show break-even analysis
    And recommendations should be based on expected healthcare usage

  @premium @healthcare @premium-savings
  Scenario: Premium savings calculations are accurate
    Given the Healthcare Cost Optimizer has completed analysis
    When I examine premium savings calculations
    Then I should see annual premium differences for each deductible
    And higher deductibles should show higher premium savings
    And savings should be calculated based on my canton and age
    And all amounts should be displayed in CHF
    And calculations should reflect current Swiss healthcare rates

  @premium @healthcare @break-even-analysis
  Scenario: Break-even analysis helps decision making
    Given the Healthcare Cost Optimizer has completed analysis
    When I view the break-even analysis
    Then each deductible option should show break-even point
    And break-even should be calculated in CHF of medical expenses
    And I should see "You save money if annual medical costs are below CHF X"
    And break-even analysis should help choose optimal deductible
    And recommendations should consider my risk tolerance

  @premium @healthcare @risk-assessment
  Scenario: Healthcare risk assessment is provided
    Given the Healthcare Cost Optimizer has completed analysis
    When I view the risk assessment
    Then I should see my healthcare risk profile
    And risk should be based on age, family size, and health status
    And I should see recommendations for conservative vs aggressive optimization
    And risk assessment should influence deductible recommendations
    And I should see guidance for different risk tolerance levels

  @premium @healthcare @family-considerations
  Scenario: Family size affects healthcare optimization
    Given I have a family size of 4
    When the Healthcare Cost Optimizer analyzes my situation
    Then recommendations should consider family healthcare needs
    And calculations should account for multiple family members
    And I should see family-specific optimization strategies
    Given I have a family size of 1
    When the analysis recalculates
    Then recommendations should adapt to single person needs
    And optimization should focus on individual healthcare costs

  @premium @healthcare @age-based-recommendations
  Scenario: Age affects healthcare cost optimization
    Given I am 25 years old
    When the Healthcare Cost Optimizer analyzes my situation
    Then I should see recommendations for young, healthy individuals
    And higher deductibles should be more strongly recommended
    And premium savings should be emphasized
    Given I am 55 years old
    When the analysis recalculates
    Then recommendations should be more conservative
    And lower deductibles might be recommended
    And healthcare usage expectations should increase

  @premium @healthcare @canton-specific
  Scenario: Canton-specific healthcare costs are considered
    Given I live in Zurich (ZH)
    When the Healthcare Cost Optimizer analyzes costs
    Then premium calculations should reflect Zurich rates
    And I should see canton-specific healthcare information
    Given I change my canton to Geneva (GE)
    When the analysis recalculates
    Then premium calculations should update for Geneva rates
    And cost differences between cantons should be reflected

  @premium @healthcare @fire-integration
  Scenario: Healthcare optimization integrates with FIRE planning
    Given the Healthcare Cost Optimizer has completed analysis
    When I view the FIRE integration section
    Then I should see how healthcare savings affect my FIRE timeline
    And annual savings should be added to my savings rate
    And I should see "Healthcare optimization can accelerate FIRE by X months"
    And optimization should be integrated into overall financial planning
    And I should see long-term impact of healthcare cost reduction

  @premium @healthcare @recommendations
  Scenario: Personalized recommendations are provided
    Given the Healthcare Cost Optimizer has completed analysis
    When I view the recommendations section
    Then I should see "Recommended Deductible" clearly highlighted
    And I should see "Potential Annual Savings" amount
    And I should see reasoning for the recommendation
    And recommendations should consider my risk profile and usage patterns
    And I should see implementation guidance

  @premium @healthcare @scenario-comparison
  Scenario: Different healthcare scenarios can be compared
    Given the Healthcare Cost Optimizer has completed analysis
    When I view scenario comparisons
    Then I should see comparison of all deductible options
    And I should see total annual costs for each scenario
    And scenarios should include premiums plus expected out-of-pocket costs
    And I should be able to compare conservative vs aggressive strategies
    And visual comparison should help decision making

  @premium @healthcare @supplemental-insurance
  Scenario: Supplemental insurance considerations are included
    Given the Healthcare Cost Optimizer has completed analysis
    When I view supplemental insurance analysis
    Then I should see analysis of supplemental insurance options
    And I should see cost-benefit analysis of additional coverage
    And recommendations should consider my income and risk tolerance
    And I should see guidance on dental, vision, and alternative medicine coverage

  @premium @healthcare @annual-review
  Scenario: Annual healthcare review recommendations are provided
    Given the Healthcare Cost Optimizer has completed analysis
    When I view the annual review section
    Then I should see recommendations for annual healthcare review
    And I should see guidance on when to reassess deductible choices
    And I should see triggers for changing healthcare strategy
    And I should see calendar reminders for optimization review

  @premium @healthcare @savings-tracking
  Scenario: Healthcare savings can be tracked over time
    Given the Healthcare Cost Optimizer has completed analysis
    When I implement the recommended deductible
    Then I should be able to track actual savings
    And I should see comparison of projected vs actual savings
    And tracking should integrate with overall financial progress
    And I should see cumulative healthcare savings over time

  @premium @healthcare @emergency-fund
  Scenario: Emergency fund recommendations consider healthcare costs
    Given the Healthcare Cost Optimizer has completed analysis
    When I view emergency fund recommendations
    Then I should see how deductible choice affects emergency fund needs
    And higher deductibles should increase recommended emergency fund
    And I should see "Increase emergency fund by CHF X for higher deductible"
    And emergency fund guidance should be integrated with healthcare optimization

  @premium @healthcare @tax-implications
  Scenario: Tax implications of healthcare costs are considered
    Given the Healthcare Cost Optimizer has completed analysis
    When I view tax implications
    Then I should see how healthcare costs affect my taxes
    And I should see information about healthcare tax deductions
    And I should see guidance on HSA-equivalent Swiss options
    And tax optimization should be integrated with healthcare planning

  @premium @healthcare @mobile
  Scenario: Healthcare Cost Optimizer works on mobile devices
    Given I am using a mobile device
    And I access the Healthcare Cost Optimizer
    When the analysis completes
    Then all components should be visible and functional on mobile
    And deductible comparisons should be readable on small screens
    And I should be able to scroll through all recommendations
    And the interface should be touch-friendly

  @premium @healthcare @refresh
  Scenario: Healthcare analysis can be refreshed with updated information
    Given the Healthcare Cost Optimizer has completed analysis
    When I update my age to 40
    And I click "Refresh Healthcare Analysis"
    Then I should see "Analyzing..." loading state
    And the analysis should recalculate with updated age
    And recommendations should adapt to the age change
    And savings calculations should update accordingly

  @premium @healthcare @performance
  Scenario: Healthcare analysis completes within reasonable time
    Given I access the Healthcare Cost Optimizer
    When the system starts analyzing healthcare costs
    Then the analysis should complete within 5 seconds
    And the interface should remain responsive during analysis
    And loading indicators should show progress
    And there should be no performance issues

  @premium @healthcare @edge-cases
  Scenario: Healthcare optimizer handles edge cases gracefully
    Given I have entered unusual data:
      | Age         | 0     |
      | Income      | 0     |
      | Family Size | 0     |
    When I access the Healthcare Cost Optimizer
    Then the system should handle the edge case gracefully
    And I should not see any error messages
    And the component should not crash
    And I should see appropriate guidance for incomplete data

  @premium @healthcare @dark-mode
  Scenario: Healthcare Cost Optimizer works in dark mode
    Given I have enabled dark mode
    When I access the Healthcare Cost Optimizer
    Then all components should display correctly in dark mode
    And text should be readable with proper contrast
    And charts and comparisons should be visible in dark mode
    And all visual elements should be properly styled
