Feature: Risk Assessment Metrics
  As a Swiss resident planning for FIRE
  I want comprehensive analysis of financial risks
  So that I can identify and mitigate potential threats to my financial independence

  Background:
    Given I am on the Swiss FIRE Calculator homepage
    And I have entered comprehensive financial information:
      | Field                | Value  |
      | Current Age          | 40     |
      | Retirement Age       | 65     |
      | Current Savings      | 200000 |
      | Monthly Income       | 10000  |
      | Monthly Expenses     | 6000   |
      | Expected Return      | 7      |
      | Inflation Rate       | 2.5    |
      | Safe Withdrawal Rate | 4      |
    And I have added investment portfolio:
      | Investment Type | Value  |
      | Swiss ETF       | 80000  |
      | Bonds          | 40000  |
      | Real Estate    | 60000  |
    And I navigate to the Advanced Features tab

  @smoke @advanced @risk
  Scenario: Risk Assessment Metrics displays comprehensive analysis
    When I access the Risk Assessment Metrics section
    Then I should see the Risk Assessment Metrics component
    And I should see "Comprehensive analysis of financial risks"
    And the system should automatically analyze my risk profile
    And I should see a loading message "Analyzing risk profile"
    When the risk analysis completes
    Then I should see the "Overall Risk Profile" section
    And I should see my risk score displayed
    And I should see my risk level classification
    And I should see the count of risk factors

  @advanced @risk @metrics
  Scenario: All 8 risk metrics are displayed and analyzed
    Given the Risk Assessment has completed analysis
    When I view the risk metrics section
    Then I should see "Risk Metrics Analysis"
    And I should see all 8 risk metrics:
      | Risk Metric                  |
      | Emergency Fund Coverage      |
      | Savings Rate Sustainability  |
      | Investment Concentration     |
      | Time Horizon Risk           |
      | Income Stability            |
      | Inflation Protection        |
      | Withdrawal Rate Safety      |
      | Sequence of Returns Risk    |
    And each metric should display a score out of 100
    And each metric should show a risk severity level

  @advanced @risk @categories
  Scenario: Risk metrics can be filtered by category
    Given the Risk Assessment has completed analysis
    When I view the risk category filter
    Then I should see a category dropdown
    When I select "Financial" from the category filter
    Then I should see "Emergency Fund Coverage"
    And I should see "Savings Rate Sustainability"
    And I should see "Withdrawal Rate Safety"
    When I select "Market" from the category filter
    Then I should see "Investment Concentration"
    And I should see "Sequence of Returns Risk"
    When I select "Personal" from the category filter
    Then I should see "Time Horizon Risk"
    And I should see "Income Stability"
    When I select "Economic" from the category filter
    Then I should see "Inflation Protection"

  @advanced @risk @severity
  Scenario: Risk severity levels are correctly classified
    Given the Risk Assessment has completed analysis
    When I view the risk metrics
    Then I should see risk severity levels displayed
    And severity levels should include "LOW RISK", "MEDIUM RISK", "HIGH RISK", or "CRITICAL RISK"
    And each metric should have an appropriate severity classification
    And severity should be color-coded for easy identification

  @advanced @risk @recommendations
  Scenario: Risk management recommendations are provided
    Given the Risk Assessment has completed analysis
    When I view the recommendations section
    Then I should see "Risk Management Recommendations"
    And I should see numbered recommendation steps
    And I should see at least 4 actionable recommendations
    And each recommendation should be specific and implementable
    And recommendations should address the highest risk areas

  @advanced @risk @strengths-weaknesses
  Scenario: Strengths and weaknesses are identified
    Given the Risk Assessment has completed analysis
    When I view the analysis results
    Then I should see a "Strengths" section
    And I should see an "Areas for Improvement" section
    And strengths should highlight positive aspects of my financial situation
    And weaknesses should identify areas requiring attention

  @advanced @risk @emergency-fund
  Scenario: Emergency fund coverage is accurately calculated
    Given I have monthly expenses of 5000 CHF
    And I have current savings of 30000 CHF
    When the Risk Assessment analyzes my emergency fund
    Then I should see "Emergency Fund Coverage" metric
    And it should show "6.0 months" of coverage
    And it should be classified as adequate coverage
    And it should show a good risk score

  @advanced @risk @time-horizon
  Scenario: Time horizon risk is correctly assessed
    Given I am 60 years old with retirement age 65
    When the Risk Assessment analyzes my time horizon
    Then I should see "Time Horizon Risk" metric
    And it should show "5 years" until retirement
    And it should be classified as higher risk due to short timeline
    Given I am 30 years old with retirement age 65
    When the Risk Assessment analyzes my time horizon
    Then it should show "35 years" until retirement
    And it should be classified as lower risk due to long timeline

  @advanced @risk @scenarios
  Scenario: Risk assessment adapts to different financial scenarios
    Given I have a high-risk financial profile:
      | Current Savings  | 5000  |
      | Monthly Income   | 4000  |
      | Monthly Expenses | 3800  |
      | Current Age      | 60    |
      | Retirement Age   | 65    |
    When the Risk Assessment analyzes my situation
    Then I should see "CRITICAL RISK" or "HIGH RISK" classifications
    And I should see urgent recommendations
    Given I have a low-risk financial profile:
      | Current Savings  | 500000 |
      | Monthly Income   | 15000  |
      | Monthly Expenses | 5000   |
      | Current Age      | 30     |
      | Retirement Age   | 65     |
    When the Risk Assessment analyzes my situation
    Then I should see "LOW RISK" classifications
    And I should see optimization recommendations

  @advanced @risk @refresh
  Scenario: Risk analysis can be refreshed with updated data
    Given the Risk Assessment has completed analysis
    When I update my current savings to 300000
    And I click "Refresh Risk Analysis"
    Then I should see "Analyzing..." loading state
    And the risk assessment should recalculate
    And the updated savings should improve my risk scores
    And I should see updated recommendations

  @advanced @risk @confidence
  Scenario: Risk metrics display confidence levels
    Given the Risk Assessment has completed analysis
    When I view the risk metrics
    Then each metric should display confidence information
    And I should see "confidence" indicators
    And I should see "Actionable" badges on relevant metrics
    And confidence levels should help prioritize actions

  @advanced @risk @descriptions
  Scenario: Risk metrics include detailed descriptions and recommendations
    Given the Risk Assessment has completed analysis
    When I view each risk metric
    Then each metric should have a description explaining its importance
    And each metric should have specific recommendations for improvement
    And descriptions should be clear and educational
    And recommendations should be actionable

  @advanced @risk @mobile
  Scenario: Risk Assessment works on mobile devices
    Given I am using a mobile device
    And I access the Risk Assessment Metrics
    When the analysis completes
    Then all risk components should be visible and functional
    And the category filter should work on mobile
    And I should be able to scroll through all metrics
    And the interface should be touch-friendly

  @advanced @risk @performance
  Scenario: Risk analysis completes within reasonable time
    Given I access the Risk Assessment Metrics
    When the system starts analyzing my risk profile
    Then the analysis should complete within 8 seconds
    And the loading indicators should be responsive
    And the interface should remain interactive during analysis
    And there should be no performance issues

  @advanced @risk @edge-cases
  Scenario: Risk assessment handles edge cases gracefully
    Given I have entered minimal financial data:
      | Current Savings  | 0 |
      | Monthly Income   | 0 |
      | Monthly Expenses | 0 |
    When I access the Risk Assessment Metrics
    Then the system should handle the edge case gracefully
    And I should not see any error messages
    And the component should not crash
    And I should see appropriate guidance for incomplete data

  @advanced @risk @dark-mode
  Scenario: Risk Assessment works in dark mode
    Given I have enabled dark mode
    When I access the Risk Assessment Metrics
    Then the risk components should display correctly in dark mode
    And text should be readable with proper contrast
    And risk severity colors should be appropriate for dark mode
    And all visual elements should be properly styled

  @advanced @risk @actionable
  Scenario: Risk insights are marked as actionable
    Given the Risk Assessment has completed analysis
    When I view the risk metrics
    Then I should see "Actionable" badges on relevant insights
    And actionable items should have clear next steps
    And I should be able to prioritize actions based on risk levels
    And recommendations should be specific to my situation
