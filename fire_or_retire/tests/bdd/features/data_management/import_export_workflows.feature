Feature: Data Import/Export and Management Workflows
  As a Swiss Budget Pro user
  I want to efficiently manage my financial data through import/export
  So that I can backup, migrate, and share my financial planning data

  Background:
    Given I am on the Swiss Budget Pro homepage
    And the application has loaded successfully
    And I have a complete financial profile set up

  @critical @data-management @export
  Scenario: Complete financial data export in multiple formats
    Given I have a comprehensive financial profile with:
      | Data Category           | Items Count | Sample Data                    |
      | Income Sources          | 3           | Salary, Freelance, Investments |
      | Expense Categories      | 15          | Swiss standard categories      |
      | Savings Goals           | 5           | Emergency, FIRE, Home, Travel  |
      | Investment Portfolio    | 8           | Stocks, Bonds, REITs, Crypto   |
      | Tax Optimization Data   | 26          | All Swiss cantons             |
      | FIRE Projections        | 1000        | Monte Carlo scenarios          |
    When I navigate to the data export section
    And I select "Complete Profile Export"
    And I choose export format "JSON"
    And I click "Export Data"
    Then I should see export progress indicator
    And I should receive a JSON file within 10 seconds
    And the JSON file should contain all financial data categories
    And the JSON file should include metadata:
      | Field               | Expected Value        |
      | Export Date         | Current timestamp     |
      | App Version         | Current version       |
      | Data Version        | Schema version        |
      | Total Records       | >1000                 |

  @critical @data-management @import @json
  Scenario: Complete data import from JSON backup
    Given I have exported my data previously as JSON
    And I have made changes to my financial profile
    When I navigate to the data import section
    And I select "Import from JSON Backup"
    And I drag and drop my JSON backup file
    Then I should see file validation progress
    And I should see data preview with:
      | Data Category           | Records Found | Status    |
      | Income Sources          | 3             | Valid     |
      | Expense Categories      | 15            | Valid     |
      | Savings Goals           | 5             | Valid     |
      | Investment Portfolio    | 8             | Valid     |
      | Tax Data               | 26            | Valid     |
    And I should see import options:
      | Option                  | Default | Description                    |
      | Merge with Existing     | No      | Combine with current data      |
      | Replace All Data        | Yes     | Overwrite all existing data    |
      | Backup Current Data     | Yes     | Create backup before import    |
    When I select "Replace All Data" with backup
    And I click "Confirm Import"
    Then I should see "Import completed successfully"
    And all my data should be restored from the backup

  @data-management @import @csv @bank-data
  Scenario: Import bank transaction data from CSV
    Given I have downloaded transaction data from my Swiss bank
    When I navigate to the data import section
    And I select "Import Bank Transactions"
    And I upload my bank CSV file
    Then I should see CSV format detection:
      | Field                   | Detected Value    | Confidence |
      | Date Column             | Column 1          | 95%        |
      | Amount Column           | Column 4          | 90%        |
      | Description Column      | Column 3          | 85%        |
      | Currency                | CHF               | 100%       |
    And I should see transaction preview:
      | Date       | Description           | Amount  | Category (Auto) |
      | 15.01.2024 | MIGROS ZURICH        | -85.50  | Food            |
      | 15.01.2024 | SBB ONLINE           | -45.00  | Transportation  |
      | 16.01.2024 | SALARY PAYMENT       | 8500.00 | Income          |
    When I review and adjust category mappings
    And I click "Import Transactions"
    Then I should see "180 transactions imported successfully"
    And my expense tracking should be updated with imported data

  @data-management @backup @automated
  Scenario: Automated backup and versioning system
    Given I have enabled automatic backups
    When I navigate to the backup management section
    Then I should see my backup history:
      | Backup Date         | Type        | Size   | Status    |
      | 2024-01-15 09:00   | Daily       | 2.5 MB | Complete  |
      | 2024-01-14 09:00   | Daily       | 2.4 MB | Complete  |
      | 2024-01-08 09:00   | Weekly      | 2.3 MB | Complete  |
      | 2024-01-01 09:00   | Monthly     | 2.1 MB | Complete  |
    And I should see backup settings:
      | Setting                 | Current Value | Options                    |
      | Auto Backup Frequency   | Daily         | Daily, Weekly, Monthly     |
      | Backup Retention        | 30 days       | 7, 14, 30, 90 days        |
      | Include Historical Data | Yes           | Yes, No                    |
      | Encryption              | Enabled       | Enabled, Disabled          |

  @data-management @privacy @gdpr
  Scenario: Privacy-compliant data export and deletion
    Given I want to export my data for privacy compliance
    When I navigate to privacy and data management
    And I select "Export All Personal Data"
    Then I should see GDPR-compliant export options:
      | Data Category           | Include | Personal Data | Anonymize Option |
      | Financial Transactions  | Yes     | Yes           | Available        |
      | Personal Information    | Yes     | Yes           | Not Available    |
      | Usage Analytics         | Yes     | No            | N/A              |
      | System Logs             | No      | Partial       | Available        |
    When I select "Include All Personal Data"
    And I click "Generate Privacy Export"
    Then I should receive a comprehensive data package
    And I should see data export certificate with:
      | Field                   | Value                     |
      | Export Date             | Current timestamp         |
      | Data Completeness       | 100%                      |
      | Legal Compliance        | GDPR, Swiss DPA          |
