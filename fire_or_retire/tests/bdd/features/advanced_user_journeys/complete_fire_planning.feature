Feature: Complete FIRE Planning User Journey
  As a Swiss resident planning for financial independence
  I want to complete a comprehensive FIRE planning workflow
  So that I can create a detailed retirement strategy with all Swiss considerations

  Background:
    Given I am on the Swiss Budget Pro homepage
    And the application has loaded successfully
    And I can see the main calculator interface

  @critical @fire @comprehensive
  Scenario: Complete FIRE planning workflow for young professional in Zurich
    Given I am a 28-year-old software engineer planning for FIRE
    When I start the comprehensive FIRE planning workflow
    And I enter my basic information:
      | Field                | Value  |
      | Age                  | 28     |
      | Canton               | Zurich |
      | Monthly Income       | 8500   |
      | Current Savings      | 50000  |
      | Target FIRE Age      | 50     |
    And I proceed to the expense analysis step
    And I categorize my monthly expenses:
      | Category             | Amount | Essential |
      | Rent                 | 1800   | Yes       |
      | Food & Groceries     | 600    | Yes       |
      | Transportation       | 200    | Yes       |
      | Entertainment        | 400    | No        |
      | Travel               | 500    | No        |
    And I proceed to the savings goals step
    And I set up my savings goals:
      | Goal Type           | Target Amount | Priority |
      | Emergency Fund      | 25000         | High     |
      | Pillar 3a           | 7056          | High     |
      | FIRE Portfolio      | 2500000       | High     |
    And I proceed to the investment strategy step
    And I configure my investment allocation:
      | Asset Class         | Allocation | Expected Return |
      | Swiss Stocks        | 30%        | 7.5%           |
      | Global Stocks       | 40%        | 8.0%           |
      | Swiss Bonds         | 20%        | 2.5%           |
      | Real Estate         | 10%        | 6.0%           |
    And I run Monte Carlo simulations with stress testing
    Then I should see my FIRE timeline is approximately 22 years
    And I should see my FIRE number is 2500000 CHF
    And I should see my savings rate is approximately 35%
    And I should see Monte Carlo success rate is above 75%
    And I should see detailed risk metrics
    And the complete workflow should complete within 10 seconds

  @fire @family @comprehensive
  Scenario: Family FIRE planning with children in Geneva
    Given I am a 35-year-old family with 2 children planning for FIRE
    When I start the comprehensive FIRE planning workflow
    And I enter my family information:
      | Field                | Value  |
      | Age                  | 35     |
      | Canton               | Geneva |
      | Combined Income      | 18000  |
      | Current Savings      | 180000 |
      | Children             | 2      |
    And I proceed to the family expense analysis
    And I categorize my family monthly expenses:
      | Category             | Amount | Essential |
      | Rent                 | 2800   | Yes       |
      | Food & Groceries     | 1200   | Yes       |
      | Childcare            | 2000   | Yes       |
      | Education            | 500    | Yes       |
      | Family Entertainment | 600    | No        |
    And I set up family-oriented savings goals:
      | Goal Type              | Target Amount | Priority |
      | Emergency Fund         | 60000         | High     |
      | Children Education     | 100000        | High     |
      | FIRE Portfolio         | 4000000       | High     |
    And I run family-focused Monte Carlo simulations
    Then I should see my family FIRE timeline is approximately 20 years
    And I should see my family FIRE number is 4000000 CHF
    And I should see children education funding is on track
    And I should see family emergency fund recommendations

  @fire @optimization @mid-career
  Scenario: Mid-career professional optimization workflow
    Given I am a 42-year-old mid-career professional optimizing my FIRE plan
    When I start the FIRE optimization workflow
    And I enter my current financial situation:
      | Field                | Value  |
      | Age                  | 42     |
      | Canton               | Zug    |
      | Monthly Income       | 15000  |
      | Current Portfolio    | 650000 |
    And I proceed to the optimization analysis
    And I identify optimization opportunities:
      | Area                   | Current | Optimized | Improvement |
      | Savings Rate          | 28%     | 35%       | +7%         |
      | Tax Efficiency        | 75%     | 90%       | +15%        |
      | Investment Allocation | 60/40   | 70/30     | +0.8%       |
    And I apply the optimization recommendations
    Then I should see my optimized FIRE timeline is 16 years
    And I should see annual tax savings of 4500 CHF
    And I should see Monte Carlo success rate improved to 85%
