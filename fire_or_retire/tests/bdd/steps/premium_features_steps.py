"""
Step definitions for Premium Features BDD tests
"""

from behave import given, when, then
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import Select
from selenium.common.exceptions import TimeoutException
import time


# Common Navigation Steps
@given('I am on the Swiss FIRE Calculator homepage')
def step_navigate_to_homepage(context):
    """Navigate to the Swiss FIRE Calculator homepage"""
    context.driver.get(context.base_url)
    wait = WebDriverWait(context.driver, 10)
    wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))


@given('I navigate to the Premium Features tab')
def step_navigate_to_premium_tab(context):
    """Navigate to the Premium Features tab"""
    wait = WebDriverWait(context.driver, 10)
    premium_tab = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, '[data-testid="tab-premium"]')))
    premium_tab.click()
    time.sleep(1)  # Allow tab to load


@given('I navigate to the Advanced Features tab')
def step_navigate_to_advanced_tab(context):
    """Navigate to the Advanced Features tab"""
    wait = WebDriverWait(context.driver, 10)
    advanced_tab = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, '[data-testid="tab-advanced"]')))
    advanced_tab.click()
    time.sleep(1)  # Allow tab to load


@given('I navigate to the Analysis tab')
def step_navigate_to_analysis_tab(context):
    """Navigate to the Analysis tab"""
    wait = WebDriverWait(context.driver, 10)
    analysis_tab = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, '[data-testid="tab-analysis"]')))
    analysis_tab.click()
    time.sleep(1)  # Allow tab to load


# Data Entry Steps
@given('I have entered my basic financial information')
def step_enter_basic_financial_info(context):
    """Enter basic financial information from table"""
    wait = WebDriverWait(context.driver, 10)
    
    for row in context.table:
        field_name = row['Field']
        value = row['Value']
        
        # Map field names to data-testid attributes
        field_mapping = {
            'Current Age': 'current-age',
            'Retirement Age': 'retirement-age',
            'Current Savings': 'current-savings',
            'Monthly Income': 'monthly-income',
            'Monthly Expenses': 'monthly-expenses',
            'Expected Return': 'expected-return',
            'Inflation Rate': 'inflation-rate',
            'Safe Withdrawal Rate': 'safe-withdrawal-rate'
        }
        
        if field_name in field_mapping:
            field_id = field_mapping[field_name]
            field_element = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, f'[data-testid="{field_id}"]')))
            field_element.clear()
            field_element.send_keys(value)


@given('I have entered comprehensive financial information')
def step_enter_comprehensive_financial_info(context):
    """Enter comprehensive financial information"""
    step_enter_basic_financial_info(context)


@given('I have entered retirement planning information')
def step_enter_retirement_planning_info(context):
    """Enter retirement planning information"""
    step_enter_basic_financial_info(context)


@given('I have entered my financial information')
def step_enter_financial_info(context):
    """Enter financial information"""
    step_enter_basic_financial_info(context)


@given('I have added investment portfolio')
def step_add_investment_portfolio(context):
    """Add investment portfolio from table"""
    # This would typically involve adding investments through the UI
    # For now, we'll assume the investments are already configured
    pass


@given('I have added expenses and investments')
def step_add_expenses_and_investments(context):
    """Add expenses and investments"""
    # This would typically involve adding expenses and investments through the UI
    pass


# AI Financial Advisor Steps
@when('I access the AI Financial Advisor section')
def step_access_ai_advisor(context):
    """Access the AI Financial Advisor section"""
    wait = WebDriverWait(context.driver, 10)
    ai_section = wait.until(EC.presence_of_element_located((By.XPATH, "//h3[contains(text(), 'AI Financial Advisor')]")))
    context.driver.execute_script("arguments[0].scrollIntoView();", ai_section)


@then('I should see the AI Financial Advisor component')
def step_verify_ai_advisor_component(context):
    """Verify AI Financial Advisor component is visible"""
    wait = WebDriverWait(context.driver, 10)
    ai_heading = wait.until(EC.presence_of_element_located((By.XPATH, "//h3[contains(text(), '🤖 AI Financial Advisor')]")))
    assert ai_heading.is_displayed()


@then('I should see "{text}"')
def step_verify_text_present(context, text):
    """Verify specific text is present on the page"""
    wait = WebDriverWait(context.driver, 10)
    element = wait.until(EC.presence_of_element_located((By.XPATH, f"//*[contains(text(), '{text}')]")))
    assert element.is_displayed()


@then('the AI should automatically analyze my financial situation')
def step_ai_analyzes_situation(context):
    """Verify AI starts analyzing financial situation"""
    wait = WebDriverWait(context.driver, 10)
    try:
        loading_element = wait.until(EC.presence_of_element_located((By.XPATH, "//*[contains(text(), 'analyzing')]")))
        assert loading_element.is_displayed()
    except TimeoutException:
        # Analysis might complete quickly, check for completed state
        pass


@when('the AI analysis completes')
def step_ai_analysis_completes(context):
    """Wait for AI analysis to complete"""
    wait = WebDriverWait(context.driver, 15)
    try:
        # Wait for analysis completion indicator
        wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, '[data-testid="ai-analysis-complete"]')))
    except TimeoutException:
        # If no specific indicator, wait for key sections to appear
        wait.until(EC.presence_of_element_located((By.XPATH, "//h3[contains(text(), 'AI Financial Health Score')]")))


@given('the AI Financial Advisor has completed analysis')
def step_ai_analysis_completed(context):
    """Ensure AI analysis has completed"""
    step_access_ai_advisor(context)
    step_ai_analysis_completes(context)


@when('I view the personalized FIRE plan section')
def step_view_fire_plan(context):
    """View the personalized FIRE plan section"""
    wait = WebDriverWait(context.driver, 10)
    fire_plan = wait.until(EC.presence_of_element_located((By.XPATH, "//h3[contains(text(), 'Your Personalized FIRE Plan')]")))
    context.driver.execute_script("arguments[0].scrollIntoView();", fire_plan)


@then('I should see {number:d} numbered plan steps')
def step_verify_plan_steps(context, number):
    """Verify the number of plan steps"""
    wait = WebDriverWait(context.driver, 10)
    steps = wait.until(EC.presence_of_all_elements_located((By.CSS_SELECTOR, '[data-testid="plan-step"]')))
    assert len(steps) == number, f"Expected {number} plan steps, found {len(steps)}"


@when('I view the AI insights section')
def step_view_ai_insights(context):
    """View the AI insights section"""
    wait = WebDriverWait(context.driver, 10)
    insights = wait.until(EC.presence_of_element_located((By.XPATH, "//h3[contains(text(), 'AI Insights & Recommendations')]")))
    context.driver.execute_script("arguments[0].scrollIntoView();", insights)


@when('I select "{option}" from the category filter')
def step_select_category_filter(context, option):
    """Select option from category filter"""
    wait = WebDriverWait(context.driver, 10)
    filter_select = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, '[data-testid="insights-category-filter"]')))
    select = Select(filter_select)
    
    # Map display names to values
    option_mapping = {
        'All Insights': 'all',
        'Optimization': 'optimization',
        'Risk Management': 'risk',
        'Swiss Specific': 'swiss'
    }
    
    if option in option_mapping:
        select.select_by_value(option_mapping[option])
    else:
        select.select_by_visible_text(option)
    
    time.sleep(0.5)  # Allow filter to apply


@when('I access the AI chat section')
def step_access_ai_chat(context):
    """Access the AI chat section"""
    wait = WebDriverWait(context.driver, 10)
    chat_section = wait.until(EC.presence_of_element_located((By.XPATH, "//h3[contains(text(), 'Ask Your AI Financial Advisor')]")))
    context.driver.execute_script("arguments[0].scrollIntoView();", chat_section)


@when('I type "{message}" in the chat')
def step_type_chat_message(context, message):
    """Type a message in the AI chat"""
    wait = WebDriverWait(context.driver, 10)
    chat_input = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, '[data-testid="ai-chat-input"]')))
    chat_input.clear()
    chat_input.send_keys(message)


@when('I click the send button')
def step_click_send_button(context):
    """Click the chat send button"""
    wait = WebDriverWait(context.driver, 10)
    send_button = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, '[data-testid="ai-chat-send"]')))
    send_button.click()


@then('I should receive an AI response within {seconds:d} seconds')
def step_verify_ai_response(context, seconds):
    """Verify AI response is received within specified time"""
    wait = WebDriverWait(context.driver, seconds)
    ai_message = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, '[data-testid="ai-message"]')))
    assert ai_message.is_displayed()


# Risk Assessment Steps
@when('I access the Risk Assessment Metrics section')
def step_access_risk_assessment(context):
    """Access the Risk Assessment Metrics section"""
    wait = WebDriverWait(context.driver, 10)
    risk_section = wait.until(EC.presence_of_element_located((By.XPATH, "//h3[contains(text(), 'Risk Assessment Metrics')]")))
    context.driver.execute_script("arguments[0].scrollIntoView();", risk_section)


@then('I should see the Risk Assessment Metrics component')
def step_verify_risk_assessment_component(context):
    """Verify Risk Assessment Metrics component is visible"""
    wait = WebDriverWait(context.driver, 10)
    risk_heading = wait.until(EC.presence_of_element_located((By.XPATH, "//h3[contains(text(), '⚠️ Risk Assessment Metrics')]")))
    assert risk_heading.is_displayed()


@when('the risk analysis completes')
def step_risk_analysis_completes(context):
    """Wait for risk analysis to complete"""
    wait = WebDriverWait(context.driver, 15)
    try:
        wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, '[data-testid="risk-analysis-complete"]')))
    except TimeoutException:
        wait.until(EC.presence_of_element_located((By.XPATH, "//h3[contains(text(), 'Overall Risk Profile')]")))


@given('the Risk Assessment has completed analysis')
def step_risk_analysis_completed(context):
    """Ensure risk analysis has completed"""
    step_access_risk_assessment(context)
    step_risk_analysis_completes(context)


# Safe Withdrawal Rate Steps
@when('I access the Safe Withdrawal Rate Analysis section')
def step_access_withdrawal_analysis(context):
    """Access the Safe Withdrawal Rate Analysis section"""
    wait = WebDriverWait(context.driver, 10)
    withdrawal_section = wait.until(EC.presence_of_element_located((By.XPATH, "//h3[contains(text(), 'Safe Withdrawal Rate Analysis')]")))
    context.driver.execute_script("arguments[0].scrollIntoView();", withdrawal_section)


@then('I should see the Safe Withdrawal Rate Analysis component')
def step_verify_withdrawal_analysis_component(context):
    """Verify Safe Withdrawal Rate Analysis component is visible"""
    wait = WebDriverWait(context.driver, 10)
    withdrawal_heading = wait.until(EC.presence_of_element_located((By.XPATH, "//h3[contains(text(), '📊 Safe Withdrawal Rate Analysis')]")))
    assert withdrawal_heading.is_displayed()


@when('the withdrawal analysis completes')
def step_withdrawal_analysis_completes(context):
    """Wait for withdrawal analysis to complete"""
    wait = WebDriverWait(context.driver, 20)  # Monte Carlo takes longer
    try:
        wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, '[data-testid="withdrawal-analysis-complete"]')))
    except TimeoutException:
        wait.until(EC.presence_of_element_located((By.XPATH, "//h3[contains(text(), 'Current Rate Assessment')]")))


@given('the Safe Withdrawal Rate Analysis has completed')
def step_withdrawal_analysis_completed(context):
    """Ensure withdrawal analysis has completed"""
    step_access_withdrawal_analysis(context)
    step_withdrawal_analysis_completes(context)


# Historical Tracking Steps
@when('I access the Historical Tracking Charts section')
def step_access_historical_tracking(context):
    """Access the Historical Tracking Charts section"""
    wait = WebDriverWait(context.driver, 10)
    tracking_section = wait.until(EC.presence_of_element_located((By.XPATH, "//h3[contains(text(), 'Historical Tracking')]")))
    context.driver.execute_script("arguments[0].scrollIntoView();", tracking_section)


@then('I should see the Historical Tracking Charts component')
def step_verify_historical_tracking_component(context):
    """Verify Historical Tracking Charts component is visible"""
    wait = WebDriverWait(context.driver, 10)
    tracking_heading = wait.until(EC.presence_of_element_located((By.XPATH, "//h3[contains(text(), '📈 Historical Tracking')]")))
    assert tracking_heading.is_displayed()


@when('I click "{button_text}"')
def step_click_button(context, button_text):
    """Click a button with specific text"""
    wait = WebDriverWait(context.driver, 10)
    button = wait.until(EC.element_to_be_clickable((By.XPATH, f"//button[contains(text(), '{button_text}')]")))
    button.click()


@given('I have generated sample historical data')
def step_generate_sample_data(context):
    """Generate sample historical data"""
    step_access_historical_tracking(context)
    step_click_button(context, "Generate Sample Data")
    wait = WebDriverWait(context.driver, 10)
    wait.until(EC.presence_of_element_located((By.XPATH, "//h3[contains(text(), 'Summary Statistics')]")))


# Canton Relocation Steps
@when('I access the Canton Relocation Tax Optimizer section')
def step_access_canton_relocation(context):
    """Access the Canton Relocation Tax Optimizer section"""
    wait = WebDriverWait(context.driver, 10)
    canton_section = wait.until(EC.presence_of_element_located((By.XPATH, "//h3[contains(text(), 'Canton Relocation')]")))
    context.driver.execute_script("arguments[0].scrollIntoView();", canton_section)


@then('I should see the Canton Relocation Tax Optimizer component')
def step_verify_canton_relocation_component(context):
    """Verify Canton Relocation Tax Optimizer component is visible"""
    wait = WebDriverWait(context.driver, 10)
    canton_heading = wait.until(EC.presence_of_element_located((By.XPATH, "//h3[contains(text(), '🏛️ Canton Relocation')]")))
    assert canton_heading.is_displayed()


@when('the relocation analysis completes')
def step_relocation_analysis_completes(context):
    """Wait for relocation analysis to complete"""
    wait = WebDriverWait(context.driver, 15)
    try:
        wait.until(EC.presence_of_element_located((By.XPATH, "//*[contains(text(), '(ZG)') or contains(text(), '(SZ)')]")))
    except TimeoutException:
        pass  # Analysis might complete quickly


@given('the Canton Relocation Analysis has completed')
def step_relocation_analysis_completed(context):
    """Ensure relocation analysis has completed"""
    step_access_canton_relocation(context)
    step_relocation_analysis_completes(context)


# Healthcare Cost Optimizer Steps
@when('I access the Healthcare Cost Optimizer section')
def step_access_healthcare_optimizer(context):
    """Access the Healthcare Cost Optimizer section"""
    wait = WebDriverWait(context.driver, 10)
    healthcare_section = wait.until(EC.presence_of_element_located((By.XPATH, "//h3[contains(text(), 'Healthcare Cost')]")))
    context.driver.execute_script("arguments[0].scrollIntoView();", healthcare_section)


@then('I should see the Healthcare Cost Optimizer component')
def step_verify_healthcare_optimizer_component(context):
    """Verify Healthcare Cost Optimizer component is visible"""
    wait = WebDriverWait(context.driver, 10)
    healthcare_heading = wait.until(EC.presence_of_element_located((By.XPATH, "//h3[contains(text(), 'Healthcare Cost')]")))
    assert healthcare_heading.is_displayed()


@when('the healthcare analysis completes')
def step_healthcare_analysis_completes(context):
    """Wait for healthcare analysis to complete"""
    wait = WebDriverWait(context.driver, 10)
    try:
        wait.until(EC.presence_of_element_located((By.XPATH, "//h3[contains(text(), 'Current Healthcare Analysis')]")))
    except TimeoutException:
        pass


@given('the Healthcare Cost Optimizer has completed analysis')
def step_healthcare_analysis_completed(context):
    """Ensure healthcare analysis has completed"""
    step_access_healthcare_optimizer(context)
    step_healthcare_analysis_completes(context)


# Common Verification Steps
@then('I should see all {number:d} risk metrics')
def step_verify_risk_metrics_count(context, number):
    """Verify the number of risk metrics displayed"""
    wait = WebDriverWait(context.driver, 10)

    # Check for each risk metric in the table
    for row in context.table:
        metric_name = row['Risk Metric']
        metric_element = wait.until(EC.presence_of_element_located((By.XPATH, f"//*[contains(text(), '{metric_name}')]")))
        assert metric_element.is_displayed(), f"Risk metric '{metric_name}' not found"


@then('I should see {number:d} different withdrawal rate scenarios')
def step_verify_withdrawal_scenarios(context, number):
    """Verify the number of withdrawal rate scenarios"""
    wait = WebDriverWait(context.driver, 10)

    # Check for each scenario in the table
    for row in context.table:
        rate = row['Withdrawal Rate']
        approach = row['Approach']

        rate_element = wait.until(EC.presence_of_element_located((By.XPATH, f"//*[contains(text(), '{rate} Withdrawal Rate')]")))
        assert rate_element.is_displayed(), f"Withdrawal rate '{rate}' not found"

        approach_element = wait.until(EC.presence_of_element_located((By.XPATH, f"//*[contains(text(), '{approach} APPROACH')]")))
        assert approach_element.is_displayed(), f"Approach '{approach}' not found"


@then('I should see timeframe buttons')
def step_verify_timeframe_buttons(context):
    """Verify timeframe buttons are present"""
    wait = WebDriverWait(context.driver, 10)

    for row in context.table:
        timeframe = row['Timeframe']
        button = wait.until(EC.presence_of_element_located((By.XPATH, f"//button[contains(text(), '{timeframe}')]")))
        assert button.is_displayed(), f"Timeframe button '{timeframe}' not found"


@then('I should see metric checkboxes')
def step_verify_metric_checkboxes(context):
    """Verify metric checkboxes are present"""
    wait = WebDriverWait(context.driver, 10)

    for row in context.table:
        metric = row['Metric']
        checkbox_label = wait.until(EC.presence_of_element_located((By.XPATH, f"//label[contains(text(), '{metric}')]")))
        assert checkbox_label.is_displayed(), f"Metric checkbox '{metric}' not found"


@then('I should see table headers')
def step_verify_table_headers(context):
    """Verify table headers are present"""
    wait = WebDriverWait(context.driver, 10)

    for row in context.table:
        header = row['Header']
        header_element = wait.until(EC.presence_of_element_located((By.XPATH, f"//th[contains(text(), '{header}')]")))
        assert header_element.is_displayed(), f"Table header '{header}' not found"


@then('I should see all summary metrics')
def step_verify_summary_metrics(context):
    """Verify summary metrics are present"""
    wait = WebDriverWait(context.driver, 10)

    for row in context.table:
        metric = row['Metric']
        metric_element = wait.until(EC.presence_of_element_located((By.XPATH, f"//*[contains(text(), '{metric}')]")))
        assert metric_element.is_displayed(), f"Summary metric '{metric}' not found"


@then('I should see all {number:d} risk factors')
def step_verify_risk_factors(context, number):
    """Verify risk factors are present"""
    wait = WebDriverWait(context.driver, 10)

    for row in context.table:
        risk_factor = row['Risk Factor']
        factor_element = wait.until(EC.presence_of_element_located((By.XPATH, f"//*[contains(text(), '{risk_factor}')]")))
        assert factor_element.is_displayed(), f"Risk factor '{risk_factor}' not found"


@then('I should see analysis for all Swiss deductible levels')
def step_verify_deductible_levels(context):
    """Verify all Swiss deductible levels are analyzed"""
    wait = WebDriverWait(context.driver, 10)

    for row in context.table:
        deductible = row['Deductible']
        deductible_type = row['Type']

        deductible_element = wait.until(EC.presence_of_element_located((By.XPATH, f"//*[contains(text(), '{deductible}')]")))
        assert deductible_element.is_displayed(), f"Deductible '{deductible}' not found"


@then('I should see low-tax cantons like')
def step_verify_low_tax_cantons(context):
    """Verify low-tax cantons are displayed"""
    wait = WebDriverWait(context.driver, 10)

    for row in context.table:
        canton = row['Canton']
        canton_element = wait.until(EC.presence_of_element_located((By.XPATH, f"//*[contains(text(), '{canton}')]")))
        assert canton_element.is_displayed(), f"Canton '{canton}' not found"


@then('I should see key metrics for each canton')
def step_verify_canton_metrics(context):
    """Verify key metrics are displayed for cantons"""
    wait = WebDriverWait(context.driver, 10)

    for row in context.table:
        metric = row['Metric']
        metric_element = wait.until(EC.presence_of_element_located((By.XPATH, f"//*[contains(text(), '{metric}')]")))
        assert metric_element.is_displayed(), f"Canton metric '{metric}' not found"


@then('I should see recommendation levels')
def step_verify_recommendation_levels(context):
    """Verify recommendation levels are displayed"""
    wait = WebDriverWait(context.driver, 10)

    for row in context.table:
        level = row['Level']
        icon = row['Icon']

        level_element = wait.until(EC.presence_of_element_located((By.XPATH, f"//*[contains(text(), '{level}') or contains(text(), '{icon}')]")))
        assert level_element.is_displayed(), f"Recommendation level '{level}' not found"


# Mobile and Performance Steps
@given('I am using a mobile device')
def step_set_mobile_viewport(context):
    """Set mobile viewport for testing"""
    context.driver.set_window_size(375, 667)  # iPhone 6/7/8 size


@given('I have enabled dark mode')
def step_enable_dark_mode(context):
    """Enable dark mode for testing"""
    # This would typically involve clicking a dark mode toggle
    # For now, we'll assume dark mode is enabled through other means
    pass


@then('the analysis should complete within {seconds:d} seconds')
def step_verify_analysis_performance(context, seconds):
    """Verify analysis completes within specified time"""
    start_time = time.time()

    # Wait for analysis to complete
    wait = WebDriverWait(context.driver, seconds)
    try:
        wait.until(EC.presence_of_element_located((By.XPATH, "//*[contains(text(), 'complete') or contains(text(), 'Analysis') or contains(text(), 'Score')]")))
    except TimeoutException:
        elapsed_time = time.time() - start_time
        assert False, f"Analysis took longer than {seconds} seconds (actual: {elapsed_time:.1f}s)"


@then('all components should be visible and functional on mobile')
def step_verify_mobile_functionality(context):
    """Verify components work on mobile"""
    # Basic check that main content is visible
    wait = WebDriverWait(context.driver, 10)
    main_content = wait.until(EC.presence_of_element_located((By.TAG_NAME, "main")))
    assert main_content.is_displayed()


@then('the system should handle the edge case gracefully')
def step_verify_graceful_error_handling(context):
    """Verify system handles edge cases without crashing"""
    # Check that no error messages are displayed
    error_indicators = context.driver.find_elements(By.XPATH, "//*[contains(text(), 'Error') or contains(text(), 'error') or contains(text(), 'ERROR')]")

    # Filter out expected error-related text (like "Error Boundary" in component names)
    actual_errors = [elem for elem in error_indicators if 'boundary' not in elem.text.lower()]

    assert len(actual_errors) == 0, f"Found error messages: {[elem.text for elem in actual_errors]}"


@then('I should not see any error messages')
def step_verify_no_errors(context):
    """Verify no error messages are displayed"""
    step_verify_graceful_error_handling(context)


@then('the component should not crash')
def step_verify_no_crash(context):
    """Verify component doesn't crash"""
    # Check that the page is still responsive
    wait = WebDriverWait(context.driver, 5)
    body = wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
    assert body.is_displayed()


# Data Update Steps
@when('I update my monthly income to {amount:d}')
def step_update_monthly_income(context, amount):
    """Update monthly income field"""
    wait = WebDriverWait(context.driver, 10)
    income_field = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, '[data-testid="monthly-income"]')))
    income_field.clear()
    income_field.send_keys(str(amount))


@when('I update my current savings to {amount:d}')
def step_update_current_savings(context, amount):
    """Update current savings field"""
    wait = WebDriverWait(context.driver, 10)
    savings_field = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, '[data-testid="current-savings"]')))
    savings_field.clear()
    savings_field.send_keys(str(amount))


@when('I update my age to {age:d}')
def step_update_age(context, age):
    """Update age field"""
    wait = WebDriverWait(context.driver, 10)
    age_field = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, '[data-testid="current-age"]')))
    age_field.clear()
    age_field.send_keys(str(age))
