"""
Step definitions for advanced user journey scenarios
Supports comprehensive FIRE planning workflows and complex user interactions
"""

from behave import given, when, then, step
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import <PERSON>Chains
from selenium.common.exceptions import TimeoutException
import time
import json
import re
from decimal import Decimal


@given('I am a {age:d}-year-old {profession} planning for FIRE')
def step_given_fire_planner_profile(context, age, profession):
    """Set up user profile for FIRE planning scenario"""
    context.user_profile = {
        'age': age,
        'profession': profession,
        'planning_type': 'FIRE',
        'target_retirement_age': age + 22  # Default 22 years to FIRE
    }
    context.execute_steps(f'''
        Given I am on the Swiss Budget Pro homepage
        And the application has loaded successfully
    ''')


@when('I start the comprehensive FIRE planning workflow')
def step_when_start_fire_workflow(context):
    """Initiate the comprehensive FIRE planning workflow"""
    try:
        # Look for FIRE planning button or wizard
        fire_button = WebDriverWait(context.driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'FIRE Planning') or contains(text(), 'Start Planning')]"))
        )
        fire_button.click()
        
        # Wait for workflow to initialize
        WebDriverWait(context.driver, 10).until(
            EC.presence_of_element_located((By.CLASS_NAME, "fire-workflow"))
        )
        
        context.workflow_started = True
        
    except TimeoutException:
        # Fallback: navigate to planning section
        planning_nav = context.driver.find_element(By.XPATH, "//nav//a[contains(text(), 'Planning')]")
        planning_nav.click()
        context.workflow_started = True


@when('I enter my basic information')
def step_when_enter_basic_info(context):
    """Enter basic financial information from the table"""
    for row in context.table:
        field_name = row['Field']
        field_value = row['Value']
        
        # Map field names to input selectors
        field_mapping = {
            'Age': "input[name='age'], input[placeholder*='age']",
            'Canton': "select[name='canton'], select[placeholder*='canton']",
            'Civil Status': "select[name='civilStatus'], select[placeholder*='civil']",
            'Monthly Gross Income': "input[name='monthlyIncome'], input[placeholder*='income']",
            'Current Savings': "input[name='currentSavings'], input[placeholder*='savings']",
            'Target FIRE Age': "input[name='targetAge'], input[placeholder*='retirement']"
        }
        
        try:
            selector = field_mapping.get(field_name, f"input[name='{field_name.lower().replace(' ', '')}']")
            element = WebDriverWait(context.driver, 5).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, selector))
            )
            
            if element.tag_name == 'select':
                from selenium.webdriver.support.ui import Select
                select = Select(element)
                select.select_by_visible_text(field_value)
            else:
                element.clear()
                element.send_keys(field_value)
                
        except TimeoutException:
            print(f"Warning: Could not find field {field_name}")


@when('I proceed to the {step_name} step')
def step_when_proceed_to_step(context, step_name):
    """Navigate to the next step in the workflow"""
    try:
        # Look for Next button or specific step button
        next_button = WebDriverWait(context.driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, f"//button[contains(text(), 'Next') or contains(text(), '{step_name}')]"))
        )
        next_button.click()
        
        # Wait for step to load
        time.sleep(2)
        
        # Verify we're on the correct step
        step_indicator = context.driver.find_elements(By.XPATH, f"//*[contains(text(), '{step_name}')]")
        assert len(step_indicator) > 0, f"Failed to navigate to {step_name} step"
        
    except TimeoutException:
        print(f"Warning: Could not find navigation to {step_name}")


@when('I categorize my monthly expenses')
def step_when_categorize_expenses(context):
    """Categorize monthly expenses from the table"""
    for row in context.table:
        category = row['Category']
        amount = row['Amount']
        essential = row['Essential'] == 'Yes'
        
        try:
            # Find expense input for this category
            category_input = WebDriverWait(context.driver, 5).until(
                EC.presence_of_element_located((By.XPATH, f"//input[@data-category='{category}' or @placeholder*='{category}']"))
            )
            category_input.clear()
            category_input.send_keys(amount)
            
            # Set essential/non-essential if checkbox exists
            if essential:
                essential_checkbox = context.driver.find_elements(By.XPATH, f"//input[@type='checkbox' and @data-category='{category}']")
                if essential_checkbox and not essential_checkbox[0].is_selected():
                    essential_checkbox[0].click()
                    
        except TimeoutException:
            print(f"Warning: Could not find expense input for {category}")


@when('I set up my savings goals')
def step_when_setup_savings_goals(context):
    """Set up savings goals from the table"""
    for row in context.table:
        goal_type = row['Goal Type']
        target_amount = row['Target Amount']
        priority = row['Priority']
        timeline = row['Timeline']
        
        try:
            # Add new goal if needed
            add_goal_button = context.driver.find_elements(By.XPATH, "//button[contains(text(), 'Add Goal')]")
            if add_goal_button:
                add_goal_button[0].click()
                time.sleep(1)
            
            # Fill goal details
            goal_name_input = WebDriverWait(context.driver, 5).until(
                EC.presence_of_element_located((By.XPATH, "//input[@placeholder*='goal name' or @name='goalName']"))
            )
            goal_name_input.clear()
            goal_name_input.send_keys(goal_type)
            
            target_input = context.driver.find_element(By.XPATH, "//input[@placeholder*='target' or @name='targetAmount']")
            target_input.clear()
            target_input.send_keys(target_amount)
            
            # Set priority if dropdown exists
            priority_select = context.driver.find_elements(By.XPATH, "//select[@name='priority']")
            if priority_select:
                from selenium.webdriver.support.ui import Select
                select = Select(priority_select[0])
                select.select_by_visible_text(priority)
                
        except TimeoutException:
            print(f"Warning: Could not set up goal {goal_type}")


@when('I configure my investment allocation')
def step_when_configure_investment_allocation(context):
    """Configure investment allocation from the table"""
    for row in context.table:
        asset_class = row['Asset Class']
        allocation = row['Allocation']
        expected_return = row['Expected Return']
        risk_level = row['Risk Level']
        
        try:
            # Find allocation slider or input for this asset class
            allocation_input = WebDriverWait(context.driver, 5).until(
                EC.presence_of_element_located((By.XPATH, f"//input[@data-asset='{asset_class}' or contains(@placeholder, '{asset_class}')]"))
            )
            
            # Clear and set allocation percentage
            allocation_value = allocation.replace('%', '')
            allocation_input.clear()
            allocation_input.send_keys(allocation_value)
            
            # Set expected return if field exists
            return_input = context.driver.find_elements(By.XPATH, f"//input[@data-return='{asset_class}']")
            if return_input:
                return_value = expected_return.replace('%', '')
                return_input[0].clear()
                return_input[0].send_keys(return_value)
                
        except TimeoutException:
            print(f"Warning: Could not configure allocation for {asset_class}")


@when('I run Monte Carlo simulations with stress testing')
def step_when_run_monte_carlo_simulations(context):
    """Run Monte Carlo simulations with stress testing"""
    try:
        # Find and click Monte Carlo simulation button
        monte_carlo_button = WebDriverWait(context.driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Monte Carlo') or contains(text(), 'Stress Test')]"))
        )
        monte_carlo_button.click()
        
        # Configure simulation parameters if available
        iterations_input = context.driver.find_elements(By.XPATH, "//input[@name='iterations' or @placeholder*='iterations']")
        if iterations_input:
            iterations_input[0].clear()
            iterations_input[0].send_keys("5000")
        
        # Start simulation
        run_button = WebDriverWait(context.driver, 5).until(
            EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Run Simulation') or contains(text(), 'Calculate')]"))
        )
        run_button.click()
        
        # Wait for simulation to complete
        WebDriverWait(context.driver, 30).until(
            EC.presence_of_element_located((By.XPATH, "//*[contains(text(), 'Success Rate') or contains(text(), 'Simulation Complete')]"))
        )
        
        context.monte_carlo_completed = True
        
    except TimeoutException:
        print("Warning: Could not complete Monte Carlo simulation")
        context.monte_carlo_completed = False


@then('I should see my FIRE timeline is approximately {years:d} years')
def step_then_see_fire_timeline(context, years):
    """Verify FIRE timeline calculation"""
    try:
        timeline_element = WebDriverWait(context.driver, 10).until(
            EC.presence_of_element_located((By.XPATH, "//*[contains(text(), 'years') and contains(text(), 'FIRE')]"))
        )
        
        timeline_text = timeline_element.text
        # Extract number from text like "22 years to FIRE"
        timeline_match = re.search(r'(\d+)', timeline_text)
        
        if timeline_match:
            actual_years = int(timeline_match.group(1))
            # Allow for ±2 years tolerance
            assert abs(actual_years - years) <= 2, f"Expected ~{years} years, got {actual_years} years"
        else:
            print(f"Warning: Could not parse timeline from '{timeline_text}'")
            
    except TimeoutException:
        print("Warning: Could not find FIRE timeline element")


@then('I should see my FIRE number is {amount:d} CHF')
def step_then_see_fire_number(context, amount):
    """Verify FIRE number calculation"""
    try:
        fire_number_element = WebDriverWait(context.driver, 10).until(
            EC.presence_of_element_located((By.XPATH, "//*[contains(text(), 'CHF') and (contains(text(), 'FIRE') or contains(text(), 'target'))]"))
        )
        
        fire_number_text = fire_number_element.text
        # Extract number from text like "CHF 2,500,000"
        fire_number_match = re.search(r'CHF\s*([\d,\']+)', fire_number_text)
        
        if fire_number_match:
            actual_amount_str = fire_number_match.group(1).replace(',', '').replace('\'', '')
            actual_amount = int(actual_amount_str)
            # Allow for ±10% tolerance
            tolerance = amount * 0.1
            assert abs(actual_amount - amount) <= tolerance, f"Expected ~{amount} CHF, got {actual_amount} CHF"
        else:
            print(f"Warning: Could not parse FIRE number from '{fire_number_text}'")
            
    except TimeoutException:
        print("Warning: Could not find FIRE number element")


@then('I should see my savings rate is approximately {rate:d}%')
def step_then_see_savings_rate(context, rate):
    """Verify savings rate calculation"""
    try:
        savings_rate_element = WebDriverWait(context.driver, 10).until(
            EC.presence_of_element_located((By.XPATH, "//*[contains(text(), '%') and contains(text(), 'savings')]"))
        )
        
        savings_rate_text = savings_rate_element.text
        # Extract percentage from text like "42% savings rate"
        rate_match = re.search(r'(\d+)%', savings_rate_text)
        
        if rate_match:
            actual_rate = int(rate_match.group(1))
            # Allow for ±3% tolerance
            assert abs(actual_rate - rate) <= 3, f"Expected ~{rate}%, got {actual_rate}%"
        else:
            print(f"Warning: Could not parse savings rate from '{savings_rate_text}'")
            
    except TimeoutException:
        print("Warning: Could not find savings rate element")


@then('I should see Monte Carlo success rate is above {min_rate:d}%')
def step_then_see_monte_carlo_success_rate(context, min_rate):
    """Verify Monte Carlo simulation success rate"""
    if not getattr(context, 'monte_carlo_completed', False):
        print("Warning: Monte Carlo simulation was not completed")
        return
        
    try:
        success_rate_element = WebDriverWait(context.driver, 10).until(
            EC.presence_of_element_located((By.XPATH, "//*[contains(text(), '%') and (contains(text(), 'success') or contains(text(), 'Success'))]"))
        )
        
        success_rate_text = success_rate_element.text
        # Extract percentage from text like "Success Rate: 85%"
        rate_match = re.search(r'(\d+)%', success_rate_text)
        
        if rate_match:
            actual_rate = int(rate_match.group(1))
            assert actual_rate >= min_rate, f"Expected success rate ≥{min_rate}%, got {actual_rate}%"
        else:
            print(f"Warning: Could not parse success rate from '{success_rate_text}'")
            
    except TimeoutException:
        print("Warning: Could not find Monte Carlo success rate element")


@then('I should see detailed risk metrics including VaR and Expected Shortfall')
def step_then_see_risk_metrics(context):
    """Verify presence of detailed risk metrics"""
    risk_metrics = ['VaR', 'Value at Risk', 'Expected Shortfall', 'Percentile']
    
    found_metrics = []
    for metric in risk_metrics:
        try:
            metric_element = WebDriverWait(context.driver, 5).until(
                EC.presence_of_element_located((By.XPATH, f"//*[contains(text(), '{metric}')]"))
            )
            found_metrics.append(metric)
        except TimeoutException:
            continue
    
    assert len(found_metrics) >= 2, f"Expected to find at least 2 risk metrics, found: {found_metrics}"


@then('the complete workflow should complete within {max_seconds:d} seconds')
def step_then_workflow_completes_within_time(context, max_seconds):
    """Verify workflow completion time"""
    workflow_start_time = getattr(context, 'workflow_start_time', time.time())
    current_time = time.time()
    elapsed_time = current_time - workflow_start_time
    
    assert elapsed_time <= max_seconds, f"Workflow took {elapsed_time:.1f}s, expected ≤{max_seconds}s"


@given('I am a {age:d}-year-old family with {children:d} children planning for FIRE')
def step_given_family_fire_planner(context, age, children):
    """Set up family profile for FIRE planning"""
    context.user_profile = {
        'age': age,
        'family_type': 'family',
        'children_count': children,
        'planning_type': 'FIRE',
        'civil_status': 'married'
    }
    context.execute_steps(f'''
        Given I am on the Swiss Budget Pro homepage
        And the application has loaded successfully
    ''')


@when('I enter my family information')
def step_when_enter_family_info(context):
    """Enter family-specific information"""
    for row in context.table:
        field_name = row['Field']
        field_value = row['Value']
        
        # Family-specific field mapping
        field_mapping = {
            'Age': "input[name='age']",
            'Canton': "select[name='canton']",
            'Civil Status': "select[name='civilStatus']",
            'Children': "input[name='children'], select[name='children']",
            'Combined Gross Income': "input[name='combinedIncome'], input[name='monthlyIncome']",
            'Current Savings': "input[name='currentSavings']",
            'Target FIRE Age': "input[name='targetAge']"
        }
        
        try:
            selector = field_mapping.get(field_name, f"input[name='{field_name.lower().replace(' ', '')}']")
            element = WebDriverWait(context.driver, 5).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, selector))
            )
            
            if element.tag_name == 'select':
                from selenium.webdriver.support.ui import Select
                select = Select(element)
                select.select_by_visible_text(field_value)
            else:
                element.clear()
                element.send_keys(field_value)
                
        except TimeoutException:
            print(f"Warning: Could not find family field {field_name}")


@when('I categorize my family monthly expenses')
def step_when_categorize_family_expenses(context):
    """Categorize family-specific monthly expenses"""
    for row in context.table:
        category = row['Category']
        amount = row['Amount']
        essential = row['Essential'] == 'Yes'
        family_factor = row.get('Family Factor', 'Medium')
        
        try:
            # Find expense input for this category
            category_input = WebDriverWait(context.driver, 5).until(
                EC.presence_of_element_located((By.XPATH, f"//input[@data-category='{category}' or contains(@placeholder, '{category}')]"))
            )
            category_input.clear()
            category_input.send_keys(amount)
            
            # Set essential/non-essential
            if essential:
                essential_checkbox = context.driver.find_elements(By.XPATH, f"//input[@type='checkbox' and @data-category='{category}']")
                if essential_checkbox and not essential_checkbox[0].is_selected():
                    essential_checkbox[0].click()
            
            # Set family factor if available
            family_factor_select = context.driver.find_elements(By.XPATH, f"//select[@data-family-factor='{category}']")
            if family_factor_select:
                from selenium.webdriver.support.ui import Select
                select = Select(family_factor_select[0])
                select.select_by_visible_text(family_factor)
                
        except TimeoutException:
            print(f"Warning: Could not find family expense input for {category}")


@then('I should see my family FIRE timeline is approximately {years:d} years')
def step_then_see_family_fire_timeline(context, years):
    """Verify family FIRE timeline calculation"""
    step_then_see_fire_timeline(context, years)


@then('I should see my family FIRE number is {amount:d} CHF')
def step_then_see_family_fire_number(context, amount):
    """Verify family FIRE number calculation"""
    step_then_see_fire_number(context, amount)


@then('I should see children education funding is on track')
def step_then_see_education_funding_on_track(context):
    """Verify children education funding status"""
    try:
        education_element = WebDriverWait(context.driver, 10).until(
            EC.presence_of_element_located((By.XPATH, "//*[contains(text(), 'education') and (contains(text(), 'on track') or contains(text(), 'funded'))]"))
        )
        
        education_text = education_element.text.lower()
        assert 'on track' in education_text or 'funded' in education_text, "Education funding not on track"
        
    except TimeoutException:
        print("Warning: Could not find education funding status")


@then('I should see family emergency fund recommendations')
def step_then_see_family_emergency_fund_recommendations(context):
    """Verify family emergency fund recommendations"""
    try:
        emergency_fund_element = WebDriverWait(context.driver, 10).until(
            EC.presence_of_element_located((By.XPATH, "//*[contains(text(), 'emergency') and contains(text(), 'family')]"))
        )

        # Family emergency fund should be higher than individual
        emergency_text = emergency_fund_element.text
        amount_match = re.search(r'CHF\s*([\d,\']+)', emergency_text)

        if amount_match:
            amount_str = amount_match.group(1).replace(',', '').replace('\'', '')
            amount = int(amount_str)
            # Family emergency fund should be at least 40,000 CHF
            assert amount >= 40000, f"Family emergency fund too low: {amount} CHF"

    except TimeoutException:
        print("Warning: Could not find family emergency fund recommendations")


@given('I have a comprehensive financial profile with')
def step_given_comprehensive_financial_profile(context):
    """Set up comprehensive financial profile for data export testing"""
    context.financial_profile = {}

    for row in context.table:
        category = row['Data Category']
        items_count = int(row['Items Count'])
        sample_data = row['Sample Data']

        context.financial_profile[category] = {
            'count': items_count,
            'sample': sample_data
        }

    # Simulate having this data in the application
    context.execute_steps('''
        Given I am on the Swiss Budget Pro homepage
        And the application has loaded successfully
    ''')


@when('I navigate to the data export section')
def step_when_navigate_to_data_export(context):
    """Navigate to data export functionality"""
    try:
        # Look for data management or export menu
        export_menu = WebDriverWait(context.driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, "//a[contains(text(), 'Export') or contains(text(), 'Data') or contains(text(), 'Settings')]"))
        )
        export_menu.click()

        # Wait for export section to load
        WebDriverWait(context.driver, 10).until(
            EC.presence_of_element_located((By.XPATH, "//*[contains(text(), 'Export') or contains(text(), 'Download')]"))
        )

    except TimeoutException:
        print("Warning: Could not find data export section")


@when('I select "{export_type}"')
def step_when_select_export_type(context, export_type):
    """Select specific export type"""
    try:
        export_option = WebDriverWait(context.driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, f"//button[contains(text(), '{export_type}') or contains(@value, '{export_type}')]"))
        )
        export_option.click()

    except TimeoutException:
        print(f"Warning: Could not find export type {export_type}")


@when('I choose export format "{format_type}"')
def step_when_choose_export_format(context, format_type):
    """Choose export format (JSON, CSV, etc.)"""
    try:
        format_selector = WebDriverWait(context.driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, f"//select[@name='format']//option[text()='{format_type}'] | //button[contains(text(), '{format_type}')]"))
        )
        format_selector.click()

    except TimeoutException:
        print(f"Warning: Could not select format {format_type}")


@when('I click "{button_text}"')
def step_when_click_button(context, button_text):
    """Click a button with specific text"""
    try:
        button = WebDriverWait(context.driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, f"//button[contains(text(), '{button_text}')]"))
        )
        button.click()

        # Store action time for timeout verification
        context.action_start_time = time.time()

    except TimeoutException:
        print(f"Warning: Could not find button '{button_text}'")


@then('I should see export progress indicator')
def step_then_see_export_progress(context):
    """Verify export progress indicator is shown"""
    try:
        progress_element = WebDriverWait(context.driver, 10).until(
            EC.presence_of_element_located((By.XPATH, "//*[contains(@class, 'progress') or contains(text(), 'Exporting') or contains(text(), 'Processing')]"))
        )

        assert progress_element.is_displayed(), "Export progress indicator not visible"

    except TimeoutException:
        print("Warning: Could not find export progress indicator")


@then('I should receive a {file_type} file within {max_seconds:d} seconds')
def step_then_receive_file_within_time(context, file_type, max_seconds):
    """Verify file download within specified time"""
    start_time = getattr(context, 'action_start_time', time.time())

    try:
        # Wait for download completion indicator or file download
        download_complete = WebDriverWait(context.driver, max_seconds).until(
            EC.any_of(
                EC.presence_of_element_located((By.XPATH, "//*[contains(text(), 'Download complete') or contains(text(), 'Export successful')]")),
                EC.presence_of_element_located((By.XPATH, f"//a[contains(@href, '.{file_type.lower()}') or contains(@download, '.{file_type.lower()}')]"))
            )
        )

        elapsed_time = time.time() - start_time
        assert elapsed_time <= max_seconds, f"File download took {elapsed_time:.1f}s, expected ≤{max_seconds}s"

        context.downloaded_file_type = file_type

    except TimeoutException:
        print(f"Warning: {file_type} file not received within {max_seconds} seconds")


@then('the {file_type} file should contain all financial data categories')
def step_then_file_contains_all_categories(context, file_type):
    """Verify exported file contains expected data categories"""
    # This would typically involve checking the actual downloaded file
    # For now, we'll verify the UI indicates successful export
    try:
        success_message = WebDriverWait(context.driver, 10).until(
            EC.presence_of_element_located((By.XPATH, "//*[contains(text(), 'Export successful') or contains(text(), 'All data exported')]"))
        )

        assert success_message.is_displayed(), f"{file_type} export did not complete successfully"

    except TimeoutException:
        print(f"Warning: Could not verify {file_type} file contents")


@then('the {file_type} file should include metadata')
def step_then_file_includes_metadata(context, file_type):
    """Verify exported file includes required metadata"""
    expected_metadata = {}

    for row in context.table:
        field = row['Field']
        expected_value = row['Expected Value']
        expected_metadata[field] = expected_value

    # In a real implementation, this would parse the downloaded file
    # For now, verify the export summary shows metadata
    try:
        metadata_section = WebDriverWait(context.driver, 10).until(
            EC.presence_of_element_located((By.XPATH, "//*[contains(text(), 'Export Date') or contains(text(), 'metadata')]"))
        )

        assert metadata_section.is_displayed(), "Export metadata not found"

    except TimeoutException:
        print("Warning: Could not verify file metadata")


@when('I configure CSV export options')
def step_when_configure_csv_options(context):
    """Configure CSV export options from table"""
    for row in context.table:
        option = row['Option']
        setting = row['Setting']

        try:
            # Map options to form elements
            option_mapping = {
                'Date Range': "select[name='dateRange']",
                'Include Headers': "input[name='includeHeaders']",
                'Decimal Separator': "select[name='decimalSeparator']",
                'Thousands Separator': "select[name='thousandsSeparator']",
                'Currency Format': "select[name='currencyFormat']",
                'Date Format': "select[name='dateFormat']"
            }

            selector = option_mapping.get(option, f"input[name='{option.lower().replace(' ', '')}']")
            element = WebDriverWait(context.driver, 5).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, selector))
            )

            if element.tag_name == 'select':
                from selenium.webdriver.support.ui import Select
                select = Select(element)
                select.select_by_visible_text(setting)
            elif element.get_attribute('type') == 'checkbox':
                if (setting.lower() == 'yes') != element.is_selected():
                    element.click()
            else:
                element.clear()
                element.send_keys(setting)

        except TimeoutException:
            print(f"Warning: Could not configure option {option}")


@when('I select data categories to export')
def step_when_select_data_categories(context):
    """Select specific data categories for export"""
    for row in context.table:
        category = row['Category']
        include = row['Include'] == 'Yes'
        granularity = row['Granularity']

        try:
            # Find checkbox for this category
            category_checkbox = WebDriverWait(context.driver, 5).until(
                EC.presence_of_element_located((By.XPATH, f"//input[@type='checkbox' and (@data-category='{category}' or contains(@name, '{category}'))]"))
            )

            if include != category_checkbox.is_selected():
                category_checkbox.click()

            # Set granularity if dropdown exists
            granularity_select = context.driver.find_elements(By.XPATH, f"//select[@data-granularity='{category}']")
            if granularity_select:
                from selenium.webdriver.support.ui import Select
                select = Select(granularity_select[0])
                select.select_by_visible_text(granularity)

        except TimeoutException:
            print(f"Warning: Could not configure category {category}")


@then('I should receive multiple CSV files in a ZIP archive')
def step_then_receive_csv_zip_archive(context):
    """Verify receipt of ZIP archive with multiple CSV files"""
    try:
        zip_download = WebDriverWait(context.driver, 15).until(
            EC.presence_of_element_located((By.XPATH, "//a[contains(@href, '.zip') or contains(@download, '.zip')] | //*[contains(text(), 'ZIP') and contains(text(), 'download')]"))
        )

        assert zip_download.is_displayed(), "ZIP archive download not available"
        context.downloaded_file_type = 'ZIP'

    except TimeoutException:
        print("Warning: Could not find ZIP archive download")


@then('the ZIP should contain files')
def step_then_zip_contains_files(context):
    """Verify ZIP archive contains expected files"""
    expected_files = {}

    for row in context.table:
        filename = row['Filename']
        expected_rows = int(row['Expected Rows'])
        key_columns = row['Key Columns']

        expected_files[filename] = {
            'rows': expected_rows,
            'columns': key_columns
        }

    # In a real implementation, this would extract and verify the ZIP contents
    # For now, verify the export summary lists the expected files
    try:
        file_list = WebDriverWait(context.driver, 10).until(
            EC.presence_of_element_located((By.XPATH, "//*[contains(text(), 'Files included') or contains(text(), 'Export summary')]"))
        )

        file_list_text = file_list.text
        for filename in expected_files.keys():
            assert filename in file_list_text, f"Expected file {filename} not found in export summary"

    except TimeoutException:
        print("Warning: Could not verify ZIP file contents")
