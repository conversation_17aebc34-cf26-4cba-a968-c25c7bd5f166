"""
Swiss Tax System BDD Step Definitions
Comprehensive step definitions for Swiss Tax Planning Dashboard and Social Insurance Calculator
"""

from behave import given, when, then
import time

# Swiss Tax Planning Dashboard Steps

@given('I navigate to the "Analysis" tab')
def step_navigate_to_analysis_tab(context):
    """Navigate to the Analysis tab."""
    context.page.click('[data-testid="analysis-tab"]')
    context.page.wait_for_timeout(1000)

@given('I select the "Tax Optimization" secondary tab')
def step_select_tax_optimization_tab(context):
    """Select the Tax Optimization secondary tab."""
    context.page.click('[data-testid="tax-optimization-tab"]')
    context.page.wait_for_timeout(1000)

@when('the Swiss Tax Planning Dashboard loads')
def step_swiss_tax_dashboard_loads(context):
    """Wait for Swiss Tax Planning Dashboard to load."""
    context.page.wait_for_selector('[data-testid="swiss-tax-dashboard"]', timeout=10000)

@then('I should see the dashboard header "{header_text}"')
def step_see_dashboard_header(context, header_text):
    """Verify dashboard header is displayed."""
    header = context.page.locator('h3').filter(has_text=header_text)
    assert header.is_visible()

@then('I should see the description "{description_text}"')
def step_see_description(context, description_text):
    """Verify description text is displayed."""
    description = context.page.locator('text=' + description_text)
    assert description.is_visible()

@then('I should see {count:d} navigation tabs: {tab_list}')
def step_see_navigation_tabs(context, count, tab_list):
    """Verify navigation tabs are present."""
    tabs = tab_list.split(', ')
    tabs = [tab.strip('"') for tab in tabs]
    
    for tab in tabs:
        tab_selector = f'[data-testid="{tab.lower().replace(" ", "-")}-tab"]'
        assert context.page.is_visible(tab_selector), f"Tab {tab} not visible"

@then('the "{tab_name}" tab should be selected by default')
def step_tab_selected_by_default(context, tab_name):
    """Verify default tab selection."""
    tab_selector = f'[data-testid="{tab_name.lower()}-tab"]'
    tab_element = context.page.locator(tab_selector)
    assert 'bg-blue-600' in tab_element.get_attribute('class')

@given('the Swiss Tax Planning Dashboard is loaded')
def step_swiss_tax_dashboard_loaded(context):
    """Ensure Swiss Tax Planning Dashboard is loaded."""
    context.page.wait_for_selector('[data-testid="swiss-tax-dashboard"]', timeout=10000)

@when('I click on the "{tab_name}" tab')
def step_click_tab(context, tab_name):
    """Click on a specific tab."""
    tab_selector = f'[data-testid="{tab_name.lower().replace(" ", "-")}-tab"]'
    context.page.click(tab_selector)
    context.page.wait_for_timeout(1000)

@then('I should see the Swiss Tax Optimization Engine')
def step_see_tax_optimization_engine(context):
    """Verify Tax Optimization Engine is displayed."""
    context.page.wait_for_selector('[data-testid="optimization-strategies"]', timeout=5000)
    assert context.page.is_visible('[data-testid="optimization-strategies"]')

@then('I should see the Swiss Tax Deduction Optimizer')
def step_see_tax_deduction_optimizer(context):
    """Verify Tax Deduction Optimizer is displayed."""
    context.page.wait_for_selector('[data-testid="optimization-potential"]', timeout=5000)
    assert context.page.is_visible('[data-testid="optimization-potential"]')

@then('I should see the tax planning scenarios')
def step_see_tax_planning_scenarios(context):
    """Verify tax planning scenarios are displayed."""
    context.page.wait_for_selector('[data-testid="scenario-buttons"]', timeout=5000)
    assert context.page.is_visible('[data-testid="scenario-buttons"]')

@then('I should see the current tax situation overview')
def step_see_current_tax_situation(context):
    """Verify current tax situation overview is displayed."""
    context.page.wait_for_selector('[data-testid="current-tax-situation"]', timeout=5000)
    assert context.page.is_visible('[data-testid="current-tax-situation"]')

@given('I am on the "{tab_name}" tab of the tax planning dashboard')
def step_on_tax_planning_tab(context, tab_name):
    """Navigate to specific tab of tax planning dashboard."""
    tab_selector = f'[data-testid="{tab_name.lower()}-tab"]'
    context.page.click(tab_selector)
    context.page.wait_for_timeout(1000)

@when('the tax analysis completes')
def step_tax_analysis_completes(context):
    """Wait for tax analysis to complete."""
    context.page.wait_for_selector('[data-testid="gross-annual-income"]', timeout=10000)

@then('I should see my "{field_name}" displayed in CHF')
def step_see_field_in_chf(context, field_name):
    """Verify field is displayed in CHF format."""
    field_selector = f'[data-testid="{field_name.lower().replace(" ", "-")}"]'
    field_element = context.page.locator(field_selector)
    assert field_element.is_visible()
    field_text = field_element.text_content()
    assert 'CHF' in field_text

@then('I should see my "{field_name}" displayed')
def step_see_field_displayed(context, field_name):
    """Verify field is displayed."""
    field_selector = f'[data-testid="{field_name.lower().replace(" ", "-")}"]'
    assert context.page.is_visible(field_selector)

@then('I should see my "{field_name}" with effective rate percentage')
def step_see_field_with_percentage(context, field_name):
    """Verify field is displayed with percentage."""
    field_selector = f'[data-testid="{field_name.lower().replace(" ", "-")}"]'
    field_element = context.page.locator(field_selector)
    assert field_element.is_visible()
    
    # Check for percentage in the same section
    percentage_element = context.page.locator('[data-testid="effective-rate"]')
    assert percentage_element.is_visible()
    percentage_text = percentage_element.text_content()
    assert '%' in percentage_text

@then('I should see a tax breakdown showing percentages for social insurance, income tax, and wealth tax')
def step_see_tax_breakdown(context):
    """Verify tax breakdown with percentages."""
    breakdown_section = context.page.locator('[data-testid="tax-breakdown"]')
    assert breakdown_section.is_visible()
    
    # Check for percentage indicators
    percentages = context.page.locator('text=/%/')
    assert percentages.count() >= 3  # At least 3 percentage values

@when('I scroll to the social insurance section')
def step_scroll_to_social_insurance(context):
    """Scroll to social insurance breakdown section."""
    context.page.locator('[data-testid="social-insurance-breakdown"]').scroll_into_view_if_needed()

@then('I should see "{component}" contributions with "{rate}" rate')
def step_see_contribution_with_rate(context, component, rate):
    """Verify social insurance component with rate."""
    component_selector = f'[data-testid="{component.lower().replace("/", "-").replace(" ", "-")}-contribution"]'
    rate_selector = f'[data-testid="{component.lower().replace("/", "-").replace(" ", "-")}-rate"]'
    
    assert context.page.is_visible(component_selector)
    assert context.page.is_visible(rate_selector)
    
    rate_text = context.page.locator(rate_selector).text_content()
    assert rate in rate_text

@then('all contribution amounts should be displayed in CHF format')
def step_all_contributions_in_chf(context):
    """Verify all contribution amounts are in CHF format."""
    chf_elements = context.page.locator('text=/CHF [0-9,]+/')
    assert chf_elements.count() >= 4  # At least 4 CHF amounts for social insurance

# Tax Optimization Steps

@given('I am on the "{tab_name}" tab')
def step_on_tab(context, tab_name):
    """Navigate to specific tab."""
    tab_selector = f'[data-testid="{tab_name.lower().replace(" ", "-")}-tab"]'
    context.page.click(tab_selector)
    context.page.wait_for_timeout(1000)

@when('the optimization analysis completes')
def step_optimization_analysis_completes(context):
    """Wait for optimization analysis to complete."""
    context.page.wait_for_selector('[data-testid="optimization-strategies"]', timeout=10000)

@then('I should see "{section_name}" section')
def step_see_section(context, section_name):
    """Verify section is displayed."""
    section_selector = f'[data-testid="{section_name.lower().replace(" ", "-")}"]'
    assert context.page.is_visible(section_selector)

@then('I should see strategies with annual savings amounts in CHF')
def step_see_strategies_with_savings(context):
    """Verify strategies show CHF savings amounts."""
    strategies = context.page.locator('[data-testid="strategy-item"]')
    assert strategies.count() > 0
    
    # Check first strategy has CHF amount
    first_strategy = strategies.first()
    savings_element = first_strategy.locator('[data-testid="annual-savings"]')
    assert savings_element.is_visible()
    savings_text = savings_element.text_content()
    assert 'CHF' in savings_text

@then('each strategy should show risk level (Low/Medium/High)')
def step_strategies_show_risk_level(context):
    """Verify strategies show risk levels."""
    risk_elements = context.page.locator('[data-testid="risk-level"]')
    assert risk_elements.count() > 0
    
    # Check risk level text
    for i in range(min(3, risk_elements.count())):
        risk_text = risk_elements.nth(i).text_content()
        assert any(level in risk_text for level in ['Low', 'Medium', 'High'])

@then('each strategy should have implementation steps')
def step_strategies_have_implementation_steps(context):
    """Verify strategies have implementation steps."""
    implementation_elements = context.page.locator('[data-testid="implementation-steps"]')
    assert implementation_elements.count() > 0

@then('I should see time to implement estimates')
def step_see_time_estimates(context):
    """Verify time to implement estimates are shown."""
    time_elements = context.page.locator('[data-testid="time-to-implement"]')
    assert time_elements.count() > 0

@then('I should see legal compliance indicators')
def step_see_legal_compliance(context):
    """Verify legal compliance indicators are shown."""
    compliance_elements = context.page.locator('[data-testid="legal-compliance"]')
    assert compliance_elements.count() > 0

# Deduction Optimizer Steps

@when('the deduction optimizer loads')
def step_deduction_optimizer_loads(context):
    """Wait for deduction optimizer to load."""
    context.page.wait_for_selector('[data-testid="optimization-potential"]', timeout=10000)

@then('I should see "{field1}", "{field2}", and "{field3}"')
def step_see_three_fields(context, field1, field2, field3):
    """Verify three specific fields are displayed."""
    fields = [field1, field2, field3]
    for field in fields:
        field_selector = f'[data-testid="{field.lower().replace(" ", "-")}"]'
        assert context.page.is_visible(field_selector)

@then('I should see deduction category tabs with potential savings indicators')
def step_see_deduction_category_tabs(context):
    """Verify deduction category tabs with savings indicators."""
    category_tabs = context.page.locator('[data-testid*="-tab"]')
    assert category_tabs.count() >= 3  # At least 3 categories
    
    # Check for savings indicators
    savings_indicators = context.page.locator('text=/\\+CHF [0-9]+/')
    assert savings_indicators.count() > 0

@then('I can select different deduction categories to view details')
def step_select_deduction_categories(context):
    """Test selecting different deduction categories."""
    # Click on Pillar 3a category
    context.page.click('[data-testid="pillar3a-tab"]')
    context.page.wait_for_timeout(500)
    assert context.page.is_visible('[data-testid="category-details"]')
    
    # Click on Professional category
    context.page.click('[data-testid="professional-tab"]')
    context.page.wait_for_timeout(500)
    assert context.page.is_visible('[data-testid="category-details"]')

@then('each category should show requirements, tips, and documentation needed')
def step_categories_show_details(context):
    """Verify categories show detailed information."""
    assert context.page.is_visible('[data-testid="requirements-list"]')
    assert context.page.is_visible('[data-testid="tips-list"]')
    assert context.page.is_visible('[data-testid="documentation-list"]')

# Scenario Comparison Steps

@when('the scenarios load')
def step_scenarios_load(context):
    """Wait for scenarios to load."""
    context.page.wait_for_selector('[data-testid="scenario-buttons"]', timeout=10000)

@then('I should see scenario buttons: {scenario_list}')
def step_see_scenario_buttons(context, scenario_list):
    """Verify scenario buttons are present."""
    scenarios = scenario_list.split(', ')
    scenarios = [scenario.strip('"') for scenario in scenarios]
    
    for scenario in scenarios:
        scenario_selector = f'[data-testid="{scenario.lower().replace(" ", "-").replace("(", "").replace(")", "")}-scenario"]'
        assert context.page.is_visible(scenario_selector)

@then('each scenario button should show potential savings if applicable')
def step_scenario_buttons_show_savings(context):
    """Verify scenario buttons show savings where applicable."""
    savings_indicators = context.page.locator('text=/\\+CHF [0-9,]+/')
    assert savings_indicators.count() >= 1  # At least one scenario should show savings

@when('I select a scenario')
def step_select_scenario(context):
    """Select a tax scenario."""
    context.page.click('[data-testid="optimized-scenario"]')
    context.page.wait_for_timeout(1000)

@then('I should see detailed scenario information including gross income, total tax burden, and net income')
def step_see_scenario_details(context):
    """Verify detailed scenario information is displayed."""
    assert context.page.is_visible('[data-testid="scenario-details"]')
    assert context.page.is_visible('[data-testid="gross-income"]')
    assert context.page.is_visible('[data-testid="total-tax-burden"]')
    assert context.page.is_visible('[data-testid="net-income"]')

@then('scenarios with savings should highlight the annual tax savings amount')
def step_scenarios_highlight_savings(context):
    """Verify scenarios highlight savings amounts."""
    savings_highlight = context.page.locator('[data-testid="annual-tax-savings"]')
    if savings_highlight.is_visible():
        savings_text = savings_highlight.text_content()
        assert 'CHF' in savings_text

# Swiss Social Insurance Calculator Steps

@given('I have access to the Swiss Social Insurance Calculator')
def step_access_social_insurance_calculator(context):
    """Access the Swiss Social Insurance Calculator."""
    # This would typically be part of the tax planning dashboard
    context.page.wait_for_selector('[data-testid="social-insurance-calculator"]', timeout=5000)

@given('I have an annual salary of CHF {amount:d}')
def step_set_annual_salary(context, amount):
    """Set annual salary for calculations."""
    monthly_amount = amount // 12
    context.page.fill('[data-testid="monthly-income"]', str(monthly_amount))
    context.annual_salary = amount

@given('I am {age:d} years old')
def step_set_age(context, age):
    """Set age for calculations."""
    context.page.fill('[data-testid="current-age"]', str(age))
    context.age = age

@when('I calculate my social insurance contributions')
def step_calculate_social_insurance(context):
    """Calculate social insurance contributions."""
    # Trigger calculation by navigating to tax dashboard
    context.page.click('[data-testid="analysis-tab"]')
    context.page.click('[data-testid="tax-optimization-tab"]')
    context.page.wait_for_selector('[data-testid="social-insurance-breakdown"]', timeout=10000)

@then('my AHV/IV/EO employee contribution should be CHF {amount:d}')
def step_verify_ahv_employee_contribution(context, amount):
    """Verify AHV/IV/EO employee contribution amount."""
    ahv_element = context.page.locator('[data-testid="ahv-employee-contribution"]')
    ahv_text = ahv_element.text_content()
    assert str(amount) in ahv_text.replace(',', '')

@then('my AHV/IV/EO employer contribution should be CHF {amount:d}')
def step_verify_ahv_employer_contribution(context, amount):
    """Verify AHV/IV/EO employer contribution amount."""
    ahv_element = context.page.locator('[data-testid="ahv-employer-contribution"]')
    ahv_text = ahv_element.text_content()
    assert str(amount) in ahv_text.replace(',', '')

@then('the total AHV/IV/EO contribution should be CHF {amount:d}')
def step_verify_ahv_total_contribution(context, amount):
    """Verify total AHV/IV/EO contribution amount."""
    ahv_total = context.page.locator('[data-testid="ahv-total-contribution"]')
    ahv_text = ahv_total.text_content()
    assert str(amount) in ahv_text.replace(',', '')

@then('the AHV/IV/EO rate should be displayed as "{rate}" for employee and employer')
def step_verify_ahv_rate(context, rate):
    """Verify AHV/IV/EO rate display."""
    rate_element = context.page.locator('[data-testid="ahv-rate"]')
    rate_text = rate_element.text_content()
    assert rate in rate_text
