{"cantons": [{"code": "ZH", "name": "Zurich", "taxRate": 0.12}, {"code": "GE", "name": "Geneva", "taxRate": 0.15}, {"code": "VD", "name": "<PERSON><PERSON>", "taxRate": 0.14}, {"code": "BE", "name": "Bern", "taxRate": 0.13}, {"code": "ZG", "name": "<PERSON>ug", "taxRate": 0.08}, {"code": "BS", "name": "Basel-Stadt", "taxRate": 0.16}, {"code": "BL", "name": "Basel-Landschaft", "taxRate": 0.11}, {"code": "AG", "name": "Aargau", "taxRate": 0.1}], "userProfiles": [{"id": "young-professional", "age": 28, "income": 85000, "expenses": 60000, "savings": 25000, "canton": "ZH", "goals": "FIRE by 50"}, {"id": "mid-career-family", "age": 42, "income": 180000, "expenses": 120000, "savings": 350000, "canton": "GE", "goals": "Traditional retirement", "married": true, "children": 2}, {"id": "high-earner", "age": 35, "income": 250000, "expenses": 100000, "savings": 800000, "canton": "ZG", "goals": "Early retirement optimization"}], "healthcareProfiles": [{"id": "low-risk", "age": 30, "healthStatus": "excellent", "annualCosts": 1200, "recommendedDeductible": 2500}, {"id": "medium-risk", "age": 45, "healthStatus": "good", "annualCosts": 2800, "recommendedDeductible": 1500}, {"id": "high-risk", "age": 60, "healthStatus": "fair", "annualCosts": 4500, "recommendedDeductible": 300}], "timestamp": "2025-06-14T23:43:18.517Z"}