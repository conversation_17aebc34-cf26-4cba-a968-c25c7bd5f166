{"taxRates": {"federal": {"brackets": [{"min": 0, "max": 14500, "rate": 0, "description": "Tax-free threshold"}, {"min": 14500, "max": 31600, "rate": 0.077, "description": "First tax bracket"}, {"min": 31600, "max": 41400, "rate": 0.088, "description": "Second tax bracket"}, {"min": 41400, "max": 55200, "rate": 0.11, "description": "Third tax bracket"}, {"min": 55200, "max": 72500, "rate": 0.132, "description": "Fourth tax bracket"}, {"min": 72500, "max": 78100, "rate": 0.154, "description": "Fifth tax bracket"}, {"min": 78100, "max": 755200, "rate": 0.165, "description": "Sixth tax bracket"}, {"min": 755200, "max": *********, "rate": 0.115, "description": "Top tax bracket"}]}, "cantonal": {"ZH": {"rate": 0.12, "wealthTax": 0.002, "wealthTaxThreshold": 100000, "description": "Zurich cantonal tax rates"}, "GE": {"rate": 0.15, "wealthTax": 0.003, "wealthTaxThreshold": 75000, "description": "Geneva cantonal tax rates"}, "VD": {"rate": 0.14, "wealthTax": 0.0025, "wealthTaxThreshold": 80000, "description": "Vaud cantonal tax rates"}, "BE": {"rate": 0.13, "wealthTax": 0.002, "wealthTaxThreshold": 90000, "description": "Bern cantonal tax rates"}, "ZG": {"rate": 0.08, "wealthTax": 0.001, "wealthTaxThreshold": 150000, "description": "Zug cantonal tax rates (low tax)"}, "BS": {"rate": 0.16, "wealthTax": 0.003, "wealthTaxThreshold": 70000, "description": "Basel-Stadt cantonal tax rates"}, "BL": {"rate": 0.11, "wealthTax": 0.002, "wealthTaxThreshold": 95000, "description": "Basel-Landschaft cantonal tax rates"}, "AG": {"rate": 0.1, "wealthTax": 0.0015, "wealthTaxThreshold": 100000, "description": "Aargau cantonal tax rates"}}}, "socialInsurance": {"ahv": {"rate": 0.0525, "maxIncome": 88200, "description": "AHV/IV/EO contributions"}, "alv": {"rate": 0.011, "maxIncome": 148200, "additionalRate": 0.005, "additionalThreshold": 148200, "description": "Unemployment insurance"}, "nbv": {"rate": 0.0125, "description": "Accident insurance (estimated)"}}, "pillar3a": {"maxContribution2024": 7056, "maxContributionSelfEmployed2024": 35280, "taxDeductible": true, "withdrawalAge": 60, "earlyWithdrawalPenalty": 0.05, "description": "Pillar 3a retirement savings limits and rules"}, "healthcareDeductibles": [{"amount": 300, "description": "Minimum deductible"}, {"amount": 500, "description": "Low deductible option"}, {"amount": 1000, "description": "Medium deductible option"}, {"amount": 1500, "description": "Medium-high deductible option"}, {"amount": 2000, "description": "High deductible option"}, {"amount": 2500, "description": "Maximum deductible"}], "healthcarePremiums": {"ZH": {"average": 450, "range": [380, 520], "description": "Zurich average healthcare premiums"}, "GE": {"average": 520, "range": [450, 590], "description": "Geneva average healthcare premiums"}, "VD": {"average": 480, "range": [410, 550], "description": "Vaud average healthcare premiums"}, "BE": {"average": 420, "range": [360, 480], "description": "Bern average healthcare premiums"}, "ZG": {"average": 380, "range": [320, 440], "description": "Zug average healthcare premiums"}, "BS": {"average": 500, "range": [430, 570], "description": "Basel-Stadt average healthcare premiums"}, "BL": {"average": 440, "range": [380, 500], "description": "Basel-Landschaft average healthcare premiums"}, "AG": {"average": 400, "range": [340, 460], "description": "Aargau average healthcare premiums"}}, "fireCalculations": {"withdrawalRate": 0.04, "safeWithdrawalRate": 0.035, "conservativeWithdrawalRate": 0.03, "multiplier": 25, "safeMultiplier": 28.57, "conservativeMultiplier": 33.33, "description": "FIRE calculation parameters and withdrawal rates"}, "marketAssumptions": {"stockReturn": {"nominal": 0.07, "real": 0.05, "volatility": 0.15, "description": "Long-term stock market return assumptions"}, "bondReturn": {"nominal": 0.03, "real": 0.01, "volatility": 0.05, "description": "Long-term bond return assumptions"}, "inflation": {"target": 0.02, "historical": 0.018, "range": [0.01, 0.03], "description": "Swiss inflation assumptions"}, "realEstate": {"nominal": 0.04, "real": 0.02, "volatility": 0.1, "description": "Real estate return assumptions"}}, "livingCosts": {"ZH": {"single": 3500, "couple": 5500, "family": 7500, "description": "Zurich average monthly living costs"}, "GE": {"single": 3800, "couple": 6000, "family": 8200, "description": "Geneva average monthly living costs"}, "VD": {"single": 3200, "couple": 5200, "family": 7200, "description": "Vaud average monthly living costs"}, "BE": {"single": 3000, "couple": 4800, "family": 6800, "description": "Bern average monthly living costs"}, "ZG": {"single": 3600, "couple": 5800, "family": 8000, "description": "Zug average monthly living costs"}, "BS": {"single": 3400, "couple": 5400, "family": 7400, "description": "Basel-Stadt average monthly living costs"}, "BL": {"single": 3100, "couple": 5000, "family": 7000, "description": "Basel-Landschaft average monthly living costs"}, "AG": {"single": 2900, "couple": 4600, "family": 6600, "description": "Aargau average monthly living costs"}}, "salaryBenchmarks": {"entry": {"min": 50000, "median": 65000, "max": 80000, "description": "Entry-level salary ranges"}, "experienced": {"min": 80000, "median": 110000, "max": 150000, "description": "Experienced professional salary ranges"}, "senior": {"min": 120000, "median": 160000, "max": 220000, "description": "Senior professional salary ranges"}, "executive": {"min": 200000, "median": 300000, "max": 500000, "description": "Executive salary ranges"}}, "retirementPlanning": {"pillar1": {"maxPension": 2450, "minPension": 1225, "contributionYears": 44, "description": "AHV state pension system"}, "pillar2": {"minSalary": 22050, "maxSalary": 88200, "conversionRate": 0.068, "description": "Occupational pension system"}, "pillar3b": {"taxAdvantages": "limited", "flexibility": "high", "description": "Private retirement savings"}}, "calculationFormulas": {"fireNumber": "annual_expenses * withdrawal_rate_multiplier", "yearsToFire": "(fire_number - current_savings) / annual_savings", "savingsRate": "annual_savings / gross_annual_income", "netIncome": "gross_income - taxes - social_insurance", "effectiveTaxRate": "total_taxes / gross_income", "description": "Key calculation formulas used in Swiss Budget Pro"}, "validationRules": {"income": {"min": 0, "max": 10000000, "required": true, "description": "Annual income validation rules"}, "expenses": {"min": 0, "max": 5000000, "required": true, "description": "Annual expenses validation rules"}, "savings": {"min": 0, "max": *********, "required": false, "description": "Current savings validation rules"}, "age": {"min": 18, "max": 100, "required": true, "description": "Age validation rules"}, "retirementAge": {"min": 50, "max": 100, "required": true, "description": "Retirement age validation rules"}}, "metadata": {"version": "2024.1", "lastUpdated": "2024-01-15", "dataSource": "Swiss Federal Tax Administration, cantonal tax authorities", "currency": "CHF", "locale": "de-CH", "description": "Swiss financial data for accurate calculations and testing"}}