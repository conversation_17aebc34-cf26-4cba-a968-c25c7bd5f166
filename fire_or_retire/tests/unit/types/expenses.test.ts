import { describe, expect, it } from 'vitest';
import {
  calculateAnnualAmount,
  calculateMonthlyAmount,
  getCategoryInfo,
  SWISS_EXPENSE_CATEGORIES,
  type Expense,
  type ExpenseCategory,
  type ExpenseFrequency,
  type ExpensePriority,
} from '../../../src/types/expenses';

describe('Expense Types and Utilities', () => {
  describe('calculateMonthlyAmount', () => {
    it('should return monthly amount for monthly frequency', () => {
      const expense: Expense = {
        id: '1',
        category: 'housing',
        name: 'Rent',
        amount: 2000,
        priority: 'essential',
        frequency: 'monthly',
        isActive: true,
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      };

      expect(calculateMonthlyAmount(expense)).toBe(2000);
    });

    it('should calculate monthly amount for quarterly frequency', () => {
      const expense: Expense = {
        id: '1',
        category: 'insurance',
        name: 'Car Insurance',
        amount: 600,
        priority: 'essential',
        frequency: 'quarterly',
        isActive: true,
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      };

      expect(calculateMonthlyAmount(expense)).toBe(200); // 600 / 3
    });

    it('should calculate monthly amount for annual frequency', () => {
      const expense: Expense = {
        id: '1',
        category: 'insurance',
        name: 'Health Insurance',
        amount: 3600,
        priority: 'essential',
        frequency: 'annually',
        isActive: true,
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      };

      expect(calculateMonthlyAmount(expense)).toBe(300); // 3600 / 12
    });

    it('should return 0 for one-time frequency', () => {
      const expense: Expense = {
        id: '1',
        category: 'other',
        name: 'Laptop',
        amount: 2000,
        priority: 'important',
        frequency: 'one-time',
        isActive: true,
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      };

      expect(calculateMonthlyAmount(expense)).toBe(0);
    });

    it('should calculate amount regardless of active status', () => {
      const expense: Expense = {
        id: '1',
        category: 'housing',
        name: 'Old Rent',
        amount: 2000,
        priority: 'essential',
        frequency: 'monthly',
        isActive: false,
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      };

      // The function calculates amount regardless of isActive status
      expect(calculateMonthlyAmount(expense)).toBe(2000);
    });
  });

  describe('calculateAnnualAmount', () => {
    it('should calculate annual amount for monthly frequency', () => {
      const expense: Expense = {
        id: '1',
        category: 'housing',
        name: 'Rent',
        amount: 2000,
        priority: 'essential',
        frequency: 'monthly',
        isActive: true,
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      };

      expect(calculateAnnualAmount(expense)).toBe(24000); // 2000 * 12
    });

    it('should calculate annual amount for quarterly frequency', () => {
      const expense: Expense = {
        id: '1',
        category: 'insurance',
        name: 'Car Insurance',
        amount: 600,
        priority: 'essential',
        frequency: 'quarterly',
        isActive: true,
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      };

      expect(calculateAnnualAmount(expense)).toBe(2400); // 600 * 4
    });

    it('should return annual amount for annual frequency', () => {
      const expense: Expense = {
        id: '1',
        category: 'insurance',
        name: 'Health Insurance',
        amount: 3600,
        priority: 'essential',
        frequency: 'annually',
        isActive: true,
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      };

      expect(calculateAnnualAmount(expense)).toBe(3600);
    });

    it('should return amount for one-time frequency', () => {
      const expense: Expense = {
        id: '1',
        category: 'other',
        name: 'Laptop',
        amount: 2000,
        priority: 'important',
        frequency: 'one-time',
        isActive: true,
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      };

      expect(calculateAnnualAmount(expense)).toBe(2000);
    });

    it('should calculate amount regardless of active status', () => {
      const expense: Expense = {
        id: '1',
        category: 'housing',
        name: 'Old Rent',
        amount: 2000,
        priority: 'essential',
        frequency: 'monthly',
        isActive: false,
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      };

      // The function calculates amount regardless of isActive status
      expect(calculateAnnualAmount(expense)).toBe(24000);
    });
  });

  describe('getCategoryInfo', () => {
    it('should return correct info for housing category', () => {
      const info = getCategoryInfo('housing');

      expect(info.id).toBe('housing');
      expect(info.name).toBe('Housing');
      expect(info.icon).toBe('🏠');
      expect(info.swissSpecific).toBe(true);
    });

    it('should return correct info for food category', () => {
      const info = getCategoryInfo('food');

      expect(info.id).toBe('food');
      expect(info.name).toBe('Food & Groceries');
      expect(info.icon).toBe('🛒');
      expect(info.swissSpecific).toBe(false);
    });

    it('should return correct info for transportation category', () => {
      const info = getCategoryInfo('transportation');

      expect(info.id).toBe('transportation');
      expect(info.name).toBe('Transportation');
      expect(info.icon).toBe('🚗');
      expect(info.swissSpecific).toBe(true);
    });

    it('should handle unknown categories gracefully', () => {
      const info = getCategoryInfo('unknown' as ExpenseCategory);

      expect(info.id).toBe('other');
      expect(info.name).toBe('Other');
      expect(info.icon).toBe('📦');
    });
  });

  describe('SWISS_EXPENSE_CATEGORIES', () => {
    it('should contain all expected categories', () => {
      const categoryIds = SWISS_EXPENSE_CATEGORIES.map(cat => cat.id);

      expect(categoryIds).toContain('housing');
      expect(categoryIds).toContain('food');
      expect(categoryIds).toContain('transportation');
      expect(categoryIds).toContain('healthcare');
      expect(categoryIds).toContain('insurance');
      expect(categoryIds).toContain('utilities');
      expect(categoryIds).toContain('entertainment');
      expect(categoryIds).toContain('education');
      expect(categoryIds).toContain('clothing');
      expect(categoryIds).toContain('personal_care');
      expect(categoryIds).toContain('savings_investments');
      expect(categoryIds).toContain('taxes');
      expect(categoryIds).toContain('debt_payments');
      expect(categoryIds).toContain('gifts_donations');
      expect(categoryIds).toContain('other');
    });

    it('should have 15 categories total', () => {
      expect(SWISS_EXPENSE_CATEGORIES).toHaveLength(15);
    });

    it('should have Swiss-specific categories marked correctly', () => {
      const swissSpecific = SWISS_EXPENSE_CATEGORIES.filter(
        cat => cat.swissSpecific,
      );
      const nonSwissSpecific = SWISS_EXPENSE_CATEGORIES.filter(
        cat => !cat.swissSpecific,
      );

      expect(swissSpecific.length).toBeGreaterThan(0);
      expect(nonSwissSpecific.length).toBeGreaterThan(0);
    });

    it('should have all categories with required properties', () => {
      SWISS_EXPENSE_CATEGORIES.forEach(category => {
        expect(category).toHaveProperty('id');
        expect(category).toHaveProperty('name');
        expect(category).toHaveProperty('icon');
        expect(category).toHaveProperty('swissSpecific');
        expect(category).toHaveProperty('description');

        expect(typeof category.id).toBe('string');
        expect(typeof category.name).toBe('string');
        expect(typeof category.icon).toBe('string');
        expect(typeof category.swissSpecific).toBe('boolean');
        expect(typeof category.description).toBe('string');
      });
    });

    it('should have unique category IDs', () => {
      const ids = SWISS_EXPENSE_CATEGORIES.map(cat => cat.id);
      const uniqueIds = [...new Set(ids)];

      expect(ids).toHaveLength(uniqueIds.length);
    });

    it('should have meaningful descriptions', () => {
      SWISS_EXPENSE_CATEGORIES.forEach(category => {
        expect(category.description.length).toBeGreaterThan(10);
      });
    });
  });

  describe('Type Safety', () => {
    it('should enforce correct expense frequency types', () => {
      const validFrequencies: ExpenseFrequency[] = [
        'monthly',
        'quarterly',
        'annually',
        'one-time',
      ];

      validFrequencies.forEach(frequency => {
        const expense: Expense = {
          id: '1',
          category: 'housing',
          name: 'Test',
          amount: 100,
          priority: 'essential',
          frequency,
          isActive: true,
          createdAt: '2024-01-01',
          updatedAt: '2024-01-01',
        };

        expect(expense.frequency).toBe(frequency);
      });
    });

    it('should enforce correct expense priority types', () => {
      const validPriorities: ExpensePriority[] = [
        'essential',
        'important',
        'nice-to-have',
      ];

      validPriorities.forEach(priority => {
        const expense: Expense = {
          id: '1',
          category: 'housing',
          name: 'Test',
          amount: 100,
          priority,
          frequency: 'monthly',
          isActive: true,
          createdAt: '2024-01-01',
          updatedAt: '2024-01-01',
        };

        expect(expense.priority).toBe(priority);
      });
    });

    it('should enforce correct expense category types', () => {
      const validCategories: ExpenseCategory[] = [
        'housing',
        'utilities',
        'food',
        'transportation',
        'insurance',
        'healthcare',
        'taxes',
        'education',
        'entertainment',
        'clothing',
        'personal_care',
        'gifts_donations',
        'debt_payments',
        'savings_investments',
        'other',
      ];

      validCategories.forEach(category => {
        const expense: Expense = {
          id: '1',
          category,
          name: 'Test',
          amount: 100,
          priority: 'essential',
          frequency: 'monthly',
          isActive: true,
          createdAt: '2024-01-01',
          updatedAt: '2024-01-01',
        };

        expect(expense.category).toBe(category);
      });
    });
  });
});
