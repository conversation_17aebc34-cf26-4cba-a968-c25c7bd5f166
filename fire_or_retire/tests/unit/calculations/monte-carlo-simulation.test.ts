/**
 * Comprehensive Unit Tests for Monte Carlo Simulation Engine
 *
 * This test suite covers the Monte Carlo simulation functionality with:
 * - Multiple stress test scenarios
 * - Risk assessment metrics
 * - Performance validation
 * - Swiss-specific market conditions
 */

import { describe, it, expect, beforeEach } from 'vitest';

// Mock Monte Carlo simulation functions (these would be imported from actual implementation)
interface MonteCarloResult {
  scenarios: number[];
  successRate: number;
  medianOutcome: number;
  percentile10: number;
  percentile90: number;
  valueAtRisk: number;
  expectedShortfall: number;
}

interface SimulationParams {
  initialAmount: number;
  monthlyContribution: number;
  years: number;
  expectedReturn: number;
  volatility: number;
  iterations: number;
  inflationRate?: number;
  withdrawalRate?: number;
}

// Mock implementation for testing
const runMonteCarloSimulation = (params: SimulationParams): MonteCarloResult => {
  const scenarios: number[] = [];
  const { initialAmount, monthlyContribution, years, expectedReturn, volatility, iterations } = params;
  
  for (let i = 0; i < iterations; i++) {
    let amount = initialAmount;
    for (let year = 0; year < years; year++) {
      // Simulate random return with normal distribution
      const randomReturn = expectedReturn + (Math.random() - 0.5) * volatility * 2;
      amount = amount * (1 + randomReturn) + monthlyContribution * 12;
    }
    scenarios.push(amount);
  }
  
  scenarios.sort((a, b) => a - b);
  const successRate = scenarios.filter(s => s >= initialAmount * 25).length / iterations; // 4% rule
  
  return {
    scenarios,
    successRate,
    medianOutcome: scenarios[Math.floor(iterations / 2)],
    percentile10: scenarios[Math.floor(iterations * 0.1)],
    percentile90: scenarios[Math.floor(iterations * 0.9)],
    valueAtRisk: scenarios[Math.floor(iterations * 0.05)],
    expectedShortfall: scenarios.slice(0, Math.floor(iterations * 0.05)).reduce((a, b) => a + b, 0) / Math.floor(iterations * 0.05),
  };
};

describe('Monte Carlo Simulation Engine', () => {
  let baseParams: SimulationParams;

  beforeEach(() => {
    baseParams = {
      initialAmount: 100000,
      monthlyContribution: 3000,
      years: 20,
      expectedReturn: 0.07,
      volatility: 0.15,
      iterations: 1000,
    };
  });

  describe('Basic Simulation Functionality', () => {
    it('should run simulation with default parameters', () => {
      const result = runMonteCarloSimulation(baseParams);
      
      expect(result.scenarios).toHaveLength(1000);
      expect(result.successRate).toBeGreaterThanOrEqual(0);
      expect(result.successRate).toBeLessThanOrEqual(1);
      expect(result.medianOutcome).toBeGreaterThan(0);
      expect(result.percentile90).toBeGreaterThan(result.percentile10);
    });

    it('should handle different iteration counts', () => {
      const smallSim = runMonteCarloSimulation({ ...baseParams, iterations: 100 });
      const largeSim = runMonteCarloSimulation({ ...baseParams, iterations: 10000 });
      
      expect(smallSim.scenarios).toHaveLength(100);
      expect(largeSim.scenarios).toHaveLength(10000);
      
      // Larger simulation should be more stable
      expect(Math.abs(largeSim.successRate - 0.8)).toBeLessThan(Math.abs(smallSim.successRate - 0.8));
    });

    it('should validate risk metrics calculations', () => {
      const result = runMonteCarloSimulation(baseParams);
      
      expect(result.valueAtRisk).toBeLessThan(result.percentile10);
      expect(result.expectedShortfall).toBeLessThan(result.valueAtRisk);
      expect(result.percentile10).toBeLessThan(result.medianOutcome);
      expect(result.medianOutcome).toBeLessThan(result.percentile90);
    });
  });

  describe('Swiss Market Stress Test Scenarios', () => {
    it('should handle bear market scenario (2008-style crash)', () => {
      const bearMarketParams = {
        ...baseParams,
        expectedReturn: 0.03, // Reduced returns
        volatility: 0.25,     // Increased volatility
        iterations: 5000,
      };
      
      const result = runMonteCarloSimulation(bearMarketParams);
      
      expect(result.successRate).toBeLessThan(0.7); // Lower success rate expected
      expect(result.valueAtRisk).toBeLessThan(baseParams.initialAmount * 20); // Significant downside risk
    });

    it('should handle high inflation scenario', () => {
      const highInflationParams = {
        ...baseParams,
        expectedReturn: 0.05, // Real returns reduced by inflation
        inflationRate: 0.06,  // High inflation
        volatility: 0.20,
        iterations: 5000,
      };
      
      const result = runMonteCarloSimulation(highInflationParams);
      
      expect(result.successRate).toBeLessThan(0.8); // Inflation erodes purchasing power
      expect(result.medianOutcome).toBeGreaterThan(0);
    });

    it('should handle recession scenario', () => {
      const recessionParams = {
        ...baseParams,
        expectedReturn: 0.02, // Very low returns
        volatility: 0.30,     // High volatility
        iterations: 5000,
      };
      
      const result = runMonteCarloSimulation(recessionParams);
      
      expect(result.successRate).toBeLessThan(0.6); // Challenging scenario
      expect(result.expectedShortfall).toBeLessThan(result.valueAtRisk);
    });

    it('should handle stagflation scenario', () => {
      const stagflationParams = {
        ...baseParams,
        expectedReturn: 0.01, // Minimal real returns
        volatility: 0.25,
        inflationRate: 0.08,  // High inflation with low growth
        iterations: 5000,
      };
      
      const result = runMonteCarloSimulation(stagflationParams);
      
      expect(result.successRate).toBeLessThan(0.5); // Very challenging scenario
      expect(result.medianOutcome).toBeGreaterThan(0);
    });

    it('should handle Swiss franc strength scenario', () => {
      const strongFrancParams = {
        ...baseParams,
        expectedReturn: 0.04, // Reduced returns due to currency strength
        volatility: 0.12,     // Lower volatility due to safe haven status
        iterations: 5000,
      };
      
      const result = runMonteCarloSimulation(strongFrancParams);
      
      expect(result.volatility).toBeLessThan(baseParams.volatility);
      expect(result.successRate).toBeGreaterThan(0.6); // More stable but lower returns
    });
  });

  describe('Performance and Edge Cases', () => {
    it('should complete large simulations within reasonable time', () => {
      const startTime = Date.now();
      const result = runMonteCarloSimulation({ ...baseParams, iterations: 10000 });
      const endTime = Date.now();
      
      expect(endTime - startTime).toBeLessThan(5000); // Should complete within 5 seconds
      expect(result.scenarios).toHaveLength(10000);
    });

    it('should handle extreme parameter values', () => {
      const extremeParams = {
        initialAmount: 0,
        monthlyContribution: 100000, // Very high contribution
        years: 50,
        expectedReturn: 0.15, // Very high return
        volatility: 0.50,     // Very high volatility
        iterations: 1000,
      };
      
      const result = runMonteCarloSimulation(extremeParams);
      
      expect(result.scenarios.every(s => s >= 0)).toBe(true); // No negative outcomes
      expect(result.percentile90).toBeGreaterThan(result.medianOutcome);
    });

    it('should handle zero contribution scenarios', () => {
      const zeroContributionParams = {
        ...baseParams,
        monthlyContribution: 0,
      };
      
      const result = runMonteCarloSimulation(zeroContributionParams);
      
      expect(result.medianOutcome).toBeGreaterThan(baseParams.initialAmount); // Growth from returns only
      expect(result.scenarios.every(s => s > 0)).toBe(true);
    });

    it('should validate Swiss FIRE number calculations', () => {
      const swissFIREParams = {
        initialAmount: 200000,
        monthlyContribution: 5000,
        years: 15,
        expectedReturn: 0.06, // Conservative Swiss market return
        volatility: 0.18,
        iterations: 5000,
      };
      
      const result = runMonteCarloSimulation(swissFIREParams);
      const fireNumber = 2500000; // 25x annual expenses of 100k CHF
      
      const fireSuccessRate = result.scenarios.filter(s => s >= fireNumber).length / result.scenarios.length;
      expect(fireSuccessRate).toBeGreaterThan(0.3); // Reasonable chance of reaching FIRE
      expect(result.medianOutcome).toBeGreaterThan(1000000); // Significant wealth accumulation
    });
  });

  describe('Risk Assessment Metrics', () => {
    it('should calculate Value at Risk (VaR) correctly', () => {
      const result = runMonteCarloSimulation({ ...baseParams, iterations: 10000 });
      
      // VaR should represent 5th percentile loss
      const sortedLosses = result.scenarios.map(s => baseParams.initialAmount - s).sort((a, b) => b - a);
      const expectedVaR = sortedLosses[Math.floor(sortedLosses.length * 0.05)];
      
      expect(Math.abs(result.valueAtRisk - expectedVaR)).toBeLessThan(10000); // Within reasonable tolerance
    });

    it('should calculate Expected Shortfall correctly', () => {
      const result = runMonteCarloSimulation({ ...baseParams, iterations: 10000 });
      
      // Expected Shortfall should be worse than VaR
      expect(result.expectedShortfall).toBeLessThan(result.valueAtRisk);
      expect(result.expectedShortfall).toBeGreaterThan(0);
    });

    it('should provide consistent risk metrics across runs', () => {
      const result1 = runMonteCarloSimulation({ ...baseParams, iterations: 5000 });
      const result2 = runMonteCarloSimulation({ ...baseParams, iterations: 5000 });
      
      // Results should be similar (within 10% tolerance)
      expect(Math.abs(result1.successRate - result2.successRate)).toBeLessThan(0.1);
      expect(Math.abs(result1.medianOutcome - result2.medianOutcome) / result1.medianOutcome).toBeLessThan(0.1);
    });
  });
});
