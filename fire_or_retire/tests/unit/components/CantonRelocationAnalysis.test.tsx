import { describe, it, expect, beforeEach, vi } from 'vitest';
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import CantonRelocationAnalysis from '../../../src/components/CantonRelocationAnalysis';

// Mock the useTranslation hook
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string, defaultValue?: string) => defaultValue || key,
  }),
}));

// Mock the useLocalStorage hook
vi.mock('../../../src/hooks/useLocalStorage', () => ({
  useLocalStorage: jest.fn((key: string, defaultValue: any) => [defaultValue, jest.fn()]),
}));

const mockUserData = {
  monthlyIncome: 10000,
  currentSavings: 250000,
  familySize: 2,
};

describe('CantonRelocationAnalysis', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the Canton Relocation Analysis component', () => {
    render(
      <CantonRelocationAnalysis
        darkMode={false}
        userData={mockUserData}
      />,
    );

    expect(screen.getByText('🏛️ Canton Relocation Tax Optimizer')).toBeInTheDocument();
    expect(screen.getByText(/Analyze potential tax savings and lifestyle impacts/)).toBeInTheDocument();
  });

  it('displays analysis configuration options', () => {
    render(
      <CantonRelocationAnalysis
        darkMode={false}
        userData={mockUserData}
      />,
    );

    expect(screen.getByText('⚙️ Analysis Configuration')).toBeInTheDocument();
    expect(screen.getByText('Current Canton')).toBeInTheDocument();
    expect(screen.getByText('Sort By')).toBeInTheDocument();
    expect(screen.getByText('Show only recommended')).toBeInTheDocument();
  });

  it('shows loading state during analysis', () => {
    render(
      <CantonRelocationAnalysis
        darkMode={false}
        userData={mockUserData}
      />,
    );

    expect(screen.getByText(/Analyzing canton relocation opportunities/)).toBeInTheDocument();
  });

  it('generates relocation analysis after loading', async () => {
    render(
      <CantonRelocationAnalysis
        darkMode={false}
        userData={mockUserData}
      />,
    );

    await waitFor(() => {
      // Should show canton analysis results
      const cantonElements = screen.queryAllByText(/\(ZG\)|\(SZ\)|\(NW\)/);
      expect(cantonElements.length).toBeGreaterThan(0);
    }, { timeout: 5000 });
  });

  it('displays Swiss canton options in current canton selector', () => {
    render(
      <CantonRelocationAnalysis
        darkMode={false}
        userData={mockUserData}
      />,
    );

    const currentCantonSelect = screen.getByDisplayValue(/Zurich \(ZH\)/);
    expect(currentCantonSelect).toBeInTheDocument();

    // Test changing current canton
    fireEvent.change(currentCantonSelect, { target: { value: 'ZG' } });
    fireEvent.change(currentCantonSelect, { target: { value: 'GE' } });
  });

  it('handles sort by options correctly', async () => {
    render(
      <CantonRelocationAnalysis
        darkMode={false}
        userData={mockUserData}
      />,
    );

    await waitFor(() => {
      const sortSelect = screen.getByDisplayValue('Net Benefit');
      expect(sortSelect).toBeInTheDocument();
    }, { timeout: 3000 });

    const sortSelect = screen.getByDisplayValue('Net Benefit');

    // Test different sort options
    fireEvent.change(sortSelect, { target: { value: 'savings' } });
    fireEvent.change(sortSelect, { target: { value: 'score' } });
    fireEvent.change(sortSelect, { target: { value: 'netBenefit' } });
  });

  it('handles show only recommended filter', async () => {
    render(
      <CantonRelocationAnalysis
        darkMode={false}
        userData={mockUserData}
      />,
    );

    await waitFor(() => {
      const checkbox = screen.getByRole('checkbox');
      expect(checkbox).toBeInTheDocument();
    }, { timeout: 3000 });

    const checkbox = screen.getByRole('checkbox');

    // Test toggling the filter
    fireEvent.click(checkbox);
    expect(checkbox).toBeChecked();

    fireEvent.click(checkbox);
    expect(checkbox).not.toBeChecked();
  });

  it('displays canton analysis with key metrics', async () => {
    render(
      <CantonRelocationAnalysis
        darkMode={false}
        userData={mockUserData}
      />,
    );

    await waitFor(() => {
      // Should show key metrics
      expect(screen.getByText('Tax Savings')).toBeInTheDocument();
      expect(screen.getByText('Cost Difference')).toBeInTheDocument();
      expect(screen.getByText('Payback Period')).toBeInTheDocument();
      expect(screen.getByText('20-Year Value')).toBeInTheDocument();
    }, { timeout: 5000 });
  });

  it('shows canton advantages and considerations', async () => {
    render(
      <CantonRelocationAnalysis
        darkMode={false}
        userData={mockUserData}
      />,
    );

    await waitFor(() => {
      expect(screen.getByText('✅ Advantages')).toBeInTheDocument();
      expect(screen.getByText('⚠️ Considerations')).toBeInTheDocument();
    }, { timeout: 5000 });
  });

  it('displays recommendation levels correctly', async () => {
    render(
      <CantonRelocationAnalysis
        darkMode={false}
        userData={mockUserData}
      />,
    );

    await waitFor(() => {
      // Should show recommendation levels
      const recommendations = ['🌟 Highly Recommended', '👍 Recommended', '🤔 Neutral', '❌ Not Recommended'];
      let foundRecommendation = false;

      for (const rec of recommendations) {
        if (screen.queryByText(rec)) {
          foundRecommendation = true;
          break;
        }
      }

      expect(foundRecommendation).toBe(true);
    }, { timeout: 5000 });
  });

  it('shows CHF formatting for financial values', async () => {
    render(
      <CantonRelocationAnalysis
        darkMode={false}
        userData={mockUserData}
      />,
    );

    await waitFor(() => {
      expect(screen.getByText(/CHF/)).toBeInTheDocument();
    }, { timeout: 5000 });
  });

  it('displays canton details and statistics', async () => {
    render(
      <CantonRelocationAnalysis
        darkMode={false}
        userData={mockUserData}
      />,
    );

    await waitFor(() => {
      // Should show canton statistics
      expect(screen.getByText('Income Tax:')).toBeInTheDocument();
      expect(screen.getByText('Quality of Life:')).toBeInTheDocument();
      expect(screen.getByText('Employment:')).toBeInTheDocument();
      expect(screen.getByText('Languages:')).toBeInTheDocument();
    }, { timeout: 5000 });
  });

  it('handles refresh analysis functionality', async () => {
    render(
      <CantonRelocationAnalysis
        darkMode={false}
        userData={mockUserData}
      />,
    );

    await waitFor(() => {
      expect(screen.getByText('🔄 Refresh Analysis')).toBeInTheDocument();
    }, { timeout: 5000 });

    const refreshButton = screen.getByText('🔄 Refresh Analysis');
    fireEvent.click(refreshButton);

    expect(screen.getByText(/Analyzing.../)).toBeInTheDocument();
  });

  it('shows empty state when no opportunities found', async () => {
    // Test with data that might not generate recommendations
    const limitedUserData = {
      monthlyIncome: 0,
      currentSavings: 0,
      familySize: 1,
    };

    render(
      <CantonRelocationAnalysis
        darkMode={false}
        userData={limitedUserData}
      />,
    );

    // Enable show only recommended filter
    await waitFor(() => {
      const checkbox = screen.getByRole('checkbox');
      fireEvent.click(checkbox);
    }, { timeout: 3000 });

    await waitFor(() => {
      // Might show no opportunities message
      const noOpportunities = screen.queryByText(/No relocation opportunities found/);
      if (noOpportunities) {
        expect(noOpportunities).toBeInTheDocument();
      }
    }, { timeout: 5000 });
  });

  it('handles different family sizes', async () => {
    const largeFamilyData = {
      ...mockUserData,
      familySize: 4,
    };

    render(
      <CantonRelocationAnalysis
        darkMode={false}
        userData={largeFamilyData}
      />,
    );

    await waitFor(() => {
      // Should generate analysis for larger family
      const cantonElements = screen.queryAllByText(/\(ZG\)|\(SZ\)|\(NW\)/);
      expect(cantonElements.length).toBeGreaterThan(0);
    }, { timeout: 5000 });
  });

  it('works in dark mode', () => {
    render(
      <CantonRelocationAnalysis
        darkMode={true}
        userData={mockUserData}
      />,
    );

    expect(screen.getByText('🏛️ Canton Relocation Tax Optimizer')).toBeInTheDocument();
  });

  it('handles edge cases gracefully', async () => {
    const edgeCaseData = {
      monthlyIncome: 0,
      currentSavings: 0,
      familySize: 0,
    };

    render(
      <CantonRelocationAnalysis
        darkMode={false}
        userData={edgeCaseData}
      />,
    );

    // Should handle gracefully without crashing
    await waitFor(() => {
      expect(screen.getByText('🏛️ Canton Relocation Tax Optimizer')).toBeInTheDocument();
    }, { timeout: 5000 });
  });

  it('shows score ratings for cantons', async () => {
    render(
      <CantonRelocationAnalysis
        darkMode={false}
        userData={mockUserData}
      />,
    );

    await waitFor(() => {
      // Should show score ratings
      expect(screen.getByText(/Score:/)).toBeInTheDocument();
      expect(screen.getByText(/\/100/)).toBeInTheDocument();
    }, { timeout: 5000 });
  });

  it('displays payback period calculations', async () => {
    render(
      <CantonRelocationAnalysis
        darkMode={false}
        userData={mockUserData}
      />,
    );

    await waitFor(() => {
      // Should show payback period
      const paybackElements = screen.queryAllByText(/months|years/);
      expect(paybackElements.length).toBeGreaterThan(0);
    }, { timeout: 5000 });
  });

  it('shows language information for cantons', async () => {
    render(
      <CantonRelocationAnalysis
        darkMode={false}
        userData={mockUserData}
      />,
    );

    await waitFor(() => {
      // Should show language information
      const languages = ['German', 'French', 'Italian', 'English'];
      let foundLanguage = false;

      for (const lang of languages) {
        if (screen.queryByText(lang)) {
          foundLanguage = true;
          break;
        }
      }

      expect(foundLanguage).toBe(true);
    }, { timeout: 5000 });
  });
});
