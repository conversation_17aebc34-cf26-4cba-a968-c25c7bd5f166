/**
 * Unit Tests for DataVisualizationDashboard Component
 * Comprehensive testing of dashboard functionality and user interactions
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import '@testing-library/jest-dom';
import DataVisualizationDashboard from '../../../src/components/DataVisualizationDashboard';
import { UserFinancialData } from '../../../src/types/financial';

// Mock D3.js
vi.mock('d3', () => ({
  select: vi.fn(() => ({
    selectAll: vi.fn(() => ({
      data: vi.fn(() => ({
        enter: vi.fn(() => ({
          append: vi.fn(() => ({
            attr: vi.fn(() => ({ attr: vi.fn() })),
            style: vi.fn(() => ({ style: vi.fn() })),
          })),
        })),
        exit: vi.fn(() => ({ remove: vi.fn() })),
      })),
    })),
    append: vi.fn(() => ({
      attr: vi.fn(() => ({ attr: vi.fn() })),
      style: vi.fn(() => ({ style: vi.fn() })),
    })),
  })),
  scaleTime: vi.fn(() => ({
    domain: vi.fn(() => ({ range: vi.fn() })),
    range: vi.fn(() => ({ domain: vi.fn() })),
  })),
  scaleLinear: vi.fn(() => ({
    domain: vi.fn(() => ({ range: vi.fn() })),
    range: vi.fn(() => ({ domain: vi.fn() })),
    nice: vi.fn(),
  })),
  line: vi.fn(() => ({
    x: vi.fn(() => ({ y: vi.fn() })),
    y: vi.fn(() => ({ x: vi.fn() })),
    curve: vi.fn(),
  })),
  axisBottom: vi.fn(),
  axisLeft: vi.fn(),
}));

// Mock performance API
Object.defineProperty(window, 'performance', {
  value: {
    now: vi.fn(() => Date.now()),
    memory: {
      usedJSHeapSize: 50 * 1024 * 1024, // 50MB
      totalJSHeapSize: 100 * 1024 * 1024, // 100MB
    },
  },
});

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

describe('DataVisualizationDashboard', () => {
  const mockUserData: UserFinancialData = {
    currentAge: 30,
    retirementAge: 60,
    currentSavings: 100000,
    monthlyIncome: 8000,
    monthlyExpenses: 5000,
    expectedReturn: 7,
    inflationRate: 2,
    safeWithdrawalRate: 4,
  };

  const mockExpenses = [
    { id: '1', category: 'Housing', amount: 2000, isActive: true, priority: 'high' as const },
    { id: '2', category: 'Food', amount: 800, isActive: true, priority: 'medium' as const },
    { id: '3', category: 'Transport', amount: 500, isActive: true, priority: 'low' as const },
  ];

  const mockInvestments = [
    { id: '1', name: 'Swiss ETF', amount: 50000, type: 'stocks' as const, expectedReturn: 8 },
    { id: '2', name: 'Bonds', amount: 30000, type: 'bonds' as const, expectedReturn: 3 },
    { id: '3', name: 'Real Estate', amount: 20000, type: 'real-estate' as const, expectedReturn: 6 },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    // Mock localStorage
    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: vi.fn(() => null),
        setItem: vi.fn(),
        removeItem: vi.fn(),
        clear: vi.fn(),
      },
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Component Rendering', () => {
    it('renders dashboard with all main sections', () => {
      render(
        <DataVisualizationDashboard
          darkMode={false}
          userData={mockUserData}
          expenses={mockExpenses}
          investments={mockInvestments}
        />,
      );

      expect(screen.getByText(/Data Visualization Dashboard/i)).toBeInTheDocument();
      expect(screen.getByText(/Enhanced View/i)).toBeInTheDocument();
      expect(screen.getByText(/Mobile View/i)).toBeInTheDocument();
      expect(screen.getByText(/Performance Monitor/i)).toBeInTheDocument();
    });

    it('renders with dark mode styling', () => {
      render(
        <DataVisualizationDashboard
          darkMode={true}
          userData={mockUserData}
          expenses={mockExpenses}
          investments={mockInvestments}
        />,
      );

      const dashboard = screen.getByTestId('visualization-dashboard');
      expect(dashboard).toHaveClass('dark-mode');
    });

    it('displays dashboard statistics correctly', () => {
      render(
        <DataVisualizationDashboard
          darkMode={false}
          userData={mockUserData}
          expenses={mockExpenses}
          investments={mockInvestments}
        />,
      );

      expect(screen.getByText(/Net Worth/i)).toBeInTheDocument();
      expect(screen.getByText(/CHF 100,000/i)).toBeInTheDocument();
      expect(screen.getByText(/Savings Rate/i)).toBeInTheDocument();
      expect(screen.getByText(/37.5%/i)).toBeInTheDocument(); // (8000-5000)/8000 * 100
    });
  });

  describe('View Switching', () => {
    it('switches between Enhanced and Mobile views', async () => {
      render(
        <DataVisualizationDashboard
          darkMode={false}
          userData={mockUserData}
          expenses={mockExpenses}
          investments={mockInvestments}
        />,
      );

      const mobileViewButton = screen.getByText(/Mobile View/i);
      fireEvent.click(mobileViewButton);

      await waitFor(() => {
        expect(screen.getByTestId('mobile-optimized-chart')).toBeInTheDocument();
      });

      const enhancedViewButton = screen.getByText(/Enhanced View/i);
      fireEvent.click(enhancedViewButton);

      await waitFor(() => {
        expect(screen.getByTestId('enhanced-historical-charts')).toBeInTheDocument();
      });
    });

    it('shows performance monitor when selected', async () => {
      render(
        <DataVisualizationDashboard
          darkMode={false}
          userData={mockUserData}
          expenses={mockExpenses}
          investments={mockInvestments}
        />,
      );

      const performanceButton = screen.getByText(/Performance Monitor/i);
      fireEvent.click(performanceButton);

      await waitFor(() => {
        expect(screen.getByTestId('chart-performance-monitor')).toBeInTheDocument();
        expect(screen.getByText(/Render Time/i)).toBeInTheDocument();
        expect(screen.getByText(/Memory Usage/i)).toBeInTheDocument();
      });
    });
  });

  describe('Settings Management', () => {
    it('toggles performance monitor setting', async () => {
      render(
        <DataVisualizationDashboard
          darkMode={false}
          userData={mockUserData}
          expenses={mockExpenses}
          investments={mockInvestments}
        />,
      );

      const settingsButton = screen.getByLabelText(/Dashboard Settings/i);
      fireEvent.click(settingsButton);

      const performanceToggle = screen.getByLabelText(/Show Performance Monitor/i);
      fireEvent.click(performanceToggle);

      expect(localStorage.setItem).toHaveBeenCalledWith(
        'dashboardSettings',
        expect.stringContaining('"showPerformanceMonitor":true'),
      );
    });

    it('toggles mobile optimization setting', async () => {
      render(
        <DataVisualizationDashboard
          darkMode={false}
          userData={mockUserData}
          expenses={mockExpenses}
          investments={mockInvestments}
        />,
      );

      const settingsButton = screen.getByLabelText(/Dashboard Settings/i);
      fireEvent.click(settingsButton);

      const mobileToggle = screen.getByLabelText(/Enable Mobile Optimization/i);
      fireEvent.click(mobileToggle);

      expect(localStorage.setItem).toHaveBeenCalledWith(
        'dashboardSettings',
        expect.stringContaining('"enableMobileOptimization":true'),
      );
    });

    it('loads settings from localStorage on mount', () => {
      const mockSettings = {
        showPerformanceMonitor: true,
        enableMobileOptimization: false,
        autoDetectMobile: true,
      };

      (localStorage.getItem as any).mockReturnValue(JSON.stringify(mockSettings));

      render(
        <DataVisualizationDashboard
          darkMode={false}
          userData={mockUserData}
          expenses={mockExpenses}
          investments={mockInvestments}
        />,
      );

      expect(localStorage.getItem).toHaveBeenCalledWith('dashboardSettings');
    });
  });

  describe('Data Processing', () => {
    it('calculates dashboard statistics correctly', () => {
      render(
        <DataVisualizationDashboard
          darkMode={false}
          userData={mockUserData}
          expenses={mockExpenses}
          investments={mockInvestments}
        />,
      );

      // Net Worth = currentSavings + investments
      const expectedNetWorth = mockUserData.currentSavings + 
        mockInvestments.reduce((sum, inv) => sum + inv.amount, 0);
      
      expect(screen.getByText(new RegExp(expectedNetWorth.toLocaleString()))).toBeInTheDocument();

      // Savings Rate = (income - expenses) / income * 100
      const expectedSavingsRate = ((mockUserData.monthlyIncome - mockUserData.monthlyExpenses) / 
        mockUserData.monthlyIncome * 100).toFixed(1);
      
      expect(screen.getByText(new RegExp(`${expectedSavingsRate}%`))).toBeInTheDocument();
    });

    it('handles missing optional data gracefully', () => {
      render(
        <DataVisualizationDashboard
          darkMode={false}
          userData={mockUserData}
        />,
      );

      // Should still render without expenses and investments
      expect(screen.getByText(/Data Visualization Dashboard/i)).toBeInTheDocument();
      expect(screen.getByText(/Net Worth/i)).toBeInTheDocument();
    });

    it('updates statistics when data changes', () => {
      const { rerender } = render(
        <DataVisualizationDashboard
          darkMode={false}
          userData={mockUserData}
          expenses={mockExpenses}
          investments={mockInvestments}
        />,
      );

      const updatedUserData = {
        ...mockUserData,
        monthlyIncome: 10000,
        monthlyExpenses: 6000,
      };

      rerender(
        <DataVisualizationDashboard
          darkMode={false}
          userData={updatedUserData}
          expenses={mockExpenses}
          investments={mockInvestments}
        />,
      );

      // New savings rate should be (10000-6000)/10000 * 100 = 40%
      expect(screen.getByText(/40.0%/i)).toBeInTheDocument();
    });
  });

  describe('Performance Monitoring', () => {
    it('tracks render performance', async () => {
      const performanceNowSpy = vi.spyOn(performance, 'now');
      performanceNowSpy.mockReturnValueOnce(1000).mockReturnValueOnce(1500);

      render(
        <DataVisualizationDashboard
          darkMode={false}
          userData={mockUserData}
          expenses={mockExpenses}
          investments={mockInvestments}
        />,
      );

      // Switch to performance monitor view
      const performanceButton = screen.getByText(/Performance Monitor/i);
      fireEvent.click(performanceButton);

      await waitFor(() => {
        expect(screen.getByTestId('chart-performance-monitor')).toBeInTheDocument();
      });

      expect(performanceNowSpy).toHaveBeenCalled();
    });

    it('displays memory usage information', async () => {
      render(
        <DataVisualizationDashboard
          darkMode={false}
          userData={mockUserData}
          expenses={mockExpenses}
          investments={mockInvestments}
        />,
      );

      const performanceButton = screen.getByText(/Performance Monitor/i);
      fireEvent.click(performanceButton);

      await waitFor(() => {
        expect(screen.getByText(/Memory Usage/i)).toBeInTheDocument();
        expect(screen.getByText(/50 MB/i)).toBeInTheDocument(); // Mocked value
      });
    });
  });

  describe('Error Handling', () => {
    it('handles invalid user data gracefully', () => {
      const invalidUserData = {
        ...mockUserData,
        monthlyIncome: -1000, // Invalid negative income
      };

      render(
        <DataVisualizationDashboard
          darkMode={false}
          userData={invalidUserData}
          expenses={mockExpenses}
          investments={mockInvestments}
        />,
      );

      // Should still render but show error state or default values
      expect(screen.getByText(/Data Visualization Dashboard/i)).toBeInTheDocument();
    });

    it('handles D3.js rendering errors', () => {
      // Mock D3 to throw an error
      vi.mocked(require('d3').select).mockImplementation(() => {
        throw new Error('D3 rendering error');
      });

      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      render(
        <DataVisualizationDashboard
          darkMode={false}
          userData={mockUserData}
          expenses={mockExpenses}
          investments={mockInvestments}
        />,
      );

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Error rendering chart'),
        expect.any(Error),
      );

      consoleSpy.mockRestore();
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA labels and roles', () => {
      render(
        <DataVisualizationDashboard
          darkMode={false}
          userData={mockUserData}
          expenses={mockExpenses}
          investments={mockInvestments}
        />,
      );

      expect(screen.getByRole('main')).toHaveAttribute('aria-label', 'Data Visualization Dashboard');
      expect(screen.getByRole('tablist')).toBeInTheDocument();
      expect(screen.getAllByRole('tab')).toHaveLength(3); // Enhanced, Mobile, Performance
    });

    it('supports keyboard navigation', () => {
      render(
        <DataVisualizationDashboard
          darkMode={false}
          userData={mockUserData}
          expenses={mockExpenses}
          investments={mockInvestments}
        />,
      );

      const enhancedTab = screen.getByRole('tab', { name: /Enhanced View/i });
      enhancedTab.focus();
      expect(enhancedTab).toHaveFocus();

      fireEvent.keyDown(enhancedTab, { key: 'ArrowRight' });
      const mobileTab = screen.getByRole('tab', { name: /Mobile View/i });
      expect(mobileTab).toHaveFocus();
    });

    it('provides screen reader friendly content', () => {
      render(
        <DataVisualizationDashboard
          darkMode={false}
          userData={mockUserData}
          expenses={mockExpenses}
          investments={mockInvestments}
        />,
      );

      expect(screen.getByText(/Net Worth: CHF 100,000/i)).toBeInTheDocument();
      expect(screen.getByText(/Savings Rate: 37.5%/i)).toBeInTheDocument();
    });
  });

  describe('Responsive Behavior', () => {
    it('adapts to mobile viewport', () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });

      render(
        <DataVisualizationDashboard
          darkMode={false}
          userData={mockUserData}
          expenses={mockExpenses}
          investments={mockInvestments}
        />,
      );

      const dashboard = screen.getByTestId('visualization-dashboard');
      expect(dashboard).toHaveClass('mobile-layout');
    });

    it('handles window resize events', () => {
      render(
        <DataVisualizationDashboard
          darkMode={false}
          userData={mockUserData}
          expenses={mockExpenses}
          investments={mockInvestments}
        />,
      );

      // Simulate window resize
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 768,
      });

      fireEvent(window, new Event('resize'));

      // Should trigger responsive layout updates
      expect(global.ResizeObserver).toHaveBeenCalled();
    });
  });
});
