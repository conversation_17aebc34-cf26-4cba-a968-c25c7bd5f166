import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { describe, it, expect, beforeEach, vi } from 'vitest';
import SwissFinancialChart, { SwissFinancialDataPoint, SwissChartConfig } from '../../../../src/components/charts/SwissFinancialChart';

// Mock the useTranslation hook
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string, defaultValue?: string) => defaultValue || key,
  }),
}));

// Mock D3.js
vi.mock('d3', () => ({
  select: vi.fn(() => ({
    selectAll: vi.fn(() => ({ remove: vi.fn() })),
    attr: vi.fn(() => ({ attr: vi.fn() })),
    append: vi.fn(() => ({
      attr: vi.fn(() => ({ attr: vi.fn() })),
      datum: vi.fn(() => ({ attr: vi.fn() })),
    })),
  })),
  scaleTime: vi.fn(() => ({
    domain: vi.fn(() => ({ range: vi.fn() })),
    range: vi.fn(() => ({ domain: vi.fn() })),
  })),
  scaleLinear: vi.fn(() => ({
    domain: vi.fn(() => ({ range: vi.fn(), nice: vi.fn() })),
    range: vi.fn(() => ({ domain: vi.fn(), nice: vi.fn() })),
    nice: vi.fn(() => ({ domain: vi.fn(), range: vi.fn() })),
  })),
  line: vi.fn(() => ({
    x: vi.fn(() => ({ y: vi.fn(), curve: vi.fn() })),
    y: vi.fn(() => ({ x: vi.fn(), curve: vi.fn() })),
    curve: vi.fn(),
  })),
  area: vi.fn(() => ({
    x: vi.fn(() => ({ y0: vi.fn(), y1: vi.fn(), curve: vi.fn() })),
    y0: vi.fn(() => ({ x: vi.fn(), y1: vi.fn(), curve: vi.fn() })),
    y1: vi.fn(() => ({ x: vi.fn(), y0: vi.fn(), curve: vi.fn() })),
    curve: vi.fn(),
  })),
  pie: vi.fn(() => ({
    value: vi.fn(),
  })),
  arc: vi.fn(() => ({
    innerRadius: vi.fn(() => ({ outerRadius: vi.fn() })),
    outerRadius: vi.fn(() => ({ innerRadius: vi.fn() })),
  })),
  axisBottom: vi.fn(() => ({
    tickSize: vi.fn(),
    tickFormat: vi.fn(),
  })),
  axisLeft: vi.fn(() => ({
    tickSize: vi.fn(),
    tickFormat: vi.fn(),
  })),
  extent: vi.fn(() => [new Date('2023-01-01'), new Date('2023-12-31')]),
  curveMonotoneX: 'curveMonotoneX',
  schemeCategory10: ['#1f77b4', '#ff7f0e', '#2ca02c'],
  formatLocale: vi.fn(() => ({
    format: vi.fn(() => vi.fn((value) => `CHF ${value}`)),
  })),
}));

// Mock the useD3Chart hook
vi.mock('../../../../src/hooks/useD3Chart', () => ({
  useD3Chart: () => ({
    svgRef: { current: document.createElementNS('http://www.w3.org/2000/svg', 'svg') },
    containerRef: { current: document.createElement('div') },
    createSVG: vi.fn(() => ({
      svg: {
        selectAll: vi.fn(() => ({ remove: vi.fn() })),
        attr: vi.fn(() => ({ attr: vi.fn() })),
        append: vi.fn(() => ({
          attr: vi.fn(() => ({ attr: vi.fn() })),
          datum: vi.fn(() => ({ attr: vi.fn() })),
        })),
      },
      dimensions: { width: 1000, height: 400, margin: { top: 20, right: 60, bottom: 80, left: 80 } },
    })),
    createTimeScale: vi.fn(() => vi.fn()),
    createLinearScale: vi.fn(() => vi.fn()),
    animatePathDraw: vi.fn(),
    createTooltip: vi.fn(),
    showTooltip: vi.fn(),
    hideTooltip: vi.fn(),
    formatCurrency: vi.fn((value) => `CHF ${value}`),
    formatDate: vi.fn((date) => date.toLocaleDateString()),
    formatPercentage: vi.fn((value) => `${value}%`),
  }),
}));

const mockData: SwissFinancialDataPoint[] = [
  {
    date: new Date('2023-01-01'),
    value: 10000,
    category: 'netWorth',
    label: 'Net Worth',
  },
  {
    date: new Date('2023-02-01'),
    value: 12000,
    category: 'netWorth',
    label: 'Net Worth',
  },
  {
    date: new Date('2023-03-01'),
    value: 11500,
    category: 'netWorth',
    label: 'Net Worth',
  },
];

const mockConfig: SwissChartConfig = {
  type: 'line',
  metric: 'netWorth',
  showTrendline: false,
  showConfidenceInterval: false,
  enableZoom: true,
  enableBrush: true,
  showCantonComparison: false,
  swissFormatting: true,
  interactive: true,
  animated: true,
  responsive: true,
};

describe('SwissFinancialChart', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the Swiss financial chart component', () => {
    render(
      <SwissFinancialChart
        data={mockData}
        config={mockConfig}
        darkMode={false}
      />
    );

    expect(screen.getByRole('img', { hidden: true })).toBeInTheDocument();
  });

  it('handles different chart types', () => {
    const { rerender } = render(
      <SwissFinancialChart
        data={mockData}
        config={{ ...mockConfig, type: 'line' }}
        darkMode={false}
      />
    );

    expect(screen.getByRole('img', { hidden: true })).toBeInTheDocument();

    // Test area chart
    rerender(
      <SwissFinancialChart
        data={mockData}
        config={{ ...mockConfig, type: 'area' }}
        darkMode={false}
      />
    );

    expect(screen.getByRole('img', { hidden: true })).toBeInTheDocument();

    // Test bar chart
    rerender(
      <SwissFinancialChart
        data={mockData}
        config={{ ...mockConfig, type: 'bar' }}
        darkMode={false}
      />
    );

    expect(screen.getByRole('img', { hidden: true })).toBeInTheDocument();

    // Test donut chart
    rerender(
      <SwissFinancialChart
        data={mockData}
        config={{ ...mockConfig, type: 'donut' }}
        darkMode={false}
      />
    );

    expect(screen.getByRole('img', { hidden: true })).toBeInTheDocument();
  });

  it('handles different metrics', () => {
    const metrics: Array<SwissChartConfig['metric']> = [
      'netWorth', 'savings', 'taxes', 'pillar3a', 'healthcare', 'expenses'
    ];

    metrics.forEach(metric => {
      const { rerender } = render(
        <SwissFinancialChart
          data={mockData}
          config={{ ...mockConfig, metric }}
          darkMode={false}
        />
      );

      expect(screen.getByRole('img', { hidden: true })).toBeInTheDocument();
    });
  });

  it('works in dark mode', () => {
    render(
      <SwissFinancialChart
        data={mockData}
        config={mockConfig}
        darkMode={true}
      />
    );

    expect(screen.getByRole('img', { hidden: true })).toBeInTheDocument();
  });

  it('handles empty data gracefully', () => {
    render(
      <SwissFinancialChart
        data={[]}
        config={mockConfig}
        darkMode={false}
      />
    );

    expect(screen.getByRole('img', { hidden: true })).toBeInTheDocument();
  });

  it('handles click events', () => {
    const onDataPointClick = vi.fn();
    
    render(
      <SwissFinancialChart
        data={mockData}
        config={mockConfig}
        darkMode={false}
        onDataPointClick={onDataPointClick}
      />
    );

    // Since we're mocking D3, we can't easily test actual click events
    // but we can verify the component renders with the callback
    expect(screen.getByRole('img', { hidden: true })).toBeInTheDocument();
  });

  it('handles hover events', () => {
    const onDataPointHover = vi.fn();
    
    render(
      <SwissFinancialChart
        data={mockData}
        config={mockConfig}
        darkMode={false}
        onDataPointHover={onDataPointHover}
      />
    );

    expect(screen.getByRole('img', { hidden: true })).toBeInTheDocument();
  });

  it('handles zoom events', () => {
    const onZoomChange = vi.fn();
    
    render(
      <SwissFinancialChart
        data={mockData}
        config={mockConfig}
        darkMode={false}
        onZoomChange={onZoomChange}
      />
    );

    expect(screen.getByRole('img', { hidden: true })).toBeInTheDocument();
  });

  it('applies custom dimensions', () => {
    render(
      <SwissFinancialChart
        data={mockData}
        config={mockConfig}
        darkMode={false}
        width={800}
        height={600}
      />
    );

    expect(screen.getByRole('img', { hidden: true })).toBeInTheDocument();
  });

  it('applies custom className', () => {
    render(
      <SwissFinancialChart
        data={mockData}
        config={mockConfig}
        darkMode={false}
        className="custom-chart-class"
      />
    );

    const chartContainer = screen.getByRole('img', { hidden: true }).parentElement;
    expect(chartContainer).toHaveClass('custom-chart-class');
  });

  it('handles responsive configuration', () => {
    render(
      <SwissFinancialChart
        data={mockData}
        config={{ ...mockConfig, responsive: true }}
        darkMode={false}
      />
    );

    expect(screen.getByRole('img', { hidden: true })).toBeInTheDocument();
  });

  it('handles animation configuration', () => {
    render(
      <SwissFinancialChart
        data={mockData}
        config={{ ...mockConfig, animated: false }}
        darkMode={false}
      />
    );

    expect(screen.getByRole('img', { hidden: true })).toBeInTheDocument();
  });

  it('handles interactive configuration', () => {
    render(
      <SwissFinancialChart
        data={mockData}
        config={{ ...mockConfig, interactive: false }}
        darkMode={false}
      />
    );

    expect(screen.getByRole('img', { hidden: true })).toBeInTheDocument();
  });

  it('handles Swiss formatting configuration', () => {
    render(
      <SwissFinancialChart
        data={mockData}
        config={{ ...mockConfig, swissFormatting: true }}
        darkMode={false}
      />
    );

    expect(screen.getByRole('img', { hidden: true })).toBeInTheDocument();
  });

  it('handles trendline configuration', () => {
    render(
      <SwissFinancialChart
        data={mockData}
        config={{ ...mockConfig, showTrendline: true }}
        darkMode={false}
      />
    );

    expect(screen.getByRole('img', { hidden: true })).toBeInTheDocument();
  });

  it('handles confidence interval configuration', () => {
    render(
      <SwissFinancialChart
        data={mockData}
        config={{ ...mockConfig, showConfidenceInterval: true }}
        darkMode={false}
      />
    );

    expect(screen.getByRole('img', { hidden: true })).toBeInTheDocument();
  });

  it('handles canton comparison configuration', () => {
    render(
      <SwissFinancialChart
        data={mockData}
        config={{ ...mockConfig, showCantonComparison: true }}
        darkMode={false}
      />
    );

    expect(screen.getByRole('img', { hidden: true })).toBeInTheDocument();
  });

  it('handles zoom and brush configuration', () => {
    render(
      <SwissFinancialChart
        data={mockData}
        config={{ 
          ...mockConfig, 
          enableZoom: true,
          enableBrush: true 
        }}
        darkMode={false}
      />
    );

    expect(screen.getByRole('img', { hidden: true })).toBeInTheDocument();
  });

  it('handles data with different categories', () => {
    const mixedData: SwissFinancialDataPoint[] = [
      ...mockData,
      {
        date: new Date('2023-04-01'),
        value: 5000,
        category: 'savings',
        label: 'Savings',
      },
      {
        date: new Date('2023-05-01'),
        value: 3000,
        category: 'expenses',
        label: 'Expenses',
      },
    ];

    render(
      <SwissFinancialChart
        data={mixedData}
        config={mockConfig}
        darkMode={false}
      />
    );

    expect(screen.getByRole('img', { hidden: true })).toBeInTheDocument();
  });

  it('handles data with Swiss-specific properties', () => {
    const swissData: SwissFinancialDataPoint[] = [
      {
        date: new Date('2023-01-01'),
        value: 10000,
        category: 'netWorth',
        canton: 'ZH',
        taxRate: 25,
        pillar3a: 7056,
        healthcare: 500,
        label: 'Zurich Data',
      },
    ];

    render(
      <SwissFinancialChart
        data={swissData}
        config={mockConfig}
        darkMode={false}
      />
    );

    expect(screen.getByRole('img', { hidden: true })).toBeInTheDocument();
  });
});
