import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { describe, it, expect, beforeEach, vi } from 'vitest';
import EnhancedChartControls, { ChartControlsState } from '../../../../src/components/charts/EnhancedChartControls';

// Mock the useTranslation hook
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string, defaultValue?: string) => defaultValue || key,
  }),
}));

const mockState: ChartControlsState = {
  chartType: 'line',
  timeframe: '1Y',
  selectedMetrics: ['netWorth', 'savings'],
  selectedCantons: ['ZH', 'BE'],
  showTrendline: false,
  showConfidenceInterval: false,
  enableAnimations: true,
  enableInteractivity: true,
  showComparison: false,
  darkMode: false,
};

const mockOnChange = vi.fn();

describe('EnhancedChartControls', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the chart controls component', () => {
    render(
      <EnhancedChartControls
        state={mockState}
        onChange={mockOnChange}
      />
    );

    expect(screen.getByText('Chart Type')).toBeInTheDocument();
    expect(screen.getByText('Timeframe')).toBeInTheDocument();
    expect(screen.getByText('Metrics to Display')).toBeInTheDocument();
  });

  it('displays chart type options', () => {
    render(
      <EnhancedChartControls
        state={mockState}
        onChange={mockOnChange}
      />
    );

    expect(screen.getByText('Line Chart')).toBeInTheDocument();
    expect(screen.getByText('Area Chart')).toBeInTheDocument();
    expect(screen.getByText('Bar Chart')).toBeInTheDocument();
    expect(screen.getByText('Donut Chart')).toBeInTheDocument();
    expect(screen.getByText('Heat Map')).toBeInTheDocument();
  });

  it('handles chart type selection', () => {
    render(
      <EnhancedChartControls
        state={mockState}
        onChange={mockOnChange}
      />
    );

    const areaChartButton = screen.getByText('Area Chart');
    fireEvent.click(areaChartButton);

    expect(mockOnChange).toHaveBeenCalledWith({ chartType: 'area' });
  });

  it('displays timeframe options', () => {
    render(
      <EnhancedChartControls
        state={mockState}
        onChange={mockOnChange}
      />
    );

    expect(screen.getByText('1 Month')).toBeInTheDocument();
    expect(screen.getByText('3 Months')).toBeInTheDocument();
    expect(screen.getByText('6 Months')).toBeInTheDocument();
    expect(screen.getByText('1 Year')).toBeInTheDocument();
    expect(screen.getByText('2 Years')).toBeInTheDocument();
    expect(screen.getByText('5 Years')).toBeInTheDocument();
    expect(screen.getByText('All Time')).toBeInTheDocument();
  });

  it('handles timeframe selection', () => {
    render(
      <EnhancedChartControls
        state={mockState}
        onChange={mockOnChange}
      />
    );

    const sixMonthButton = screen.getByText('6 Months');
    fireEvent.click(sixMonthButton);

    expect(mockOnChange).toHaveBeenCalledWith({ timeframe: '6M' });
  });

  it('displays metric options', () => {
    render(
      <EnhancedChartControls
        state={mockState}
        onChange={mockOnChange}
        availableMetrics={['netWorth', 'savings', 'expenses', 'income']}
      />
    );

    expect(screen.getByText('Net Worth')).toBeInTheDocument();
    expect(screen.getByText('Savings')).toBeInTheDocument();
    expect(screen.getByText('Expenses')).toBeInTheDocument();
    expect(screen.getByText('Income')).toBeInTheDocument();
  });

  it('handles metric selection and deselection', () => {
    render(
      <EnhancedChartControls
        state={mockState}
        onChange={mockOnChange}
        availableMetrics={['netWorth', 'savings', 'expenses', 'income']}
      />
    );

    // Add a new metric
    const expensesButton = screen.getByText('Expenses');
    fireEvent.click(expensesButton);

    expect(mockOnChange).toHaveBeenCalledWith({
      selectedMetrics: ['netWorth', 'savings', 'expenses']
    });

    // Remove an existing metric
    const savingsButton = screen.getByText('Savings');
    fireEvent.click(savingsButton);

    expect(mockOnChange).toHaveBeenCalledWith({
      selectedMetrics: ['netWorth']
    });
  });

  it('shows canton selector when enabled', () => {
    render(
      <EnhancedChartControls
        state={mockState}
        onChange={mockOnChange}
        showCantonSelector={true}
        availableCantons={['ZH', 'BE', 'LU', 'ZG']}
      />
    );

    expect(screen.getByText('Swiss Cantons')).toBeInTheDocument();
    expect(screen.getByText('ZH')).toBeInTheDocument();
    expect(screen.getByText('BE')).toBeInTheDocument();
    expect(screen.getByText('LU')).toBeInTheDocument();
    expect(screen.getByText('ZG')).toBeInTheDocument();
  });

  it('handles canton selection and deselection', () => {
    render(
      <EnhancedChartControls
        state={mockState}
        onChange={mockOnChange}
        showCantonSelector={true}
        availableCantons={['ZH', 'BE', 'LU', 'ZG']}
      />
    );

    // Add a new canton
    const lucerneButton = screen.getByText('LU');
    fireEvent.click(lucerneButton);

    expect(mockOnChange).toHaveBeenCalledWith({
      selectedCantons: ['ZH', 'BE', 'LU']
    });

    // Remove an existing canton
    const bernButton = screen.getByText('BE');
    fireEvent.click(bernButton);

    expect(mockOnChange).toHaveBeenCalledWith({
      selectedCantons: ['ZH']
    });
  });

  it('shows advanced options when enabled', () => {
    render(
      <EnhancedChartControls
        state={mockState}
        onChange={mockOnChange}
        showAdvancedOptions={true}
      />
    );

    const advancedButton = screen.getByText('Advanced Options');
    expect(advancedButton).toBeInTheDocument();

    fireEvent.click(advancedButton);

    expect(screen.getByText('Show Trendline')).toBeInTheDocument();
    expect(screen.getByText('Confidence Interval')).toBeInTheDocument();
    expect(screen.getByText('Enable Animations')).toBeInTheDocument();
    expect(screen.getByText('Interactive Features')).toBeInTheDocument();
    expect(screen.getByText('Show Comparison')).toBeInTheDocument();
  });

  it('handles advanced option toggles', () => {
    render(
      <EnhancedChartControls
        state={mockState}
        onChange={mockOnChange}
        showAdvancedOptions={true}
      />
    );

    // Open advanced options
    const advancedButton = screen.getByText('Advanced Options');
    fireEvent.click(advancedButton);

    // Toggle trendline
    const trendlineCheckbox = screen.getByLabelText('Show Trendline');
    fireEvent.click(trendlineCheckbox);

    expect(mockOnChange).toHaveBeenCalledWith({ showTrendline: true });

    // Toggle confidence interval
    const confidenceCheckbox = screen.getByLabelText('Confidence Interval');
    fireEvent.click(confidenceCheckbox);

    expect(mockOnChange).toHaveBeenCalledWith({ showConfidenceInterval: true });

    // Toggle animations
    const animationsCheckbox = screen.getByLabelText('Enable Animations');
    fireEvent.click(animationsCheckbox);

    expect(mockOnChange).toHaveBeenCalledWith({ enableAnimations: false });
  });

  it('applies dark mode styling', () => {
    const darkModeState = { ...mockState, darkMode: true };

    render(
      <EnhancedChartControls
        state={darkModeState}
        onChange={mockOnChange}
      />
    );

    // Check that the component renders (dark mode styling is applied via CSS classes)
    expect(screen.getByText('Chart Type')).toBeInTheDocument();
  });

  it('filters available metrics correctly', () => {
    render(
      <EnhancedChartControls
        state={mockState}
        onChange={mockOnChange}
        availableMetrics={['netWorth', 'savings']} // Limited metrics
      />
    );

    expect(screen.getByText('Net Worth')).toBeInTheDocument();
    expect(screen.getByText('Savings')).toBeInTheDocument();
    expect(screen.queryByText('Expenses')).not.toBeInTheDocument();
    expect(screen.queryByText('Income')).not.toBeInTheDocument();
  });

  it('filters available cantons correctly', () => {
    render(
      <EnhancedChartControls
        state={mockState}
        onChange={mockOnChange}
        showCantonSelector={true}
        availableCantons={['ZH', 'BE']} // Limited cantons
      />
    );

    expect(screen.getByText('ZH')).toBeInTheDocument();
    expect(screen.getByText('BE')).toBeInTheDocument();
    expect(screen.queryByText('LU')).not.toBeInTheDocument();
    expect(screen.queryByText('ZG')).not.toBeInTheDocument();
  });

  it('applies custom className', () => {
    const { container } = render(
      <EnhancedChartControls
        state={mockState}
        onChange={mockOnChange}
        className="custom-controls-class"
      />
    );

    expect(container.firstChild).toHaveClass('custom-controls-class');
  });

  it('handles empty metric selection', () => {
    const emptyMetricsState = { ...mockState, selectedMetrics: [] };

    render(
      <EnhancedChartControls
        state={emptyMetricsState}
        onChange={mockOnChange}
        availableMetrics={['netWorth', 'savings']}
      />
    );

    const netWorthButton = screen.getByText('Net Worth');
    fireEvent.click(netWorthButton);

    expect(mockOnChange).toHaveBeenCalledWith({
      selectedMetrics: ['netWorth']
    });
  });

  it('handles empty canton selection', () => {
    const emptyCantonState = { ...mockState, selectedCantons: [] };

    render(
      <EnhancedChartControls
        state={emptyCantonState}
        onChange={mockOnChange}
        showCantonSelector={true}
        availableCantons={['ZH', 'BE']}
      />
    );

    const zurichButton = screen.getByText('ZH');
    fireEvent.click(zurichButton);

    expect(mockOnChange).toHaveBeenCalledWith({
      selectedCantons: ['ZH']
    });
  });

  it('shows metric icons and colors correctly', () => {
    render(
      <EnhancedChartControls
        state={mockState}
        onChange={mockOnChange}
        availableMetrics={['netWorth', 'savings', 'expenses']}
      />
    );

    // Check that metric buttons contain icons (emojis)
    expect(screen.getByText('💰')).toBeInTheDocument(); // Net Worth
    expect(screen.getByText('🏦')).toBeInTheDocument(); // Savings
    expect(screen.getByText('💸')).toBeInTheDocument(); // Expenses
  });

  it('shows canton flags correctly', () => {
    render(
      <EnhancedChartControls
        state={mockState}
        onChange={mockOnChange}
        showCantonSelector={true}
        availableCantons={['ZH', 'BE', 'ZG']}
      />
    );

    // Check that canton buttons contain flag emojis
    expect(screen.getByText('🏔️')).toBeInTheDocument(); // Zurich/Bern
    expect(screen.getByText('🐻')).toBeInTheDocument(); // Bern
    expect(screen.getByText('💰')).toBeInTheDocument(); // Zug
  });
});
