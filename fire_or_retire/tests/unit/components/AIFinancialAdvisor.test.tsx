import { describe, it, expect, beforeEach, vi } from 'vitest';
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import AIFinancialAdvisor from '../../../src/components/AIFinancialAdvisor';

// Mock the useTranslation hook
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string, defaultValue?: string) => defaultValue || key,
  }),
}));

// Mock the useLocalStorage hook
vi.mock('../../../src/hooks/useLocalStorage', () => ({
  useLocalStorage: jest.fn((key: string, defaultValue: any) => [defaultValue, jest.fn()]),
}));

const mockUserData = {
  currentAge: 35,
  retirementAge: 65,
  currentSavings: 100000,
  monthlyIncome: 8000,
  monthlyExpenses: 5000,
  expectedReturn: 7,
  inflationRate: 2.5,
  safeWithdrawalRate: 4,
};

const mockExpenses = [
  { id: '1', name: 'Housing', amount: 2000, category: 'housing' },
  { id: '2', name: 'Food', amount: 800, category: 'food' },
];

const mockInvestments = [
  { id: '1', name: 'Swiss ETF', currentValue: 50000, type: 'etf' },
  { id: '2', name: 'Bonds', currentValue: 30000, type: 'bonds' },
];

const mockSavingsGoals = [
  { id: '1', name: 'Emergency Fund', currentAmount: 20000, targetAmount: 30000 },
  { id: '2', name: 'Pillar 3a', currentAmount: 15000, targetAmount: 7056 },
];

describe('AIFinancialAdvisor', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the AI Financial Advisor component', () => {
    render(
      <AIFinancialAdvisor
        darkMode={false}
        userData={mockUserData}
        expenses={mockExpenses}
        investments={mockInvestments}
        savingsGoals={mockSavingsGoals}
      />,
    );

    expect(screen.getByText('🤖 AI Financial Advisor')).toBeInTheDocument();
    expect(screen.getByText(/Personalized financial insights/)).toBeInTheDocument();
  });

  it('displays loading state during analysis', () => {
    render(
      <AIFinancialAdvisor
        darkMode={false}
        userData={mockUserData}
        expenses={mockExpenses}
        investments={mockInvestments}
        savingsGoals={mockSavingsGoals}
      />,
    );

    expect(screen.getByText(/AI is analyzing your financial situation/)).toBeInTheDocument();
  });

  it('generates financial health score after analysis', async () => {
    render(
      <AIFinancialAdvisor
        darkMode={false}
        userData={mockUserData}
        expenses={mockExpenses}
        investments={mockInvestments}
        savingsGoals={mockSavingsGoals}
      />,
    );

    await waitFor(() => {
      expect(screen.getByText('📊 AI Financial Health Score')).toBeInTheDocument();
    }, { timeout: 5000 });

    expect(screen.getByText('Overall Score')).toBeInTheDocument();
    expect(screen.getByText('FIRE Readiness')).toBeInTheDocument();
    expect(screen.getByText('Risk Level')).toBeInTheDocument();
    expect(screen.getByText('Optimization')).toBeInTheDocument();
  });

  it('displays personalized FIRE plan', async () => {
    render(
      <AIFinancialAdvisor
        darkMode={false}
        userData={mockUserData}
        expenses={mockExpenses}
        investments={mockInvestments}
        savingsGoals={mockSavingsGoals}
      />,
    );

    await waitFor(() => {
      expect(screen.getByText('🎯 Your Personalized FIRE Plan')).toBeInTheDocument();
    }, { timeout: 5000 });

    // Should have numbered plan steps
    expect(screen.getByText('1')).toBeInTheDocument();
    expect(screen.getByText(/Primary Goal/)).toBeInTheDocument();
  });

  it('shows AI insights with category filtering', async () => {
    render(
      <AIFinancialAdvisor
        darkMode={false}
        userData={mockUserData}
        expenses={mockExpenses}
        investments={mockInvestments}
        savingsGoals={mockSavingsGoals}
      />,
    );

    await waitFor(() => {
      expect(screen.getByText('💡 AI Insights & Recommendations')).toBeInTheDocument();
    }, { timeout: 5000 });

    // Test category filter
    const categorySelect = screen.getByRole('combobox');
    expect(categorySelect).toBeInTheDocument();

    fireEvent.change(categorySelect, { target: { value: 'optimization' } });
    fireEvent.change(categorySelect, { target: { value: 'risk' } });
    fireEvent.change(categorySelect, { target: { value: 'swiss' } });
  });

  it('handles chat functionality', async () => {
    render(
      <AIFinancialAdvisor
        darkMode={false}
        userData={mockUserData}
        expenses={mockExpenses}
        investments={mockInvestments}
        savingsGoals={mockSavingsGoals}
      />,
    );

    await waitFor(() => {
      expect(screen.getByText('💬 Ask Your AI Financial Advisor')).toBeInTheDocument();
    }, { timeout: 5000 });

    const chatInput = screen.getByPlaceholderText(/Ask about investments/);
    const sendButton = screen.getByRole('button', { name: /📤/ });

    expect(chatInput).toBeInTheDocument();
    expect(sendButton).toBeInTheDocument();

    // Test sending a message
    fireEvent.change(chatInput, { target: { value: 'What about Pillar 3a?' } });
    fireEvent.click(sendButton);

    await waitFor(() => {
      expect(screen.getByText(/What about Pillar 3a?/)).toBeInTheDocument();
    }, { timeout: 3000 });
  });

  it('generates Swiss-specific recommendations', async () => {
    const swissUserData = {
      ...mockUserData,
      monthlyIncome: 10000,
      monthlyExpenses: 6000,
    };

    render(
      <AIFinancialAdvisor
        darkMode={false}
        userData={swissUserData}
        expenses={mockExpenses}
        investments={mockInvestments}
        savingsGoals={mockSavingsGoals}
      />,
    );

    await waitFor(() => {
      expect(screen.getByText('💡 AI Insights & Recommendations')).toBeInTheDocument();
    }, { timeout: 5000 });

    // Should mention Swiss-specific concepts
    await waitFor(() => {
      const swissContent = screen.queryByText(/Pillar 3a|CHF|Swiss|canton/i);
      expect(swissContent).toBeInTheDocument();
    }, { timeout: 2000 });
  });

  it('handles different savings rate scenarios', async () => {
    // Test low savings rate scenario
    const lowSavingsData = {
      ...mockUserData,
      monthlyIncome: 6000,
      monthlyExpenses: 5800,
    };

    const { rerender } = render(
      <AIFinancialAdvisor
        darkMode={false}
        userData={lowSavingsData}
        expenses={mockExpenses}
        investments={mockInvestments}
        savingsGoals={mockSavingsGoals}
      />,
    );

    await waitFor(() => {
      expect(screen.getByText('💡 AI Insights & Recommendations')).toBeInTheDocument();
    }, { timeout: 5000 });

    // Test high savings rate scenario
    const highSavingsData = {
      ...mockUserData,
      monthlyIncome: 10000,
      monthlyExpenses: 4000,
    };

    rerender(
      <AIFinancialAdvisor
        darkMode={false}
        userData={highSavingsData}
        expenses={mockExpenses}
        investments={mockInvestments}
        savingsGoals={mockSavingsGoals}
      />,
    );

    await waitFor(() => {
      expect(screen.getByText('💡 AI Insights & Recommendations')).toBeInTheDocument();
    }, { timeout: 5000 });
  });

  it('handles refresh analysis functionality', async () => {
    render(
      <AIFinancialAdvisor
        darkMode={false}
        userData={mockUserData}
        expenses={mockExpenses}
        investments={mockInvestments}
        savingsGoals={mockSavingsGoals}
      />,
    );

    await waitFor(() => {
      expect(screen.getByText('🔄 Refresh AI Analysis')).toBeInTheDocument();
    }, { timeout: 5000 });

    const refreshButton = screen.getByText('🔄 Refresh AI Analysis');
    fireEvent.click(refreshButton);

    expect(screen.getByText(/Analyzing.../)).toBeInTheDocument();
  });

  it('responds to different chat question types', async () => {
    render(
      <AIFinancialAdvisor
        darkMode={false}
        userData={mockUserData}
        expenses={mockExpenses}
        investments={mockInvestments}
        savingsGoals={mockSavingsGoals}
      />,
    );

    await waitFor(() => {
      expect(screen.getByText('💬 Ask Your AI Financial Advisor')).toBeInTheDocument();
    }, { timeout: 5000 });

    const chatInput = screen.getByPlaceholderText(/Ask about investments/);
    const sendButton = screen.getByRole('button', { name: /📤/ });

    // Test investment question
    fireEvent.change(chatInput, { target: { value: 'How should I invest?' } });
    fireEvent.click(sendButton);

    await waitFor(() => {
      expect(screen.getByText(/How should I invest?/)).toBeInTheDocument();
    }, { timeout: 3000 });

    // Test tax question
    fireEvent.change(chatInput, { target: { value: 'How can I optimize taxes?' } });
    fireEvent.click(sendButton);

    await waitFor(() => {
      expect(screen.getByText(/How can I optimize taxes?/)).toBeInTheDocument();
    }, { timeout: 3000 });
  });

  it('works in dark mode', () => {
    render(
      <AIFinancialAdvisor
        darkMode={true}
        userData={mockUserData}
        expenses={mockExpenses}
        investments={mockInvestments}
        savingsGoals={mockSavingsGoals}
      />,
    );

    expect(screen.getByText('🤖 AI Financial Advisor')).toBeInTheDocument();
  });

  it('handles empty data gracefully', async () => {
    const emptyUserData = {
      currentAge: 0,
      retirementAge: 0,
      currentSavings: 0,
      monthlyIncome: 0,
      monthlyExpenses: 0,
      expectedReturn: 0,
      inflationRate: 0,
      safeWithdrawalRate: 0,
    };

    render(
      <AIFinancialAdvisor
        darkMode={false}
        userData={emptyUserData}
        expenses={[]}
        investments={[]}
        savingsGoals={[]}
      />,
    );

    await waitFor(() => {
      expect(screen.getByText('📊 AI Financial Health Score')).toBeInTheDocument();
    }, { timeout: 5000 });

    // Should handle gracefully without crashing
    expect(screen.getByText('Overall Score')).toBeInTheDocument();
  });
});
