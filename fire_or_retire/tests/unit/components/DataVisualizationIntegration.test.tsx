/**
 * Comprehensive Integration Tests for Data Visualization Components
 *
 * This test suite covers the integration between different visualization components:
 * - Chart data consistency across components
 * - Interactive chart behaviors
 * - Performance with large datasets
 * - Swiss-specific formatting and localization
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, beforeEach, vi } from 'vitest';
import '@testing-library/jest-dom';

// Mock D3 and chart components
vi.mock('d3', () => ({
  select: vi.fn(() => ({
    selectAll: vi.fn(() => ({
      data: vi.fn(() => ({
        enter: vi.fn(() => ({
          append: vi.fn(() => ({
            attr: vi.fn(() => ({ attr: vi.fn() })),
            style: vi.fn(() => ({ style: vi.fn() })),
            text: vi.fn(),
          })),
        })),
        exit: vi.fn(() => ({ remove: vi.fn() })),
        attr: vi.fn(() => ({ attr: vi.fn() })),
        style: vi.fn(() => ({ style: vi.fn() })),
      })),
    })),
    append: vi.fn(() => ({
      attr: vi.fn(() => ({ attr: vi.fn() })),
      style: vi.fn(() => ({ style: vi.fn() })),
    })),
    attr: vi.fn(() => ({ attr: vi.fn() })),
    style: vi.fn(() => ({ style: vi.fn() })),
  })),
  scaleLinear: vi.fn(() => ({
    domain: vi.fn(() => ({ range: vi.fn() })),
    range: vi.fn(() => ({ domain: vi.fn() })),
  })),
  scaleTime: vi.fn(() => ({
    domain: vi.fn(() => ({ range: vi.fn() })),
    range: vi.fn(() => ({ domain: vi.fn() })),
  })),
  line: vi.fn(() => ({
    x: vi.fn(() => ({ y: vi.fn() })),
    y: vi.fn(),
  })),
  axisBottom: vi.fn(),
  axisLeft: vi.fn(),
  format: vi.fn(() => (d: number) => `CHF ${d.toLocaleString('de-CH')}`),
}));

// Mock chart components
const MockFireProjectionChart = ({ data, onDataPointClick }: any) => (
  <div data-testid="fire-projection-chart">
    <svg width="800" height="400">
      {data.map((point: any, index: number) => (
        <circle
          key={index}
          cx={point.x}
          cy={point.y}
          r="4"
          data-testid={`data-point-${index}`}
          onClick={() => onDataPointClick?.(point)}
        />
      ))}
    </svg>
  </div>
);

const MockSavingsProgressChart = ({ data, highlightGoal }: any) => (
  <div data-testid="savings-progress-chart">
    <div className={`goal-${highlightGoal}`}>
      {data.map((goal: any, index: number) => (
        <div key={index} data-testid={`goal-${index}`}>
          {goal.name}: {goal.progress}%
        </div>
      ))}
    </div>
  </div>
);

const MockExpenseBreakdownChart = ({ data, selectedCategory }: any) => (
  <div data-testid="expense-breakdown-chart">
    {data.map((category: any, index: number) => (
      <div
        key={index}
        data-testid={`category-${index}`}
        className={selectedCategory === category.name ? 'selected' : ''}
      >
        {category.name}: CHF {category.amount}
      </div>
    ))}
  </div>
);

const MockRiskAssessmentChart = ({ scenarios, selectedScenario }: any) => (
  <div data-testid="risk-assessment-chart">
    {scenarios.map((scenario: any, index: number) => (
      <div
        key={index}
        data-testid={`scenario-${index}`}
        className={selectedScenario === scenario.name ? 'active' : ''}
      >
        {scenario.name}: {scenario.successRate}%
      </div>
    ))}
  </div>
);

// Integrated Dashboard Component
const DataVisualizationDashboard = ({ 
  fireData, 
  savingsData, 
  expenseData, 
  riskData,
  onChartInteraction, 
}: any) => {
  const [selectedDataPoint, setSelectedDataPoint] = React.useState(null);
  const [highlightedGoal, setHighlightedGoal] = React.useState(null);
  const [selectedExpenseCategory, setSelectedExpenseCategory] = React.useState(null);
  const [activeRiskScenario, setActiveRiskScenario] = React.useState('base');

  const handleFireDataClick = (point: any) => {
    setSelectedDataPoint(point);
    onChartInteraction?.('fire-projection', point);
  };

  const handleGoalHighlight = (goalIndex: number) => {
    setHighlightedGoal(goalIndex);
    onChartInteraction?.('savings-progress', goalIndex);
  };

  const handleExpenseSelect = (category: string) => {
    setSelectedExpenseCategory(category);
    onChartInteraction?.('expense-breakdown', category);
  };

  const handleRiskScenarioChange = (scenario: string) => {
    setActiveRiskScenario(scenario);
    onChartInteraction?.('risk-assessment', scenario);
  };

  return (
    <div data-testid="visualization-dashboard">
      <div className="chart-grid">
        <div className="fire-section">
          <h3>FIRE Projection</h3>
          <MockFireProjectionChart 
            data={fireData} 
            onDataPointClick={handleFireDataClick}
          />
          {selectedDataPoint && (
            <div data-testid="fire-tooltip">
              Year: {(selectedDataPoint as any).year}, 
              Amount: CHF {(selectedDataPoint as any).amount?.toLocaleString('de-CH')}
            </div>
          )}
        </div>

        <div className="savings-section">
          <h3>Savings Progress</h3>
          <MockSavingsProgressChart 
            data={savingsData} 
            highlightGoal={highlightedGoal}
          />
          <div className="goal-controls">
            {savingsData.map((goal: any, index: number) => (
              <button
                key={index}
                data-testid={`highlight-goal-${index}`}
                onClick={() => handleGoalHighlight(index)}
              >
                Highlight {goal.name}
              </button>
            ))}
          </div>
        </div>

        <div className="expense-section">
          <h3>Expense Breakdown</h3>
          <MockExpenseBreakdownChart 
            data={expenseData}
            selectedCategory={selectedExpenseCategory}
          />
          <div className="category-controls">
            {expenseData.map((category: any, index: number) => (
              <button
                key={index}
                data-testid={`select-category-${index}`}
                onClick={() => handleExpenseSelect(category.name)}
              >
                Select {category.name}
              </button>
            ))}
          </div>
        </div>

        <div className="risk-section">
          <h3>Risk Assessment</h3>
          <MockRiskAssessmentChart 
            scenarios={riskData}
            selectedScenario={activeRiskScenario}
          />
          <div className="scenario-controls">
            {riskData.map((scenario: any, index: number) => (
              <button
                key={index}
                data-testid={`select-scenario-${index}`}
                onClick={() => handleRiskScenarioChange(scenario.name)}
              >
                {scenario.name}
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

describe('Data Visualization Integration', () => {
  let mockFireData: any[];
  let mockSavingsData: any[];
  let mockExpenseData: any[];
  let mockRiskData: any[];
  let mockOnChartInteraction: any;

  beforeEach(() => {
    mockFireData = [
      { year: 2024, amount: 100000, x: 50, y: 300 },
      { year: 2029, amount: 500000, x: 150, y: 200 },
      { year: 2034, amount: 1000000, x: 250, y: 150 },
      { year: 2039, amount: 1800000, x: 350, y: 100 },
      { year: 2044, amount: 2500000, x: 450, y: 50 },
    ];

    mockSavingsData = [
      { name: 'Emergency Fund', target: 25000, current: 20000, progress: 80 },
      { name: 'Pillar 3a', target: 7056, current: 5000, progress: 71 },
      { name: 'FIRE Portfolio', target: 2500000, current: 800000, progress: 32 },
      { name: 'Home Down Payment', target: 200000, current: 50000, progress: 25 },
    ];

    mockExpenseData = [
      { name: 'Housing', amount: 2200, essential: true },
      { name: 'Food', amount: 800, essential: true },
      { name: 'Transportation', amount: 300, essential: true },
      { name: 'Entertainment', amount: 500, essential: false },
      { name: 'Travel', amount: 600, essential: false },
    ];

    mockRiskData = [
      { name: 'base', successRate: 85, medianOutcome: 2800000 },
      { name: 'bear-market', successRate: 65, medianOutcome: 2100000 },
      { name: 'high-inflation', successRate: 70, medianOutcome: 2300000 },
      { name: 'recession', successRate: 55, medianOutcome: 1900000 },
    ];

    mockOnChartInteraction = vi.fn();
  });

  describe('Chart Integration and Data Consistency', () => {
    it('should render all chart components with consistent data', () => {
      render(
        <DataVisualizationDashboard
          fireData={mockFireData}
          savingsData={mockSavingsData}
          expenseData={mockExpenseData}
          riskData={mockRiskData}
          onChartInteraction={mockOnChartInteraction}
        />,
      );

      expect(screen.getByTestId('fire-projection-chart')).toBeInTheDocument();
      expect(screen.getByTestId('savings-progress-chart')).toBeInTheDocument();
      expect(screen.getByTestId('expense-breakdown-chart')).toBeInTheDocument();
      expect(screen.getByTestId('risk-assessment-chart')).toBeInTheDocument();

      // Verify data points are rendered
      expect(screen.getAllByTestId(/data-point-/)).toHaveLength(5);
      expect(screen.getAllByTestId(/goal-/)).toHaveLength(4);
      expect(screen.getAllByTestId(/category-/)).toHaveLength(5);
      expect(screen.getAllByTestId(/scenario-/)).toHaveLength(4);
    });

    it('should handle interactive chart behaviors', async () => {
      render(
        <DataVisualizationDashboard
          fireData={mockFireData}
          savingsData={mockSavingsData}
          expenseData={mockExpenseData}
          riskData={mockRiskData}
          onChartInteraction={mockOnChartInteraction}
        />,
      );

      // Test FIRE projection interaction
      const dataPoint = screen.getByTestId('data-point-2');
      fireEvent.click(dataPoint);

      await waitFor(() => {
        expect(screen.getByTestId('fire-tooltip')).toBeInTheDocument();
        expect(screen.getByTestId('fire-tooltip')).toHaveTextContent('Year: 2034');
        expect(screen.getByTestId('fire-tooltip')).toHaveTextContent('CHF 1\'000\'000');
      });

      expect(mockOnChartInteraction).toHaveBeenCalledWith('fire-projection', mockFireData[2]);
    });

    it('should handle savings goal highlighting', async () => {
      render(
        <DataVisualizationDashboard
          fireData={mockFireData}
          savingsData={mockSavingsData}
          expenseData={mockExpenseData}
          riskData={mockRiskData}
          onChartInteraction={mockOnChartInteraction}
        />,
      );

      const highlightButton = screen.getByTestId('highlight-goal-1');
      fireEvent.click(highlightButton);

      await waitFor(() => {
        expect(screen.getByTestId('savings-progress-chart')).toHaveClass('goal-1');
      });

      expect(mockOnChartInteraction).toHaveBeenCalledWith('savings-progress', 1);
    });

    it('should handle expense category selection', async () => {
      render(
        <DataVisualizationDashboard
          fireData={mockFireData}
          savingsData={mockSavingsData}
          expenseData={mockExpenseData}
          riskData={mockRiskData}
          onChartInteraction={mockOnChartInteraction}
        />,
      );

      const selectButton = screen.getByTestId('select-category-0');
      fireEvent.click(selectButton);

      await waitFor(() => {
        expect(screen.getByTestId('category-0')).toHaveClass('selected');
      });

      expect(mockOnChartInteraction).toHaveBeenCalledWith('expense-breakdown', 'Housing');
    });

    it('should handle risk scenario changes', async () => {
      render(
        <DataVisualizationDashboard
          fireData={mockFireData}
          savingsData={mockSavingsData}
          expenseData={mockExpenseData}
          riskData={mockRiskData}
          onChartInteraction={mockOnChartInteraction}
        />,
      );

      const scenarioButton = screen.getByTestId('select-scenario-1');
      fireEvent.click(scenarioButton);

      await waitFor(() => {
        expect(screen.getByTestId('scenario-1')).toHaveClass('active');
      });

      expect(mockOnChartInteraction).toHaveBeenCalledWith('risk-assessment', 'bear-market');
    });
  });

  describe('Swiss Formatting and Localization', () => {
    it('should format Swiss currency correctly in tooltips', async () => {
      render(
        <DataVisualizationDashboard
          fireData={mockFireData}
          savingsData={mockSavingsData}
          expenseData={mockExpenseData}
          riskData={mockRiskData}
          onChartInteraction={mockOnChartInteraction}
        />,
      );

      const dataPoint = screen.getByTestId('data-point-4');
      fireEvent.click(dataPoint);

      await waitFor(() => {
        expect(screen.getByTestId('fire-tooltip')).toHaveTextContent('CHF 2\'500\'000');
      });
    });

    it('should display Swiss expense categories correctly', () => {
      render(
        <DataVisualizationDashboard
          fireData={mockFireData}
          savingsData={mockSavingsData}
          expenseData={mockExpenseData}
          riskData={mockRiskData}
          onChartInteraction={mockOnChartInteraction}
        />,
      );

      expect(screen.getByTestId('category-0')).toHaveTextContent('Housing: CHF 2200');
      expect(screen.getByTestId('category-1')).toHaveTextContent('Food: CHF 800');
      expect(screen.getByTestId('category-2')).toHaveTextContent('Transportation: CHF 300');
    });

    it('should handle Swiss savings goal formatting', () => {
      render(
        <DataVisualizationDashboard
          fireData={mockFireData}
          savingsData={mockSavingsData}
          expenseData={mockExpenseData}
          riskData={mockRiskData}
          onChartInteraction={mockOnChartInteraction}
        />,
      );

      expect(screen.getByTestId('goal-0')).toHaveTextContent('Emergency Fund: 80%');
      expect(screen.getByTestId('goal-1')).toHaveTextContent('Pillar 3a: 71%');
      expect(screen.getByTestId('goal-2')).toHaveTextContent('FIRE Portfolio: 32%');
    });
  });

  describe('Performance and Large Dataset Handling', () => {
    it('should handle large FIRE projection datasets efficiently', () => {
      const largeFIREData = Array.from({ length: 1000 }, (_, i) => ({
        year: 2024 + i / 12,
        amount: 100000 + i * 2000,
        x: i * 0.8,
        y: 400 - (i * 0.3),
      }));

      const startTime = performance.now();
      
      render(
        <DataVisualizationDashboard
          fireData={largeFIREData}
          savingsData={mockSavingsData}
          expenseData={mockExpenseData}
          riskData={mockRiskData}
          onChartInteraction={mockOnChartInteraction}
        />,
      );

      const endTime = performance.now();
      expect(endTime - startTime).toBeLessThan(1000); // Should render within 1 second

      expect(screen.getAllByTestId(/data-point-/)).toHaveLength(1000);
    });

    it('should handle multiple chart interactions without performance degradation', async () => {
      render(
        <DataVisualizationDashboard
          fireData={mockFireData}
          savingsData={mockSavingsData}
          expenseData={mockExpenseData}
          riskData={mockRiskData}
          onChartInteraction={mockOnChartInteraction}
        />,
      );

      const startTime = performance.now();

      // Perform multiple rapid interactions
      for (let i = 0; i < 10; i++) {
        fireEvent.click(screen.getByTestId(`data-point-${i % 5}`));
        fireEvent.click(screen.getByTestId(`highlight-goal-${i % 4}`));
        fireEvent.click(screen.getByTestId(`select-category-${i % 5}`));
        fireEvent.click(screen.getByTestId(`select-scenario-${i % 4}`));
      }

      const endTime = performance.now();
      expect(endTime - startTime).toBeLessThan(500); // Should handle interactions quickly

      expect(mockOnChartInteraction).toHaveBeenCalledTimes(40);
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle empty datasets gracefully', () => {
      render(
        <DataVisualizationDashboard
          fireData={[]}
          savingsData={[]}
          expenseData={[]}
          riskData={[]}
          onChartInteraction={mockOnChartInteraction}
        />,
      );

      expect(screen.getByTestId('visualization-dashboard')).toBeInTheDocument();
      expect(screen.queryAllByTestId(/data-point-/)).toHaveLength(0);
      expect(screen.queryAllByTestId(/goal-/)).toHaveLength(0);
      expect(screen.queryAllByTestId(/category-/)).toHaveLength(0);
      expect(screen.queryAllByTestId(/scenario-/)).toHaveLength(0);
    });

    it('should handle missing callback functions', () => {
      render(
        <DataVisualizationDashboard
          fireData={mockFireData}
          savingsData={mockSavingsData}
          expenseData={mockExpenseData}
          riskData={mockRiskData}
          onChartInteraction={undefined}
        />,
      );

      // Should not throw errors when clicking without callback
      expect(() => {
        fireEvent.click(screen.getByTestId('data-point-0'));
      }).not.toThrow();
    });

    it('should handle malformed data gracefully', () => {
      const malformedData = [
        { year: null, amount: undefined, x: 'invalid', y: NaN },
        { year: 2024, amount: 100000, x: 50, y: 300 },
      ];

      expect(() => {
        render(
          <DataVisualizationDashboard
            fireData={malformedData}
            savingsData={mockSavingsData}
            expenseData={mockExpenseData}
            riskData={mockRiskData}
            onChartInteraction={mockOnChartInteraction}
          />,
        );
      }).not.toThrow();
    });
  });
});
