/**
 * Unit Tests for EnhancedD3Chart Component
 * Testing D3.js integration, chart rendering, and interactive features
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import '@testing-library/jest-dom';
import EnhancedD3Chart from '../../../src/components/EnhancedD3Chart';
import { ChartData, ChartConfig } from '../../../src/types/chart';

// Mock D3.js with comprehensive functionality
const mockD3 = {
  select: vi.fn(() => ({
    selectAll: vi.fn(() => ({
      data: vi.fn(() => ({
        enter: vi.fn(() => ({
          append: vi.fn(() => ({
            attr: vi.fn().mockReturnThis(),
            style: vi.fn().mockReturnThis(),
            on: vi.fn().mockReturnThis(),
            transition: vi.fn(() => ({
              duration: vi.fn().mockReturnThis(),
              attr: vi.fn().mockReturnThis(),
              style: vi.fn().mockReturnThis(),
            })),
          })),
        })),
        exit: vi.fn(() => ({
          remove: vi.fn(),
          transition: vi.fn(() => ({
            duration: vi.fn().mockReturnThis(),
            style: vi.fn().mockReturnThis(),
            remove: vi.fn(),
          })),
        })),
        merge: vi.fn(() => ({
          attr: vi.fn().mockReturnThis(),
          style: vi.fn().mockReturnThis(),
          on: vi.fn().mockReturnThis(),
          transition: vi.fn(() => ({
            duration: vi.fn().mockReturnThis(),
            attr: vi.fn().mockReturnThis(),
            style: vi.fn().mockReturnThis(),
          })),
        })),
      })),
    })),
    append: vi.fn(() => ({
      attr: vi.fn().mockReturnThis(),
      style: vi.fn().mockReturnThis(),
      text: vi.fn().mockReturnThis(),
      call: vi.fn().mockReturnThis(),
    })),
    attr: vi.fn().mockReturnThis(),
    style: vi.fn().mockReturnThis(),
    node: vi.fn(() => ({
      getBBox: vi.fn(() => ({ width: 100, height: 50 })),
    })),
  })),
  scaleTime: vi.fn(() => ({
    domain: vi.fn().mockReturnThis(),
    range: vi.fn().mockReturnThis(),
  })),
  scaleLinear: vi.fn(() => ({
    domain: vi.fn().mockReturnThis(),
    range: vi.fn().mockReturnThis(),
    nice: vi.fn().mockReturnThis(),
  })),
  line: vi.fn(() => ({
    x: vi.fn().mockReturnThis(),
    y: vi.fn().mockReturnThis(),
    curve: vi.fn().mockReturnThis(),
  })),
  area: vi.fn(() => ({
    x: vi.fn().mockReturnThis(),
    y0: vi.fn().mockReturnThis(),
    y1: vi.fn().mockReturnThis(),
    curve: vi.fn().mockReturnThis(),
  })),
  axisBottom: vi.fn(() => ({
    tickFormat: vi.fn().mockReturnThis(),
    ticks: vi.fn().mockReturnThis(),
  })),
  axisLeft: vi.fn(() => ({
    tickFormat: vi.fn().mockReturnThis(),
    ticks: vi.fn().mockReturnThis(),
  })),
  curveMonotoneX: 'curveMonotoneX',
  timeFormat: vi.fn(() => vi.fn((date) => date.toLocaleDateString())),
  format: vi.fn(() => vi.fn((value) => value.toLocaleString())),
  extent: vi.fn((data, accessor) => {
    const values = data.map(accessor);
    return [Math.min(...values), Math.max(...values)];
  }),
  max: vi.fn((data, accessor) => Math.max(...data.map(accessor))),
  min: vi.fn((data, accessor) => Math.min(...data.map(accessor))),
};

vi.mock('d3', () => mockD3);

describe('EnhancedD3Chart', () => {
  const mockData: ChartData[] = [
    { date: new Date('2024-01-01'), value: 100000, label: 'Start' },
    { date: new Date('2024-06-01'), value: 120000, label: 'Mid Year' },
    { date: new Date('2024-12-01'), value: 150000, label: 'Current' },
  ];

  const defaultConfig: ChartConfig = {
    type: 'line',
    width: 600,
    height: 400,
    margin: { top: 20, right: 30, bottom: 40, left: 50 },
    color: '#3B82F6',
    gradient: false,
    animated: true,
    interactive: true,
    responsive: true,
  };

  const mockOnDataPointClick = vi.fn();
  const mockOnDataPointHover = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock SVG element methods
    Object.defineProperty(SVGElement.prototype, 'getBBox', {
      value: vi.fn(() => ({ width: 100, height: 50, x: 0, y: 0 })),
    });

    // Mock ResizeObserver
    global.ResizeObserver = vi.fn().mockImplementation(() => ({
      observe: vi.fn(),
      unobserve: vi.fn(),
      disconnect: vi.fn(),
    }));
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Component Rendering', () => {
    it('renders SVG chart container', () => {
      render(
        <EnhancedD3Chart
          data={mockData}
          config={defaultConfig}
          darkMode={false}
        />,
      );

      const svgElement = screen.getByRole('img');
      expect(svgElement).toBeInTheDocument();
      expect(svgElement).toHaveAttribute('width', '600');
      expect(svgElement).toHaveAttribute('height', '400');
    });

    it('applies dark mode styling', () => {
      render(
        <EnhancedD3Chart
          data={mockData}
          config={defaultConfig}
          darkMode={true}
        />,
      );

      const chartContainer = screen.getByTestId('enhanced-d3-chart');
      expect(chartContainer).toHaveClass('dark-mode');
    });

    it('renders with custom dimensions', () => {
      const customConfig = { ...defaultConfig, width: 800, height: 500 };
      
      render(
        <EnhancedD3Chart
          data={mockData}
          config={customConfig}
          darkMode={false}
        />,
      );

      const svgElement = screen.getByRole('img');
      expect(svgElement).toHaveAttribute('width', '800');
      expect(svgElement).toHaveAttribute('height', '500');
    });

    it('has proper accessibility attributes', () => {
      render(
        <EnhancedD3Chart
          data={mockData}
          config={defaultConfig}
          darkMode={false}
        />,
      );

      const svgElement = screen.getByRole('img');
      expect(svgElement).toHaveAttribute('aria-label');
      expect(svgElement).toHaveAttribute('role', 'img');
    });
  });

  describe('Chart Types', () => {
    it('renders line chart correctly', () => {
      const lineConfig = { ...defaultConfig, type: 'line' as const };
      
      render(
        <EnhancedD3Chart
          data={mockData}
          config={lineConfig}
          darkMode={false}
        />,
      );

      expect(mockD3.line).toHaveBeenCalled();
      expect(mockD3.select).toHaveBeenCalled();
    });

    it('renders area chart correctly', () => {
      const areaConfig = { ...defaultConfig, type: 'area' as const };
      
      render(
        <EnhancedD3Chart
          data={mockData}
          config={areaConfig}
          darkMode={false}
        />,
      );

      expect(mockD3.area).toHaveBeenCalled();
    });

    it('renders bar chart correctly', () => {
      const barConfig = { ...defaultConfig, type: 'bar' as const };
      
      render(
        <EnhancedD3Chart
          data={mockData}
          config={barConfig}
          darkMode={false}
        />,
      );

      // Bar charts use selectAll for rectangles
      expect(mockD3.select).toHaveBeenCalled();
    });

    it('renders scatter plot correctly', () => {
      const scatterConfig = { ...defaultConfig, type: 'scatter' as const };
      
      render(
        <EnhancedD3Chart
          data={mockData}
          config={scatterConfig}
          darkMode={false}
        />,
      );

      // Scatter plots use circles
      expect(mockD3.select).toHaveBeenCalled();
    });
  });

  describe('Data Processing', () => {
    it('handles empty data gracefully', () => {
      render(
        <EnhancedD3Chart
          data={[]}
          config={defaultConfig}
          darkMode={false}
        />,
      );

      const svgElement = screen.getByRole('img');
      expect(svgElement).toBeInTheDocument();
    });

    it('processes data with missing values', () => {
      const incompleteData = [
        { date: new Date('2024-01-01'), value: 100000 },
        { date: new Date('2024-06-01'), value: 0 }, // Zero value
        { date: new Date('2024-12-01'), value: 150000 },
      ];

      render(
        <EnhancedD3Chart
          data={incompleteData}
          config={defaultConfig}
          darkMode={false}
        />,
      );

      expect(mockD3.select).toHaveBeenCalled();
    });

    it('sorts data by date automatically', () => {
      const unsortedData = [
        { date: new Date('2024-12-01'), value: 150000 },
        { date: new Date('2024-01-01'), value: 100000 },
        { date: new Date('2024-06-01'), value: 120000 },
      ];

      render(
        <EnhancedD3Chart
          data={unsortedData}
          config={defaultConfig}
          darkMode={false}
        />,
      );

      // Data should be processed and sorted
      expect(mockD3.scaleTime).toHaveBeenCalled();
      expect(mockD3.scaleLinear).toHaveBeenCalled();
    });
  });

  describe('Interactive Features', () => {
    it('handles data point click events', async () => {
      render(
        <EnhancedD3Chart
          data={mockData}
          config={defaultConfig}
          darkMode={false}
          onDataPointClick={mockOnDataPointClick}
        />,
      );

      // Simulate clicking on a data point
      const dataPoint = screen.getByTestId('data-point-0');
      fireEvent.click(dataPoint);

      expect(mockOnDataPointClick).toHaveBeenCalledWith(mockData[0]);
    });

    it('handles data point hover events', async () => {
      render(
        <EnhancedD3Chart
          data={mockData}
          config={defaultConfig}
          darkMode={false}
          onDataPointHover={mockOnDataPointHover}
        />,
      );

      const dataPoint = screen.getByTestId('data-point-0');
      fireEvent.mouseEnter(dataPoint);

      expect(mockOnDataPointHover).toHaveBeenCalledWith(mockData[0]);

      fireEvent.mouseLeave(dataPoint);
      expect(mockOnDataPointHover).toHaveBeenCalledWith(null);
    });

    it('disables interactions when interactive is false', () => {
      const nonInteractiveConfig = { ...defaultConfig, interactive: false };
      
      render(
        <EnhancedD3Chart
          data={mockData}
          config={nonInteractiveConfig}
          darkMode={false}
          onDataPointClick={mockOnDataPointClick}
        />,
      );

      // Should not have interactive elements
      expect(screen.queryByTestId('data-point-0')).not.toBeInTheDocument();
    });
  });

  describe('Animations', () => {
    it('enables animations when configured', () => {
      const animatedConfig = { ...defaultConfig, animated: true };
      
      render(
        <EnhancedD3Chart
          data={mockData}
          config={animatedConfig}
          darkMode={false}
        />,
      );

      // D3 transition should be called for animations
      expect(mockD3.select().selectAll().data().enter().append().transition).toHaveBeenCalled();
    });

    it('disables animations when configured', () => {
      const staticConfig = { ...defaultConfig, animated: false };
      
      render(
        <EnhancedD3Chart
          data={mockData}
          config={staticConfig}
          darkMode={false}
        />,
      );

      // Should render without transitions
      expect(mockD3.select).toHaveBeenCalled();
    });

    it('respects prefers-reduced-motion', () => {
      // Mock reduced motion preference
      Object.defineProperty(window, 'matchMedia', {
        value: vi.fn(() => ({
          matches: true, // prefers-reduced-motion: reduce
          addEventListener: vi.fn(),
          removeEventListener: vi.fn(),
        })),
      });

      render(
        <EnhancedD3Chart
          data={mockData}
          config={defaultConfig}
          darkMode={false}
        />,
      );

      // Animations should be disabled
      expect(window.matchMedia).toHaveBeenCalledWith('(prefers-reduced-motion: reduce)');
    });
  });

  describe('Responsive Behavior', () => {
    it('adapts to container size when responsive', () => {
      const responsiveConfig = { ...defaultConfig, responsive: true };
      
      render(
        <EnhancedD3Chart
          data={mockData}
          config={responsiveConfig}
          darkMode={false}
        />,
      );

      expect(global.ResizeObserver).toHaveBeenCalled();
    });

    it('maintains fixed size when not responsive', () => {
      const fixedConfig = { ...defaultConfig, responsive: false };
      
      render(
        <EnhancedD3Chart
          data={mockData}
          config={fixedConfig}
          darkMode={false}
        />,
      );

      const svgElement = screen.getByRole('img');
      expect(svgElement).toHaveAttribute('width', '600');
      expect(svgElement).toHaveAttribute('height', '400');
    });
  });

  describe('Swiss Localization', () => {
    it('formats currency values in CHF', () => {
      render(
        <EnhancedD3Chart
          data={mockData}
          config={defaultConfig}
          darkMode={false}
        />,
      );

      // Should use Swiss currency formatting
      expect(mockD3.format).toHaveBeenCalled();
    });

    it('formats dates in Swiss locale', () => {
      render(
        <EnhancedD3Chart
          data={mockData}
          config={defaultConfig}
          darkMode={false}
        />,
      );

      // Should use Swiss date formatting
      expect(mockD3.timeFormat).toHaveBeenCalled();
    });
  });

  describe('Performance', () => {
    it('handles large datasets efficiently', () => {
      const largeDataset = Array.from({ length: 1000 }, (_, i) => ({
        date: new Date(2024, 0, i + 1),
        value: 100000 + Math.random() * 50000,
      }));

      const startTime = performance.now();
      
      render(
        <EnhancedD3Chart
          data={largeDataset}
          config={defaultConfig}
          darkMode={false}
        />,
      );

      const endTime = performance.now();
      const renderTime = endTime - startTime;

      // Should render within reasonable time (adjust threshold as needed)
      expect(renderTime).toBeLessThan(1000); // 1 second
    });

    it('cleans up D3 elements on unmount', () => {
      const { unmount } = render(
        <EnhancedD3Chart
          data={mockData}
          config={defaultConfig}
          darkMode={false}
        />,
      );

      unmount();

      // ResizeObserver should be disconnected
      expect(global.ResizeObserver().disconnect).toHaveBeenCalled();
    });
  });

  describe('Error Handling', () => {
    it('handles D3 rendering errors gracefully', () => {
      // Mock D3 to throw an error
      mockD3.select.mockImplementation(() => {
        throw new Error('D3 rendering error');
      });

      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      render(
        <EnhancedD3Chart
          data={mockData}
          config={defaultConfig}
          darkMode={false}
        />,
      );

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Error rendering chart'),
        expect.any(Error),
      );

      consoleSpy.mockRestore();
    });

    it('handles invalid data gracefully', () => {
      const invalidData = [
        { date: 'invalid-date' as any, value: 'invalid-value' as any },
      ];

      render(
        <EnhancedD3Chart
          data={invalidData}
          config={defaultConfig}
          darkMode={false}
        />,
      );

      // Should render without crashing
      const svgElement = screen.getByRole('img');
      expect(svgElement).toBeInTheDocument();
    });
  });

  describe('Accessibility Features', () => {
    it('provides keyboard navigation support', () => {
      render(
        <EnhancedD3Chart
          data={mockData}
          config={defaultConfig}
          darkMode={false}
        />,
      );

      const chartContainer = screen.getByTestId('enhanced-d3-chart');
      expect(chartContainer).toHaveAttribute('tabIndex', '0');
    });

    it('includes descriptive ARIA labels', () => {
      render(
        <EnhancedD3Chart
          data={mockData}
          config={defaultConfig}
          darkMode={false}
        />,
      );

      const svgElement = screen.getByRole('img');
      const ariaLabel = svgElement.getAttribute('aria-label');
      
      expect(ariaLabel).toContain('chart');
      expect(ariaLabel).toContain('financial data');
    });

    it('provides alternative text for screen readers', () => {
      render(
        <EnhancedD3Chart
          data={mockData}
          config={defaultConfig}
          darkMode={false}
        />,
      );

      const description = screen.getByTestId('chart-description');
      expect(description).toBeInTheDocument();
      expect(description).toHaveTextContent(/showing.*growth/i);
    });
  });
});
