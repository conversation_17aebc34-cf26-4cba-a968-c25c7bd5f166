import React from 'react';
import '@testing-library/jest-dom';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import SafeWithdrawalRateAnalysis from '../../../src/components/SafeWithdrawalRateAnalysis';

// Mock the useTranslation hook
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string, defaultValue?: string) => defaultValue || key,
  }),
}));

const mockUserData = {
  currentAge: 45,
  retirementAge: 65,
  currentSavings: 300000,
  monthlyIncome: 12000,
  monthlyExpenses: 7000,
  expectedReturn: 7,
  inflationRate: 2.5,
  safeWithdrawalRate: 4,
};

describe('SafeWithdrawalRateAnalysis', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the Safe Withdrawal Rate Analysis component', () => {
    render(
      <SafeWithdrawalRateAnalysis darkMode={false} userData={mockUserData} />
    );

    expect(
      screen.getByText('📊 Safe Withdrawal Rate Analysis')
    ).toBeInTheDocument();
    expect(
      screen.getByText(/Advanced analysis of withdrawal rates/)
    ).toBeInTheDocument();
  });

  it('displays analysis configuration options', () => {
    render(
      <SafeWithdrawalRateAnalysis darkMode={false} userData={mockUserData} />
    );

    expect(screen.getByText('⚙️ Analysis Parameters')).toBeInTheDocument();
    expect(screen.getByText('Retirement Length (Years)')).toBeInTheDocument();
    expect(screen.getByText('Stock Allocation (%)')).toBeInTheDocument();
    expect(screen.getByText('🔄 Refresh Analysis')).toBeInTheDocument();
  });

  it('shows loading state during Monte Carlo simulation', () => {
    render(
      <SafeWithdrawalRateAnalysis darkMode={false} userData={mockUserData} />
    );

    expect(
      screen.getByText(/Running Monte Carlo simulations/)
    ).toBeInTheDocument();
  });

  it('generates current rate assessment after analysis', async () => {
    render(
      <SafeWithdrawalRateAnalysis darkMode={false} userData={mockUserData} />
    );

    await waitFor(
      () => {
        expect(
          screen.getByText('🎯 Current Rate Assessment')
        ).toBeInTheDocument();
      },
      { timeout: 10000 }
    );

    expect(screen.getByText('Your Current Rate')).toBeInTheDocument();
    expect(screen.getByText('Recommended Rate')).toBeInTheDocument();
    expect(screen.getByText('4.0%')).toBeInTheDocument(); // Current rate
  });

  it('displays risk factors analysis', async () => {
    render(
      <SafeWithdrawalRateAnalysis darkMode={false} userData={mockUserData} />
    );

    await waitFor(
      () => {
        expect(screen.getByText('⚠️ Risk Factors')).toBeInTheDocument();
      },
      { timeout: 10000 }
    );

    // Check for all 4 risk factors
    expect(screen.getByText('Retirement Length')).toBeInTheDocument();
    expect(screen.getByText('Market Volatility')).toBeInTheDocument();
    expect(screen.getByText('Inflation Impact')).toBeInTheDocument();
    expect(screen.getByText('Sequence Risk')).toBeInTheDocument();
  });

  it('shows withdrawal rate scenarios with success rates', async () => {
    render(
      <SafeWithdrawalRateAnalysis darkMode={false} userData={mockUserData} />
    );

    await waitFor(
      () => {
        expect(
          screen.getByText('📈 Withdrawal Rate Scenarios')
        ).toBeInTheDocument();
      },
      { timeout: 10000 }
    );

    // Should show multiple withdrawal rate scenarios
    expect(screen.getByText('2.5% Withdrawal Rate')).toBeInTheDocument();
    expect(screen.getByText('4.0% Withdrawal Rate')).toBeInTheDocument();
    expect(screen.getByText('6.0% Withdrawal Rate')).toBeInTheDocument();

    // Should show success rates
    expect(screen.getByText('Success Rate')).toBeInTheDocument();
  });

  it('handles retirement length configuration changes', async () => {
    render(
      <SafeWithdrawalRateAnalysis darkMode={false} userData={mockUserData} />
    );

    await waitFor(
      () => {
        expect(screen.getByText('⚙️ Analysis Parameters')).toBeInTheDocument();
      },
      { timeout: 5000 }
    );

    const retirementLengthSelect = screen.getByDisplayValue('30 years');
    expect(retirementLengthSelect).toBeInTheDocument();

    // Test changing retirement length
    fireEvent.change(retirementLengthSelect, { target: { value: '25' } });
    fireEvent.change(retirementLengthSelect, { target: { value: '40' } });
  });

  it('handles portfolio allocation changes', async () => {
    render(
      <SafeWithdrawalRateAnalysis darkMode={false} userData={mockUserData} />
    );

    await waitFor(
      () => {
        expect(screen.getByText('Stock Allocation (%)')).toBeInTheDocument();
      },
      { timeout: 5000 }
    );

    const stockSlider = screen.getByRole('slider');
    expect(stockSlider).toBeInTheDocument();

    // Test changing stock allocation
    fireEvent.change(stockSlider, { target: { value: '80' } });
    expect(screen.getByText('80% Stocks, 20% Bonds')).toBeInTheDocument();

    fireEvent.change(stockSlider, { target: { value: '30' } });
    expect(screen.getByText('30% Stocks, 70% Bonds')).toBeInTheDocument();
  });

  it('displays detailed scenario metrics', async () => {
    render(
      <SafeWithdrawalRateAnalysis darkMode={false} userData={mockUserData} />
    );

    await waitFor(
      () => {
        expect(
          screen.getByText('📈 Withdrawal Rate Scenarios')
        ).toBeInTheDocument();
      },
      { timeout: 10000 }
    );

    // Check for detailed metrics in scenarios
    expect(screen.getByText('Median Portfolio')).toBeInTheDocument();
    expect(screen.getByText('Worst Case')).toBeInTheDocument();
    expect(screen.getByText('Years to Depletion')).toBeInTheDocument();

    // Should show CHF formatting
    await waitFor(
      () => {
        expect(screen.getByText(/CHF/)).toBeInTheDocument();
      },
      { timeout: 2000 }
    );
  });

  it('shows scenario recommendations correctly', async () => {
    render(
      <SafeWithdrawalRateAnalysis darkMode={false} userData={mockUserData} />
    );

    await waitFor(
      () => {
        expect(
          screen.getByText('📈 Withdrawal Rate Scenarios')
        ).toBeInTheDocument();
      },
      { timeout: 10000 }
    );

    // Should show different recommendation levels
    const recommendations = ['CONSERVATIVE', 'MODERATE', 'AGGRESSIVE', 'RISKY'];
    let foundRecommendation = false;

    for (const rec of recommendations) {
      if (screen.queryByText(`${rec} APPROACH`)) {
        foundRecommendation = true;
        break;
      }
    }

    expect(foundRecommendation).toBe(true);
  });

  it('displays key insights section', async () => {
    render(
      <SafeWithdrawalRateAnalysis darkMode={false} userData={mockUserData} />
    );

    await waitFor(
      () => {
        expect(screen.getByText('💡 Key Insights')).toBeInTheDocument();
      },
      { timeout: 10000 }
    );

    // Should mention key concepts
    expect(screen.getByText(/4% rule/)).toBeInTheDocument();
    expect(screen.getByText(/success rate/)).toBeInTheDocument();
    expect(screen.getByText(/bond tent/)).toBeInTheDocument();
  });

  it('validates 4% rule analysis', async () => {
    render(
      <SafeWithdrawalRateAnalysis darkMode={false} userData={mockUserData} />
    );

    await waitFor(
      () => {
        expect(screen.getByText('4.0% Withdrawal Rate')).toBeInTheDocument();
      },
      { timeout: 10000 }
    );

    // Should show traditional safe withdrawal rate description
    expect(
      screen.getByText(/Traditional safe withdrawal rate/)
    ).toBeInTheDocument();
  });

  it('handles refresh analysis functionality', async () => {
    render(
      <SafeWithdrawalRateAnalysis darkMode={false} userData={mockUserData} />
    );

    await waitFor(
      () => {
        expect(screen.getByText('🔄 Refresh Analysis')).toBeInTheDocument();
      },
      { timeout: 5000 }
    );

    const refreshButton = screen.getByText('🔄 Refresh Analysis');
    fireEvent.click(refreshButton);

    expect(screen.getByText(/Analyzing.../)).toBeInTheDocument();
  });

  it('shows current rate assessment accurately', async () => {
    const customUserData = {
      ...mockUserData,
      safeWithdrawalRate: 3.5,
    };

    render(
      <SafeWithdrawalRateAnalysis darkMode={false} userData={customUserData} />
    );

    await waitFor(
      () => {
        expect(
          screen.getByText('🎯 Current Rate Assessment')
        ).toBeInTheDocument();
      },
      { timeout: 10000 }
    );

    // Should show the custom withdrawal rate
    expect(screen.getByText('3.5%')).toBeInTheDocument();
    expect(
      screen.getByText(/Your current 3.5% withdrawal rate/)
    ).toBeInTheDocument();
  });

  it('handles extreme scenarios correctly', async () => {
    render(
      <SafeWithdrawalRateAnalysis darkMode={false} userData={mockUserData} />
    );

    await waitFor(
      () => {
        expect(
          screen.getByText('📈 Withdrawal Rate Scenarios')
        ).toBeInTheDocument();
      },
      { timeout: 10000 }
    );

    // Conservative scenario (2.5%)
    expect(screen.getByText('2.5% Withdrawal Rate')).toBeInTheDocument();
    await waitFor(
      () => {
        expect(screen.getByText('CONSERVATIVE APPROACH')).toBeInTheDocument();
      },
      { timeout: 2000 }
    );

    // Aggressive scenario (6.0%)
    expect(screen.getByText('6.0% Withdrawal Rate')).toBeInTheDocument();
    await waitFor(
      () => {
        expect(screen.getByText('RISKY APPROACH')).toBeInTheDocument();
      },
      { timeout: 2000 }
    );
  });

  it('works in dark mode', () => {
    render(
      <SafeWithdrawalRateAnalysis darkMode={true} userData={mockUserData} />
    );

    expect(
      screen.getByText('📊 Safe Withdrawal Rate Analysis')
    ).toBeInTheDocument();
  });

  it('handles edge cases gracefully', async () => {
    const edgeCaseData = {
      ...mockUserData,
      monthlyExpenses: 0,
      expectedReturn: 0,
    };

    render(
      <SafeWithdrawalRateAnalysis darkMode={false} userData={edgeCaseData} />
    );

    await waitFor(
      () => {
        expect(
          screen.getByText('📊 Safe Withdrawal Rate Analysis')
        ).toBeInTheDocument();
      },
      { timeout: 5000 }
    );

    // Should handle gracefully without crashing
    expect(screen.getByText('⚙️ Analysis Parameters')).toBeInTheDocument();
  });

  it('shows portfolio value projections', async () => {
    render(
      <SafeWithdrawalRateAnalysis darkMode={false} userData={mockUserData} />
    );

    await waitFor(
      () => {
        expect(
          screen.getByText('📈 Withdrawal Rate Scenarios')
        ).toBeInTheDocument();
      },
      { timeout: 10000 }
    );

    // Should show portfolio value projections
    await waitFor(
      () => {
        expect(screen.getByText('Median Portfolio')).toBeInTheDocument();
        expect(screen.getByText('Worst Case')).toBeInTheDocument();
      },
      { timeout: 2000 }
    );
  });
});
