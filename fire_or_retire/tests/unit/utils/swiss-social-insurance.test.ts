import { describe, expect, it } from 'vitest';
import {
  calculateAHVContributions,
  calculateALVContributions,
  calculateAnnualSocialInsurance,
  calculateFamilyAllowances,
  calculateNBUContributions,
  calculatePensionFundContributions,
  calculateSelfEmployedSocialInsurance,
  calculateSocialInsurance,
  getSocialInsuranceSummary,
  PENSION_FUND_RATES_2024,
  SOCIAL_INSURANCE_RATES_2024,
} from '../../../src/utils/swiss-social-insurance';

describe('Swiss Social Insurance Calculations', () => {
  describe('calculateAHVContributions', () => {
    it('should calculate AHV contributions correctly', () => {
      const result = calculateAHVContributions(100000);

      expect(result.employee).toBeCloseTo(4350, 2); // 4.35% of 100,000
      expect(result.employer).toBeCloseTo(4350, 2); // 4.35% of 100,000
      expect(result.total).toBeCloseTo(8700, 2); // 8.7% of 100,000
    });

    it('should handle zero income', () => {
      const result = calculateAHVContributions(0);

      expect(result.employee).toBe(0);
      expect(result.employer).toBe(0);
      expect(result.total).toBe(0);
    });

    it('should handle high income levels', () => {
      const result = calculateAHVContributions(500000);

      expect(result.employee).toBe(21750); // 4.35% of 500,000
      expect(result.employer).toBe(21750); // 4.35% of 500,000
      expect(result.total).toBe(43500); // 8.7% of 500,000
    });
  });

  describe('calculateALVContributions', () => {
    it('should calculate ALV contributions for income below maximum', () => {
      const result = calculateALVContributions(100000);

      expect(result.employee).toBeCloseTo(1100, 2); // 1.1% of 100,000
      expect(result.employer).toBeCloseTo(1100, 2); // 1.1% of 100,000
      expect(result.total).toBeCloseTo(2200, 2); // 2.2% of 100,000
      expect(result.additional).toBe(0); // No additional ALV below threshold
    });

    it('should calculate ALV contributions for income above maximum', () => {
      const result = calculateALVContributions(200000);

      // Standard ALV on income up to CHF 148,200
      const standardEmployee = 148200 * 0.011; // 1.1%
      const standardEmployer = 148200 * 0.011; // 1.1%

      // Additional ALV on income above CHF 148,200 (employee only)
      const additionalIncome = 200000 - 148200;
      const additional = additionalIncome * 0.005; // 0.5%

      expect(result.employee).toBeCloseTo(standardEmployee + additional, 2);
      expect(result.employer).toBeCloseTo(standardEmployer, 2);
      expect(result.additional).toBeCloseTo(additional, 2);
      expect(result.total).toBeCloseTo(
        standardEmployee + standardEmployer + additional,
        2,
      );
    });

    it('should handle income exactly at maximum', () => {
      const result = calculateALVContributions(148200);

      expect(result.employee).toBe(1630.2); // 1.1% of 148,200
      expect(result.employer).toBe(1630.2); // 1.1% of 148,200
      expect(result.additional).toBe(0); // No additional ALV at threshold
    });
  });

  describe('calculateNBUContributions', () => {
    it('should calculate NBU contributions correctly', () => {
      const result = calculateNBUContributions(100000);

      expect(result.employee).toBe(1000); // 1.0% of 100,000
      expect(result.employer).toBe(1000); // 1.0% of 100,000
      expect(result.total).toBe(2000); // 2.0% of 100,000
    });

    it('should handle zero income', () => {
      const result = calculateNBUContributions(0);

      expect(result.employee).toBe(0);
      expect(result.employer).toBe(0);
      expect(result.total).toBe(0);
    });
  });

  describe('calculatePensionFundContributions', () => {
    it('should calculate pension fund contributions for age 30', () => {
      const result = calculatePensionFundContributions(100000, 30);

      // Insured salary = min(100,000 - 25,725, 88,200 - 25,725) = min(74,275, 62,475) = 62,475
      const insuredSalary = 62475;

      // Age 25-34: 3.5% employee + 3.5% employer for savings
      const savingsEmployee = insuredSalary * 0.035;
      const savingsEmployer = insuredSalary * 0.035;

      // Risk premiums and admin costs: 1.3% total (0.7% + 0.3% + 0.3%), split equally (0.65% each)
      const riskAndAdmin = (insuredSalary * 0.013) / 2;

      expect(result.insuredSalary).toBe(insuredSalary);
      expect(result.employee).toBeCloseTo(savingsEmployee + riskAndAdmin, 2);
      expect(result.employer).toBeCloseTo(savingsEmployer + riskAndAdmin, 2);
      expect(result.total).toBeCloseTo(
        savingsEmployee + savingsEmployer + riskAndAdmin * 2,
        2,
      );
    });

    it('should calculate pension fund contributions for age 50', () => {
      const result = calculatePensionFundContributions(100000, 50);

      // Insured salary = min(100,000 - 25,725, 88,200 - 25,725) = min(74,275, 62,475) = 62,475
      const insuredSalary = 62475;

      // Age 45-54: 7.5% employee + 7.5% employer for savings
      const savingsEmployee = insuredSalary * 0.075;
      const savingsEmployer = insuredSalary * 0.075;

      // Risk premiums and admin costs: 1.3% total (0.7% + 0.3% + 0.3%), split equally (0.65% each)
      const riskAndAdmin = (insuredSalary * 0.013) / 2;

      expect(result.insuredSalary).toBe(insuredSalary);
      expect(result.employee).toBeCloseTo(savingsEmployee + riskAndAdmin, 2);
      expect(result.employer).toBeCloseTo(savingsEmployer + riskAndAdmin, 2);
    });

    it('should handle income below minimum insured salary', () => {
      const result = calculatePensionFundContributions(20000, 30);

      // Income below minimum threshold, no BVG contributions
      expect(result.insuredSalary).toBe(0);
      expect(result.employee).toBe(0);
      expect(result.employer).toBe(0);
      expect(result.total).toBe(0);
    });

    it('should handle age below 25', () => {
      const result = calculatePensionFundContributions(100000, 24);

      // No BVG contributions below age 25
      expect(result.insuredSalary).toBe(0);
      expect(result.employee).toBe(0);
      expect(result.employer).toBe(0);
      expect(result.total).toBe(0);
    });

    it('should handle age above 65', () => {
      const result = calculatePensionFundContributions(100000, 66);

      // No BVG contributions above age 65
      expect(result.insuredSalary).toBe(0);
      expect(result.employee).toBe(0);
      expect(result.employer).toBe(0);
      expect(result.total).toBe(0);
    });
  });

  describe('calculateFamilyAllowances', () => {
    it('should calculate family allowances correctly', () => {
      const result = calculateFamilyAllowances(100000);

      expect(result.employer).toBe(1200); // 1.2% of 100,000
    });

    it('should handle zero income', () => {
      const result = calculateFamilyAllowances(0);

      expect(result.employer).toBe(0);
    });
  });

  describe('calculateSocialInsurance', () => {
    it('should calculate complete social insurance for typical employee', () => {
      const result = calculateSocialInsurance(100000, 35);

      // Verify all components are calculated
      expect(result.ahv.employee).toBeCloseTo(4350, 2);
      expect(result.ahv.employer).toBeCloseTo(4350, 2);
      expect(result.alv.employee).toBeCloseTo(1100, 2);
      expect(result.alv.employer).toBeCloseTo(1100, 2);
      expect(result.nbu.employee).toBeCloseTo(1000, 2);
      expect(result.nbu.employer).toBeCloseTo(1000, 2);
      expect(result.familyAllowances.employer).toBeCloseTo(1200, 2);

      // Verify totals
      expect(result.totalEmployee).toBeGreaterThan(6000);
      expect(result.totalEmployer).toBeGreaterThan(7000);
      expect(result.totalContributions).toBeGreaterThan(13000);
      expect(result.netSalary).toBeLessThan(100000);
    });

    it('should handle high income with additional ALV', () => {
      const result = calculateSocialInsurance(200000, 45);

      // Should have additional ALV for high earners
      expect(result.alv.additional).toBeGreaterThan(0);
      expect(result.alv.employee).toBeGreaterThan(result.alv.employer);
    });

    it('should handle young employee with lower BVG rates', () => {
      const result = calculateSocialInsurance(80000, 28);

      // Young employee should have lower BVG contributions
      const olderResult = calculateSocialInsurance(80000, 50);
      expect(result.pensionFund.employee).toBeLessThan(
        olderResult.pensionFund.employee,
      );
    });
  });

  describe('calculateAnnualSocialInsurance', () => {
    it('should calculate annual contributions with 13th salary', () => {
      const result = calculateAnnualSocialInsurance(100000, 35, 0, true);

      // Should include 13th month salary
      const expectedAnnualSalary = 100000 + 100000 / 12;
      const monthlyResult = calculateSocialInsurance(expectedAnnualSalary, 35);

      expect(result.totalEmployee).toBeCloseTo(monthlyResult.totalEmployee, 2);
    });

    it('should calculate annual contributions without 13th salary', () => {
      const result = calculateAnnualSocialInsurance(100000, 35, 0, false);

      const monthlyResult = calculateSocialInsurance(100000, 35);
      expect(result.totalEmployee).toBeCloseTo(monthlyResult.totalEmployee, 2);
    });

    it('should include monthly bonuses', () => {
      const result = calculateAnnualSocialInsurance(100000, 35, 500, true);

      // Should include 12 months of CHF 500 bonus
      const expectedAnnualSalary = 100000 + 100000 / 12 + 500 * 12;
      const monthlyResult = calculateSocialInsurance(expectedAnnualSalary, 35);

      expect(result.totalEmployee).toBeCloseTo(monthlyResult.totalEmployee, 2);
    });
  });

  describe('calculateSelfEmployedSocialInsurance', () => {
    it('should calculate self-employed contributions without BVG', () => {
      const result = calculateSelfEmployedSocialInsurance(100000, 35, false);

      // Self-employed pay double AHV rate (8.7%)
      expect(result.ahv?.total).toBeCloseTo(8700, 2);
      expect(result.pensionFund?.total).toBe(0);
      expect(result.totalEmployee).toBeCloseTo(8700, 2);
      expect(result.totalEmployer).toBe(0);
    });

    it('should calculate self-employed contributions with voluntary BVG', () => {
      const result = calculateSelfEmployedSocialInsurance(100000, 35, true);

      // Should include both AHV and BVG contributions
      expect(result.ahv?.total).toBeCloseTo(8700, 2);
      expect(result.pensionFund?.total).toBeGreaterThan(0);
      expect(result.totalEmployee).toBeGreaterThan(8700);
    });
  });

  describe('getSocialInsuranceSummary', () => {
    it('should provide summary for different income levels', () => {
      const summary = getSocialInsuranceSummary(35);

      expect(summary).toHaveLength(6); // 6 income levels
      expect(summary[0].income).toBe(50000);
      expect(summary[5].income).toBe(200000);

      // Effective rates should be reasonable (social insurance only, not including income tax)
      summary.forEach(item => {
        expect(item.effectiveRate).toBeGreaterThan(8); // Lower bound for social insurance
        expect(item.effectiveRate).toBeLessThan(15); // Upper bound for social insurance
      });
    });

    it('should show progressive nature of contributions', () => {
      const summary = getSocialInsuranceSummary(35);

      // Higher income should generally have higher absolute contributions
      for (let i = 1; i < summary.length; i++) {
        expect(summary[i].contributions.totalEmployee).toBeGreaterThan(
          summary[i - 1].contributions.totalEmployee,
        );
      }
    });
  });

  describe('Social Insurance Constants', () => {
    it('should have correct 2024 rates', () => {
      expect(SOCIAL_INSURANCE_RATES_2024.ahv.employee).toBe(4.35);
      expect(SOCIAL_INSURANCE_RATES_2024.ahv.employer).toBe(4.35);
      expect(SOCIAL_INSURANCE_RATES_2024.alv.employee).toBe(1.1);
      expect(SOCIAL_INSURANCE_RATES_2024.alv.maxIncome).toBe(148200);
      expect(SOCIAL_INSURANCE_RATES_2024.familyAllowances.rate).toBe(1.2);
    });

    it('should have correct pension fund rates', () => {
      expect(PENSION_FUND_RATES_2024.coordinationDeduction).toBe(25725);
      expect(PENSION_FUND_RATES_2024.ageGroups.age25to34.employee).toBe(3.5);
      expect(PENSION_FUND_RATES_2024.ageGroups.age55to65.employee).toBe(9.0);
    });
  });
});
