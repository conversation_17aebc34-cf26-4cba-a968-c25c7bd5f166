/**
 * Swiss Budget Pro - Security Monitor Tests
 * 
 * Comprehensive tests for security monitoring, threat detection,
 * and audit trail functionality.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { 
  securityMonitor, 
  SecurityEventType, 
  SecuritySeverity, 
} from '../../../src/security/security-monitor';

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(() => null), // Default to null to prevent loading old events
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

Object.defineProperty(global, 'localStorage', {
  value: mockLocalStorage,
  writable: true,
});

// Mock console methods
const mockConsole = {
  log: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
};

Object.defineProperty(global, 'console', {
  value: mockConsole,
  writable: true,
});

// Mock PerformanceObserver
const mockPerformanceObserver = vi.fn();
Object.defineProperty(global, 'PerformanceObserver', {
  value: mockPerformanceObserver,
  writable: true,
});

// Mock location
Object.defineProperty(global, 'location', {
  value: {
    protocol: 'https:',
    hostname: 'localhost',
  },
  writable: true,
});

// Mock navigator
Object.defineProperty(global, 'navigator', {
  value: {
    userAgent: 'Mozilla/5.0 (Test Browser)',
  },
  writable: true,
});

describe('SecurityMonitor', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    vi.clearAllMocks();
    mockLocalStorage.getItem.mockReturnValue(null);

    // Mock setInterval to prevent actual timers
    vi.spyOn(global, 'setInterval').mockImplementation(() => 123 as any);

    // Reset security monitor state for each test
    securityMonitor.resetForTesting();

    // Disable threat detection to prevent automatic events
    securityMonitor.disableThreatDetectionForTesting();
  });

  afterEach(() => {
    // Clean up after each test
    vi.clearAllMocks();
    vi.restoreAllMocks();
  });

  describe('Event Logging', () => {
    it('should log security events', () => {
      const eventId = securityMonitor.logEvent(
        SecurityEventType.DATA_ACCESS,
        SecuritySeverity.LOW,
        'User accessed financial data',
        { userId: 'test-user', dataType: 'financial' },
      );

      expect(typeof eventId).toBe('string');
      expect(eventId.length).toBeGreaterThan(0);

      const events = securityMonitor.getEvents();
      expect(events).toHaveLength(1);
      
      const event = events[0];
      expect(event.id).toBe(eventId);
      expect(event.type).toBe(SecurityEventType.DATA_ACCESS);
      expect(event.severity).toBe(SecuritySeverity.LOW);
      expect(event.description).toBe('User accessed financial data');
      expect(event.details).toEqual({ userId: 'test-user', dataType: 'financial' });
      expect(event.userAgent).toBe('Mozilla/5.0 (Test Browser)');
      expect(event.resolved).toBe(false);
      expect(event.timestamp).toBeInstanceOf(Date);
    });

    it('should log events with different severity levels', () => {
      const initialEventCount = securityMonitor.getEvents().length;

      securityMonitor.logEvent(SecurityEventType.ENCRYPTION_SUCCESS, SecuritySeverity.LOW, 'Low severity event');
      securityMonitor.logEvent(SecurityEventType.SUSPICIOUS_ACTIVITY, SecuritySeverity.MEDIUM, 'Medium severity event');
      securityMonitor.logEvent(SecurityEventType.INTEGRITY_VIOLATION, SecuritySeverity.HIGH, 'High severity event');
      securityMonitor.logEvent(SecurityEventType.UNAUTHORIZED_ACCESS_ATTEMPT, SecuritySeverity.CRITICAL, 'Critical severity event');

      const events = securityMonitor.getEvents();

      // Events might be limited to 1000, so check that we have at least the initial count
      expect(events.length).toBeGreaterThanOrEqual(Math.min(initialEventCount, 1000));
      expect(events.length).toBeLessThanOrEqual(1000);

      // Check the most recent events contain our new severities
      const recentEvents = events.slice(0, 10); // Check top 10 events
      const severities = recentEvents.map(e => e.severity);
      expect(severities).toContain(SecuritySeverity.LOW);
      expect(severities).toContain(SecuritySeverity.MEDIUM);
      expect(severities).toContain(SecuritySeverity.HIGH);
      expect(severities).toContain(SecuritySeverity.CRITICAL);
    });

    it('should save events to localStorage', () => {
      securityMonitor.logEvent(
        SecurityEventType.DATA_MODIFICATION,
        SecuritySeverity.MEDIUM,
        'Data modified',
      );

      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'swiss-budget-security-events',
        expect.any(String),
      );
    });

    it('should log to console for development', () => {
      securityMonitor.logEvent(
        SecurityEventType.APPLICATION_ERROR,
        SecuritySeverity.HIGH,
        'Application error occurred',
        { error: 'Test error' },
      );

      expect(mockConsole.log).toHaveBeenCalledWith(
        '[SECURITY] HIGH: Application error occurred',
        { error: 'Test error' },
      );
    });
  });

  describe('Event Filtering', () => {
    beforeEach(() => {
      // Add test events
      securityMonitor.logEvent(SecurityEventType.DATA_ACCESS, SecuritySeverity.LOW, 'Access 1');
      securityMonitor.logEvent(SecurityEventType.DATA_MODIFICATION, SecuritySeverity.MEDIUM, 'Modification 1');
      securityMonitor.logEvent(SecurityEventType.SUSPICIOUS_ACTIVITY, SecuritySeverity.HIGH, 'Suspicious 1');
      securityMonitor.logEvent(SecurityEventType.DATA_ACCESS, SecuritySeverity.LOW, 'Access 2');
    });

    it('should filter events by type', () => {
      const accessEvents = securityMonitor.getEvents({ type: SecurityEventType.DATA_ACCESS });
      
      expect(accessEvents).toHaveLength(2);
      expect(accessEvents.every(e => e.type === SecurityEventType.DATA_ACCESS)).toBe(true);
    });

    it('should filter events by severity', () => {
      const highSeverityEvents = securityMonitor.getEvents({ severity: SecuritySeverity.HIGH });
      
      expect(highSeverityEvents).toHaveLength(1);
      expect(highSeverityEvents[0].severity).toBe(SecuritySeverity.HIGH);
    });

    it('should filter events by date range', () => {
      const now = new Date();
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
      
      const recentEvents = securityMonitor.getEvents({ startDate: oneHourAgo });
      
      expect(recentEvents.length).toBeGreaterThan(0);
      expect(recentEvents.every(e => e.timestamp >= oneHourAgo)).toBe(true);
    });

    it('should filter events by resolution status', () => {
      const events = securityMonitor.getEvents();
      const eventId = events[0].id;
      
      // Resolve one event
      securityMonitor.resolveEvent(eventId);
      
      const unresolvedEvents = securityMonitor.getEvents({ resolved: false });
      const resolvedEvents = securityMonitor.getEvents({ resolved: true });
      
      expect(unresolvedEvents.length).toBe(3);
      expect(resolvedEvents.length).toBe(1);
      expect(resolvedEvents[0].id).toBe(eventId);
    });

    it('should return events in reverse chronological order', () => {
      const events = securityMonitor.getEvents();
      
      for (let i = 1; i < events.length; i++) {
        expect(events[i - 1].timestamp.getTime()).toBeGreaterThanOrEqual(events[i].timestamp.getTime());
      }
    });
  });

  describe('Event Resolution', () => {
    it('should resolve security events', () => {
      const eventId = securityMonitor.logEvent(
        SecurityEventType.SUSPICIOUS_ACTIVITY,
        SecuritySeverity.MEDIUM,
        'Suspicious activity detected',
      );

      const resolved = securityMonitor.resolveEvent(eventId, 'admin');
      
      expect(resolved).toBe(true);

      const events = securityMonitor.getEvents();
      const event = events.find(e => e.id === eventId);
      
      expect(event?.resolved).toBe(true);
      expect(event?.resolvedAt).toBeInstanceOf(Date);
      expect(event?.resolvedBy).toBe('admin');
    });

    it('should handle resolution of non-existent events', () => {
      const resolved = securityMonitor.resolveEvent('non-existent-id');
      
      expect(resolved).toBe(false);
    });

    it('should use default resolver when not specified', () => {
      const eventId = securityMonitor.logEvent(
        SecurityEventType.INTEGRITY_VIOLATION,
        SecuritySeverity.HIGH,
        'Integrity violation',
      );

      // Verify the event was created
      let events = securityMonitor.getEvents();
      let event = events.find(e => e.id === eventId);
      expect(event).toBeDefined();

      const resolved = securityMonitor.resolveEvent(eventId);
      expect(resolved).toBe(true);

      events = securityMonitor.getEvents();
      event = events.find(e => e.id === eventId);

      expect(event).toBeDefined();
      expect(event?.resolved).toBe(true);
      expect(event?.resolvedBy).toBe('system');
    });
  });

  describe('Security Metrics', () => {
    beforeEach(() => {
      // Add test events for metrics
      securityMonitor.logEvent(SecurityEventType.DATA_ACCESS, SecuritySeverity.LOW, 'Access 1');
      securityMonitor.logEvent(SecurityEventType.DATA_ACCESS, SecuritySeverity.LOW, 'Access 2');
      securityMonitor.logEvent(SecurityEventType.SUSPICIOUS_ACTIVITY, SecuritySeverity.HIGH, 'Threat 1');
      securityMonitor.logEvent(SecurityEventType.INTEGRITY_VIOLATION, SecuritySeverity.CRITICAL, 'Threat 2');
    });

    it('should calculate security metrics', () => {
      const metrics = securityMonitor.getMetrics();

      // Events are limited to 1000, so just check that we have reasonable counts
      expect(metrics.totalEvents).toBeLessThanOrEqual(1000);
      expect(metrics.totalEvents).toBeGreaterThan(0);
      expect(metrics.eventsByType[SecurityEventType.DATA_ACCESS]).toBeGreaterThanOrEqual(2);
      expect(metrics.eventsByType[SecurityEventType.SUSPICIOUS_ACTIVITY]).toBeGreaterThanOrEqual(1);
      expect(metrics.eventsByType[SecurityEventType.INTEGRITY_VIOLATION]).toBeGreaterThanOrEqual(1);
      expect(metrics.eventsBySeverity[SecuritySeverity.LOW]).toBeGreaterThanOrEqual(2);
      expect(metrics.eventsBySeverity[SecuritySeverity.HIGH]).toBeGreaterThanOrEqual(1);
      expect(metrics.eventsBySeverity[SecuritySeverity.CRITICAL]).toBeGreaterThanOrEqual(1);
      expect(metrics.threatsDetected).toBeGreaterThanOrEqual(2);
      expect(metrics.threatsResolved).toBeGreaterThanOrEqual(0);
    });

    it('should track threat resolution', () => {
      const events = securityMonitor.getEvents();
      const threatEvent = events.find(e => e.type === SecurityEventType.SUSPICIOUS_ACTIVITY);
      
      if (threatEvent) {
        securityMonitor.resolveEvent(threatEvent.id);
      }

      const metrics = securityMonitor.getMetrics();
      expect(metrics.threatsResolved).toBe(1);
    });

    it('should calculate average response time', () => {
      // Create a new event and resolve it
      const eventId = securityMonitor.logEvent(
        SecurityEventType.DATA_ACCESS,
        SecuritySeverity.LOW,
        'Test event for response time',
      );

      // Resolve event to generate response time
      securityMonitor.resolveEvent(eventId);

      const metrics = securityMonitor.getMetrics();
      expect(metrics.averageResponseTime).toBeGreaterThanOrEqual(0);
    });

    it('should track last threat detection time', () => {
      const metrics = securityMonitor.getMetrics();
      
      expect(metrics.lastThreatDetected).toBeInstanceOf(Date);
    });
  });

  describe('Security Status', () => {
    it('should report secure status when no threats', () => {
      // Log only benign events
      securityMonitor.logEvent(SecurityEventType.DATA_ACCESS, SecuritySeverity.LOW, 'Normal access');
      securityMonitor.logEvent(SecurityEventType.ENCRYPTION_SUCCESS, SecuritySeverity.LOW, 'Encryption success');

      const status = securityMonitor.getSecurityStatus();
      
      expect(status.overall).toBe('secure');
      expect(status.threats).toBe(0);
      expect(status.lastScan).toBeInstanceOf(Date);
      expect(Array.isArray(status.recommendations)).toBe(true);
      expect(status.complianceScore).toBeGreaterThan(0);
    });

    it('should report warning status for unresolved threats', () => {
      securityMonitor.logEvent(SecurityEventType.SUSPICIOUS_ACTIVITY, SecuritySeverity.MEDIUM, 'Suspicious activity');

      const status = securityMonitor.getSecurityStatus();
      
      expect(status.overall).toBe('warning');
      expect(status.threats).toBe(1);
      expect(status.recommendations.length).toBeGreaterThan(0);
    });

    it('should report critical status for critical events', () => {
      // First clear any existing critical events by resolving them
      const existingEvents = securityMonitor.getEvents();
      existingEvents.forEach(event => {
        if (event.severity === SecuritySeverity.CRITICAL && !event.resolved) {
          securityMonitor.resolveEvent(event.id);
        }
      });

      // Now add a new critical event
      securityMonitor.logEvent(SecurityEventType.INTEGRITY_VIOLATION, SecuritySeverity.CRITICAL, 'Critical violation');

      const status = securityMonitor.getSecurityStatus();

      expect(status.overall).toBe('critical');
      expect(status.recommendations.some(r => r.includes('critical'))).toBe(true);
    });

    it('should provide security recommendations', () => {
      // Create multiple suspicious activities
      for (let i = 0; i < 6; i++) {
        securityMonitor.logEvent(SecurityEventType.SUSPICIOUS_ACTIVITY, SecuritySeverity.MEDIUM, `Suspicious ${i}`);
      }

      const status = securityMonitor.getSecurityStatus();
      
      expect(status.recommendations.some(r => r.includes('suspicious activities'))).toBe(true);
    });

    it('should calculate compliance score', () => {
      const status = securityMonitor.getSecurityStatus();
      
      expect(status.complianceScore).toBeGreaterThanOrEqual(0);
      expect(status.complianceScore).toBeLessThanOrEqual(100);
    });
  });

  describe('Monitoring Control', () => {
    it('should start monitoring', () => {
      securityMonitor.startMonitoring();
      
      // Should log application start event
      const events = securityMonitor.getEvents();
      expect(events.some(e => e.type === SecurityEventType.APPLICATION_START)).toBe(true);
      
      // Should set up interval for threat detection
      expect(global.setInterval).toHaveBeenCalled();
    });

    it('should stop monitoring', () => {
      securityMonitor.stopMonitoring();
      
      // Should log monitoring stop event
      const events = securityMonitor.getEvents();
      expect(events.some(e => e.description.includes('stopped'))).toBe(true);
    });
  });

  describe('Browser Security Monitoring', () => {
    it('should detect non-HTTPS protocol', () => {
      // Mock non-HTTPS location
      Object.defineProperty(global, 'location', {
        value: {
          protocol: 'http:',
          hostname: 'example.com',
        },
        writable: true,
      });

      securityMonitor.startMonitoring();

      const events = securityMonitor.getEvents();
      expect(events.some(e => 
        e.type === SecurityEventType.BROWSER_SECURITY_WARNING &&
        e.description.includes('HTTPS'),
      )).toBe(true);
    });

    it('should detect missing Web Crypto API', () => {
      // Mock missing crypto
      Object.defineProperty(global, 'crypto', {
        value: undefined,
        writable: true,
      });

      securityMonitor.startMonitoring();

      const events = securityMonitor.getEvents();
      expect(events.some(e => 
        e.type === SecurityEventType.BROWSER_SECURITY_WARNING &&
        e.description.includes('Web Crypto API'),
      )).toBe(true);
    });
  });
});
