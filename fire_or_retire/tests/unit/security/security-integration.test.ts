/**
 * Swiss Budget Pro - Security Integration Tests
 *
 * Comprehensive integration tests covering real-world scenarios
 * and cross-module interactions between encryption, privacy controls,
 * and security monitoring.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { encryption, SecurityUtils } from '../../../src/security/encryption';
import { privacyControls, DataCategory } from '../../../src/security/privacy-controls';
import { securityMonitor, SecurityEventType, SecuritySeverity } from '../../../src/security/security-monitor';

// Mock Web Crypto API
const mockCrypto = {
  getRandomValues: vi.fn((array: Uint8Array) => {
    for (let i = 0; i < array.length; i++) {
      array[i] = Math.floor(Math.random() * 256);
    }
    return array;
  }),
  subtle: {
    importKey: vi.fn(),
    deriveKey: vi.fn(),
    encrypt: vi.fn(),
    decrypt: vi.fn(),
    sign: vi.fn(),
  },
};

Object.defineProperty(global, 'crypto', {
  value: mockCrypto,
  writable: true,
});

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(() => null),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

Object.defineProperty(global, 'localStorage', {
  value: mockLocalStorage,
  writable: true,
});

// Mock console
const mockConsole = {
  log: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
};

Object.defineProperty(global, 'console', {
  value: mockConsole,
  writable: true,
});

// Mock browser environment
Object.defineProperty(global, 'location', {
  value: { protocol: 'https:', hostname: 'localhost' },
  writable: true,
});

Object.defineProperty(global, 'navigator', {
  value: { userAgent: 'Mozilla/5.0 (Test Browser)' },
  writable: true,
});

global.PerformanceObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  disconnect: vi.fn(),
}));

describe('Security Integration Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockLocalStorage.getItem.mockReturnValue(null);
    vi.spyOn(global, 'setInterval').mockImplementation(() => 123 as any);
    
    // Reset all modules
    privacyControls.resetForTesting();
    securityMonitor.resetForTesting();
    securityMonitor.disableThreatDetectionForTesting();
  });

  afterEach(() => {
    vi.clearAllMocks();
    vi.restoreAllMocks();
  });

  describe('End-to-End Data Protection Workflow', () => {
    it('should handle complete data lifecycle with encryption and privacy controls', async () => {
      // Setup sensitive data first
      const sensitiveData = {
        accountNumber: 'CH93 0076 2011 6238 5295 7',
        balance: 50000,
        transactions: [
          { date: '2024-01-01', amount: -1200, description: 'Rent' },
          { date: '2024-01-02', amount: 5000, description: 'Salary' },
        ],
      };

      // Setup encryption
      const mockKey = { type: 'secret' } as CryptoKey;
      const mockEncryptedBuffer = new ArrayBuffer(32);
      const mockDecryptedBuffer = new TextEncoder().encode(JSON.stringify(sensitiveData));

      mockCrypto.subtle.importKey.mockResolvedValue({});
      mockCrypto.subtle.deriveKey.mockResolvedValue(mockKey);
      mockCrypto.subtle.encrypt.mockResolvedValue(mockEncryptedBuffer);
      mockCrypto.subtle.decrypt.mockResolvedValue(mockDecryptedBuffer.buffer);

      // 1. Record user consent
      const consentId = privacyControls.recordConsent(
        'data_processing',
        [DataCategory.FINANCIAL_DATA],
        true,
      );

      expect(typeof consentId).toBe('string');

      // 2. Register sensitive data
      privacyControls.registerData(
        'sensitive-financial-data',
        DataCategory.FINANCIAL_DATA,
        'User financial data',
        'object',
        JSON.stringify(sensitiveData).length,
      );

      // 3. Encrypt the data
      const passphrase = SecurityUtils.generateSecurePassphrase(32);
      const keyResult = await encryption.deriveKey(passphrase);
      const encryptedData = await encryption.encrypt(JSON.stringify(sensitiveData), keyResult);

      expect(encryptedData).toHaveProperty('data');
      expect(encryptedData).toHaveProperty('iv');
      expect(encryptedData).toHaveProperty('salt');

      // 4. Log security events
      securityMonitor.logEvent(
        SecurityEventType.DATA_ACCESS,
        SecuritySeverity.LOW,
        'User accessed financial data',
      );

      securityMonitor.logEvent(
        SecurityEventType.ENCRYPTION_SUCCESS,
        SecuritySeverity.LOW,
        'Financial data encrypted successfully',
      );

      // 5. Verify data can be decrypted
      const decryptedData = await encryption.decrypt(encryptedData, keyResult);

      // The decrypted data should match the original
      expect(decryptedData).toBe(JSON.stringify(sensitiveData));

      // 6. Check privacy compliance
      const complianceStatus = privacyControls.getComplianceStatus();
      expect(complianceStatus.gdprCompliant).toBe(true);

      // 7. Verify security monitoring
      const securityStatus = securityMonitor.getSecurityStatus();
      expect(securityStatus.overall).toBe('secure');

      const events = securityMonitor.getEvents();
      expect(events.length).toBeGreaterThan(0);
    });

    it('should handle data breach simulation and response', async () => {
      // Setup
      const mockKey = { type: 'secret' } as CryptoKey;
      mockCrypto.subtle.importKey.mockResolvedValue({});
      mockCrypto.subtle.deriveKey.mockResolvedValue(mockKey);

      // 1. Register user data
      privacyControls.recordConsent(
        'data_processing',
        [DataCategory.PERSONAL_INFO, DataCategory.FINANCIAL_DATA],
        true,
      );

      const userData = {
        name: 'John Doe',
        email: '<EMAIL>',
        financialData: { balance: 10000 },
      };

      privacyControls.registerData(
        'personal-info-data',
        DataCategory.PERSONAL_INFO,
        'User personal information',
        'object',
        JSON.stringify({ name: userData.name, email: userData.email }).length,
      );

      privacyControls.registerData(
        'financial-data',
        DataCategory.FINANCIAL_DATA,
        'User financial data',
        'object',
        JSON.stringify(userData.financialData).length,
      );

      // 2. Simulate suspicious activities
      for (let i = 0; i < 10; i++) {
        securityMonitor.logEvent(
          SecurityEventType.UNAUTHORIZED_ACCESS_ATTEMPT,
          SecuritySeverity.HIGH,
          `Failed login attempt ${i + 1} from suspicious IP`,
        );
      }

      // 3. Simulate data integrity violation
      securityMonitor.logEvent(
        SecurityEventType.INTEGRITY_VIOLATION,
        SecuritySeverity.CRITICAL,
        'Unauthorized data modification detected',
      );

      // 4. Check security status
      const securityStatus = securityMonitor.getSecurityStatus();
      expect(securityStatus.overall).toBe('critical');
      expect(securityStatus.threats).toBeGreaterThan(0);

      // 5. Respond to breach - record consent withdrawal
      privacyControls.recordConsent(
        'data_processing',
        [DataCategory.PERSONAL_INFO],
        false, // Withdraw consent
      );

      // 6. Perform emergency data cleanup
      privacyControls.updatePrivacySettings(DataCategory.PERSONAL_INFO, {
        retentionDays: 0,
        autoCleanup: true,
      });

      privacyControls.performDataCleanup();
      // Cleanup should complete without errors
      expect(true).toBe(true);

      // 7. Log incident response
      securityMonitor.logEvent(
        SecurityEventType.APPLICATION_ERROR,
        SecuritySeverity.HIGH,
        'Emergency data cleanup performed due to security breach',
      );
    });
  });

  describe('Swiss Compliance Scenarios', () => {
    it('should handle Swiss banking data retention requirements', () => {
      // 1. Setup Swiss financial data
      const bankingData = {
        accountNumber: 'CH93 0076 2011 6238 5295 7',
        transactions: [],
        accountType: 'savings',
      };

      privacyControls.recordConsent(
        'data_processing',
        [DataCategory.FINANCIAL_DATA],
        true,
      );

      privacyControls.registerData(
        'swiss-banking-data',
        DataCategory.FINANCIAL_DATA,
        'Swiss banking data',
        'object',
        JSON.stringify(bankingData).length,
      );

      // 2. Set retention period below Swiss requirement
      privacyControls.updatePrivacySettings(DataCategory.FINANCIAL_DATA, {
        retentionDays: 1000, // Less than 7 years (2555 days)
      });

      // 3. Check compliance
      const complianceStatus = privacyControls.getComplianceStatus();
      expect(complianceStatus.swissCompliant).toBe(false);
      expect(complianceStatus.issues).toContain(
        'Financial data retention period below Swiss legal requirement',
      );

      // 4. Correct the retention period
      privacyControls.updatePrivacySettings(DataCategory.FINANCIAL_DATA, {
        retentionDays: 2555, // 7 years
      });

      const correctedStatus = privacyControls.getComplianceStatus();
      expect(correctedStatus.swissCompliant).toBe(true);

      // 5. Log compliance event
      securityMonitor.logEvent(
        SecurityEventType.APPLICATION_START,
        SecuritySeverity.LOW,
        'Swiss banking compliance verified',
      );
    });

    it('should handle GDPR data portability request', () => {
      // 1. Setup user data across multiple categories
      const userData = {
        personal: { name: 'Maria Müller', age: 35 },
        financial: { salary: 85000, expenses: 4500 },
        preferences: { language: 'de', currency: 'CHF' },
      };

      privacyControls.recordConsent(
        'data_processing',
        [DataCategory.PERSONAL_INFO, DataCategory.FINANCIAL_DATA, DataCategory.PREFERENCES],
        true,
      );

      Object.entries(userData).forEach(([category, data], index) => {
        const dataCategory = category === 'personal' ? DataCategory.PERSONAL_INFO :
                           category === 'financial' ? DataCategory.FINANCIAL_DATA :
                           DataCategory.PREFERENCES;

        privacyControls.registerData(
          `user-profile-${category}`,
          dataCategory,
          `User ${category} data`,
          'object',
          JSON.stringify(data).length,
        );
      });

      // 2. Submit data portability request
      const requestId = privacyControls.submitUserRightsRequest(
        'portability',
        [DataCategory.PERSONAL_INFO, DataCategory.FINANCIAL_DATA, DataCategory.PREFERENCES],
        'GDPR Article 20 - Data Portability Request',
      );

      expect(typeof requestId).toBe('string');

      // 3. Verify data can be accessed for export
      const personalData = privacyControls.getDataByCategory(DataCategory.PERSONAL_INFO);
      const financialData = privacyControls.getDataByCategory(DataCategory.FINANCIAL_DATA);
      const preferencesData = privacyControls.getDataByCategory(DataCategory.PREFERENCES);

      expect(personalData.length).toBeGreaterThan(0);
      expect(financialData.length).toBeGreaterThan(0);
      expect(preferencesData.length).toBeGreaterThan(0);

      // 4. Log GDPR compliance event
      securityMonitor.logEvent(
        SecurityEventType.USER_RIGHTS_REQUEST,
        SecuritySeverity.MEDIUM,
        `GDPR data portability request processed: ${requestId}`,
      );
    });
  });

  describe('Performance and Scalability Scenarios', () => {
    it('should handle high-volume data processing', async () => {
      const mockKey = { type: 'secret' } as CryptoKey;
      const mockEncryptedBuffer = new ArrayBuffer(32);

      mockCrypto.subtle.importKey.mockResolvedValue({});
      mockCrypto.subtle.deriveKey.mockResolvedValue(mockKey);
      mockCrypto.subtle.encrypt.mockResolvedValue(mockEncryptedBuffer);

      // Enable usage analytics first
      privacyControls.updatePrivacySettings(DataCategory.USAGE_ANALYTICS, {
        enabled: true,
      });

      const startTime = Date.now();

      // 1. Process large volume of data
      const promises = [];
      for (let i = 0; i < 100; i++) {
        // Register data
        privacyControls.registerData(
          `analytics-${i}`,
          DataCategory.USAGE_ANALYTICS,
          `Analytics session ${i}`,
          'object',
          100,
        );

        // Encrypt data
        const passphrase = SecurityUtils.generateSecurePassphrase(16);
        promises.push(
          encryption.deriveKey(passphrase).then(keyResult =>
            encryption.encrypt(`data-${i}`, keyResult),
          ),
        );

        // Log security events
        securityMonitor.logEvent(
          SecurityEventType.DATA_ACCESS,
          SecuritySeverity.LOW,
          `Bulk processing item ${i}`,
        );
      }

      await Promise.all(promises);

      const endTime = Date.now();
      const processingTime = endTime - startTime;

      // Should complete within reasonable time
      expect(processingTime).toBeLessThan(5000); // 5 seconds

      // Verify all data was processed
      const inventory = privacyControls.getDataInventory();
      const analyticsData = inventory.filter(item => item.category === DataCategory.USAGE_ANALYTICS);
      expect(analyticsData).toHaveLength(100);

      const events = securityMonitor.getEvents();
      expect(events.length).toBeGreaterThan(0);
    });

    it('should handle concurrent user sessions', () => {
      // Simulate multiple users performing operations simultaneously
      const userOperations = Array.from({ length: 10 }, (_, userId) => {
        return Promise.resolve().then(() => {
          // Each user records consent
          const consentId = privacyControls.recordConsent(
            'data_processing',
            [DataCategory.PERSONAL_INFO],
            true,
          );

          // Each user registers data
          privacyControls.registerData(
            `user-session-${userId}`,
            DataCategory.PERSONAL_INFO,
            `User ${userId} session data`,
            'object',
            100,
          );

          // Each user generates security events
          securityMonitor.logEvent(
            SecurityEventType.DATA_ACCESS,
            SecuritySeverity.LOW,
            `User ${userId} logged in`,
          );

          return { userId, consentId };
        });
      });

      return Promise.all(userOperations).then((results) => {
        expect(results).toHaveLength(10);

        // Verify data integrity
        const inventory = privacyControls.getDataInventory();
        const personalData = inventory.filter(item => item.category === DataCategory.PERSONAL_INFO);
        expect(personalData).toHaveLength(10);

        const events = securityMonitor.getEvents();
        const loginEvents = events.filter(e => e.description.includes('logged in'));
        expect(loginEvents.length).toBe(10);
      });
    });
  });

  describe('Error Recovery Scenarios', () => {
    it('should recover from encryption failures', async () => {
      // 1. Setup failing encryption
      mockCrypto.subtle.encrypt.mockRejectedValueOnce(new Error('Encryption failed'));
      mockCrypto.subtle.encrypt.mockResolvedValue(new ArrayBuffer(32));

      const mockKey = { type: 'secret' } as CryptoKey;
      mockCrypto.subtle.importKey.mockResolvedValue({});
      mockCrypto.subtle.deriveKey.mockResolvedValue(mockKey);

      const passphrase = SecurityUtils.generateSecurePassphrase(32);
      const keyResult = await encryption.deriveKey(passphrase);

      // 2. First encryption should fail
      await expect(encryption.encrypt('test data', keyResult)).rejects.toThrow('Encryption failed');

      // 3. Log the failure
      securityMonitor.logEvent(
        SecurityEventType.ENCRYPTION_FAILURE,
        SecuritySeverity.HIGH,
        'Encryption operation failed',
      );

      // 4. Retry should succeed
      const retryResult = await encryption.encrypt('test data', keyResult);
      expect(retryResult).toHaveProperty('data');

      // 5. Log recovery
      securityMonitor.logEvent(
        SecurityEventType.ENCRYPTION_SUCCESS,
        SecuritySeverity.LOW,
        'Encryption recovered after failure',
      );

      const events = securityMonitor.getEvents();
      expect(events.some(e => e.description.includes('failed'))).toBe(true);
      expect(events.some(e => e.description.includes('recovered'))).toBe(true);
    });

    it('should handle storage failures gracefully', () => {
      // 1. Setup failing localStorage
      mockLocalStorage.setItem.mockImplementation(() => {
        throw new Error('Storage quota exceeded');
      });

      // 2. Operations should continue despite storage failures
      expect(() => {
        privacyControls.updatePrivacySettings(DataCategory.FINANCIAL_DATA, {
          enabled: false,
        });
      }).toThrow('Storage quota exceeded');

      expect(() => {
        securityMonitor.logEvent(
          SecurityEventType.DATA_ACCESS,
          SecuritySeverity.LOW,
          'Test event during storage failure',
        );
      }).not.toThrow();

      // 3. Verify error logging
      expect(mockConsole.error).toHaveBeenCalled();

      // 4. Restore storage and verify recovery
      mockLocalStorage.setItem.mockImplementation(() => {});

      privacyControls.updatePrivacySettings(DataCategory.FINANCIAL_DATA, {
        enabled: true,
      });

      const settings = privacyControls.getPrivacySettings();
      expect(settings[DataCategory.FINANCIAL_DATA].enabled).toBe(true);
    });
  });

  describe('Security Audit Scenarios', () => {
    it('should provide comprehensive security audit trail', () => {
      // 1. Perform various operations
      const operations = [
        () => privacyControls.recordConsent('data_processing', [DataCategory.FINANCIAL_DATA], true),
        () => privacyControls.registerData('audit-test-data', DataCategory.FINANCIAL_DATA, 'Audit test data', 'object', 100),
        () => securityMonitor.logEvent(SecurityEventType.DATA_ACCESS, SecuritySeverity.LOW, 'Audit: Data accessed'),
        () => securityMonitor.logEvent(SecurityEventType.DATA_MODIFICATION, SecuritySeverity.MEDIUM, 'Audit: Data modified'),
        () => privacyControls.submitUserRightsRequest('access', [DataCategory.FINANCIAL_DATA], 'Audit request'),
      ];

      operations.forEach(op => op());

      // 2. Generate audit report
      const securityStatus = securityMonitor.getSecurityStatus();
      const complianceStatus = privacyControls.getComplianceStatus();
      const metrics = securityMonitor.getMetrics();
      const events = securityMonitor.getEvents();

      // 3. Verify audit trail completeness
      expect(events.length).toBeGreaterThan(0);
      expect(metrics.totalEvents).toBeGreaterThan(0);
      expect(securityStatus).toHaveProperty('overall');
      expect(complianceStatus).toHaveProperty('gdprCompliant');

      // 4. Verify event types are logged
      const eventTypes = events.map(e => e.type);
      expect(eventTypes).toContain(SecurityEventType.DATA_ACCESS);
      expect(eventTypes).toContain(SecurityEventType.DATA_MODIFICATION);

      // 5. Log audit completion
      securityMonitor.logEvent(
        SecurityEventType.APPLICATION_START,
        SecuritySeverity.LOW,
        `Security audit completed. Events: ${events.length}, Compliance: ${complianceStatus.gdprCompliant}`,
      );
    });
  });

  describe('Stress Testing Scenarios', () => {
    it('should handle extreme data volumes', () => {
      // Enable usage analytics first
      privacyControls.updatePrivacySettings(DataCategory.USAGE_ANALYTICS, {
        enabled: true,
      });

      const startTime = Date.now();

      // Generate massive amounts of data (reduced for performance)
      for (let i = 0; i < 1000; i++) {
        privacyControls.registerData(
          `stress-data-${i}`,
          DataCategory.USAGE_ANALYTICS,
          `Stress test data ${i}`,
          'object',
          1000,
        );

        if (i % 100 === 0) {
          securityMonitor.logEvent(
            SecurityEventType.DATA_ACCESS,
            SecuritySeverity.LOW,
            `Stress test batch ${Math.floor(i / 100)}`,
          );
        }
      }

      const endTime = Date.now();
      const processingTime = endTime - startTime;

      // Should handle large volumes efficiently
      expect(processingTime).toBeLessThan(10000); // 10 seconds

      const inventory = privacyControls.getDataInventory();
      const analyticsData = inventory.filter(item => item.category === DataCategory.USAGE_ANALYTICS);
      expect(analyticsData).toHaveLength(1000);
    });

    it('should maintain performance under concurrent load', () => {
      const concurrentOperations = 50;
      const operationsPerThread = 20;

      const promises = Array.from({ length: concurrentOperations }, (_, threadId) => {
        return Promise.resolve().then(() => {
          for (let i = 0; i < operationsPerThread; i++) {
            // Mix of operations
            privacyControls.registerData(
              `thread-${threadId}-op-${i}`,
              DataCategory.PREFERENCES,
              `Thread ${threadId} operation ${i}`,
              'object',
              100,
            );

            securityMonitor.logEvent(
              SecurityEventType.DATA_ACCESS,
              SecuritySeverity.LOW,
              `Thread ${threadId} operation ${i}`,
            );

            if (i % 5 === 0) {
              privacyControls.updatePrivacySettings(DataCategory.PREFERENCES, {
                retentionDays: 100 + threadId + i,
              });
            }
          }
        });
      });

      return Promise.all(promises).then(() => {
        const inventory = privacyControls.getDataInventory();
        const events = securityMonitor.getEvents();

        const preferencesData = inventory.filter(item => item.category === DataCategory.PREFERENCES);
        expect(preferencesData).toHaveLength(concurrentOperations * operationsPerThread);
        expect(events.length).toBeGreaterThan(0);
      });
    });
  });
});
