/**
 * Swiss Budget Pro - Privacy Controls Tests
 *
 * Comprehensive tests for privacy controls, data management,
 * and GDPR/Swiss compliance features.
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import {
  DataCategory,
  PrivacyControlsManager,
  privacyControls,
} from '../../../src/security/privacy-controls';

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

Object.defineProperty(global, 'localStorage', {
  value: mockLocalStorage,
  writable: true,
});

describe('PrivacyControlsManager', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    vi.clearAllMocks();
    mockLocalStorage.getItem.mockReturnValue(null);
  });

  afterEach(() => {
    // Clean up after each test
    vi.clearAllMocks();
  });

  describe('Privacy Settings Management', () => {
    it('should initialize with default privacy settings', () => {
      const settings = privacyControls.getPrivacySettings();

      expect(settings).toHaveProperty(DataCategory.FINANCIAL_DATA);
      expect(settings).toHaveProperty(DataCategory.PERSONAL_INFO);
      expect(settings).toHaveProperty(DataCategory.USAGE_ANALYTICS);
      expect(settings).toHaveProperty(DataCategory.PREFERENCES);
      expect(settings).toHaveProperty(DataCategory.CACHE_DATA);
      expect(settings).toHaveProperty(DataCategory.TEMPORARY_DATA);

      // Financial data should be enabled by default with 7-year retention
      expect(settings[DataCategory.FINANCIAL_DATA].enabled).toBe(true);
      expect(settings[DataCategory.FINANCIAL_DATA].retentionDays).toBe(2555); // 7 years
      expect(settings[DataCategory.FINANCIAL_DATA].encryptionRequired).toBe(
        true,
      );

      // Usage analytics should be opt-in (disabled by default)
      expect(settings[DataCategory.USAGE_ANALYTICS].enabled).toBe(false);
      expect(settings[DataCategory.USAGE_ANALYTICS].anonymized).toBe(true);
    });

    it('should update privacy settings for specific category', () => {
      const newSettings = {
        enabled: false,
        retentionDays: 30,
      };

      privacyControls.updatePrivacySettings(
        DataCategory.USAGE_ANALYTICS,
        newSettings,
      );

      const settings = privacyControls.getPrivacySettings();
      expect(settings[DataCategory.USAGE_ANALYTICS].enabled).toBe(false);
      expect(settings[DataCategory.USAGE_ANALYTICS].retentionDays).toBe(30);

      // Should save to localStorage
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'swiss-budget-privacy-settings',
        expect.any(String),
      );
    });

    it('should check data collection permissions', () => {
      // Financial data should be allowed by default
      expect(
        privacyControls.isDataCollectionAllowed(DataCategory.FINANCIAL_DATA),
      ).toBe(true);

      // Usage analytics should not be allowed by default
      expect(
        privacyControls.isDataCollectionAllowed(DataCategory.USAGE_ANALYTICS),
      ).toBe(false);
    });

    it('should load saved privacy settings from localStorage', () => {
      const savedSettings = {
        [DataCategory.FINANCIAL_DATA]: {
          enabled: false,
          retentionDays: 1000,
          encryptionRequired: false,
          exportAllowed: false,
        },
      };

      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(savedSettings));

      // Update settings to trigger loading from localStorage
      privacyControls.updatePrivacySettings(DataCategory.FINANCIAL_DATA, {
        enabled: false,
        retentionDays: 1000,
        encryptionRequired: false,
        exportAllowed: false,
      });

      const settings = privacyControls.getPrivacySettings();

      // Should have the updated settings
      expect(settings[DataCategory.FINANCIAL_DATA].enabled).toBe(false);
      expect(settings[DataCategory.FINANCIAL_DATA].retentionDays).toBe(1000);
    });
  });

  describe('Data Inventory Management', () => {
    it('should register data in inventory', () => {
      // Enable financial data collection for this test
      privacyControls.updatePrivacySettings(DataCategory.FINANCIAL_DATA, {
        enabled: true,
      });

      privacyControls.registerData(
        'test-data-1',
        DataCategory.FINANCIAL_DATA,
        'Test financial data',
        'object',
        1024,
        true,
      );

      const inventory = privacyControls.getDataInventory();
      expect(inventory).toHaveLength(1);

      const item = inventory[0];
      expect(item.id).toBe('test-data-1');
      expect(item.category).toBe(DataCategory.FINANCIAL_DATA);
      expect(item.description).toBe('Test financial data');
      expect(item.dataType).toBe('object');
      expect(item.size).toBe(1024);
      expect(item.encrypted).toBe(true);
      expect(item.retentionDate).toBeInstanceOf(Date);
    });

    it('should prevent data registration when collection is disabled', () => {
      // Disable usage analytics
      privacyControls.updatePrivacySettings(DataCategory.USAGE_ANALYTICS, {
        enabled: false,
      });

      expect(() => {
        privacyControls.registerData(
          'analytics-data',
          DataCategory.USAGE_ANALYTICS,
          'Analytics data',
          'object',
          512,
        );
      }).toThrow('Data collection not allowed for category: usage_analytics');
    });

    it('should update data inventory items', () => {
      privacyControls.registerData(
        'test-data-2',
        DataCategory.PERSONAL_INFO,
        'Personal information',
        'object',
        256,
      );

      privacyControls.updateDataInventory('test-data-2', {
        size: 512,
        encrypted: true,
      });

      const inventory = privacyControls.getDataInventory();
      const item = inventory.find(i => i.id === 'test-data-2');

      expect(item?.size).toBe(512);
      expect(item?.encrypted).toBe(true);
      expect(item?.lastModified).toBeInstanceOf(Date);
    });

    it('should remove data from inventory', () => {
      privacyControls.registerData(
        'test-data-3',
        DataCategory.CACHE_DATA,
        'Cache data',
        'string',
        128,
      );

      let inventory = privacyControls.getDataInventory();
      expect(inventory.some(i => i.id === 'test-data-3')).toBe(true);

      privacyControls.removeDataFromInventory('test-data-3');

      inventory = privacyControls.getDataInventory();
      expect(inventory.some(i => i.id === 'test-data-3')).toBe(false);
    });

    it('should filter data by category', () => {
      // Enable financial data collection for this test
      privacyControls.updatePrivacySettings(DataCategory.FINANCIAL_DATA, {
        enabled: true,
      });

      // Clear any existing data first
      const existingInventory = privacyControls.getDataInventory();
      existingInventory.forEach(item => {
        privacyControls.removeDataFromInventory(item.id);
      });

      privacyControls.registerData(
        'financial-1',
        DataCategory.FINANCIAL_DATA,
        'Financial 1',
        'object',
        100,
      );
      privacyControls.registerData(
        'financial-2',
        DataCategory.FINANCIAL_DATA,
        'Financial 2',
        'object',
        200,
      );
      privacyControls.registerData(
        'personal-1',
        DataCategory.PERSONAL_INFO,
        'Personal 1',
        'object',
        150,
      );

      const financialData = privacyControls.getDataByCategory(
        DataCategory.FINANCIAL_DATA,
      );
      const personalData = privacyControls.getDataByCategory(
        DataCategory.PERSONAL_INFO,
      );

      expect(financialData).toHaveLength(2);
      expect(personalData).toHaveLength(1);
      expect(
        financialData.every(
          item => item.category === DataCategory.FINANCIAL_DATA,
        ),
      ).toBe(true);
      expect(
        personalData.every(item => item.category === DataCategory.PERSONAL_INFO),
      ).toBe(true);
    });
  });

  describe('Consent Management', () => {
    it('should record user consent', () => {
      const consentId = privacyControls.recordConsent(
        'data_processing',
        [DataCategory.FINANCIAL_DATA, DataCategory.PERSONAL_INFO],
        true,
        '1.0',
      );

      expect(typeof consentId).toBe('string');
      expect(consentId.length).toBeGreaterThan(0);

      const history = privacyControls.getConsentHistory();
      expect(history).toHaveLength(1);

      const consent = history[0];
      expect(consent.id).toBe(consentId);
      expect(consent.purpose).toBe('data_processing');
      expect(consent.dataCategories).toEqual([
        DataCategory.FINANCIAL_DATA,
        DataCategory.PERSONAL_INFO,
      ]);
      expect(consent.granted).toBe(true);
      expect(consent.version).toBe('1.0');
      expect(consent.timestamp).toBeInstanceOf(Date);
    });

    it('should check consent for specific purposes', () => {
      privacyControls.recordConsent(
        'analytics',
        [DataCategory.USAGE_ANALYTICS],
        true,
      );

      privacyControls.recordConsent(
        'marketing',
        [DataCategory.PERSONAL_INFO],
        false,
      );

      expect(
        privacyControls.hasConsent('analytics', [DataCategory.USAGE_ANALYTICS]),
      ).toBe(true);
      expect(
        privacyControls.hasConsent('marketing', [DataCategory.PERSONAL_INFO]),
      ).toBe(false);
      expect(
        privacyControls.hasConsent('unknown', [DataCategory.FINANCIAL_DATA]),
      ).toBe(false);
    });

    it('should require consent for all requested data categories', () => {
      privacyControls.recordConsent(
        'partial_consent',
        [DataCategory.FINANCIAL_DATA],
        true,
      );

      // Should have consent for financial data only
      expect(
        privacyControls.hasConsent('partial_consent', [
          DataCategory.FINANCIAL_DATA,
        ]),
      ).toBe(true);

      // Should not have consent for both financial and personal data
      expect(
        privacyControls.hasConsent('partial_consent', [
          DataCategory.FINANCIAL_DATA,
          DataCategory.PERSONAL_INFO,
        ]),
      ).toBe(false);
    });
  });

  describe('User Rights Requests', () => {
    it('should submit data access request', () => {
      const requestId = privacyControls.submitUserRightsRequest(
        'access',
        [DataCategory.FINANCIAL_DATA, DataCategory.PERSONAL_INFO],
        'Need to review my data',
      );

      expect(typeof requestId).toBe('string');
      expect(requestId.length).toBeGreaterThan(0);
    });

    it('should submit data erasure request', () => {
      const requestId = privacyControls.submitUserRightsRequest(
        'erasure',
        [DataCategory.CACHE_DATA, DataCategory.TEMPORARY_DATA],
        'Delete temporary data',
      );

      expect(typeof requestId).toBe('string');
      expect(requestId.length).toBeGreaterThan(0);
    });

    it('should submit data portability request', () => {
      const requestId = privacyControls.submitUserRightsRequest(
        'portability',
        [DataCategory.FINANCIAL_DATA],
        'Export my financial data',
      );

      expect(typeof requestId).toBe('string');
      expect(requestId.length).toBeGreaterThan(0);
    });

    it('should submit data rectification request', () => {
      const requestId = privacyControls.submitUserRightsRequest(
        'rectification',
        [DataCategory.PERSONAL_INFO],
        'Correct my personal information',
      );

      expect(typeof requestId).toBe('string');
      expect(requestId.length).toBeGreaterThan(0);
    });
  });

  describe('Data Cleanup', () => {
    it('should perform automatic data cleanup', () => {
      // Register data with short retention period
      privacyControls.updatePrivacySettings(DataCategory.TEMPORARY_DATA, {
        retentionDays: 1,
        autoCleanup: true,
      });

      privacyControls.registerData(
        'expired-data',
        DataCategory.TEMPORARY_DATA,
        'Expired temporary data',
        'string',
        64,
      );

      // Manually set retention date to past
      const inventory = privacyControls.getDataInventory();
      const item = inventory.find(i => i.id === 'expired-data');
      if (item) {
        item.retentionDate = new Date(Date.now() - 24 * 60 * 60 * 1000); // Yesterday
        privacyControls.updateDataInventory('expired-data', {
          retentionDate: item.retentionDate,
        });
      }

      // Perform cleanup
      privacyControls.performDataCleanup();

      // Data should be removed
      const updatedInventory = privacyControls.getDataInventory();
      expect(updatedInventory.some(i => i.id === 'expired-data')).toBe(false);
    });

    it('should respect Swiss financial record retention requirements', () => {
      // Enable financial data collection for this test
      privacyControls.updatePrivacySettings(DataCategory.FINANCIAL_DATA, {
        enabled: true,
      });

      // Register financial data
      privacyControls.registerData(
        'financial-record',
        DataCategory.FINANCIAL_DATA,
        'Important financial record',
        'object',
        1024,
      );

      // Try to perform cleanup (should not remove financial data within 7 years)
      privacyControls.performDataCleanup();

      const inventory = privacyControls.getDataInventory();
      expect(inventory.some(i => i.id === 'financial-record')).toBe(true);
    });
  });

  describe('Compliance Status', () => {
    it('should report GDPR and Swiss compliance status', () => {
      // Record valid consent
      privacyControls.recordConsent(
        'data_processing',
        [DataCategory.FINANCIAL_DATA],
        true,
      );

      const status = privacyControls.getComplianceStatus();

      expect(status).toHaveProperty('gdprCompliant');
      expect(status).toHaveProperty('swissCompliant');
      expect(status).toHaveProperty('issues');
      expect(Array.isArray(status.issues)).toBe(true);
    });

    it('should detect compliance issues', () => {
      // Set financial data retention below Swiss requirement
      privacyControls.updatePrivacySettings(DataCategory.FINANCIAL_DATA, {
        retentionDays: 365, // Less than 7 years
      });

      const status = privacyControls.getComplianceStatus();

      expect(status.swissCompliant).toBe(false);
      expect(status.issues).toContain(
        'Financial data retention period below Swiss legal requirement',
      );
    });

    it('should require valid consent for compliance', () => {
      // Create a fresh privacy controls instance to test without consent
      const freshPrivacyControls = new PrivacyControlsManager();
      const status = freshPrivacyControls.getComplianceStatus();

      // The test should check that without proper consent, compliance fails
      expect(status.issues).toContain('No valid consent records found');
    });
  });
});
