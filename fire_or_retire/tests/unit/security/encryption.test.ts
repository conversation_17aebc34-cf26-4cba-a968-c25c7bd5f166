/**
 * Swiss Budget Pro - Encryption Engine Tests
 *
 * Comprehensive tests for the advanced encryption system
 * ensuring bank-level security and performance requirements.
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import {
  EncryptedData,
  encryption,
  SecurityUtils,
} from '../../../src/security/encryption';

// Mock Web Crypto API for testing
const mockCrypto = {
  getRandomValues: vi.fn((array: Uint8Array) => {
    // Fill with non-zero random values to avoid test issues
    for (let i = 0; i < array.length; i++) {
      array[i] = Math.floor(Math.random() * 255) + 1; // 1-255 to avoid zeros
    }
    return array;
  }),
  subtle: {
    importKey: vi.fn(),
    deriveKey: vi.fn(),
    encrypt: vi.fn(),
    decrypt: vi.fn(),
    sign: vi.fn(),
  },
};

// Setup global crypto mock
Object.defineProperty(global, 'crypto', {
  value: mockCrypto,
  writable: true,
});

describe('SwissBudgetEncryption', () => {
  beforeEach(() => {
    // Clear metrics before each test
    encryption.clearMetrics();

    // Reset mocks
    vi.clearAllMocks();
  });

  afterEach(() => {
    // Clean up after each test
    encryption.clearMetrics();
  });

  describe('Key Derivation', () => {
    it('should derive encryption key from passphrase', async () => {
      // Mock successful key derivation
      const mockKey = { type: 'secret' } as CryptoKey;
      mockCrypto.subtle.importKey.mockResolvedValue({});
      mockCrypto.subtle.deriveKey.mockResolvedValue(mockKey);

      const passphrase = 'test-passphrase-123';
      const result = await encryption.deriveKey(passphrase);

      expect(result).toHaveProperty('key');
      expect(result).toHaveProperty('salt');
      expect(result.salt).toBeInstanceOf(Uint8Array);
      expect(result.salt.length).toBe(32); // 256 bits
      // Verify that the crypto methods were called
      expect(mockCrypto.subtle.importKey).toHaveBeenCalled();
      expect(mockCrypto.subtle.deriveKey).toHaveBeenCalled();
    });

    it('should use provided salt when available', async () => {
      const mockKey = { type: 'secret' } as CryptoKey;
      mockCrypto.subtle.importKey.mockResolvedValue({});
      mockCrypto.subtle.deriveKey.mockResolvedValue(mockKey);

      const passphrase = 'test-passphrase-123';
      const providedSalt = new Uint8Array(32).fill(42);

      const result = await encryption.deriveKey(passphrase, providedSalt);

      expect(result.salt).toEqual(providedSalt);
    });

    it('should handle key derivation errors', async () => {
      mockCrypto.subtle.importKey.mockRejectedValue(
        new Error('Key import failed'),
      );

      const passphrase = 'test-passphrase-123';

      await expect(encryption.deriveKey(passphrase)).rejects.toThrow(
        'Key derivation failed',
      );
    });

    it('should measure key derivation performance', async () => {
      const mockKey = { type: 'secret' } as CryptoKey;
      mockCrypto.subtle.importKey.mockResolvedValue({});
      mockCrypto.subtle.deriveKey.mockResolvedValue(mockKey);

      const passphrase = 'test-passphrase-123';
      const startTime = performance.now();

      await encryption.deriveKey(passphrase);

      const endTime = performance.now();
      const derivationTime = endTime - startTime;

      // Key derivation should complete within reasonable time
      expect(derivationTime).toBeLessThan(1000); // 1 second
    });
  });

  describe('Data Encryption', () => {
    const mockEncryptionKey = {
      key: { type: 'secret' } as CryptoKey,
      salt: new Uint8Array(32).fill(1),
    };

    it('should encrypt data successfully', async () => {
      const mockEncryptedBuffer = new ArrayBuffer(32);
      const mockEncryptedArray = new Uint8Array(mockEncryptedBuffer);
      mockEncryptedArray.fill(123); // Fill with test data

      mockCrypto.subtle.encrypt.mockResolvedValue(mockEncryptedBuffer);

      const testData = 'sensitive financial data';
      const result = await encryption.encrypt(testData, mockEncryptionKey);

      expect(result).toHaveProperty('data');
      expect(result).toHaveProperty('iv');
      expect(result).toHaveProperty('salt');
      expect(result).toHaveProperty('tag');
      expect(result).toHaveProperty('version');
      expect(result.version).toBe('1.0');

      // Verify that encryption was called
      expect(mockCrypto.subtle.encrypt).toHaveBeenCalled();
    });

    it('should generate unique IV for each encryption', async () => {
      const mockEncryptedBuffer = new ArrayBuffer(32);
      mockCrypto.subtle.encrypt.mockResolvedValue(mockEncryptedBuffer);

      const testData = 'test data';

      const result1 = await encryption.encrypt(testData, mockEncryptionKey);
      const result2 = await encryption.encrypt(testData, mockEncryptionKey);

      // IVs should be different for each encryption
      expect(result1.iv).not.toBe(result2.iv);
    });

    it('should record encryption metrics', async () => {
      const mockEncryptedBuffer = new ArrayBuffer(32);
      mockCrypto.subtle.encrypt.mockResolvedValue(mockEncryptedBuffer);

      const testData = 'test data for metrics';
      await encryption.encrypt(testData, mockEncryptionKey);

      const metrics = encryption.getMetrics();
      expect(metrics).toHaveLength(1);
      expect(metrics[0]).toHaveProperty('encryptionTime');
      expect(metrics[0]).toHaveProperty('dataSize');
      expect(metrics[0].dataSize).toBe(testData.length);
      expect(metrics[0].encryptionTime).toBeGreaterThan(0);
    });

    it('should handle encryption errors', async () => {
      mockCrypto.subtle.encrypt.mockRejectedValue(
        new Error('Encryption failed'),
      );

      const testData = 'test data';

      await expect(
        encryption.encrypt(testData, mockEncryptionKey),
      ).rejects.toThrow('Encryption failed');
    });
  });

  describe('Data Decryption', () => {
    const mockEncryptedData: EncryptedData = {
      data: 'bW9ja0VuY3J5cHRlZERhdGE=', // base64 encoded mock data
      iv: 'bW9ja0lW', // base64 encoded mock IV
      salt: 'bW9ja1NhbHQ=', // base64 encoded mock salt
      tag: 'bW9ja1RhZw==', // base64 encoded mock tag
      version: '1.0',
    };

    it('should decrypt data successfully', async () => {
      const mockDecryptedBuffer = new TextEncoder().encode('decrypted data');
      const mockKey = { type: 'secret' } as CryptoKey;

      mockCrypto.subtle.importKey.mockResolvedValue({});
      mockCrypto.subtle.deriveKey.mockResolvedValue(mockKey);
      mockCrypto.subtle.decrypt.mockResolvedValue(mockDecryptedBuffer.buffer);

      const passphrase = 'test-passphrase';
      const result = await encryption.decrypt(mockEncryptedData, passphrase);

      expect(result).toBe('decrypted data');
      expect(mockCrypto.subtle.decrypt).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'AES-GCM',
          iv: expect.any(Uint8Array),
          tagLength: 128,
        }),
        mockKey,
        expect.any(Uint8Array),
      );
    });

    it('should record decryption metrics', async () => {
      const mockDecryptedBuffer = new TextEncoder().encode('test data');
      const mockKey = { type: 'secret' } as CryptoKey;

      mockCrypto.subtle.importKey.mockResolvedValue({});
      mockCrypto.subtle.deriveKey.mockResolvedValue(mockKey);
      mockCrypto.subtle.decrypt.mockResolvedValue(mockDecryptedBuffer.buffer);

      const passphrase = 'test-passphrase';
      await encryption.decrypt(mockEncryptedData, passphrase);

      const metrics = encryption.getMetrics();
      expect(metrics).toHaveLength(1);
      expect(metrics[0]).toHaveProperty('decryptionTime');
      expect(metrics[0].decryptionTime).toBeGreaterThan(0);
    });

    it('should handle decryption errors', async () => {
      mockCrypto.subtle.importKey.mockRejectedValue(
        new Error('Key derivation failed'),
      );

      const passphrase = 'wrong-passphrase';

      await expect(
        encryption.decrypt(mockEncryptedData, passphrase),
      ).rejects.toThrow('Decryption failed');
    });
  });

  describe('Data Integrity', () => {
    const mockKey = { type: 'secret' } as CryptoKey;

    it('should generate HMAC signature', async () => {
      const mockSignature = new ArrayBuffer(32);
      mockCrypto.subtle.sign.mockResolvedValue(mockSignature);

      const testData = 'data to sign';
      const signature = await encryption.generateSignature(testData, mockKey);

      expect(typeof signature).toBe('string');
      // Verify that signing was called
      expect(mockCrypto.subtle.sign).toHaveBeenCalled();
    });

    it('should verify data integrity', async () => {
      const mockSignature = new ArrayBuffer(32);
      new Uint8Array(mockSignature).fill(42);
      mockCrypto.subtle.sign.mockResolvedValue(mockSignature);

      const testData = 'data to verify';
      const signature = btoa(
        String.fromCharCode(...new Uint8Array(mockSignature)),
      );

      const isValid = await encryption.verifyIntegrity(
        testData,
        signature,
        mockKey,
      );

      expect(isValid).toBe(true);
    });

    it('should detect tampered data', async () => {
      const mockSignature = new ArrayBuffer(32);
      new Uint8Array(mockSignature).fill(42);
      mockCrypto.subtle.sign.mockResolvedValue(mockSignature);

      const testData = 'original data';
      const wrongSignature = 'dGFtcGVyZWRTaWduYXR1cmU='; // base64 encoded "tamperedSignature"

      const isValid = await encryption.verifyIntegrity(
        testData,
        wrongSignature,
        mockKey,
      );

      expect(isValid).toBe(false);
    });
  });

  describe('Performance Requirements', () => {
    it('should meet encryption performance targets', async () => {
      const mockEncryptedBuffer = new ArrayBuffer(32);
      mockCrypto.subtle.encrypt.mockResolvedValue(mockEncryptedBuffer);

      const mockKey = {
        key: { type: 'secret' } as CryptoKey,
        salt: new Uint8Array(32),
      };

      const testData = 'performance test data';
      const startTime = performance.now();

      await encryption.encrypt(testData, mockKey);

      const endTime = performance.now();
      const encryptionTime = endTime - startTime;

      // Encryption should complete within 100ms target
      expect(encryptionTime).toBeLessThan(100);
    });

    it('should meet decryption performance targets', async () => {
      const mockDecryptedBuffer = new TextEncoder().encode('test data');
      const mockKey = { type: 'secret' } as CryptoKey;

      mockCrypto.subtle.importKey.mockResolvedValue({});
      mockCrypto.subtle.deriveKey.mockResolvedValue(mockKey);
      mockCrypto.subtle.decrypt.mockResolvedValue(mockDecryptedBuffer.buffer);

      const mockEncryptedData: EncryptedData = {
        data: 'dGVzdA==',
        iv: 'aXY=',
        salt: 'c2FsdA==',
        tag: 'dGFn',
        version: '1.0',
      };

      const startTime = performance.now();

      await encryption.decrypt(mockEncryptedData, 'test-passphrase');

      const endTime = performance.now();
      const decryptionTime = endTime - startTime;

      // Decryption should complete within 100ms target
      expect(decryptionTime).toBeLessThan(100);
    });
  });
});

describe('SecurityUtils', () => {
  describe('Passphrase Generation', () => {
    it('should generate secure passphrase of specified length', () => {
      const passphrase = SecurityUtils.generateSecurePassphrase(16);

      expect(passphrase).toHaveLength(16);
      expect(typeof passphrase).toBe('string');
    });

    it('should generate different passphrases each time', () => {
      const passphrase1 = SecurityUtils.generateSecurePassphrase(32);
      const passphrase2 = SecurityUtils.generateSecurePassphrase(32);

      expect(passphrase1).not.toBe(passphrase2);
    });

    it('should use default length when not specified', () => {
      const passphrase = SecurityUtils.generateSecurePassphrase();

      expect(passphrase).toHaveLength(32);
    });
  });

  describe('Passphrase Strength Estimation', () => {
    it('should rate strong passphrase highly', () => {
      const strongPassphrase = 'MyStr0ng!P@ssw0rd#2024';
      const result = SecurityUtils.estimatePassphraseStrength(strongPassphrase);

      expect(result.score).toBeGreaterThanOrEqual(5);
      expect(result.feedback).toHaveLength(0);
    });

    it('should rate weak passphrase poorly', () => {
      const weakPassphrase = '123';
      const result = SecurityUtils.estimatePassphraseStrength(weakPassphrase);

      expect(result.score).toBeLessThan(3);
      expect(result.feedback.length).toBeGreaterThan(0);
    });

    it('should provide helpful feedback for improvement', () => {
      const weakPassphrase = 'abc'; // Only lowercase letters, no numbers or symbols, short
      const result = SecurityUtils.estimatePassphraseStrength(weakPassphrase);

      expect(result.feedback).toContain(
        'Consider using a mix of letters, numbers, and symbols',
      );
    });

    it('should detect repeated characters', () => {
      const repeatedPassphrase = 'aaabbbccc';
      const result =
        SecurityUtils.estimatePassphraseStrength(repeatedPassphrase);

      expect(result.feedback).toContain('Avoid repeated characters');
    });
  });

  describe('Web Crypto Availability', () => {
    it('should detect Web Crypto API availability', () => {
      const isAvailable = SecurityUtils.isWebCryptoAvailable();

      // In our test environment with mocked crypto, this should return true
      expect(isAvailable).toBe(true);
    });
  });
});
