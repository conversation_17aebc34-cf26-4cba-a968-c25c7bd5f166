# Integration Testing Guide

This guide covers integration testing for Swiss Budget Pro, focusing on testing component interactions, data flow, and system integration points.

## Overview

Integration tests validate that different parts of the application work correctly together. They test the interfaces between components, data flow through the system, and integration with external services.

```{admonition} 🔗 Integration Testing Goals
:class: tip

**Component Interactions**: Verify components work together correctly
**Data Flow**: Ensure data flows properly through the system
**State Management**: Test global state updates and synchronization
**External Integration**: Validate API calls and external service integration
**Swiss Features**: Test Swiss-specific component interactions
```

## Framework Setup

### Vitest Configuration for Integration Tests

```typescript
// vitest.config.integration.ts
import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react';

export default defineConfig({
  plugins: [react()],
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./test/integration-setup.ts'],
    include: ['test/integration/**/*.test.{ts,tsx}'],
    coverage: {
      provider: 'v8',
      include: ['src/**/*.{ts,tsx}'],
      exclude: ['src/**/*.test.{ts,tsx}', 'src/**/*.stories.{ts,tsx}']
    }
  }
});
```

### Integration Test Setup

```typescript
// test/integration-setup.ts
import '@testing-library/jest-dom';
import { vi } from 'vitest';

// Mock localStorage with full implementation
const localStorageMock = {
  getItem: vi.fn((key: string) => {
    return localStorageMock.store[key] || null;
  }),
  setItem: vi.fn((key: string, value: string) => {
    localStorageMock.store[key] = value;
  }),
  removeItem: vi.fn((key: string) => {
    delete localStorageMock.store[key];
  }),
  clear: vi.fn(() => {
    localStorageMock.store = {};
  }),
  store: {} as Record<string, string>
};

global.localStorage = localStorageMock;

// Mock fetch for API calls
global.fetch = vi.fn();

// Reset mocks before each test
beforeEach(() => {
  vi.clearAllMocks();
  localStorageMock.store = {};
});
```

## Testing Patterns

### 1. Component Integration Testing

Testing how multiple components work together:

```typescript
// test/integration/dashboard-integration.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, test, expect, vi } from 'vitest';
import SwissBudgetPro from '../../src/SwissBudgetPro';

describe('Dashboard Integration', () => {
  test('income change updates all dependent calculations', async () => {
    const user = userEvent.setup();
    
    render(<SwissBudgetPro />);
    
    // Initial state verification
    expect(screen.getByTestId('fire-years')).toHaveTextContent('--');
    expect(screen.getByTestId('savings-rate')).toHaveTextContent('0%');
    
    // Update income
    const incomeInput = screen.getByLabelText(/monthly income/i);
    await user.clear(incomeInput);
    await user.type(incomeInput, '8000');
    
    // Update expenses
    const expensesInput = screen.getByLabelText(/monthly expenses/i);
    await user.clear(expensesInput);
    await user.type(expensesInput, '5000');
    
    // Wait for calculations to update
    await waitFor(() => {
      expect(screen.getByTestId('savings-rate')).toHaveTextContent('37.5%');
    });
    
    // Verify FIRE calculation updated
    await waitFor(() => {
      const fireYears = screen.getByTestId('fire-years');
      expect(fireYears).not.toHaveTextContent('--');
      expect(parseFloat(fireYears.textContent || '0')).toBeGreaterThan(0);
    });
    
    // Verify tax calculations updated
    await waitFor(() => {
      const totalTax = screen.getByTestId('total-tax');
      expect(totalTax).not.toHaveTextContent('CHF 0');
    });
  });

  test('canton change updates tax calculations across all components', async () => {
    const user = userEvent.setup();
    
    render(<SwissBudgetPro />);
    
    // Set up initial data
    await user.type(screen.getByLabelText(/monthly income/i), '10000');
    await user.type(screen.getByLabelText(/monthly expenses/i), '6000');
    
    // Change canton from ZH to GE
    const cantonSelect = screen.getByLabelText(/canton/i);
    await user.selectOptions(cantonSelect, 'GE');
    
    // Wait for tax recalculation
    await waitFor(() => {
      const totalTax = screen.getByTestId('total-tax');
      expect(totalTax).toBeInTheDocument();
    });
    
    // Navigate to tax optimization tab
    await user.click(screen.getByRole('tab', { name: /tax optimization/i }));
    
    // Verify canton-specific tax information is displayed
    await waitFor(() => {
      expect(screen.getByText(/geneva/i)).toBeInTheDocument();
    });
    
    // Verify cantonal tax amount is reasonable for Geneva
    const cantonalTax = screen.getByTestId('cantonal-tax');
    const taxAmount = parseFloat(cantonalTax.textContent?.replace(/[^\d.]/g, '') || '0');
    expect(taxAmount).toBeGreaterThan(1000); // Geneva has higher taxes
  });
});
```

### 2. Data Persistence Integration

Testing localStorage integration across components:

```typescript
// test/integration/data-persistence.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, test, expect, beforeEach } from 'vitest';
import SwissBudgetPro from '../../src/SwissBudgetPro';

describe('Data Persistence Integration', () => {
  beforeEach(() => {
    localStorage.clear();
  });

  test('saves and restores complete user scenario', async () => {
    const user = userEvent.setup();
    
    // First render - enter data
    const { unmount } = render(<SwissBudgetPro />);
    
    // Fill in comprehensive user data
    await user.type(screen.getByLabelText(/monthly income/i), '12000');
    await user.type(screen.getByLabelText(/monthly expenses/i), '8000');
    await user.type(screen.getByLabelText(/current savings/i), '150000');
    await user.selectOptions(screen.getByLabelText(/canton/i), 'VD');
    
    // Navigate to different tabs to trigger saves
    await user.click(screen.getByRole('tab', { name: /target goal/i }));
    await user.type(screen.getByLabelText(/retirement age/i), '55');
    
    await user.click(screen.getByRole('tab', { name: /company growth/i }));
    await user.type(screen.getByLabelText(/business income/i), '2000');
    
    // Unmount component
    unmount();
    
    // Second render - verify data restoration
    render(<SwissBudgetPro />);
    
    // Verify all data was restored
    expect(screen.getByDisplayValue('12000')).toBeInTheDocument();
    expect(screen.getByDisplayValue('8000')).toBeInTheDocument();
    expect(screen.getByDisplayValue('150000')).toBeInTheDocument();
    expect(screen.getByDisplayValue('VD')).toBeInTheDocument();
    
    // Navigate to other tabs and verify data
    await user.click(screen.getByRole('tab', { name: /target goal/i }));
    expect(screen.getByDisplayValue('55')).toBeInTheDocument();
    
    await user.click(screen.getByRole('tab', { name: /company growth/i }));
    expect(screen.getByDisplayValue('2000')).toBeInTheDocument();
  });

  test('handles data migration between versions', async () => {
    // Simulate old data format
    const oldData = {
      income: 8000,
      expenses: 5000,
      savings: 100000,
      // Missing new fields like canton, retirement age, etc.
    };
    
    localStorage.setItem('swissBudgetPro_data', JSON.stringify(oldData));
    
    render(<SwissBudgetPro />);
    
    // Verify old data is loaded
    expect(screen.getByDisplayValue('8000')).toBeInTheDocument();
    expect(screen.getByDisplayValue('5000')).toBeInTheDocument();
    expect(screen.getByDisplayValue('100000')).toBeInTheDocument();
    
    // Verify new fields have default values
    expect(screen.getByDisplayValue('ZH')).toBeInTheDocument(); // Default canton
    
    // Make a change to trigger save with new format
    const user = userEvent.setup();
    await user.type(screen.getByLabelText(/monthly income/i), '1'); // 80001
    
    // Verify data is now in new format
    const savedData = JSON.parse(localStorage.getItem('swissBudgetPro_data') || '{}');
    expect(savedData).toHaveProperty('canton');
    expect(savedData).toHaveProperty('retirementAge');
    expect(savedData.income).toBe(80001);
  });
});
```

### 3. Swiss Tax Engine Integration

Testing tax engine integration with UI components:

```typescript
// test/integration/swiss-tax-integration.test.tsx
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, test, expect } from 'vitest';
import SwissBudgetPro from '../../src/SwissBudgetPro';

describe('Swiss Tax Engine Integration', () => {
  test('tax calculations update across all relevant components', async () => {
    const user = userEvent.setup();
    
    render(<SwissBudgetPro />);
    
    // Set up high-income scenario
    await user.type(screen.getByLabelText(/monthly income/i), '15000'); // CHF 180k annually
    await user.type(screen.getByLabelText(/monthly expenses/i), '10000');
    await user.selectOptions(screen.getByLabelText(/canton/i), 'ZH');
    
    // Wait for tax calculations
    await waitFor(() => {
      const totalTax = screen.getByTestId('total-tax');
      expect(totalTax).not.toHaveTextContent('CHF 0');
    });
    
    // Navigate to tax optimization tab
    await user.click(screen.getByRole('tab', { name: /tax optimization/i }));
    
    // Verify detailed tax breakdown
    await waitFor(() => {
      expect(screen.getByTestId('federal-tax')).toBeInTheDocument();
      expect(screen.getByTestId('cantonal-tax')).toBeInTheDocument();
      expect(screen.getByTestId('municipal-tax')).toBeInTheDocument();
    });
    
    // Verify tax amounts are reasonable for high income in Zurich
    const federalTax = parseFloat(
      screen.getByTestId('federal-tax').textContent?.replace(/[^\d.]/g, '') || '0'
    );
    const cantonalTax = parseFloat(
      screen.getByTestId('cantonal-tax').textContent?.replace(/[^\d.]/g, '') || '0'
    );
    
    expect(federalTax).toBeGreaterThan(10000); // Significant federal tax
    expect(cantonalTax).toBeGreaterThan(15000); // Significant cantonal tax
    
    // Test canton comparison
    await user.click(screen.getByRole('button', { name: /compare cantons/i }));
    
    await waitFor(() => {
      expect(screen.getByTestId('canton-comparison-table')).toBeInTheDocument();
    });
    
    // Verify all 26 cantons are compared
    const cantonRows = screen.getAllByTestId('canton-row');
    expect(cantonRows).toHaveLength(26);
    
    // Verify Zug shows lower taxes
    const zugRow = screen.getByTestId('canton-row-ZG');
    const zugTax = parseFloat(
      zugRow.querySelector('[data-testid="total-tax"]')?.textContent?.replace(/[^\d.]/g, '') || '0'
    );
    const zurichTax = federalTax + cantonalTax;
    
    expect(zugTax).toBeLessThan(zurichTax);
  });

  test('civil status change affects tax calculations', async () => {
    const user = userEvent.setup();
    
    render(<SwissBudgetPro />);
    
    // Set up married couple scenario
    await user.type(screen.getByLabelText(/monthly income/i), '12000');
    await user.selectOptions(screen.getByLabelText(/civil status/i), 'married');
    
    // Get initial tax calculation
    await waitFor(() => {
      expect(screen.getByTestId('total-tax')).not.toHaveTextContent('CHF 0');
    });
    
    const marriedTax = parseFloat(
      screen.getByTestId('total-tax').textContent?.replace(/[^\d.]/g, '') || '0'
    );
    
    // Change to single
    await user.selectOptions(screen.getByLabelText(/civil status/i), 'single');
    
    // Wait for recalculation
    await waitFor(() => {
      const singleTax = parseFloat(
        screen.getByTestId('total-tax').textContent?.replace(/[^\d.]/g, '') || '0'
      );
      
      // Single should pay more tax than married
      expect(singleTax).toBeGreaterThan(marriedTax);
    });
  });
});
```

### 4. Healthcare Cost Optimizer Integration

Testing healthcare optimizer integration:

```typescript
// test/integration/healthcare-integration.test.tsx
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, test, expect } from 'vitest';
import SwissBudgetPro from '../../src/SwissBudgetPro';

describe('Healthcare Cost Optimizer Integration', () => {
  test('healthcare optimization integrates with FIRE calculations', async () => {
    const user = userEvent.setup();
    
    render(<SwissBudgetPro />);
    
    // Set up basic financial data
    await user.type(screen.getByLabelText(/monthly income/i), '8000');
    await user.type(screen.getByLabelText(/monthly expenses/i), '5500');
    await user.selectOptions(screen.getByLabelText(/canton/i), 'ZH');
    
    // Navigate to healthcare tab
    await user.click(screen.getByRole('tab', { name: /healthcare/i }));
    
    // Fill health profile
    await user.selectOptions(screen.getByLabelText(/risk profile/i), 'low');
    await user.type(screen.getByLabelText(/annual medical costs/i), '600');
    
    // Wait for optimization recommendations
    await waitFor(() => {
      expect(screen.getByTestId('recommended-deductible')).toBeInTheDocument();
      expect(screen.getByTestId('annual-savings')).toBeInTheDocument();
    });
    
    // Get recommended deductible (should be high for low-risk profile)
    const recommendedDeductible = screen.getByTestId('recommended-deductible');
    expect(recommendedDeductible).toHaveTextContent('2500');
    
    // Apply recommendation
    await user.click(screen.getByRole('button', { name: /apply recommendation/i }));
    
    // Navigate back to overview
    await user.click(screen.getByRole('tab', { name: /overview/i }));
    
    // Verify healthcare savings are reflected in FIRE calculation
    await waitFor(() => {
      const fireYears = screen.getByTestId('fire-years');
      const years = parseFloat(fireYears.textContent?.replace(/[^\d.]/g, '') || '0');
      expect(years).toBeGreaterThan(0);
      expect(years).toBeLessThan(25); // Should be reasonable
    });
    
    // Verify monthly expenses include optimized healthcare costs
    const totalExpenses = screen.getByTestId('total-monthly-expenses');
    expect(totalExpenses).toBeInTheDocument();
  });

  test('canton change updates healthcare premiums and recommendations', async () => {
    const user = userEvent.setup();
    
    render(<SwissBudgetPro />);
    
    // Navigate to healthcare tab
    await user.click(screen.getByRole('tab', { name: /healthcare/i }));
    
    // Set initial canton (expensive)
    await user.selectOptions(screen.getByLabelText(/canton/i), 'GE');
    await user.selectOptions(screen.getByLabelText(/risk profile/i), 'medium');
    
    // Wait for premium calculation
    await waitFor(() => {
      expect(screen.getByTestId('current-premium')).toBeInTheDocument();
    });
    
    const genevaPremium = parseFloat(
      screen.getByTestId('current-premium').textContent?.replace(/[^\d.]/g, '') || '0'
    );
    
    // Change to cheaper canton
    await user.selectOptions(screen.getByLabelText(/canton/i), 'AI');
    
    // Wait for premium recalculation
    await waitFor(() => {
      const appenzellPremium = parseFloat(
        screen.getByTestId('current-premium').textContent?.replace(/[^\d.]/g, '') || '0'
      );
      
      // Appenzell should be significantly cheaper
      expect(appenzellPremium).toBeLessThan(genevaPremium);
      expect(genevaPremium - appenzellPremium).toBeGreaterThan(50); // At least CHF 50 difference
    });
    
    // Verify geographic arbitrage recommendation appears
    await waitFor(() => {
      expect(screen.getByTestId('geographic-arbitrage-savings')).toBeInTheDocument();
    });
  });
});
```

## Best Practices

### 1. Test Organization

```typescript
// Good: Organize tests by feature integration
describe('FIRE Calculation Integration', () => {
  describe('Income Changes', () => {
    test('updates FIRE timeline when income increases', () => {});
    test('updates savings rate calculation', () => {});
  });
  
  describe('Swiss Tax Integration', () => {
    test('includes tax savings in FIRE calculation', () => {});
    test('updates when canton changes', () => {});
  });
});
```

### 2. Data Setup

```typescript
// Good: Use helper functions for complex data setup
const setupHighIncomeScenario = async (user: UserEvent) => {
  await user.type(screen.getByLabelText(/monthly income/i), '15000');
  await user.type(screen.getByLabelText(/monthly expenses/i), '10000');
  await user.type(screen.getByLabelText(/current savings/i), '200000');
  await user.selectOptions(screen.getByLabelText(/canton/i), 'ZH');
};

const setupFamilyScenario = async (user: UserEvent) => {
  await setupHighIncomeScenario(user);
  await user.selectOptions(screen.getByLabelText(/civil status/i), 'married');
  await user.type(screen.getByLabelText(/children/i), '2');
};
```

### 3. Async Testing

```typescript
// Good: Proper async handling
test('calculations update after input changes', async () => {
  const user = userEvent.setup();
  
  render(<SwissBudgetPro />);
  
  await user.type(screen.getByLabelText(/income/i), '8000');
  
  // Wait for specific calculation to complete
  await waitFor(() => {
    expect(screen.getByTestId('fire-years')).not.toHaveTextContent('--');
  }, { timeout: 3000 });
  
  // Verify calculation result
  const fireYears = screen.getByTestId('fire-years');
  expect(parseFloat(fireYears.textContent || '0')).toBeGreaterThan(0);
});
```

### 4. Error Handling

```typescript
// Good: Test error scenarios
test('handles localStorage quota exceeded gracefully', async () => {
  // Mock localStorage to throw quota exceeded error
  vi.spyOn(Storage.prototype, 'setItem').mockImplementation(() => {
    throw new Error('QuotaExceededError');
  });
  
  const user = userEvent.setup();
  render(<SwissBudgetPro />);
  
  await user.type(screen.getByLabelText(/income/i), '8000');
  
  // Should show error message but not crash
  await waitFor(() => {
    expect(screen.getByText(/storage quota exceeded/i)).toBeInTheDocument();
  });
  
  // Application should still function
  expect(screen.getByTestId('fire-years')).toBeInTheDocument();
});
```

## Running Integration Tests

### Development Commands

```bash
# Run integration tests only
npm run test:integration

# Run integration tests in watch mode
npm run test:integration:watch

# Run integration tests with coverage
npm run test:integration:coverage

# Run specific integration test file
npm run test:integration -- dashboard-integration.test.tsx
```

### CI/CD Integration

```yaml
# Integration test stage in CI/CD
integration-tests:
  runs-on: ubuntu-latest
  steps:
    - uses: actions/checkout@v4
    - uses: actions/setup-node@v4
    - run: npm ci
    - run: npm run test:integration
    - run: npm run test:integration:coverage
    - uses: actions/upload-artifact@v4
      with:
        name: integration-coverage
        path: coverage/
```

## Debugging Integration Tests

### Common Issues

1. **Timing Issues**: Use `waitFor` for async operations
2. **State Pollution**: Clear localStorage between tests
3. **Mock Conflicts**: Reset mocks in `beforeEach`
4. **Component Cleanup**: Ensure proper unmounting

### Debug Configuration

```typescript
// vitest.config.ts - Debug mode
export default defineConfig({
  test: {
    // ... other config
    reporter: 'verbose',
    logHeapUsage: true,
    testTimeout: 10000, // Longer timeout for debugging
  }
});
```

---

*This integration testing guide ensures comprehensive coverage of component interactions and data flow in Swiss Budget Pro.*
