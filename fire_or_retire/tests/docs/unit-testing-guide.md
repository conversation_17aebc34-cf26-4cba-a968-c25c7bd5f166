# Unit Testing Guide

This guide covers unit testing best practices for Swiss Budget Pro, focusing on testing individual components, functions, and calculation engines in isolation.

## Overview

Unit tests form the foundation of our testing strategy, providing fast feedback and ensuring the reliability of individual code units. They are particularly critical for financial calculations where accuracy is paramount.

```{admonition} 🎯 Unit Testing Goals
:class: tip

**Fast Execution**: Tests run in < 30 seconds
**High Coverage**: 95%+ coverage for calculation engines
**Isolation**: No external dependencies
**Deterministic**: Same input always produces same output
**Swiss-Focused**: Special attention to Swiss financial logic
```

## Framework Setup

### Vitest Configuration

```typescript
// vitest.config.ts
import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react';

export default defineConfig({
  plugins: [react()],
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./test/setup.ts'],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'test/',
        '**/*.d.ts',
        '**/*.config.*'
      ],
      thresholds: {
        global: {
          branches: 90,
          functions: 95,
          lines: 95,
          statements: 95
        }
      }
    }
  }
});
```

### Test Setup

```typescript
// test/setup.ts
import '@testing-library/jest-dom';
import { vi } from 'vitest';

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
global.localStorage = localStorageMock;

// Mock console methods in tests
global.console = {
  ...console,
  warn: vi.fn(),
  error: vi.fn(),
};
```

## Testing Patterns

### 1. Pure Function Testing

For calculation functions that don't have side effects:

```typescript
// src/utils/calculations.ts
export function calculateCompoundInterest(
  principal: number,
  rate: number,
  time: number,
  compoundingFrequency: number = 12
): number {
  return principal * Math.pow(1 + rate / compoundingFrequency, compoundingFrequency * time);
}

// test/utils/calculations.test.ts
import { describe, test, expect } from 'vitest';
import { calculateCompoundInterest } from '../../src/utils/calculations';

describe('calculateCompoundInterest', () => {
  test('calculates simple compound interest correctly', () => {
    const result = calculateCompoundInterest(1000, 0.05, 1, 12);
    expect(result).toBeCloseTo(1051.16, 2);
  });

  test('handles zero principal', () => {
    const result = calculateCompoundInterest(0, 0.05, 1, 12);
    expect(result).toBe(0);
  });

  test('handles zero interest rate', () => {
    const result = calculateCompoundInterest(1000, 0, 1, 12);
    expect(result).toBe(1000);
  });

  test('handles edge case: very high interest rate', () => {
    const result = calculateCompoundInterest(1000, 1, 1, 12);
    expect(result).toBeCloseTo(2613.04, 2);
  });
});
```

### 2. Swiss Tax Engine Testing

Critical for accuracy in Swiss financial calculations:

```typescript
// test/engines/SwissTaxEngine.test.ts
import { describe, test, expect } from 'vitest';
import { SwissTaxEngine } from '../../src/engines/SwissTaxEngine';

describe('SwissTaxEngine', () => {
  describe('Zurich Canton (ZH)', () => {
    const testCases = [
      {
        income: 50000,
        civilStatus: 'single' as const,
        expected: {
          federalTax: 0,
          cantonalTax: 2100,
          municipalTax: 840,
          churchTax: 210,
          totalTax: 3150
        }
      },
      {
        income: 100000,
        civilStatus: 'single' as const,
        expected: {
          federalTax: 2103,
          cantonalTax: 8420,
          municipalTax: 3368,
          churchTax: 842,
          totalTax: 14733
        }
      },
      {
        income: 200000,
        civilStatus: 'married' as const,
        expected: {
          federalTax: 6513,
          cantonalTax: 18840,
          municipalTax: 7536,
          churchTax: 1884,
          totalTax: 34773
        }
      }
    ];

    testCases.forEach(({ income, civilStatus, expected }) => {
      test(`calculates tax for CHF ${income} income (${civilStatus})`, () => {
        const result = SwissTaxEngine.calculateTax({
          income,
          canton: 'ZH',
          municipality: 'Zurich',
          civilStatus,
          year: 2024,
          churchTax: true
        });

        expect(result.federalTax).toBeCloseTo(expected.federalTax, 2);
        expect(result.cantonalTax).toBeCloseTo(expected.cantonalTax, 2);
        expect(result.municipalTax).toBeCloseTo(expected.municipalTax, 2);
        expect(result.churchTax).toBeCloseTo(expected.churchTax, 2);
        expect(result.totalTax).toBeCloseTo(expected.totalTax, 2);
      });
    });
  });

  describe('All Cantons Validation', () => {
    const cantons = [
      'AG', 'AI', 'AR', 'BE', 'BL', 'BS', 'FR', 'GE', 'GL', 'GR',
      'JU', 'LU', 'NE', 'NW', 'OW', 'SG', 'SH', 'SO', 'SZ', 'TG',
      'TI', 'UR', 'VD', 'VS', 'ZG', 'ZH'
    ];

    cantons.forEach(canton => {
      test(`calculates tax for canton ${canton}`, () => {
        const result = SwissTaxEngine.calculateTax({
          income: 80000,
          canton,
          civilStatus: 'single',
          year: 2024
        });

        expect(result.totalTax).toBeGreaterThan(0);
        expect(result.federalTax).toBeGreaterThanOrEqual(0);
        expect(result.cantonalTax).toBeGreaterThan(0);
        expect(result.effectiveRate).toBeGreaterThan(0);
        expect(result.effectiveRate).toBeLessThan(0.5); // Sanity check
      });
    });
  });

  describe('Edge Cases', () => {
    test('handles minimum taxable income', () => {
      const result = SwissTaxEngine.calculateTax({
        income: 1000,
        canton: 'ZH',
        civilStatus: 'single',
        year: 2024
      });

      expect(result.federalTax).toBe(0);
      expect(result.totalTax).toBeGreaterThanOrEqual(0);
    });

    test('handles very high income', () => {
      const result = SwissTaxEngine.calculateTax({
        income: 10000000,
        canton: 'ZH',
        civilStatus: 'single',
        year: 2024
      });

      expect(result.totalTax).toBeGreaterThan(0);
      expect(result.effectiveRate).toBeLessThan(0.5);
    });

    test('throws error for invalid canton', () => {
      expect(() => {
        SwissTaxEngine.calculateTax({
          income: 80000,
          canton: 'INVALID' as any,
          civilStatus: 'single',
          year: 2024
        });
      }).toThrow('Invalid canton code');
    });
  });
});
```

### 3. React Component Testing

Testing UI components with React Testing Library:

```typescript
// test/components/IncomeInput.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, test, expect, vi } from 'vitest';
import IncomeInput from '../../src/components/IncomeInput';

describe('IncomeInput', () => {
  test('renders with initial value', () => {
    render(<IncomeInput value={5000} onChange={vi.fn()} />);
    
    const input = screen.getByRole('spinbutton', { name: /monthly income/i });
    expect(input).toHaveValue(5000);
  });

  test('calls onChange when value changes', async () => {
    const user = userEvent.setup();
    const mockOnChange = vi.fn();
    
    render(<IncomeInput value={0} onChange={mockOnChange} />);
    
    const input = screen.getByRole('spinbutton', { name: /monthly income/i });
    await user.clear(input);
    await user.type(input, '7500');
    
    expect(mockOnChange).toHaveBeenCalledWith(7500);
  });

  test('validates input format', async () => {
    const user = userEvent.setup();
    const mockOnChange = vi.fn();
    
    render(<IncomeInput value={0} onChange={mockOnChange} />);
    
    const input = screen.getByRole('spinbutton', { name: /monthly income/i });
    await user.type(input, 'invalid');
    
    expect(screen.getByText(/please enter a valid number/i)).toBeInTheDocument();
    expect(mockOnChange).not.toHaveBeenCalled();
  });

  test('handles Swiss number formatting', () => {
    render(<IncomeInput value={12345.67} onChange={vi.fn()} />);
    
    // Should display with Swiss formatting (space as thousands separator)
    expect(screen.getByDisplayValue('12 345.67')).toBeInTheDocument();
  });
});
```

### 4. Custom Hook Testing

Testing React hooks in isolation:

```typescript
// test/hooks/useLocalStorage.test.ts
import { renderHook, act } from '@testing-library/react';
import { describe, test, expect, vi, beforeEach } from 'vitest';
import { useLocalStorage } from '../../src/hooks/useLocalStorage';

describe('useLocalStorage', () => {
  beforeEach(() => {
    localStorage.clear();
    vi.clearAllMocks();
  });

  test('returns initial value when no stored value exists', () => {
    const { result } = renderHook(() => useLocalStorage('test-key', 'initial'));
    
    expect(result.current[0]).toBe('initial');
  });

  test('returns stored value when it exists', () => {
    localStorage.setItem('test-key', JSON.stringify('stored-value'));
    
    const { result } = renderHook(() => useLocalStorage('test-key', 'initial'));
    
    expect(result.current[0]).toBe('stored-value');
  });

  test('updates localStorage when value changes', () => {
    const { result } = renderHook(() => useLocalStorage('test-key', 'initial'));
    
    act(() => {
      result.current[1]('new-value');
    });
    
    expect(localStorage.setItem).toHaveBeenCalledWith(
      'test-key',
      JSON.stringify('new-value')
    );
    expect(result.current[0]).toBe('new-value');
  });

  test('handles JSON parsing errors gracefully', () => {
    localStorage.setItem('test-key', 'invalid-json{');
    
    const { result } = renderHook(() => useLocalStorage('test-key', 'fallback'));
    
    expect(result.current[0]).toBe('fallback');
  });
});
```

## Swiss-Specific Testing Patterns

### Healthcare Cost Calculations

```typescript
// test/engines/HealthcareCostOptimizer.test.ts
import { describe, test, expect } from 'vitest';
import { HealthcareCostOptimizer } from '../../src/engines/HealthcareCostOptimizer';

describe('HealthcareCostOptimizer', () => {
  test('calculates optimal deductible for low-risk profile', () => {
    const result = HealthcareCostOptimizer.optimizeDeductible({
      age: 25,
      canton: 'ZH',
      riskProfile: 'low',
      expectedAnnualCosts: 500,
      currentDeductible: 300
    });

    expect(result.recommendedDeductible).toBe(2500);
    expect(result.annualSavings).toBeGreaterThan(1000);
    expect(result.breakEvenCosts).toBeGreaterThan(2000);
  });

  test('validates premium data for all cantons', () => {
    const cantons = ['ZH', 'BE', 'VD', 'AG', 'SG', 'LU', 'TI', 'VS', 'BS', 'GR'];
    
    cantons.forEach(canton => {
      const premiums = HealthcareCostOptimizer.getPremiumData(canton, 30);
      
      expect(premiums).toBeDefined();
      expect(premiums.length).toBeGreaterThan(0);
      expect(premiums[0]).toHaveProperty('insurer');
      expect(premiums[0]).toHaveProperty('premium');
      expect(premiums[0].premium).toBeGreaterThan(200);
      expect(premiums[0].premium).toBeLessThan(800);
    });
  });
});
```

### FIRE Calculation Testing

```typescript
// test/engines/FIRECalculationEngine.test.ts
import { describe, test, expect } from 'vitest';
import { FIRECalculationEngine } from '../../src/engines/FIRECalculationEngine';

describe('FIRECalculationEngine', () => {
  test('calculates FIRE timeline accurately', () => {
    const result = FIRECalculationEngine.calculateFIRETimeline({
      currentAge: 30,
      currentSavings: 50000,
      monthlyIncome: 8000,
      monthlyExpenses: 5000,
      expectedReturn: 0.07,
      inflationRate: 0.02,
      safeWithdrawalRate: 0.04
    });

    expect(result.fireNumber).toBeCloseTo(1500000, -3); // 5000 * 12 * 25
    expect(result.yearsToFIRE).toBeGreaterThan(10);
    expect(result.yearsToFIRE).toBeLessThan(20);
    expect(result.savingsRate).toBeCloseTo(0.375, 3); // 3000/8000
  });

  test('handles edge case: already financially independent', () => {
    const result = FIRECalculationEngine.calculateFIRETimeline({
      currentAge: 45,
      currentSavings: 2000000,
      monthlyIncome: 8000,
      monthlyExpenses: 5000,
      expectedReturn: 0.07,
      inflationRate: 0.02,
      safeWithdrawalRate: 0.04
    });

    expect(result.yearsToFIRE).toBe(0);
    expect(result.isFinanciallyIndependent).toBe(true);
  });
});
```

## Best Practices

### 1. Test Organization

```typescript
// Good: Descriptive test structure
describe('SwissTaxEngine', () => {
  describe('Federal Tax Calculation', () => {
    describe('Single Taxpayer', () => {
      test('calculates federal tax for income below CHF 31,600', () => {
        // Test implementation
      });
    });
  });
});
```

### 2. Test Data Management

```typescript
// Good: Use test fixtures
const testScenarios = {
  youngProfessional: {
    age: 28,
    income: 85000,
    canton: 'ZH',
    civilStatus: 'single'
  },
  midCareerFamily: {
    age: 42,
    income: 120000,
    canton: 'VD',
    civilStatus: 'married',
    children: 2
  }
};
```

### 3. Assertion Patterns

```typescript
// Good: Specific assertions
expect(result.totalTax).toBeCloseTo(14733, 2); // Within 2 decimal places
expect(result.effectiveRate).toBeGreaterThan(0);
expect(result.effectiveRate).toBeLessThan(0.5);

// Good: Multiple related assertions
expect(result).toEqual(
  expect.objectContaining({
    federalTax: expect.any(Number),
    cantonalTax: expect.any(Number),
    totalTax: expect.any(Number)
  })
);
```

### 4. Error Testing

```typescript
// Good: Test error conditions
test('throws error for invalid input', () => {
  expect(() => {
    SwissTaxEngine.calculateTax({
      income: -1000, // Invalid negative income
      canton: 'ZH',
      civilStatus: 'single',
      year: 2024
    });
  }).toThrow('Income must be positive');
});
```

## Running Unit Tests

### Development Workflow

```bash
# Run all unit tests
npm run test:unit

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run specific test file
npm run test -- SwissTaxEngine.test.ts

# Run tests matching pattern
npm run test -- --grep "tax calculation"
```

### Coverage Analysis

```bash
# Generate detailed coverage report
npm run test:coverage

# View coverage in browser
open coverage/index.html
```

## Debugging Tests

### Common Issues and Solutions

1. **Floating Point Precision**: Use `toBeCloseTo()` for decimal comparisons
2. **Async Operations**: Ensure proper async/await usage
3. **Mock Issues**: Clear mocks between tests with `vi.clearAllMocks()`
4. **DOM Cleanup**: Use `cleanup()` from Testing Library

### Debug Configuration

```typescript
// vitest.config.ts - Debug mode
export default defineConfig({
  test: {
    // ... other config
    reporter: 'verbose',
    logHeapUsage: true,
    isolate: false, // For debugging only
  }
});
```

---

*This unit testing guide ensures comprehensive coverage of Swiss Budget Pro's critical financial calculations and components.*
