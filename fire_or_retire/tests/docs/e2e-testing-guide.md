# End-to-End Testing Guide

This guide covers end-to-end (E2E) testing for Swiss Budget Pro using Playwright, ensuring complete user workflows function correctly across different browsers and devices.

## Overview

E2E tests validate the entire application from the user's perspective, testing real user scenarios in actual browser environments. These tests are crucial for ensuring the Swiss Budget Pro application works seamlessly for end users.

```{admonition} 🎯 E2E Testing Goals
:class: tip

**Real User Scenarios**: Test complete user journeys
**Cross-Browser Compatibility**: Chrome, Firefox, Safari, Edge
**Mobile Responsiveness**: Test on mobile devices
**Performance Validation**: Ensure acceptable load times
**Accessibility Compliance**: WCAG 2.1 AA standards
**Swiss-Specific Workflows**: Focus on Swiss financial planning scenarios
```

## Framework Setup

### Playwright Configuration

```typescript
// playwright.config.ts
import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './tests/e2e',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: [
    ['html'],
    ['json', { outputFile: 'test-results/results.json' }],
    ['junit', { outputFile: 'test-results/results.xml' }]
  ],
  use: {
    baseURL: 'http://localhost:4173',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
    video: 'retain-on-failure'
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] },
    },
    {
      name: 'Mobile Safari',
      use: { ...devices['iPhone 12'] },
    },
  ],
  webServer: {
    command: 'npm run preview',
    port: 4173,
    reuseExistingServer: !process.env.CI,
  },
});
```

### Test Structure

```
tests/e2e/
├── fixtures/
│   ├── swiss-scenarios.ts      # Swiss financial test data
│   └── user-profiles.ts        # Different user personas
├── pages/
│   ├── base-page.ts           # Common page functionality
│   ├── dashboard-page.ts      # Main dashboard interactions
│   ├── budget-page.ts         # Budget planning page
│   └── tax-page.ts            # Swiss tax optimization page
├── tests/
│   ├── critical-path/         # Core user journeys
│   ├── swiss-features/        # Swiss-specific functionality
│   ├── accessibility/         # WCAG compliance tests
│   └── performance/           # Performance benchmarks
└── utils/
    ├── test-helpers.ts        # Common test utilities
    └── swiss-data-helpers.ts  # Swiss-specific test helpers
```

## Page Object Model

### Base Page Class

```typescript
// pages/base-page.ts
import { Page, Locator } from '@playwright/test';

export class BasePage {
  readonly page: Page;

  constructor(page: Page) {
    this.page = page;
  }

  async goto(path: string = '/') {
    await this.page.goto(path);
    await this.waitForPageLoad();
  }

  async waitForPageLoad() {
    await this.page.waitForLoadState('networkidle');
    await this.page.waitForSelector('[data-testid="app-loaded"]', { timeout: 10000 });
  }

  async takeScreenshot(name: string) {
    await this.page.screenshot({ 
      path: `test-results/screenshots/${name}.png`,
      fullPage: true 
    });
  }

  async fillNumberInput(selector: string, value: number) {
    await this.page.fill(selector, '');
    await this.page.fill(selector, value.toString());
    await this.page.waitForTimeout(500); // Allow for calculation updates
  }

  async selectCanton(canton: string) {
    await this.page.selectOption('[data-testid="canton-select"]', canton);
    await this.page.waitForTimeout(1000); // Allow for tax recalculation
  }
}
```

### Dashboard Page Object

```typescript
// pages/dashboard-page.ts
import { Page, Locator, expect } from '@playwright/test';
import { BasePage } from './base-page';

export class DashboardPage extends BasePage {
  readonly incomeInput: Locator;
  readonly expensesInput: Locator;
  readonly savingsInput: Locator;
  readonly fireResult: Locator;
  readonly savingsRateDisplay: Locator;
  readonly cantonSelect: Locator;

  constructor(page: Page) {
    super(page);
    this.incomeInput = page.locator('[data-testid="monthly-income"]');
    this.expensesInput = page.locator('[data-testid="monthly-expenses"]');
    this.savingsInput = page.locator('[data-testid="current-savings"]');
    this.fireResult = page.locator('[data-testid="fire-years"]');
    this.savingsRateDisplay = page.locator('[data-testid="savings-rate"]');
    this.cantonSelect = page.locator('[data-testid="canton-select"]');
  }

  async fillBasicFinancialInfo(data: {
    income: number;
    expenses: number;
    savings: number;
    canton?: string;
  }) {
    await this.fillNumberInput('[data-testid="monthly-income"]', data.income);
    await this.fillNumberInput('[data-testid="monthly-expenses"]', data.expenses);
    await this.fillNumberInput('[data-testid="current-savings"]', data.savings);
    
    if (data.canton) {
      await this.selectCanton(data.canton);
    }
  }

  async verifyFIRECalculation(expectedYears: number, tolerance: number = 1) {
    const fireYears = await this.fireResult.textContent();
    const years = parseFloat(fireYears?.replace(/[^\d.]/g, '') || '0');
    
    expect(years).toBeGreaterThan(expectedYears - tolerance);
    expect(years).toBeLessThan(expectedYears + tolerance);
  }

  async verifySavingsRate(expectedRate: number, tolerance: number = 0.05) {
    const rateText = await this.savingsRateDisplay.textContent();
    const rate = parseFloat(rateText?.replace(/[^\d.]/g, '') || '0') / 100;
    
    expect(rate).toBeGreaterThan(expectedRate - tolerance);
    expect(rate).toBeLessThan(expectedRate + tolerance);
  }

  async navigateToTab(tabName: string) {
    await this.page.click(`[data-testid="tab-${tabName}"]`);
    await this.page.waitForTimeout(500);
  }
}
```

## Test Scenarios

### Critical User Journeys

```typescript
// tests/critical-path/complete-fire-planning.spec.ts
import { test, expect } from '@playwright/test';
import { DashboardPage } from '../pages/dashboard-page';
import { swissTestScenarios } from '../fixtures/swiss-scenarios';

test.describe('Complete FIRE Planning Journey', () => {
  test('Young professional completes full FIRE setup', async ({ page }) => {
    const dashboard = new DashboardPage(page);
    const scenario = swissTestScenarios.youngProfessional;

    // Step 1: Navigate to application
    await dashboard.goto();
    await expect(page).toHaveTitle(/Swiss Budget Pro/);

    // Step 2: Fill basic financial information
    await dashboard.fillBasicFinancialInfo({
      income: scenario.monthlyIncome,
      expenses: scenario.monthlyExpenses,
      savings: scenario.currentSavings,
      canton: scenario.canton
    });

    // Step 3: Verify FIRE calculation
    await dashboard.verifyFIRECalculation(scenario.expectedFIREYears);
    await dashboard.verifySavingsRate(scenario.expectedSavingsRate);

    // Step 4: Navigate to tax optimization
    await dashboard.navigateToTab('tax-optimization');
    
    // Verify tax calculations appear
    await expect(page.locator('[data-testid="federal-tax"]')).toBeVisible();
    await expect(page.locator('[data-testid="cantonal-tax"]')).toBeVisible();

    // Step 5: Navigate to healthcare optimization
    await dashboard.navigateToTab('healthcare');
    
    // Fill health profile
    await page.selectOption('[data-testid="risk-profile"]', 'low');
    await page.fill('[data-testid="annual-medical-costs"]', '800');
    
    // Verify healthcare recommendations
    await expect(page.locator('[data-testid="recommended-deductible"]')).toBeVisible();
    await expect(page.locator('[data-testid="annual-savings"]')).toBeVisible();

    // Step 6: Save scenario
    await page.click('[data-testid="save-scenario"]');
    await page.fill('[data-testid="scenario-name"]', 'Young Professional Plan');
    await page.click('[data-testid="confirm-save"]');
    
    // Verify save success
    await expect(page.locator('[data-testid="save-success"]')).toBeVisible();
  });

  test('Mid-career family optimizes for early retirement', async ({ page }) => {
    const dashboard = new DashboardPage(page);
    const scenario = swissTestScenarios.midCareerFamily;

    await dashboard.goto();

    // Fill family financial data
    await dashboard.fillBasicFinancialInfo({
      income: scenario.monthlyIncome,
      expenses: scenario.monthlyExpenses,
      savings: scenario.currentSavings,
      canton: scenario.canton
    });

    // Add family-specific information
    await page.fill('[data-testid="children-count"]', scenario.children.toString());
    await page.selectOption('[data-testid="civil-status"]', 'married');

    // Verify family tax calculations
    await dashboard.navigateToTab('tax-optimization');
    const familyTax = await page.locator('[data-testid="total-tax"]').textContent();
    expect(parseFloat(familyTax?.replace(/[^\d.]/g, '') || '0')).toBeGreaterThan(0);

    // Test cantonal comparison
    await page.click('[data-testid="compare-cantons"]');
    await expect(page.locator('[data-testid="canton-comparison-table"]')).toBeVisible();
    
    // Verify multiple cantons are compared
    const cantonRows = page.locator('[data-testid="canton-row"]');
    await expect(cantonRows).toHaveCount(26); // All Swiss cantons
  });
});
```

### Swiss-Specific Feature Tests

```typescript
// tests/swiss-features/tax-optimization.spec.ts
import { test, expect } from '@playwright/test';
import { DashboardPage } from '../pages/dashboard-page';

test.describe('Swiss Tax Optimization', () => {
  test('calculates accurate tax for all major cantons', async ({ page }) => {
    const dashboard = new DashboardPage(page);
    await dashboard.goto();

    const majorCantons = ['ZH', 'BE', 'VD', 'AG', 'SG', 'LU', 'TI', 'BS', 'GE'];
    const testIncome = 100000;

    for (const canton of majorCantons) {
      await dashboard.fillBasicFinancialInfo({
        income: testIncome / 12, // Monthly income
        expenses: 4000,
        savings: 50000,
        canton
      });

      await dashboard.navigateToTab('tax-optimization');

      // Verify tax calculations appear and are reasonable
      const federalTax = await page.locator('[data-testid="federal-tax"]').textContent();
      const cantonalTax = await page.locator('[data-testid="cantonal-tax"]').textContent();
      const totalTax = await page.locator('[data-testid="total-tax"]').textContent();

      const federal = parseFloat(federalTax?.replace(/[^\d.]/g, '') || '0');
      const cantonal = parseFloat(cantonalTax?.replace(/[^\d.]/g, '') || '0');
      const total = parseFloat(totalTax?.replace(/[^\d.]/g, '') || '0');

      // Sanity checks for tax calculations
      expect(federal).toBeGreaterThan(0);
      expect(cantonal).toBeGreaterThan(0);
      expect(total).toBeCloseTo(federal + cantonal, 100); // Allow for rounding
      expect(total).toBeLessThan(testIncome * 0.4); // Max 40% effective rate
    }
  });

  test('Pillar 3a optimization recommendations', async ({ page }) => {
    const dashboard = new DashboardPage(page);
    await dashboard.goto();

    // Set up high-income scenario
    await dashboard.fillBasicFinancialInfo({
      income: 12000, // CHF 144,000 annually
      expenses: 8000,
      savings: 200000,
      canton: 'ZH'
    });

    await dashboard.navigateToTab('pillar-3a');

    // Verify Pillar 3a recommendations
    await expect(page.locator('[data-testid="pillar-3a-limit"]')).toContainText('35,280');
    await expect(page.locator('[data-testid="tax-savings"]')).toBeVisible();
    
    // Test contribution optimization
    await page.fill('[data-testid="current-contribution"]', '20000');
    await page.click('[data-testid="optimize-contribution"]');
    
    const recommendation = await page.locator('[data-testid="optimal-contribution"]').textContent();
    expect(parseFloat(recommendation?.replace(/[^\d.]/g, '') || '0')).toBeGreaterThan(20000);
  });
});
```

### Performance Testing

```typescript
// tests/performance/load-times.spec.ts
import { test, expect } from '@playwright/test';

test.describe('Performance Benchmarks', () => {
  test('initial page load performance', async ({ page }) => {
    const startTime = Date.now();
    
    await page.goto('/');
    await page.waitForSelector('[data-testid="app-loaded"]');
    
    const loadTime = Date.now() - startTime;
    expect(loadTime).toBeLessThan(2000); // 2 second target
  });

  test('calculation performance', async ({ page }) => {
    await page.goto('/');
    
    const startTime = performance.now();
    
    // Trigger complex calculation
    await page.fill('[data-testid="monthly-income"]', '10000');
    await page.fill('[data-testid="monthly-expenses"]', '6000');
    await page.selectOption('[data-testid="canton-select"]', 'ZH');
    
    // Wait for all calculations to complete
    await page.waitForSelector('[data-testid="fire-years"]');
    await page.waitForSelector('[data-testid="total-tax"]');
    
    const endTime = performance.now();
    const calculationTime = endTime - startTime;
    
    expect(calculationTime).toBeLessThan(100); // 100ms target
  });

  test('chart rendering performance', async ({ page }) => {
    await page.goto('/');
    
    // Set up data for charts
    await page.fill('[data-testid="monthly-income"]', '8000');
    await page.fill('[data-testid="monthly-expenses"]', '5000');
    
    // Navigate to visualizations
    await page.click('[data-testid="tab-visualizations"]');
    
    const startTime = performance.now();
    
    // Wait for charts to render
    await page.waitForSelector('[data-testid="fire-projection-chart"]');
    await page.waitForSelector('[data-testid="savings-growth-chart"]');
    
    const endTime = performance.now();
    const renderTime = endTime - startTime;
    
    expect(renderTime).toBeLessThan(500); // 500ms target
  });
});
```

### Accessibility Testing

```typescript
// tests/accessibility/wcag-compliance.spec.ts
import { test, expect } from '@playwright/test';
import AxeBuilder from '@axe-core/playwright';

test.describe('Accessibility Compliance', () => {
  test('homepage meets WCAG 2.1 AA standards', async ({ page }) => {
    await page.goto('/');
    
    const accessibilityScanResults = await new AxeBuilder({ page })
      .withTags(['wcag2a', 'wcag2aa'])
      .analyze();
    
    expect(accessibilityScanResults.violations).toEqual([]);
  });

  test('keyboard navigation works correctly', async ({ page }) => {
    await page.goto('/');
    
    // Test tab navigation through form elements
    await page.keyboard.press('Tab');
    await expect(page.locator('[data-testid="monthly-income"]')).toBeFocused();
    
    await page.keyboard.press('Tab');
    await expect(page.locator('[data-testid="monthly-expenses"]')).toBeFocused();
    
    await page.keyboard.press('Tab');
    await expect(page.locator('[data-testid="current-savings"]')).toBeFocused();
    
    // Test form submission with Enter key
    await page.keyboard.press('Enter');
    await expect(page.locator('[data-testid="fire-years"]')).toBeVisible();
  });

  test('screen reader compatibility', async ({ page }) => {
    await page.goto('/');
    
    // Check for proper ARIA labels
    await expect(page.locator('[data-testid="monthly-income"]')).toHaveAttribute('aria-label');
    await expect(page.locator('[data-testid="fire-years"]')).toHaveAttribute('aria-live', 'polite');
    
    // Check for semantic HTML structure
    await expect(page.locator('main')).toBeVisible();
    await expect(page.locator('h1')).toBeVisible();
    await expect(page.locator('form')).toBeVisible();
  });
});
```

## Test Data Management

### Swiss Test Scenarios

```typescript
// fixtures/swiss-scenarios.ts
export const swissTestScenarios = {
  youngProfessional: {
    monthlyIncome: 7000,
    monthlyExpenses: 4500,
    currentSavings: 25000,
    canton: 'ZH',
    age: 28,
    expectedFIREYears: 15,
    expectedSavingsRate: 0.36
  },
  
  midCareerFamily: {
    monthlyIncome: 12000,
    monthlyExpenses: 8500,
    currentSavings: 150000,
    canton: 'VD',
    age: 42,
    children: 2,
    civilStatus: 'married',
    expectedFIREYears: 12,
    expectedSavingsRate: 0.29
  },
  
  preRetirement: {
    monthlyIncome: 15000,
    monthlyExpenses: 9000,
    currentSavings: 800000,
    canton: 'GE',
    age: 58,
    expectedFIREYears: 3,
    expectedSavingsRate: 0.40
  }
};
```

## Running E2E Tests

### Development Commands

```bash
# Run all E2E tests
npm run test:e2e

# Run tests in headed mode (see browser)
npm run test:e2e:headed

# Run tests in debug mode
npm run test:e2e:debug

# Run tests in UI mode
npm run test:e2e:ui

# Run specific test file
npx playwright test critical-path/complete-fire-planning.spec.ts

# Run tests for specific browser
npx playwright test --project=chromium
```

### CI/CD Integration

```yaml
# .github/workflows/e2e-tests.yml
name: E2E Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
      - run: npm ci
      - run: npx playwright install --with-deps
      - run: npm run build
      - run: npm run test:e2e
      - uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: playwright-report
          path: playwright-report/
```

## Best Practices

### 1. Test Isolation
- Each test should be independent
- Clean up data between tests
- Use unique test data identifiers

### 2. Reliable Selectors
- Use `data-testid` attributes
- Avoid CSS selectors that might change
- Use semantic selectors when possible

### 3. Wait Strategies
- Use `waitForSelector()` for dynamic content
- Use `waitForLoadState()` for page loads
- Avoid fixed `waitForTimeout()` when possible

### 4. Error Handling
- Take screenshots on failure
- Record videos for debugging
- Use retry mechanisms for flaky tests

---

*This E2E testing guide ensures comprehensive coverage of Swiss Budget Pro's user workflows and Swiss-specific functionality.*
