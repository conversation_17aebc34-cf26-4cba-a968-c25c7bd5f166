/**
 * E2E Performance Tests for Swiss Budget Pro
 * Tests application performance, load times, and resource usage
 */

import { test, expect } from '@playwright/test';
import { SwissBudgetProPage } from '../../page-objects/pages/SwissBudgetProPage';

test.describe('Load Performance Testing', () => {
  test('E2E-PERF-001: Page Load Performance Benchmarks', async ({ page }) => {
    const swissBudgetPage = new SwissBudgetProPage(page);

    test.step('Measure initial page load performance', async () => {
      // Start performance measurement
      const startTime = Date.now();
      
      // Navigate to application
      await page.goto('/', { waitUntil: 'networkidle' });
      
      const loadTime = Date.now() - startTime;
      console.log(`Initial page load time: ${loadTime}ms`);
      
      // Page should load within 3 seconds
      expect(loadTime).toBeLessThan(3000);
    });

    test.step('Measure Core Web Vitals', async () => {
      await swissBudgetPage.goto();
      
      // Measure Core Web Vitals using Performance API
      const webVitals = await page.evaluate(() => {
        return new Promise((resolve) => {
          const vitals: any = {};
          
          // Largest Contentful Paint (LCP)
          new PerformanceObserver((list) => {
            const entries = list.getEntries();
            const lastEntry = entries[entries.length - 1];
            vitals.lcp = lastEntry.startTime;
          }).observe({ entryTypes: ['largest-contentful-paint'] });
          
          // First Input Delay (FID) - simulated
          new PerformanceObserver((list) => {
            const entries = list.getEntries();
            if (entries.length > 0) {
              vitals.fid = entries[0].processingStart - entries[0].startTime;
            }
          }).observe({ entryTypes: ['first-input'] });
          
          // Cumulative Layout Shift (CLS)
          let clsValue = 0;
          new PerformanceObserver((list) => {
            for (const entry of list.getEntries()) {
              if (!(entry as any).hadRecentInput) {
                clsValue += (entry as any).value;
              }
            }
            vitals.cls = clsValue;
          }).observe({ entryTypes: ['layout-shift'] });
          
          // First Contentful Paint (FCP)
          new PerformanceObserver((list) => {
            const entries = list.getEntries();
            if (entries.length > 0) {
              vitals.fcp = entries[0].startTime;
            }
          }).observe({ entryTypes: ['paint'] });
          
          // Time to Interactive (TTI) - approximated
          setTimeout(() => {
            vitals.tti = performance.now();
            resolve(vitals);
          }, 2000);
        });
      });

      console.log('Core Web Vitals:', webVitals);
      
      // Verify Core Web Vitals meet good thresholds
      if ((webVitals as any).lcp) {
        expect((webVitals as any).lcp).toBeLessThan(2500); // LCP < 2.5s
      }
      if ((webVitals as any).fid) {
        expect((webVitals as any).fid).toBeLessThan(100); // FID < 100ms
      }
      if ((webVitals as any).cls) {
        expect((webVitals as any).cls).toBeLessThan(0.1); // CLS < 0.1
      }
    });

    test.step('Measure resource loading performance', async () => {
      await swissBudgetPage.goto();
      
      // Get resource timing information
      const resourceTimings = await page.evaluate(() => {
        const resources = performance.getEntriesByType('resource');
        return resources.map(resource => ({
          name: resource.name,
          duration: resource.duration,
          size: (resource as any).transferSize || 0,
          type: resource.name.split('.').pop() || 'unknown',
        }));
      });

      console.log(`Loaded ${resourceTimings.length} resources`);
      
      // Analyze resource performance
      const slowResources = resourceTimings.filter(r => r.duration > 1000);
      const largeResources = resourceTimings.filter(r => r.size > 500000); // > 500KB
      
      console.log(`Slow resources (>1s): ${slowResources.length}`);
      console.log(`Large resources (>500KB): ${largeResources.length}`);
      
      if (slowResources.length > 0) {
        console.log('Slow resources:', slowResources);
      }
      
      // Should not have too many slow or large resources
      expect(slowResources.length).toBeLessThan(5);
      expect(largeResources.length).toBeLessThan(3);
    });
  });

  test('E2E-PERF-002: Calculation Performance Benchmarks', async ({ page }) => {
    const swissBudgetPage = new SwissBudgetProPage(page);

    test.step('Measure FIRE calculation performance', async () => {
      await swissBudgetPage.goto();
      await swissBudgetPage.verifyPageLoaded();

      // Fill form with complex scenario
      await swissBudgetPage.fillBasicFinancialInfo({
        monthlyIncome: 12000,
        monthlyExpenses: 7500,
        currentSavings: 350000,
        canton: 'ZH',
        age: 35,
        retirementAge: 55,
      });

      // Measure calculation time
      const calcStartTime = Date.now();
      await swissBudgetPage.calculateFIRE();
      const calcTime = Date.now() - calcStartTime;

      console.log(`FIRE calculation time: ${calcTime}ms`);
      
      // FIRE calculation should complete within 500ms
      expect(calcTime).toBeLessThan(500);
      
      // Verify results are accurate
      const results = await swissBudgetPage.getFIREResults();
      expect(results.fireYears).toBeGreaterThan(0);
    });

    test.step('Measure tax calculation performance', async () => {
      await swissBudgetPage.navigateToTab('analysis');
      
      const taxCalcStartTime = Date.now();
      
      // Wait for tax optimization section to load
      await expect(swissBudgetPage.taxOptimizationSection).toBeVisible();
      
      const taxCalcTime = Date.now() - taxCalcStartTime;
      console.log(`Tax calculation time: ${taxCalcTime}ms`);
      
      // Tax calculations should be fast
      expect(taxCalcTime).toBeLessThan(300);
    });

    test.step('Measure healthcare optimization performance', async () => {
      const healthcareStartTime = Date.now();
      
      // Wait for healthcare section to load
      await expect(swissBudgetPage.healthcareSection).toBeVisible();
      
      const healthcareTime = Date.now() - healthcareStartTime;
      console.log(`Healthcare optimization time: ${healthcareTime}ms`);
      
      // Healthcare optimization should be fast
      expect(healthcareTime).toBeLessThan(200);
    });
  });

  test('E2E-PERF-003: Memory Usage and Leak Detection', async ({ page }) => {
    const swissBudgetPage = new SwissBudgetProPage(page);

    test.step('Monitor memory usage during normal operation', async () => {
      await swissBudgetPage.goto();
      
      // Get initial memory usage
      const initialMemory = await page.evaluate(() => {
        return (performance as any).memory ? {
          used: (performance as any).memory.usedJSHeapSize,
          total: (performance as any).memory.totalJSHeapSize,
          limit: (performance as any).memory.jsHeapSizeLimit,
        } : null;
      });

      if (initialMemory) {
        console.log('Initial memory usage:', {
          used: `${(initialMemory.used / 1024 / 1024).toFixed(2)}MB`,
          total: `${(initialMemory.total / 1024 / 1024).toFixed(2)}MB`,
          percentage: `${((initialMemory.used / initialMemory.limit) * 100).toFixed(1)}%`,
        });
      }

      // Perform multiple calculations to test memory stability
      const testScenarios = [
        { income: 5000, expenses: 3500, savings: 50000, canton: 'ZH' },
        { income: 8000, expenses: 5500, savings: 150000, canton: 'GE' },
        { income: 12000, expenses: 7000, savings: 300000, canton: 'VD' },
        { income: 15000, expenses: 9000, savings: 500000, canton: 'ZG' },
        { income: 20000, expenses: 12000, savings: 800000, canton: 'BS' },
      ];

      for (const scenario of testScenarios) {
        await swissBudgetPage.fillBasicFinancialInfo({
          monthlyIncome: scenario.income,
          monthlyExpenses: scenario.expenses,
          currentSavings: scenario.savings,
          canton: scenario.canton,
        });

        await swissBudgetPage.calculateFIRE();
        
        // Brief pause between calculations
        await page.waitForTimeout(100);
      }

      // Get final memory usage
      const finalMemory = await page.evaluate(() => {
        return (performance as any).memory ? {
          used: (performance as any).memory.usedJSHeapSize,
          total: (performance as any).memory.totalJSHeapSize,
          limit: (performance as any).memory.jsHeapSizeLimit,
        } : null;
      });

      if (initialMemory && finalMemory) {
        const memoryIncrease = finalMemory.used - initialMemory.used;
        const memoryIncreasePercent = (memoryIncrease / initialMemory.used) * 100;
        
        console.log('Final memory usage:', {
          used: `${(finalMemory.used / 1024 / 1024).toFixed(2)}MB`,
          increase: `${(memoryIncrease / 1024 / 1024).toFixed(2)}MB`,
          increasePercent: `${memoryIncreasePercent.toFixed(1)}%`,
        });

        // Memory increase should be reasonable (< 50% increase)
        expect(memoryIncreasePercent).toBeLessThan(50);
      }
    });

    test.step('Test for memory leaks during navigation', async () => {
      // Navigate between tabs multiple times
      const navigationCycles = 10;
      
      for (let i = 0; i < navigationCycles; i++) {
        await swissBudgetPage.navigateToTab('input');
        await page.waitForTimeout(100);
        
        await swissBudgetPage.navigateToTab('analysis');
        await page.waitForTimeout(100);
        
        await swissBudgetPage.navigateToTab('visualization');
        await page.waitForTimeout(100);
        
        await swissBudgetPage.navigateToTab('reports');
        await page.waitForTimeout(100);
      }

      // Check for excessive DOM nodes
      const domNodeCount = await page.evaluate(() => {
        return document.querySelectorAll('*').length;
      });

      console.log(`DOM node count after navigation: ${domNodeCount}`);
      
      // Should not have excessive DOM nodes (< 5000 for a financial app)
      expect(domNodeCount).toBeLessThan(5000);
    });
  });

  test('E2E-PERF-004: Network Performance and Optimization', async ({ page }) => {
    const swissBudgetPage = new SwissBudgetProPage(page);

    test.step('Test performance on slow network', async () => {
      // Simulate slow 3G network
      await page.route('**/*', async (route) => {
        await new Promise(resolve => setTimeout(resolve, 100)); // Add 100ms delay
        await route.continue();
      });

      const slowNetworkStartTime = Date.now();
      await swissBudgetPage.goto();
      const slowNetworkLoadTime = Date.now() - slowNetworkStartTime;

      console.log(`Load time on slow network: ${slowNetworkLoadTime}ms`);
      
      // Should still be usable on slow network (< 10 seconds)
      expect(slowNetworkLoadTime).toBeLessThan(10000);
      
      // Verify core functionality works
      await swissBudgetPage.fillBasicFinancialInfo({
        monthlyIncome: 7000,
        monthlyExpenses: 4500,
        currentSavings: 100000,
        canton: 'BE',
      });

      await swissBudgetPage.calculateFIRE();
      const results = await swissBudgetPage.getFIREResults();
      expect(results.fireYears).toBeGreaterThan(0);
    });

    test.step('Test caching and resource optimization', async () => {
      // First visit
      await swissBudgetPage.goto();
      
      const firstVisitResources = await page.evaluate(() => {
        const resources = performance.getEntriesByType('resource');
        return resources.length;
      });

      // Reload page to test caching
      await page.reload({ waitUntil: 'networkidle' });
      
      const secondVisitResources = await page.evaluate(() => {
        const resources = performance.getEntriesByType('resource');
        const cachedResources = resources.filter(r => 
          (r as any).transferSize === 0 || (r as any).transferSize < (r as any).decodedBodySize,
        );
        return {
          total: resources.length,
          cached: cachedResources.length,
          cacheRatio: cachedResources.length / resources.length,
        };
      });

      console.log('Resource caching:', secondVisitResources);
      
      // Should have reasonable cache hit ratio (> 50%)
      expect(secondVisitResources.cacheRatio).toBeGreaterThan(0.5);
    });
  });

  test('E2E-PERF-005: Rendering Performance', async ({ page }) => {
    const swissBudgetPage = new SwissBudgetProPage(page);

    test.step('Measure rendering performance', async () => {
      await swissBudgetPage.goto();
      
      // Measure frame rate during interactions
      const frameRateData = await page.evaluate(() => {
        return new Promise((resolve) => {
          const frames: number[] = [];
          let lastTime = performance.now();
          
          function measureFrame() {
            const currentTime = performance.now();
            const frameDuration = currentTime - lastTime;
            frames.push(1000 / frameDuration); // FPS
            lastTime = currentTime;
            
            if (frames.length < 60) { // Measure for ~1 second
              requestAnimationFrame(measureFrame);
            } else {
              const avgFPS = frames.reduce((a, b) => a + b, 0) / frames.length;
              const minFPS = Math.min(...frames);
              resolve({ avgFPS, minFPS, frames: frames.length });
            }
          }
          
          requestAnimationFrame(measureFrame);
        });
      });

      console.log('Rendering performance:', frameRateData);
      
      // Should maintain good frame rate (> 30 FPS average)
      expect((frameRateData as any).avgFPS).toBeGreaterThan(30);
    });

    test.step('Test smooth scrolling performance', async () => {
      // Fill page with content
      await swissBudgetPage.fillBasicFinancialInfo({
        monthlyIncome: 10000,
        monthlyExpenses: 6500,
        currentSavings: 250000,
        canton: 'GE',
      });

      await swissBudgetPage.calculateFIRE();
      await swissBudgetPage.navigateToTab('analysis');

      // Measure scroll performance
      const scrollStartTime = Date.now();
      
      // Perform smooth scroll
      await page.evaluate(() => {
        window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' });
      });
      
      // Wait for scroll to complete
      await page.waitForFunction(() => {
        return window.scrollY > document.body.scrollHeight - window.innerHeight - 100;
      });
      
      const scrollTime = Date.now() - scrollStartTime;
      console.log(`Smooth scroll time: ${scrollTime}ms`);
      
      // Smooth scroll should complete quickly
      expect(scrollTime).toBeLessThan(2000);
    });
  });

  test('E2E-PERF-006: Bundle Size and Asset Optimization', async ({ page }) => {
    test.step('Analyze bundle size and compression', async () => {
      const swissBudgetPage = new SwissBudgetProPage(page);
      await swissBudgetPage.goto();
      
      // Analyze loaded resources
      const resourceAnalysis = await page.evaluate(() => {
        const resources = performance.getEntriesByType('resource');
        const analysis = {
          totalSize: 0,
          compressedSize: 0,
          jsSize: 0,
          cssSize: 0,
          imageSize: 0,
          fontSize: 0,
          resources: [] as any[],
        };
        
        resources.forEach(resource => {
          const size = (resource as any).transferSize || 0;
          const decodedSize = (resource as any).decodedBodySize || 0;
          const name = resource.name;
          const type = name.split('.').pop()?.toLowerCase() || 'unknown';
          
          analysis.totalSize += decodedSize;
          analysis.compressedSize += size;
          
          switch (type) {
            case 'js':
              analysis.jsSize += size;
              break;
            case 'css':
              analysis.cssSize += size;
              break;
            case 'png':
            case 'jpg':
            case 'jpeg':
            case 'gif':
            case 'svg':
            case 'webp':
              analysis.imageSize += size;
              break;
            case 'woff':
            case 'woff2':
            case 'ttf':
            case 'otf':
              analysis.fontSize += size;
              break;
          }
          
          analysis.resources.push({
            name: name.split('/').pop(),
            type,
            size,
            decodedSize,
            compressionRatio: decodedSize > 0 ? size / decodedSize : 1,
          });
        });
        
        return analysis;
      });

      console.log('Bundle analysis:', {
        totalCompressed: `${(resourceAnalysis.compressedSize / 1024 / 1024).toFixed(2)}MB`,
        totalUncompressed: `${(resourceAnalysis.totalSize / 1024 / 1024).toFixed(2)}MB`,
        compressionRatio: `${((1 - resourceAnalysis.compressedSize / resourceAnalysis.totalSize) * 100).toFixed(1)}%`,
        jsSize: `${(resourceAnalysis.jsSize / 1024).toFixed(0)}KB`,
        cssSize: `${(resourceAnalysis.cssSize / 1024).toFixed(0)}KB`,
        imageSize: `${(resourceAnalysis.imageSize / 1024).toFixed(0)}KB`,
        fontSize: `${(resourceAnalysis.fontSize / 1024).toFixed(0)}KB`,
      });

      // Bundle size should be reasonable for a financial app
      expect(resourceAnalysis.compressedSize).toBeLessThan(5 * 1024 * 1024); // < 5MB total
      expect(resourceAnalysis.jsSize).toBeLessThan(2 * 1024 * 1024); // < 2MB JS
      expect(resourceAnalysis.cssSize).toBeLessThan(500 * 1024); // < 500KB CSS
    });
  });
});
