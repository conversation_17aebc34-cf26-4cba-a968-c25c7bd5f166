import { test, expect } from '@playwright/test';

test.describe('Premium Features Integration E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    
    // Set up comprehensive user profile for premium features
    await page.fill('[data-testid="monthly-income"]', '12000');
    await page.fill('[data-testid="current-age"]', '40');
    await page.fill('[data-testid="current-savings"]', '500000');
    await page.fill('[data-testid="monthly-expenses"]', '6000');
    await page.selectOption('[data-testid="canton-select"]', 'ZH');
    await page.selectOption('[data-testid="marital-status"]', 'married');
    
    // Wait for initial calculations
    await page.waitForTimeout(2000);
  });

  test('should integrate AI Financial Advisor with Tax Planning', async ({ page }) => {
    // Navigate to AI Financial Advisor
    await page.click('[data-testid="analysis-tab"]');
    await page.click('[data-testid="ai-advisor-tab"]');
    await page.waitForSelector('[data-testid="ai-advisor-dashboard"]');
    
    // Verify AI analysis includes tax considerations
    await expect(page.locator('[data-testid="ai-insights"]')).toContainText('tax');
    
    // Navigate to Tax Planning
    await page.click('[data-testid="tax-optimization-tab"]');
    await page.waitForSelector('[data-testid="swiss-tax-dashboard"]');
    
    // Verify data consistency between features
    const aiIncome = await page.locator('[data-testid="ai-income-analysis"]').textContent();
    const taxIncome = await page.locator('[data-testid="gross-annual-income"]').textContent();
    
    // Both should reference the same annual income (144,000)
    expect(aiIncome).toContain('144,000');
    expect(taxIncome).toContain('144,000');
  });

  test('should integrate Risk Assessment with Tax Optimization', async ({ page }) => {
    // Access Risk Assessment
    await page.click('[data-testid="analysis-tab"]');
    await page.click('[data-testid="risk-assessment-tab"]');
    await page.waitForSelector('[data-testid="risk-metrics"]');
    
    // Verify tax-related risk factors
    await expect(page.locator('[data-testid="tax-risk-factor"]')).toBeVisible();
    
    // Navigate to Tax Planning and verify risk considerations
    await page.click('[data-testid="tax-optimization-tab"]');
    await page.waitForSelector('[data-testid="optimization-strategies"]');
    
    // Verify risk levels are consistent
    const riskStrategies = page.locator('[data-testid="strategy-item"][data-risk="High"]');
    await expect(riskStrategies).toHaveCountGreaterThan(0);
  });

  test('should integrate Safe Withdrawal Rate with Tax Planning', async ({ page }) => {
    // Access Safe Withdrawal Rate Analysis
    await page.click('[data-testid="analysis-tab"]');
    await page.click('[data-testid="withdrawal-rate-tab"]');
    await page.waitForSelector('[data-testid="withdrawal-analysis"]');
    
    // Get withdrawal rate recommendation
    const withdrawalRate = await page.locator('[data-testid="recommended-rate"]').textContent();
    
    // Navigate to Tax Planning
    await page.click('[data-testid="tax-optimization-tab"]');
    await page.click('[data-testid="scenarios-tab"]');
    
    // Verify tax scenarios consider withdrawal implications
    await expect(page.locator('[data-testid="retirement-tax-scenario"]')).toBeVisible();
  });

  test('should integrate Historical Tracking with Tax Data', async ({ page }) => {
    // Access Historical Tracking
    await page.click('[data-testid="analysis-tab"]');
    await page.click('[data-testid="historical-tracking-tab"]');
    await page.waitForSelector('[data-testid="historical-charts"]');
    
    // Verify tax-related metrics are available
    await expect(page.locator('[data-testid="tax-burden-metric"]')).toBeVisible();
    await expect(page.locator('[data-testid="net-income-metric"]')).toBeVisible();
    
    // Generate sample data and verify tax calculations
    await page.click('[data-testid="generate-sample-data"]');
    await page.waitForSelector('[data-testid="chart-container"]');
    
    // Verify tax data is included in historical tracking
    await expect(page.locator('[data-testid="tax-data-points"]')).toHaveCountGreaterThan(0);
  });

  test('should integrate Canton Relocation with Tax Optimization', async ({ page }) => {
    // Access Canton Relocation Analysis
    await page.click('[data-testid="analysis-tab"]');
    await page.click('[data-testid="canton-relocation-tab"]');
    await page.waitForSelector('[data-testid="canton-analysis"]');
    
    // Get best canton recommendation
    const bestCanton = await page.locator('[data-testid="best-canton"]').textContent();
    
    // Navigate to Tax Planning
    await page.click('[data-testid="tax-optimization-tab"]');
    await page.waitForSelector('[data-testid="canton-comparison"]');
    
    // Verify same canton appears as best in tax comparison
    const taxBestCanton = await page.locator('[data-testid="canton-row"]').first().textContent();
    expect(taxBestCanton).toContain(bestCanton);
  });

  test('should integrate Healthcare Cost Optimizer with Tax Planning', async ({ page }) => {
    // Access Healthcare Cost Optimizer
    await page.click('[data-testid="analysis-tab"]');
    await page.click('[data-testid="healthcare-optimizer-tab"]');
    await page.waitForSelector('[data-testid="healthcare-analysis"]');
    
    // Get healthcare cost recommendations
    const healthcareCosts = await page.locator('[data-testid="annual-healthcare-costs"]').textContent();
    
    // Navigate to Tax Planning deductions
    await page.click('[data-testid="tax-optimization-tab"]');
    await page.click('[data-testid="deductions-tab"]');
    await page.click('[data-testid="insurance-tab"]');
    
    // Verify healthcare costs are reflected in insurance deductions
    await expect(page.locator('[data-testid="insurance-premiums"]')).toContainText('CHF');
  });

  test('should maintain data consistency across all premium features', async ({ page }) => {
    const features = [
      'ai-advisor-tab',
      'risk-assessment-tab',
      'withdrawal-rate-tab',
      'historical-tracking-tab',
      'canton-relocation-tab',
      'healthcare-optimizer-tab',
      'tax-optimization-tab',
    ];
    
    // Navigate through all features and collect income data
    const incomeData = [];
    
    for (const feature of features) {
      await page.click('[data-testid="analysis-tab"]');
      await page.click(`[data-testid="${feature}"]`);
      await page.waitForTimeout(1000);
      
      // Try to find income reference in each feature
      const incomeElement = page.locator('[data-testid*="income"], [data-testid*="salary"]').first();
      if (await incomeElement.isVisible()) {
        const incomeText = await incomeElement.textContent();
        incomeData.push(incomeText);
      }
    }
    
    // Verify all features reference consistent income data
    const expectedIncome = '144,000'; // 12,000 * 12
    incomeData.forEach(income => {
      expect(income).toContain(expectedIncome);
    });
  });

  test('should handle premium feature navigation smoothly', async ({ page }) => {
    await page.click('[data-testid="analysis-tab"]');
    
    // Test rapid navigation between premium features
    const features = [
      'ai-advisor-tab',
      'tax-optimization-tab',
      'risk-assessment-tab',
      'withdrawal-rate-tab',
      'canton-relocation-tab',
    ];
    
    for (const feature of features) {
      const startTime = Date.now();
      
      await page.click(`[data-testid="${feature}"]`);
      await page.waitForSelector(`[data-testid="${feature.replace('-tab', '-dashboard')}, [data-testid="${feature.replace('-tab', '-analysis')}"]`);
      
      const loadTime = Date.now() - startTime;
      expect(loadTime).toBeLessThan(3000); // Should load within 3 seconds
    }
  });

  test('should display premium features with proper Swiss formatting', async ({ page }) => {
    const features = [
      'ai-advisor-tab',
      'tax-optimization-tab',
      'risk-assessment-tab',
      'canton-relocation-tab',
      'healthcare-optimizer-tab',
    ];
    
    for (const feature of features) {
      await page.click('[data-testid="analysis-tab"]');
      await page.click(`[data-testid="${feature}"]`);
      await page.waitForTimeout(1000);
      
      // Verify CHF formatting
      const chfElements = page.locator('text=/CHF [0-9,]+/');
      if (await chfElements.count() > 0) {
        await expect(chfElements.first()).toBeVisible();
      }
      
      // Verify percentage formatting
      const percentElements = page.locator('text=/[0-9.]+%/');
      if (await percentElements.count() > 0) {
        await expect(percentElements.first()).toBeVisible();
      }
    }
  });

  test('should work correctly in mobile view for all premium features', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });
    
    const features = [
      'ai-advisor-tab',
      'tax-optimization-tab',
      'risk-assessment-tab',
      'withdrawal-rate-tab',
      'historical-tracking-tab',
    ];
    
    for (const feature of features) {
      await page.click('[data-testid="analysis-tab"]');
      await page.click(`[data-testid="${feature}"]`);
      await page.waitForTimeout(1000);
      
      // Verify feature is accessible on mobile
      const featureContent = page.locator(`[data-testid="${feature.replace('-tab', '-dashboard')}, [data-testid="${feature.replace('-tab', '-analysis')}"]`);
      await expect(featureContent).toBeVisible();
      
      // Verify touch-friendly interface
      const buttons = page.locator('button');
      if (await buttons.count() > 0) {
        const buttonSize = await buttons.first().boundingBox();
        expect(buttonSize?.height).toBeGreaterThan(40); // Minimum touch target
      }
    }
  });

  test('should maintain dark mode consistency across premium features', async ({ page }) => {
    // Enable dark mode
    await page.click('[data-testid="dark-mode-toggle"]');
    
    const features = [
      'ai-advisor-tab',
      'tax-optimization-tab',
      'risk-assessment-tab',
      'canton-relocation-tab',
    ];
    
    for (const feature of features) {
      await page.click('[data-testid="analysis-tab"]');
      await page.click(`[data-testid="${feature}"]`);
      await page.waitForTimeout(1000);
      
      // Verify dark mode styling
      const mainContainer = page.locator('[data-testid*="dashboard"], [data-testid*="analysis"]').first();
      await expect(mainContainer).toHaveClass(/bg-gray-800/);
      
      // Verify text readability in dark mode
      const headings = page.locator('h3, h4');
      if (await headings.count() > 0) {
        await expect(headings.first()).toHaveClass(/text-white/);
      }
    }
  });

  test('should handle complex calculations across integrated features', async ({ page }) => {
    // Set up complex scenario
    await page.fill('[data-testid="monthly-income"]', '25000');
    await page.fill('[data-testid="current-savings"]', '2000000');
    await page.selectOption('[data-testid="canton-select"]', 'GE');
    
    // Test AI Financial Advisor with complex data
    await page.click('[data-testid="analysis-tab"]');
    await page.click('[data-testid="ai-advisor-tab"]');
    await page.waitForSelector('[data-testid="ai-analysis-complete"]', { timeout: 10000 });
    
    // Test Tax Planning with high income
    await page.click('[data-testid="tax-optimization-tab"]');
    await page.waitForSelector('[data-testid="optimization-strategies"]', { timeout: 10000 });
    
    // Verify additional ALV is calculated for high income
    await expect(page.locator('[data-testid="additional-alv"]')).toBeVisible();
    
    // Test Canton Relocation with high wealth
    await page.click('[data-testid="canton-relocation-tab"]');
    await page.waitForSelector('[data-testid="wealth-tax-analysis"]', { timeout: 10000 });
    
    // Verify wealth tax considerations are prominent
    await expect(page.locator('[data-testid="wealth-tax-savings"]')).toContainText('CHF');
  });

  test('should provide consistent user experience across all premium features', async ({ page }) => {
    const features = [
      'ai-advisor-tab',
      'tax-optimization-tab',
      'risk-assessment-tab',
      'withdrawal-rate-tab',
      'historical-tracking-tab',
      'canton-relocation-tab',
      'healthcare-optimizer-tab',
    ];
    
    for (const feature of features) {
      await page.click('[data-testid="analysis-tab"]');
      await page.click(`[data-testid="${feature}"]`);
      await page.waitForTimeout(1000);
      
      // Verify consistent header structure
      const header = page.locator('h3').first();
      await expect(header).toBeVisible();
      
      // Verify consistent description
      const description = page.locator('p').first();
      await expect(description).toBeVisible();
      
      // Verify consistent loading states
      const loadingIndicator = page.locator('[data-testid="loading"], .animate-spin');
      if (await loadingIndicator.isVisible()) {
        await loadingIndicator.waitFor({ state: 'hidden', timeout: 10000 });
      }
      
      // Verify consistent error handling
      const errorMessage = page.locator('[data-testid="error-message"]');
      if (await errorMessage.isVisible()) {
        await expect(errorMessage).toContainText('error');
      }
    }
  });
});
