#!/usr/bin/env node

/**
 * Comprehensive E2E Test Runner
 * Executes all comprehensive test suites with detailed reporting
 */

import { execSync } from 'child_process';
import { existsSync, mkdirSync, writeFileSync } from 'fs';
import { join } from 'path';

interface TestSuite {
  name: string;
  file: string;
  description: string;
  estimatedTime: string;
  priority: 'high' | 'medium' | 'low';
}

const testSuites: TestSuite[] = [
  {
    name: 'Financial Dashboard Flow',
    file: 'financial-dashboard-flow.spec.ts',
    description: 'Complete user journey through financial planning workflow',
    estimatedTime: '5-8 minutes',
    priority: 'high',
  },
  {
    name: 'Chart Visualization',
    file: 'chart-visualization-comprehensive.spec.ts',
    description: 'Comprehensive testing of D3 charts, interactions, and responsiveness',
    estimatedTime: '8-12 minutes',
    priority: 'high',
  },
  {
    name: 'Swiss Features',
    file: 'swiss-features-comprehensive.spec.ts',
    description: 'Swiss-specific functionality including tax, pillar systems, and healthcare',
    estimatedTime: '10-15 minutes',
    priority: 'high',
  },
  {
    name: 'Mobile & Responsive',
    file: 'mobile-responsive-comprehensive.spec.ts',
    description: 'Cross-device compatibility and responsive design testing',
    estimatedTime: '12-18 minutes',
    priority: 'medium',
  },
  {
    name: 'Error Handling',
    file: 'error-handling-comprehensive.spec.ts',
    description: 'Edge cases, error recovery, and resilience testing',
    estimatedTime: '6-10 minutes',
    priority: 'medium',
  },
  {
    name: 'Performance',
    file: 'performance-comprehensive.spec.ts',
    description: 'Performance benchmarks, Core Web Vitals, and optimization testing',
    estimatedTime: '8-12 minutes',
    priority: 'low',
  },
];

interface TestResult {
  suite: string;
  status: 'passed' | 'failed' | 'skipped';
  duration: number;
  tests: number;
  passed: number;
  failed: number;
  errors?: string[];
}

class ComprehensiveTestRunner {
  private results: TestResult[] = [];
  private startTime: number = 0;
  private outputDir: string;

  constructor() {
    this.outputDir = join(process.cwd(), 'test-results', 'comprehensive');
    this.ensureOutputDir();
  }

  private ensureOutputDir(): void {
    if (!existsSync(this.outputDir)) {
      mkdirSync(this.outputDir, { recursive: true });
    }
  }

  private log(message: string, level: 'info' | 'success' | 'error' | 'warn' = 'info'): void {
    const colors = {
      info: '\x1b[36m',    // Cyan
      success: '\x1b[32m', // Green
      error: '\x1b[31m',   // Red
      warn: '\x1b[33m',    // Yellow
      reset: '\x1b[0m',
    };

    const timestamp = new Date().toISOString().substr(11, 8);
    console.log(`${colors[level]}[${timestamp}] ${message}${colors.reset}`);
  }

  private async runTestSuite(suite: TestSuite): Promise<TestResult> {
    this.log(`Starting ${suite.name} tests...`, 'info');
    this.log(`Description: ${suite.description}`, 'info');
    this.log(`Estimated time: ${suite.estimatedTime}`, 'info');

    const startTime = Date.now();
    const testFile = join(__dirname, suite.file);

    try {
      const command = `npx playwright test ${testFile} --reporter=json`;
      const output = execSync(command, { 
        encoding: 'utf8',
        timeout: 30 * 60 * 1000, // 30 minutes timeout
        env: { ...process.env, CI: 'false' },
      });

      const duration = Date.now() - startTime;
      
      // Parse Playwright JSON output
      let testData;
      try {
        testData = JSON.parse(output);
      } catch {
        // Fallback if JSON parsing fails
        testData = { stats: { total: 1, passed: 1, failed: 0 } };
      }

      const result: TestResult = {
        suite: suite.name,
        status: 'passed',
        duration,
        tests: testData.stats?.total || 1,
        passed: testData.stats?.passed || 1,
        failed: testData.stats?.failed || 0,
      };

      this.log(`✅ ${suite.name} completed successfully`, 'success');
      this.log(`   Tests: ${result.tests}, Passed: ${result.passed}, Failed: ${result.failed}`, 'info');
      this.log(`   Duration: ${(duration / 1000).toFixed(1)}s`, 'info');

      return result;

    } catch (error: any) {
      const duration = Date.now() - startTime;
      const errorMessage = error.message || 'Unknown error';

      this.log(`❌ ${suite.name} failed`, 'error');
      this.log(`   Error: ${errorMessage}`, 'error');
      this.log(`   Duration: ${(duration / 1000).toFixed(1)}s`, 'info');

      return {
        suite: suite.name,
        status: 'failed',
        duration,
        tests: 0,
        passed: 0,
        failed: 1,
        errors: [errorMessage],
      };
    }
  }

  private generateReport(): void {
    const totalDuration = Date.now() - this.startTime;
    const totalTests = this.results.reduce((sum, r) => sum + r.tests, 0);
    const totalPassed = this.results.reduce((sum, r) => sum + r.passed, 0);
    const totalFailed = this.results.reduce((sum, r) => sum + r.failed, 0);
    const passedSuites = this.results.filter(r => r.status === 'passed').length;
    const failedSuites = this.results.filter(r => r.status === 'failed').length;

    // Console report
    this.log('\n' + '='.repeat(80), 'info');
    this.log('COMPREHENSIVE E2E TEST RESULTS', 'info');
    this.log('='.repeat(80), 'info');
    
    this.log(`Total Duration: ${(totalDuration / 1000 / 60).toFixed(1)} minutes`, 'info');
    this.log(`Test Suites: ${passedSuites} passed, ${failedSuites} failed, ${this.results.length} total`, 'info');
    this.log(`Tests: ${totalPassed} passed, ${totalFailed} failed, ${totalTests} total`, 'info');
    
    if (totalFailed === 0) {
      this.log('🎉 All tests passed!', 'success');
    } else {
      this.log(`⚠️  ${totalFailed} tests failed`, 'warn');
    }

    // Detailed results
    this.log('\nDetailed Results:', 'info');
    this.results.forEach(result => {
      const status = result.status === 'passed' ? '✅' : '❌';
      const duration = (result.duration / 1000).toFixed(1);
      this.log(`${status} ${result.suite} (${duration}s) - ${result.passed}/${result.tests} passed`, 'info');
      
      if (result.errors && result.errors.length > 0) {
        result.errors.forEach(error => {
          this.log(`   Error: ${error}`, 'error');
        });
      }
    });

    // Generate HTML report
    this.generateHtmlReport(totalDuration, totalTests, totalPassed, totalFailed);
    
    // Generate JSON report
    this.generateJsonReport(totalDuration, totalTests, totalPassed, totalFailed);
  }

  private generateHtmlReport(totalDuration: number, totalTests: number, totalPassed: number, totalFailed: number): void {
    const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Swiss Budget Pro - Comprehensive E2E Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .metric { background: #f8f9fa; padding: 15px; border-radius: 6px; text-align: center; }
        .metric h3 { margin: 0 0 10px 0; color: #333; }
        .metric .value { font-size: 24px; font-weight: bold; }
        .passed { color: #28a745; }
        .failed { color: #dc3545; }
        .results { margin-top: 30px; }
        .suite { margin-bottom: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 6px; }
        .suite.passed { border-left: 4px solid #28a745; }
        .suite.failed { border-left: 4px solid #dc3545; }
        .suite-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px; }
        .suite-name { font-weight: bold; font-size: 18px; }
        .suite-status { padding: 4px 8px; border-radius: 4px; color: white; font-size: 12px; }
        .status-passed { background: #28a745; }
        .status-failed { background: #dc3545; }
        .suite-details { color: #666; font-size: 14px; }
        .errors { margin-top: 10px; padding: 10px; background: #f8d7da; border-radius: 4px; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Swiss Budget Pro - Comprehensive E2E Test Report</h1>
            <p>Generated on ${new Date().toLocaleString()}</p>
        </div>
        
        <div class="summary">
            <div class="metric">
                <h3>Total Duration</h3>
                <div class="value">${(totalDuration / 1000 / 60).toFixed(1)} min</div>
            </div>
            <div class="metric">
                <h3>Test Suites</h3>
                <div class="value">${this.results.length}</div>
            </div>
            <div class="metric">
                <h3>Total Tests</h3>
                <div class="value">${totalTests}</div>
            </div>
            <div class="metric">
                <h3>Passed</h3>
                <div class="value passed">${totalPassed}</div>
            </div>
            <div class="metric">
                <h3>Failed</h3>
                <div class="value failed">${totalFailed}</div>
            </div>
        </div>
        
        <div class="results">
            <h2>Test Suite Results</h2>
            ${this.results.map(result => `
                <div class="suite ${result.status}">
                    <div class="suite-header">
                        <div class="suite-name">${result.suite}</div>
                        <div class="suite-status status-${result.status}">${result.status.toUpperCase()}</div>
                    </div>
                    <div class="suite-details">
                        Duration: ${(result.duration / 1000).toFixed(1)}s | 
                        Tests: ${result.tests} | 
                        Passed: ${result.passed} | 
                        Failed: ${result.failed}
                    </div>
                    ${result.errors && result.errors.length > 0 ? `
                        <div class="errors">
                            <strong>Errors:</strong><br>
                            ${result.errors.map(error => `• ${error}`).join('<br>')}
                        </div>
                    ` : ''}
                </div>
            `).join('')}
        </div>
    </div>
</body>
</html>`;

    const reportPath = join(this.outputDir, 'comprehensive-test-report.html');
    writeFileSync(reportPath, html);
    this.log(`📊 HTML report generated: ${reportPath}`, 'success');
  }

  private generateJsonReport(totalDuration: number, totalTests: number, totalPassed: number, totalFailed: number): void {
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        totalDuration,
        totalTests,
        totalPassed,
        totalFailed,
        suitesPassed: this.results.filter(r => r.status === 'passed').length,
        suitesFailed: this.results.filter(r => r.status === 'failed').length,
        successRate: totalTests > 0 ? (totalPassed / totalTests * 100).toFixed(2) : '0',
      },
      results: this.results,
    };

    const reportPath = join(this.outputDir, 'comprehensive-test-results.json');
    writeFileSync(reportPath, JSON.stringify(report, null, 2));
    this.log(`📄 JSON report generated: ${reportPath}`, 'success');
  }

  public async runAll(options: { priority?: string; suites?: string[] } = {}): Promise<void> {
    this.startTime = Date.now();
    
    this.log('🚀 Starting Comprehensive E2E Test Suite', 'info');
    this.log(`Total test suites: ${testSuites.length}`, 'info');
    
    let suitesToRun = testSuites;
    
    // Filter by priority if specified
    if (options.priority) {
      suitesToRun = testSuites.filter(suite => suite.priority === options.priority);
      this.log(`Filtering by priority: ${options.priority} (${suitesToRun.length} suites)`, 'info');
    }
    
    // Filter by specific suites if specified
    if (options.suites && options.suites.length > 0) {
      suitesToRun = testSuites.filter(suite => 
        options.suites!.some(name => suite.name.toLowerCase().includes(name.toLowerCase())),
      );
      this.log(`Running specific suites: ${options.suites.join(', ')} (${suitesToRun.length} suites)`, 'info');
    }

    this.log('\n' + '-'.repeat(80), 'info');

    // Run test suites sequentially to avoid resource conflicts
    for (const suite of suitesToRun) {
      const result = await this.runTestSuite(suite);
      this.results.push(result);
      this.log('-'.repeat(80), 'info');
    }

    this.generateReport();
  }
}

// CLI interface
if (require.main === module) {
  const args = process.argv.slice(2);
  const options: { priority?: string; suites?: string[] } = {};

  // Parse command line arguments
  for (let i = 0; i < args.length; i++) {
    if (args[i] === '--priority' && args[i + 1]) {
      options.priority = args[i + 1];
      i++;
    } else if (args[i] === '--suites' && args[i + 1]) {
      options.suites = args[i + 1].split(',').map(s => s.trim());
      i++;
    } else if (args[i] === '--help') {
      console.log(`
Usage: node run-comprehensive-tests.ts [options]

Options:
  --priority <high|medium|low>  Run only tests with specified priority
  --suites <suite1,suite2>      Run only specified test suites
  --help                        Show this help message

Examples:
  node run-comprehensive-tests.ts                           # Run all tests
  node run-comprehensive-tests.ts --priority high           # Run only high priority tests
  node run-comprehensive-tests.ts --suites "chart,mobile"   # Run chart and mobile tests
      `);
      process.exit(0);
    }
  }

  const runner = new ComprehensiveTestRunner();
  runner.runAll(options).catch(error => {
    console.error('Test runner failed:', error);
    process.exit(1);
  });
}

export { ComprehensiveTestRunner, testSuites };
