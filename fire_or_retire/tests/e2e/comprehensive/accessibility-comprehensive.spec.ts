import { test, expect } from '@playwright/test';

/**
 * Comprehensive Accessibility Tests
 * Tests WCAG 2.1 compliance, screen reader compatibility, and inclusive design
 */

test.describe('Accessibility - Comprehensive Testing', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
  });

  test('should meet WCAG 2.1 AA standards', async ({ page }) => {
    await test.step('Test semantic HTML structure', async () => {
      // Check for proper heading hierarchy
      const headings = page.locator('h1, h2, h3, h4, h5, h6');
      const headingCount = await headings.count();
      expect(headingCount).toBeGreaterThan(0);
      
      // Verify h1 exists and is unique
      const h1Elements = page.locator('h1');
      const h1Count = await h1Elements.count();
      expect(h1Count).toBe(1);
      
      // Check heading content is meaningful
      const h1Text = await h1Elements.textContent();
      expect(h1Text).toBeTruthy();
      expect(h1Text!.length).toBeGreaterThan(5);
    });

    await test.step('Test landmark regions', async () => {
      // Check for main landmark
      const mainLandmark = page.locator('main, [role="main"]');
      await expect(mainLandmark).toBeVisible();
      
      // Check for navigation if present
      const navLandmark = page.locator('nav, [role="navigation"]');
      const navCount = await navLandmark.count();
      if (navCount > 0) {
        await expect(navLandmark.first()).toBeVisible();
      }
      
      // Check for banner/header
      const headerLandmark = page.locator('header, [role="banner"]');
      const headerCount = await headerLandmark.count();
      if (headerCount > 0) {
        await expect(headerLandmark.first()).toBeVisible();
      }
    });

    await test.step('Test form accessibility', async () => {
      const inputs = page.locator('input, select, textarea');
      const inputCount = await inputs.count();
      
      for (let i = 0; i < Math.min(inputCount, 10); i++) {
        const input = inputs.nth(i);
        
        // Check for accessible name (label, aria-label, or aria-labelledby)
        const hasAccessibleName = await input.evaluate(el => {
          const id = el.id;
          const ariaLabel = el.getAttribute('aria-label');
          const ariaLabelledBy = el.getAttribute('aria-labelledby');
          const label = id ? document.querySelector(`label[for="${id}"]`) : null;
          const placeholder = el.getAttribute('placeholder');
          
          return !!(ariaLabel || ariaLabelledBy || label || placeholder);
        });
        
        expect(hasAccessibleName).toBe(true);
        
        // Check for proper input types
        const inputType = await input.getAttribute('type');
        if (inputType === 'number') {
          // Number inputs should have proper attributes
          const hasMinMax = await input.evaluate(el => {
            return el.hasAttribute('min') || el.hasAttribute('max') || el.hasAttribute('step');
          });
          // This is a recommendation, not a requirement
        }
      }
    });
  });

  test('should support keyboard navigation', async ({ page }) => {
    await test.step('Test tab order and focus management', async () => {
      // Start tabbing through the page
      await page.keyboard.press('Tab');
      
      let focusedElement = page.locator(':focus');
      await expect(focusedElement).toBeVisible();
      
      // Tab through multiple elements
      const tabStops = [];
      for (let i = 0; i < 15; i++) {
        const currentFocus = await page.evaluate(() => {
          const focused = document.activeElement;
          return focused ? {
            tagName: focused.tagName,
            type: focused.getAttribute('type'),
            id: focused.id,
            className: focused.className,
            ariaLabel: focused.getAttribute('aria-label')
          } : null;
        });
        
        if (currentFocus) {
          tabStops.push(currentFocus);
        }
        
        await page.keyboard.press('Tab');
        await page.waitForTimeout(100);
      }
      
      // Should have found multiple focusable elements
      expect(tabStops.length).toBeGreaterThan(3);
      
      // Check that focus is visible
      const focusedAfterTabs = page.locator(':focus');
      await expect(focusedAfterTabs).toBeVisible();
    });

    await test.step('Test keyboard shortcuts and interactions', async () => {
      // Test Enter key on buttons
      const buttons = page.locator('button:visible');
      const buttonCount = await buttons.count();
      
      if (buttonCount > 0) {
        const firstButton = buttons.first();
        await firstButton.focus();
        
        // Verify button is focusable
        await expect(firstButton).toBeFocused();
        
        // Test space key activation
        await page.keyboard.press('Space');
        await page.waitForTimeout(500);
      }
      
      // Test escape key functionality
      await page.keyboard.press('Escape');
      await page.waitForTimeout(500);
    });

    await test.step('Test skip links if present', async () => {
      // Look for skip links (usually hidden until focused)
      const skipLinks = page.locator('a[href^="#"], [class*="skip"]');
      const skipCount = await skipLinks.count();
      
      if (skipCount > 0) {
        // Tab to potential skip link
        await page.keyboard.press('Tab');
        const firstFocused = page.locator(':focus');
        
        const isSkipLink = await firstFocused.evaluate(el => {
          const href = el.getAttribute('href');
          const text = el.textContent?.toLowerCase();
          return href?.startsWith('#') || text?.includes('skip') || text?.includes('main');
        });
        
        if (isSkipLink) {
          await expect(firstFocused).toBeVisible();
        }
      }
    });
  });

  test('should support screen readers', async ({ page }) => {
    await test.step('Test ARIA labels and descriptions', async () => {
      // Check for proper ARIA labeling on interactive elements
      const interactiveElements = page.locator('button, input, select, textarea, a, [role="button"], [role="link"]');
      const elementCount = await interactiveElements.count();
      
      for (let i = 0; i < Math.min(elementCount, 15); i++) {
        const element = interactiveElements.nth(i);
        
        const ariaInfo = await element.evaluate(el => ({
          hasAriaLabel: !!el.getAttribute('aria-label'),
          hasAriaLabelledBy: !!el.getAttribute('aria-labelledby'),
          hasAriaDescribedBy: !!el.getAttribute('aria-describedby'),
          hasTitle: !!el.getAttribute('title'),
          hasTextContent: !!el.textContent?.trim(),
          tagName: el.tagName,
          role: el.getAttribute('role')
        }));
        
        // Element should have some form of accessible name
        const hasAccessibleName = ariaInfo.hasAriaLabel || 
                                 ariaInfo.hasAriaLabelledBy || 
                                 ariaInfo.hasTitle || 
                                 ariaInfo.hasTextContent;
        
        expect(hasAccessibleName).toBe(true);
      }
    });

    await test.step('Test live regions for dynamic content', async () => {
      // Set up some data to trigger dynamic updates
      await page.fill('[data-testid="current-age-input"]', '30');
      await page.fill('[data-testid="current-savings-input"]', '50000');
      await page.waitForTimeout(2000);
      
      // Look for live regions
      const liveRegions = page.locator('[aria-live], [role="status"], [role="alert"]');
      const liveCount = await liveRegions.count();
      
      if (liveCount > 0) {
        // Verify live regions have appropriate politeness levels
        for (let i = 0; i < liveCount; i++) {
          const region = liveRegions.nth(i);
          const ariaLive = await region.getAttribute('aria-live');
          const role = await region.getAttribute('role');
          
          if (ariaLive) {
            expect(['polite', 'assertive', 'off']).toContain(ariaLive);
          }
          
          if (role) {
            expect(['status', 'alert', 'log']).toContain(role);
          }
        }
      }
    });

    await test.step('Test chart accessibility', async () => {
      // Wait for charts to load
      await page.waitForTimeout(3000);
      
      const charts = page.locator('svg, canvas, [role="img"]');
      const chartCount = await charts.count();
      
      if (chartCount > 0) {
        for (let i = 0; i < Math.min(chartCount, 3); i++) {
          const chart = charts.nth(i);
          
          // Charts should have accessible names and descriptions
          const chartAccessibility = await chart.evaluate(el => ({
            hasAriaLabel: !!el.getAttribute('aria-label'),
            hasAriaLabelledBy: !!el.getAttribute('aria-labelledby'),
            hasAriaDescribedBy: !!el.getAttribute('aria-describedby'),
            hasTitle: !!el.querySelector('title'),
            hasRole: !!el.getAttribute('role'),
            tagName: el.tagName
          }));
          
          // SVG charts should have proper accessibility
          if (chartAccessibility.tagName === 'SVG') {
            const hasAccessibility = chartAccessibility.hasAriaLabel || 
                                   chartAccessibility.hasAriaLabelledBy || 
                                   chartAccessibility.hasTitle ||
                                   chartAccessibility.hasRole;
            
            expect(hasAccessibility).toBe(true);
          }
        }
      }
    });
  });

  test('should support high contrast and reduced motion', async ({ page }) => {
    await test.step('Test high contrast mode', async () => {
      // Simulate high contrast preference
      await page.emulateMedia({ colorScheme: 'dark' });
      await page.waitForTimeout(1000);
      
      // Check that content is still visible and readable
      const textElements = page.locator('p, span, div, h1, h2, h3, h4, h5, h6, label');
      const textCount = await textElements.count();
      
      if (textCount > 0) {
        // Sample a few text elements to check visibility
        for (let i = 0; i < Math.min(textCount, 5); i++) {
          const element = textElements.nth(i);
          const hasText = await element.evaluate(el => !!el.textContent?.trim());
          
          if (hasText) {
            await expect(element).toBeVisible();
          }
        }
      }
      
      // Reset to light mode
      await page.emulateMedia({ colorScheme: 'light' });
    });

    await test.step('Test reduced motion preference', async () => {
      // Simulate reduced motion preference
      await page.emulateMedia({ reducedMotion: 'reduce' });
      await page.waitForTimeout(1000);
      
      // Set up data to trigger animations
      await page.fill('[data-testid="current-age-input"]', '35');
      await page.fill('[data-testid="current-savings-input"]', '75000');
      await page.waitForTimeout(3000);
      
      // Charts should still be functional with reduced motion
      const charts = page.locator('svg');
      const chartCount = await charts.count();
      
      if (chartCount > 0) {
        await expect(charts.first()).toBeVisible();
        
        // Test chart interaction with reduced motion
        await charts.first().hover();
        await page.waitForTimeout(500);
      }
      
      // Reset motion preference
      await page.emulateMedia({ reducedMotion: 'no-preference' });
    });
  });

  test('should provide clear error messages and feedback', async ({ page }) => {
    await test.step('Test error message accessibility', async () => {
      // Trigger validation errors
      await page.fill('[data-testid="current-age-input"]', '-5');
      await page.fill('[data-testid="retirement-age-input"]', '150');
      await page.waitForTimeout(1000);
      
      // Look for error messages
      const errorElements = page.locator('[role="alert"], .error, [aria-invalid="true"], [data-testid*="error"]');
      const errorCount = await errorElements.count();
      
      if (errorCount > 0) {
        for (let i = 0; i < errorCount; i++) {
          const error = errorElements.nth(i);
          
          // Error should be visible and have meaningful text
          await expect(error).toBeVisible();
          
          const errorText = await error.textContent();
          expect(errorText).toBeTruthy();
          expect(errorText!.length).toBeGreaterThan(3);
          
          // Check if error is properly associated with input
          const ariaDescribedBy = await error.getAttribute('aria-describedby');
          const id = await error.getAttribute('id');
          
          if (id) {
            // Look for input that references this error
            const associatedInput = page.locator(`[aria-describedby*="${id}"]`);
            const hasAssociation = await associatedInput.count() > 0;
            // Association is recommended but not always required
          }
        }
      }
    });

    await test.step('Test success feedback accessibility', async () => {
      // Clear errors and enter valid data
      await page.fill('[data-testid="current-age-input"]', '30');
      await page.fill('[data-testid="retirement-age-input"]', '65');
      await page.fill('[data-testid="current-savings-input"]', '50000');
      await page.waitForTimeout(2000);
      
      // Look for success indicators
      const successElements = page.locator('[role="status"], .success, [data-testid*="success"]');
      const successCount = await successElements.count();
      
      if (successCount > 0) {
        const success = successElements.first();
        await expect(success).toBeVisible();
        
        const successText = await success.textContent();
        expect(successText).toBeTruthy();
      }
    });
  });

  test('should support multiple languages accessibly', async ({ page }) => {
    await test.step('Test language switching accessibility', async () => {
      // Look for language selector
      const langSelector = page.locator('[data-testid="language-selector"], [aria-label*="language"], select[name*="lang"]');
      const langCount = await langSelector.count();
      
      if (langCount > 0) {
        const selector = langSelector.first();
        
        // Language selector should be accessible
        const hasAccessibleName = await selector.evaluate(el => {
          const ariaLabel = el.getAttribute('aria-label');
          const ariaLabelledBy = el.getAttribute('aria-labelledby');
          const id = el.id;
          const label = id ? document.querySelector(`label[for="${id}"]`) : null;
          
          return !!(ariaLabel || ariaLabelledBy || label);
        });
        
        expect(hasAccessibleName).toBe(true);
        
        // Test language switching
        await selector.focus();
        await page.keyboard.press('ArrowDown');
        await page.keyboard.press('Enter');
        await page.waitForTimeout(1000);
      }
    });

    await test.step('Test content language attributes', async () => {
      // Check for proper lang attributes
      const htmlLang = await page.getAttribute('html', 'lang');
      expect(htmlLang).toBeTruthy();
      
      // Check for any content in different languages
      const langElements = page.locator('[lang]');
      const langCount = await langElements.count();
      
      if (langCount > 0) {
        for (let i = 0; i < Math.min(langCount, 3); i++) {
          const element = langElements.nth(i);
          const lang = await element.getAttribute('lang');
          
          // Language code should be valid
          expect(lang).toMatch(/^[a-z]{2}(-[A-Z]{2})?$/);
        }
      }
    });
  });

  test('should handle focus management in dynamic content', async ({ page }) => {
    await test.step('Test focus management during content updates', async () => {
      // Focus on an input
      const ageInput = page.locator('[data-testid="current-age-input"]');
      await ageInput.focus();
      await expect(ageInput).toBeFocused();
      
      // Trigger content update
      await ageInput.fill('40');
      await page.waitForTimeout(2000);
      
      // Focus should be maintained or properly managed
      const currentFocus = page.locator(':focus');
      await expect(currentFocus).toBeVisible();
    });

    await test.step('Test modal and dialog focus management', async () => {
      // Look for modal triggers
      const modalTriggers = page.locator('button:has-text("Settings"), button:has-text("Help"), [data-testid*="modal"], [aria-haspopup="dialog"]');
      const triggerCount = await modalTriggers.count();
      
      if (triggerCount > 0) {
        const trigger = modalTriggers.first();
        await trigger.click();
        await page.waitForTimeout(1000);
        
        // Look for opened modal
        const modal = page.locator('[role="dialog"], .modal, [aria-modal="true"]');
        const modalCount = await modal.count();
        
        if (modalCount > 0) {
          // Focus should be trapped in modal
          const modalElement = modal.first();
          await expect(modalElement).toBeVisible();
          
          // Test escape key to close
          await page.keyboard.press('Escape');
          await page.waitForTimeout(500);
          
          // Focus should return to trigger
          await expect(trigger).toBeFocused();
        }
      }
    });
  });
});
