import { test, expect } from '@playwright/test';

/**
 * Comprehensive Data Integrity Tests
 * Tests data validation, calculation accuracy, and financial logic correctness
 */

test.describe('Data Integrity - Comprehensive Testing', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
  });

  test('should validate financial calculation accuracy', async ({ page }) => {
    await test.step('Test FIRE number calculation accuracy', async () => {
      // Test scenario: 30 years old, wants to retire at 65, 4% withdrawal rate
      await page.fill('[data-testid="current-age-input"]', '30');
      await page.fill('[data-testid="retirement-age-input"]', '65');
      await page.fill('[data-testid="monthly-expenses-input"]', '5000');
      await page.fill('[data-testid="withdrawal-rate-input"]', '4');
      
      await page.waitForTimeout(3000);
      
      // FIRE number should be 25x annual expenses (4% rule)
      const fireNumberElement = page.locator('[data-testid="fire-number"]');
      if (await fireNumberElement.count() > 0) {
        const fireText = await fireNumberElement.textContent();
        const fireMatch = fireText?.match(/CHF\s*([\d'.,]+)/);
        
        if (fireMatch) {
          const fireNumber = parseFloat(fireMatch[1].replace(/[',]/g, ''));
          const expectedFire = 5000 * 12 * 25; // 25x annual expenses
          
          // Allow 5% tolerance for calculation variations
          const tolerance = expectedFire * 0.05;
          expect(fireNumber).toBeGreaterThan(expectedFire - tolerance);
          expect(fireNumber).toBeLessThan(expectedFire + tolerance);
        }
      }
    });

    await test.step('Test compound interest calculations', async () => {
      // Test scenario with known compound interest calculation
      await page.fill('[data-testid="current-savings-input"]', '100000');
      await page.fill('[data-testid="monthly-savings-input"]', '2000');
      await page.fill('[data-testid="expected-return-input"]', '7');
      await page.fill('[data-testid="current-age-input"]', '30');
      await page.fill('[data-testid="retirement-age-input"]', '40'); // 10 years
      
      await page.waitForTimeout(3000);
      
      // Calculate expected value: 100k initial + 2k monthly for 10 years at 7%
      const monthlyRate = 0.07 / 12;
      const months = 10 * 12;
      
      // Future value of initial amount
      const futureValueInitial = 100000 * Math.pow(1 + monthlyRate, months);
      
      // Future value of monthly contributions (annuity)
      const futureValueAnnuity = 2000 * ((Math.pow(1 + monthlyRate, months) - 1) / monthlyRate);
      
      const expectedTotal = futureValueInitial + futureValueAnnuity;
      
      // Check if calculated value is close to expected
      const projectedValueElement = page.locator('[data-testid="projected-savings"], [data-testid="retirement-savings"]');
      if (await projectedValueElement.count() > 0) {
        const valueText = await projectedValueElement.textContent();
        const valueMatch = valueText?.match(/CHF\s*([\d'.,]+)/);
        
        if (valueMatch) {
          const calculatedValue = parseFloat(valueMatch[1].replace(/[',]/g, ''));
          
          // Allow 10% tolerance for calculation variations and fees
          const tolerance = expectedTotal * 0.1;
          expect(calculatedValue).toBeGreaterThan(expectedTotal - tolerance);
          expect(calculatedValue).toBeLessThan(expectedTotal + tolerance);
        }
      }
    });

    await test.step('Test Swiss tax calculation accuracy', async () => {
      // Test with known tax scenario
      await page.fill('[data-testid="annual-income-input"]', '100000');
      await page.selectOption('[data-testid="canton-selector"]', 'Zurich');
      await page.selectOption('[data-testid="tax-status-selector"]', 'single');
      
      await page.waitForTimeout(3000);
      
      // Swiss tax should be reasonable (15-35% for this income level)
      const taxElement = page.locator('[data-testid="total-tax"], [data-testid="annual-tax"]');
      if (await taxElement.count() > 0) {
        const taxText = await taxElement.textContent();
        const taxMatch = taxText?.match(/CHF\s*([\d'.,]+)/);
        
        if (taxMatch) {
          const taxAmount = parseFloat(taxMatch[1].replace(/[',]/g, ''));
          const taxRate = taxAmount / 100000;
          
          // Tax rate should be reasonable for Swiss standards
          expect(taxRate).toBeGreaterThan(0.10); // At least 10%
          expect(taxRate).toBeLessThan(0.40);    // Less than 40%
        }
      }
    });
  });

  test('should maintain data consistency across calculations', async ({ page }) => {
    await test.step('Test consistency between different calculation methods', async () => {
      // Set base scenario
      const testData = {
        currentAge: 35,
        retirementAge: 65,
        currentSavings: 150000,
        monthlyIncome: 8000,
        monthlyExpenses: 5500,
        expectedReturn: 6,
        inflationRate: 2
      };
      
      // Enter data
      await page.fill('[data-testid="current-age-input"]', testData.currentAge.toString());
      await page.fill('[data-testid="retirement-age-input"]', testData.retirementAge.toString());
      await page.fill('[data-testid="current-savings-input"]', testData.currentSavings.toString());
      await page.fill('[data-testid="monthly-income-input"]', testData.monthlyIncome.toString());
      await page.fill('[data-testid="monthly-expenses-input"]', testData.monthlyExpenses.toString());
      await page.fill('[data-testid="expected-return-input"]', testData.expectedReturn.toString());
      await page.fill('[data-testid="inflation-rate-input"]', testData.inflationRate.toString());
      
      await page.waitForTimeout(4000);
      
      // Capture all calculated values
      const calculations = await page.evaluate(() => {
        const getTextValue = (selector: string) => {
          const element = document.querySelector(selector);
          const text = element?.textContent || '';
          const match = text.match(/CHF\s*([\d'.,]+)/);
          return match ? parseFloat(match[1].replace(/[',]/g, '')) : null;
        };
        
        return {
          fireNumber: getTextValue('[data-testid="fire-number"]'),
          yearsToFire: getTextValue('[data-testid="years-to-fire"]'),
          monthlySavings: getTextValue('[data-testid="monthly-savings"]'),
          savingsRate: getTextValue('[data-testid="savings-rate"]'),
          retirementSavings: getTextValue('[data-testid="retirement-savings"]')
        };
      });
      
      // Verify internal consistency
      if (calculations.monthlySavings && calculations.savingsRate) {
        const expectedSavingsRate = (calculations.monthlySavings / testData.monthlyIncome) * 100;
        const tolerance = 2; // 2% tolerance
        
        expect(calculations.savingsRate).toBeGreaterThan(expectedSavingsRate - tolerance);
        expect(calculations.savingsRate).toBeLessThan(expectedSavingsRate + tolerance);
      }
      
      // Years to FIRE should be reasonable
      if (calculations.yearsToFire) {
        expect(calculations.yearsToFire).toBeGreaterThan(5);
        expect(calculations.yearsToFire).toBeLessThan(50);
      }
    });

    await test.step('Test data persistence and reload consistency', async () => {
      // Set specific values
      await page.fill('[data-testid="current-age-input"]', '42');
      await page.fill('[data-testid="current-savings-input"]', '275000');
      await page.fill('[data-testid="monthly-income-input"]', '9500');
      
      await page.waitForTimeout(2000);
      
      // Capture initial calculations
      const initialCalculations = await page.evaluate(() => {
        const getTextValue = (selector: string) => {
          const element = document.querySelector(selector);
          const text = element?.textContent || '';
          const match = text.match(/CHF\s*([\d'.,]+)/);
          return match ? parseFloat(match[1].replace(/[',]/g, '')) : null;
        };
        
        return {
          fireNumber: getTextValue('[data-testid="fire-number"]'),
          yearsToFire: getTextValue('[data-testid="years-to-fire"]')
        };
      });
      
      // Reload page
      await page.reload();
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(3000);
      
      // Capture calculations after reload
      const reloadCalculations = await page.evaluate(() => {
        const getTextValue = (selector: string) => {
          const element = document.querySelector(selector);
          const text = element?.textContent || '';
          const match = text.match(/CHF\s*([\d'.,]+)/);
          return match ? parseFloat(match[1].replace(/[',]/g, '')) : null;
        };
        
        return {
          fireNumber: getTextValue('[data-testid="fire-number"]'),
          yearsToFire: getTextValue('[data-testid="years-to-fire"]')
        };
      });
      
      // Values should be consistent after reload (if data persists)
      if (initialCalculations.fireNumber && reloadCalculations.fireNumber) {
        const tolerance = initialCalculations.fireNumber * 0.01; // 1% tolerance
        expect(reloadCalculations.fireNumber).toBeGreaterThan(initialCalculations.fireNumber - tolerance);
        expect(reloadCalculations.fireNumber).toBeLessThan(initialCalculations.fireNumber + tolerance);
      }
    });
  });

  test('should validate input constraints and boundaries', async ({ page }) => {
    await test.step('Test age validation boundaries', async () => {
      const ageTestCases = [
        { input: '0', shouldBeValid: false },
        { input: '18', shouldBeValid: true },
        { input: '25', shouldBeValid: true },
        { input: '65', shouldBeValid: true },
        { input: '100', shouldBeValid: false },
        { input: '150', shouldBeValid: false },
        { input: '-5', shouldBeValid: false }
      ];
      
      for (const testCase of ageTestCases) {
        await page.fill('[data-testid="current-age-input"]', testCase.input);
        await page.waitForTimeout(500);
        
        const errorElement = page.locator('[data-testid="age-error"], .error:near([data-testid="current-age-input"])');
        const hasError = await errorElement.count() > 0;
        
        if (testCase.shouldBeValid) {
          expect(hasError).toBe(false);
        } else {
          // Invalid values should show error or be prevented
          const inputValue = await page.inputValue('[data-testid="current-age-input"]');
          const isValuePrevented = inputValue !== testCase.input;
          expect(hasError || isValuePrevented).toBe(true);
        }
      }
    });

    await test.step('Test financial amount validation', async () => {
      const amountTestCases = [
        { input: '-1000', field: 'current-savings-input', shouldBeValid: false },
        { input: '0', field: 'current-savings-input', shouldBeValid: true },
        { input: '50000', field: 'current-savings-input', shouldBeValid: true },
        { input: '999999999', field: 'current-savings-input', shouldBeValid: true },
        { input: 'abc', field: 'monthly-income-input', shouldBeValid: false },
        { input: '5000.50', field: 'monthly-income-input', shouldBeValid: true },
        { input: '-500', field: 'monthly-expenses-input', shouldBeValid: false }
      ];
      
      for (const testCase of amountTestCases) {
        await page.fill(`[data-testid="${testCase.field}"]`, testCase.input);
        await page.waitForTimeout(500);
        
        const errorElement = page.locator(`[data-testid="${testCase.field.replace('-input', '-error')}"], .error:near([data-testid="${testCase.field}"])`);
        const hasError = await errorElement.count() > 0;
        
        const inputValue = await page.inputValue(`[data-testid="${testCase.field}"]`);
        
        if (testCase.shouldBeValid) {
          expect(hasError).toBe(false);
          // Valid numeric input should be accepted
          if (/^\d+(\.\d+)?$/.test(testCase.input)) {
            expect(inputValue).toBe(testCase.input);
          }
        } else {
          // Invalid values should show error or be prevented
          const isValuePrevented = inputValue !== testCase.input;
          expect(hasError || isValuePrevented).toBe(true);
        }
      }
    });

    await test.step('Test percentage validation', async () => {
      const percentageFields = [
        'expected-return-input',
        'inflation-rate-input',
        'withdrawal-rate-input'
      ];
      
      const percentageTestCases = [
        { input: '-5', shouldBeValid: false },
        { input: '0', shouldBeValid: true },
        { input: '2.5', shouldBeValid: true },
        { input: '7', shouldBeValid: true },
        { input: '15', shouldBeValid: true },
        { input: '50', shouldBeValid: false }, // Unrealistic for most fields
        { input: '100', shouldBeValid: false }
      ];
      
      for (const field of percentageFields) {
        const fieldElement = page.locator(`[data-testid="${field}"]`);
        const fieldExists = await fieldElement.count() > 0;
        
        if (fieldExists) {
          for (const testCase of percentageTestCases) {
            await page.fill(`[data-testid="${field}"]`, testCase.input);
            await page.waitForTimeout(500);
            
            const errorElement = page.locator(`[data-testid="${field.replace('-input', '-error')}"], .error:near([data-testid="${field}"])`);
            const hasError = await errorElement.count() > 0;
            
            if (!testCase.shouldBeValid) {
              const inputValue = await page.inputValue(`[data-testid="${field}"]`);
              const isValuePrevented = inputValue !== testCase.input;
              expect(hasError || isValuePrevented).toBe(true);
            }
          }
        }
      }
    });
  });

  test('should handle edge cases and extreme scenarios', async ({ page }) => {
    await test.step('Test zero and minimal values', async () => {
      // Test scenario with minimal values
      await page.fill('[data-testid="current-age-input"]', '18');
      await page.fill('[data-testid="retirement-age-input"]', '19'); // 1 year to retirement
      await page.fill('[data-testid="current-savings-input"]', '0');
      await page.fill('[data-testid="monthly-income-input"]', '1000');
      await page.fill('[data-testid="monthly-expenses-input"]', '999');
      
      await page.waitForTimeout(3000);
      
      // Application should handle minimal values gracefully
      const fireNumber = page.locator('[data-testid="fire-number"]');
      if (await fireNumber.count() > 0) {
        const fireText = await fireNumber.textContent();
        expect(fireText).toBeTruthy();
        expect(fireText).toContain('CHF');
      }
      
      // Should not crash or show NaN/Infinity
      const allNumbers = page.locator(':text-matches("CHF\\s*[\\d\',]+")');
      const numberCount = await allNumbers.count();
      
      for (let i = 0; i < numberCount; i++) {
        const numberText = await allNumbers.nth(i).textContent();
        expect(numberText).not.toContain('NaN');
        expect(numberText).not.toContain('Infinity');
        expect(numberText).not.toContain('undefined');
      }
    });

    await test.step('Test maximum realistic values', async () => {
      // Test scenario with high but realistic values
      await page.fill('[data-testid="current-age-input"]', '25');
      await page.fill('[data-testid="retirement-age-input"]', '65');
      await page.fill('[data-testid="current-savings-input"]', '5000000'); // 5M CHF
      await page.fill('[data-testid="monthly-income-input"]', '50000');    // 50k/month
      await page.fill('[data-testid="monthly-expenses-input"]', '20000');  // 20k/month
      
      await page.waitForTimeout(4000);
      
      // Application should handle large values
      const calculations = await page.evaluate(() => {
        const elements = document.querySelectorAll('[data-testid*="fire"], [data-testid*="savings"], [data-testid*="years"]');
        const results: string[] = [];
        
        elements.forEach(el => {
          const text = el.textContent || '';
          if (text.includes('CHF') || text.includes('%') || /\d+/.test(text)) {
            results.push(text);
          }
        });
        
        return results;
      });
      
      // All calculations should produce valid results
      for (const result of calculations) {
        expect(result).not.toContain('NaN');
        expect(result).not.toContain('Infinity');
        expect(result).not.toContain('undefined');
        expect(result).not.toContain('null');
      }
    });

    await test.step('Test illogical combinations', async () => {
      // Test current age > retirement age
      await page.fill('[data-testid="current-age-input"]', '65');
      await page.fill('[data-testid="retirement-age-input"]', '60');
      
      await page.waitForTimeout(1000);
      
      // Should show warning or handle gracefully
      const warningElement = page.locator('[data-testid*="warning"], .warning, [role="alert"]');
      const hasWarning = await warningElement.count() > 0;
      
      // Test expenses > income
      await page.fill('[data-testid="current-age-input"]', '30');
      await page.fill('[data-testid="retirement-age-input"]', '65');
      await page.fill('[data-testid="monthly-income-input"]', '5000');
      await page.fill('[data-testid="monthly-expenses-input"]', '6000');
      
      await page.waitForTimeout(2000);
      
      // Should handle negative savings scenario
      const negativeWarning = page.locator('[data-testid*="warning"], .warning, :text("negative"), :text("deficit")');
      const hasNegativeWarning = await negativeWarning.count() > 0;
      
      // Application should provide feedback for illogical scenarios
      if (!hasWarning && !hasNegativeWarning) {
        // At minimum, calculations should still be valid
        const fireNumber = page.locator('[data-testid="fire-number"]');
        if (await fireNumber.count() > 0) {
          const fireText = await fireNumber.textContent();
          expect(fireText).not.toContain('NaN');
        }
      }
    });
  });

  test('should maintain precision in financial calculations', async ({ page }) => {
    await test.step('Test decimal precision handling', async () => {
      // Test with precise decimal values
      await page.fill('[data-testid="current-savings-input"]', '123456.78');
      await page.fill('[data-testid="monthly-income-input"]', '7890.12');
      await page.fill('[data-testid="monthly-expenses-input"]', '5432.10');
      await page.fill('[data-testid="expected-return-input"]', '6.75');
      
      await page.waitForTimeout(3000);
      
      // Check that precision is maintained in calculations
      const preciseValues = await page.evaluate(() => {
        const elements = document.querySelectorAll('[data-testid*="savings"], [data-testid*="income"], [data-testid*="fire"]');
        const values: number[] = [];
        
        elements.forEach(el => {
          const text = el.textContent || '';
          const match = text.match(/CHF\s*([\d'.,]+)/);
          if (match) {
            const value = parseFloat(match[1].replace(/[',]/g, ''));
            if (!isNaN(value)) {
              values.push(value);
            }
          }
        });
        
        return values;
      });
      
      // Values should be reasonable and not show excessive precision
      for (const value of preciseValues) {
        expect(value).toBeGreaterThan(0);
        expect(value).toBeLessThan(100000000); // 100M CHF max
        
        // Check for reasonable decimal places (not more than 2 for currency)
        const decimalPlaces = (value.toString().split('.')[1] || '').length;
        expect(decimalPlaces).toBeLessThanOrEqual(2);
      }
    });

    await test.step('Test rounding consistency', async () => {
      // Test values that require rounding
      await page.fill('[data-testid="monthly-expenses-input"]', '3333.33');
      await page.fill('[data-testid="withdrawal-rate-input"]', '3.33');
      
      await page.waitForTimeout(2000);
      
      // FIRE number calculation: 3333.33 * 12 / 0.0333 = 1,200,000
      const fireNumber = page.locator('[data-testid="fire-number"]');
      if (await fireNumber.count() > 0) {
        const fireText = await fireNumber.textContent();
        const fireMatch = fireText?.match(/CHF\s*([\d'.,]+)/);
        
        if (fireMatch) {
          const fireValue = parseFloat(fireMatch[1].replace(/[',]/g, ''));
          
          // Should be close to expected value with proper rounding
          const expectedFire = (3333.33 * 12) / 0.0333;
          const tolerance = expectedFire * 0.02; // 2% tolerance
          
          expect(fireValue).toBeGreaterThan(expectedFire - tolerance);
          expect(fireValue).toBeLessThan(expectedFire + tolerance);
        }
      }
    });
  });
});
