import { test, expect } from '@playwright/test';

/**
 * Comprehensive Internationalization and Localization Tests
 * Tests Swiss German, French, Italian, and English language support
 */

test.describe('I18n & Localization - Comprehensive Testing', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
  });

  test('should support Swiss German (de-CH) localization', async ({ page }) => {
    await test.step('Test Swiss German language switching', async () => {
      // Look for language selector
      const langSelector = page.locator('[data-testid="language-selector"], select[name*="lang"], button:has-text("DE"), [aria-label*="language"]');
      const langCount = await langSelector.count();
      
      if (langCount > 0) {
        await langSelector.first().click();
        
        // Look for German option
        const germanOption = page.locator('option[value="de"], option[value="de-CH"], button:has-text("Deutsch"), [data-value="de"]');
        const germanCount = await germanOption.count();
        
        if (germanCount > 0) {
          await germanOption.first().click();
          await page.waitForTimeout(2000);
          
          // Verify German text appears
          const germanTerms = [
            'Alter', 'Einkommen', 'Ausgaben', 'Sparen', 'Rente', 'Pensionierung',
            'Vermögen', 'Zinssatz', 'Inflation', 'Steuer', 'Kanton', 'CHF'
          ];
          
          let germanTermsFound = 0;
          for (const term of germanTerms) {
            const termElement = page.locator(`:text("${term}")`);
            const termCount = await termElement.count();
            if (termCount > 0) {
              germanTermsFound++;
            }
          }
          
          // Should find at least some German terms
          expect(germanTermsFound).toBeGreaterThan(2);
        }
      }
    });

    await test.step('Test Swiss German number formatting', async () => {
      // Set Swiss German locale context
      await page.setExtraHTTPHeaders({
        'Accept-Language': 'de-CH,de;q=0.9,en;q=0.8'
      });
      
      await page.reload();
      await page.waitForLoadState('networkidle');
      
      // Enter large numbers
      await page.fill('[data-testid="current-savings-input"]', '123456');
      await page.fill('[data-testid="monthly-income-input"]', '7890');
      await page.waitForTimeout(2000);
      
      // Check for Swiss number formatting (apostrophes for thousands)
      const swissFormattedNumbers = page.locator(':text-matches("\\d{1,3}\'\\d{3}")');
      const formattedCount = await swissFormattedNumbers.count();
      
      if (formattedCount > 0) {
        // Verify Swiss formatting is used
        const formattedText = await swissFormattedNumbers.first().textContent();
        expect(formattedText).toMatch(/\d{1,3}'\d{3}/);
      }
    });

    await test.step('Test Swiss German date formatting', async () => {
      // Look for date displays
      const dateElements = page.locator('[data-testid*="date"], .date, :text-matches("\\d{1,2}\\.\\d{1,2}\\.\\d{4}")');
      const dateCount = await dateElements.count();
      
      if (dateCount > 0) {
        const dateText = await dateElements.first().textContent();
        
        // Swiss German uses DD.MM.YYYY format
        const swissDatePattern = /\d{1,2}\.\d{1,2}\.\d{4}/;
        expect(dateText).toMatch(swissDatePattern);
      }
    });
  });

  test('should support Swiss French (fr-CH) localization', async ({ page }) => {
    await test.step('Test Swiss French language switching', async () => {
      const langSelector = page.locator('[data-testid="language-selector"], select[name*="lang"], button:has-text("FR"), [aria-label*="language"]');
      const langCount = await langSelector.count();
      
      if (langCount > 0) {
        await langSelector.first().click();
        
        const frenchOption = page.locator('option[value="fr"], option[value="fr-CH"], button:has-text("Français"), [data-value="fr"]');
        const frenchCount = await frenchOption.count();
        
        if (frenchCount > 0) {
          await frenchOption.first().click();
          await page.waitForTimeout(2000);
          
          // Verify French text appears
          const frenchTerms = [
            'Âge', 'Revenu', 'Dépenses', 'Épargne', 'Retraite', 'Pension',
            'Patrimoine', 'Taux', 'Inflation', 'Impôt', 'Canton'
          ];
          
          let frenchTermsFound = 0;
          for (const term of frenchTerms) {
            const termElement = page.locator(`:text("${term}")`);
            const termCount = await termElement.count();
            if (termCount > 0) {
              frenchTermsFound++;
            }
          }
          
          expect(frenchTermsFound).toBeGreaterThan(2);
        }
      }
    });

    await test.step('Test Swiss French currency formatting', async () => {
      await page.setExtraHTTPHeaders({
        'Accept-Language': 'fr-CH,fr;q=0.9,en;q=0.8'
      });
      
      await page.reload();
      await page.waitForLoadState('networkidle');
      
      await page.fill('[data-testid="current-savings-input"]', '50000');
      await page.waitForTimeout(2000);
      
      // Swiss French should still use CHF
      const chfElements = page.locator(':text("CHF")');
      const chfCount = await chfElements.count();
      expect(chfCount).toBeGreaterThan(0);
      
      // Check for French-style number formatting if implemented
      const numberElements = page.locator(':text-matches("CHF\\s*[\\d\\s\',]+")');
      const numberCount = await numberElements.count();
      expect(numberCount).toBeGreaterThan(0);
    });
  });

  test('should support Swiss Italian (it-CH) localization', async ({ page }) => {
    await test.step('Test Swiss Italian language switching', async () => {
      const langSelector = page.locator('[data-testid="language-selector"], select[name*="lang"], button:has-text("IT"), [aria-label*="language"]');
      const langCount = await langSelector.count();
      
      if (langCount > 0) {
        await langSelector.first().click();
        
        const italianOption = page.locator('option[value="it"], option[value="it-CH"], button:has-text("Italiano"), [data-value="it"]');
        const italianCount = await italianOption.count();
        
        if (italianCount > 0) {
          await italianOption.first().click();
          await page.waitForTimeout(2000);
          
          // Verify Italian text appears
          const italianTerms = [
            'Età', 'Reddito', 'Spese', 'Risparmio', 'Pensione', 'Pensionamento',
            'Patrimonio', 'Tasso', 'Inflazione', 'Tassa', 'Cantone'
          ];
          
          let italianTermsFound = 0;
          for (const term of italianTerms) {
            const termElement = page.locator(`:text("${term}")`);
            const termCount = await termElement.count();
            if (termCount > 0) {
              italianTermsFound++;
            }
          }
          
          expect(italianTermsFound).toBeGreaterThan(2);
        }
      }
    });
  });

  test('should handle Swiss canton names in multiple languages', async ({ page }) => {
    await test.step('Test canton names localization', async () => {
      const cantonSelector = page.locator('[data-testid="canton-selector"], select[name*="canton"]');
      const cantonCount = await cantonSelector.count();
      
      if (cantonCount > 0) {
        // Get canton options
        const cantonOptions = cantonSelector.locator('option');
        const optionCount = await cantonOptions.count();
        
        if (optionCount > 0) {
          // Check for Swiss canton names
          const swissCantons = [
            'Zürich', 'Bern', 'Luzern', 'Uri', 'Schwyz', 'Obwalden', 'Nidwalden',
            'Glarus', 'Zug', 'Fribourg', 'Solothurn', 'Basel-Stadt', 'Basel-Landschaft',
            'Schaffhausen', 'Appenzell Ausserrhoden', 'Appenzell Innerrhoden',
            'St. Gallen', 'Graubünden', 'Aargau', 'Thurgau', 'Ticino', 'Vaud',
            'Valais', 'Neuchâtel', 'Geneva', 'Jura'
          ];
          
          let cantonsFound = 0;
          for (const canton of swissCantons.slice(0, 5)) { // Test first 5
            const cantonOption = cantonSelector.locator(`option:has-text("${canton}")`);
            const hasOption = await cantonOption.count() > 0;
            if (hasOption) {
              cantonsFound++;
            }
          }
          
          expect(cantonsFound).toBeGreaterThan(0);
        }
      }
    });

    await test.step('Test canton-specific tax calculations', async () => {
      const cantonSelector = page.locator('[data-testid="canton-selector"]');
      const cantonCount = await cantonSelector.count();
      
      if (cantonCount > 0) {
        // Test different cantons
        const testCantons = ['Zurich', 'Geneva', 'Zug', 'Bern'];
        
        for (const canton of testCantons) {
          const cantonOption = cantonSelector.locator(`option:has-text("${canton}")`);
          const hasOption = await cantonOption.count() > 0;
          
          if (hasOption) {
            await cantonSelector.selectOption({ label: canton });
            await page.fill('[data-testid="annual-income-input"]', '100000');
            await page.waitForTimeout(2000);
            
            // Tax calculations should vary by canton
            const taxElement = page.locator('[data-testid="tax-amount"], :text("tax"):near(:text("CHF"))');
            const taxCount = await taxElement.count();
            
            if (taxCount > 0) {
              const taxText = await taxElement.textContent();
              expect(taxText).toContain('CHF');
            }
          }
        }
      }
    });
  });

  test('should handle currency and financial terms correctly', async ({ page }) => {
    await test.step('Test CHF currency consistency', async () => {
      // CHF should be used consistently across all languages
      const languages = ['de', 'fr', 'it', 'en'];
      
      for (const lang of languages) {
        await page.setExtraHTTPHeaders({
          'Accept-Language': `${lang}-CH,${lang};q=0.9,en;q=0.8`
        });
        
        await page.reload();
        await page.waitForLoadState('networkidle');
        
        await page.fill('[data-testid="current-savings-input"]', '75000');
        await page.waitForTimeout(2000);
        
        // CHF should appear regardless of language
        const chfElements = page.locator(':text("CHF")');
        const chfCount = await chfElements.count();
        expect(chfCount).toBeGreaterThan(0);
      }
    });

    await test.step('Test financial term translations', async () => {
      // Test that financial terms are properly translated
      const financialTerms = {
        'de': ['FIRE', 'Rente', 'Sparen', 'Zinsen', 'Inflation'],
        'fr': ['FIRE', 'Retraite', 'Épargne', 'Intérêts', 'Inflation'],
        'it': ['FIRE', 'Pensione', 'Risparmio', 'Interessi', 'Inflazione'],
        'en': ['FIRE', 'Retirement', 'Savings', 'Interest', 'Inflation']
      };
      
      // Test German terms
      await page.setExtraHTTPHeaders({
        'Accept-Language': 'de-CH,de;q=0.9'
      });
      
      await page.reload();
      await page.waitForLoadState('networkidle');
      
      // Look for German financial terms
      let germanTermsFound = 0;
      for (const term of financialTerms.de) {
        const termElement = page.locator(`:text("${term}")`);
        const termCount = await termElement.count();
        if (termCount > 0) {
          germanTermsFound++;
        }
      }
      
      // Should find some German financial terms
      expect(germanTermsFound).toBeGreaterThan(1);
    });
  });

  test('should handle right-to-left languages gracefully', async ({ page }) => {
    await test.step('Test RTL language support', async () => {
      // Test with Arabic locale (if supported)
      await page.setExtraHTTPHeaders({
        'Accept-Language': 'ar,en;q=0.9'
      });
      
      await page.reload();
      await page.waitForLoadState('networkidle');
      
      // Check if RTL is handled
      const htmlDir = await page.getAttribute('html', 'dir');
      const bodyDir = await page.getAttribute('body', 'dir');
      
      // If RTL is supported, direction should be set
      if (htmlDir === 'rtl' || bodyDir === 'rtl') {
        // Layout should still be functional
        const mainContent = page.locator('main, [role="main"], body');
        await expect(mainContent).toBeVisible();
        
        // Forms should still work
        await page.fill('[data-testid="current-age-input"]', '30');
        const ageValue = await page.inputValue('[data-testid="current-age-input"]');
        expect(ageValue).toBe('30');
      }
    });
  });

  test('should maintain accessibility across languages', async ({ page }) => {
    await test.step('Test accessibility in different languages', async () => {
      const languages = ['de-CH', 'fr-CH', 'en'];
      
      for (const lang of languages) {
        await page.setExtraHTTPHeaders({
          'Accept-Language': `${lang},en;q=0.9`
        });
        
        await page.reload();
        await page.waitForLoadState('networkidle');
        
        // Check lang attribute
        const htmlLang = await page.getAttribute('html', 'lang');
        expect(htmlLang).toBeTruthy();
        
        // Check that form labels are still accessible
        const inputs = page.locator('input');
        const inputCount = await inputs.count();
        
        for (let i = 0; i < Math.min(inputCount, 5); i++) {
          const input = inputs.nth(i);
          
          const hasAccessibleName = await input.evaluate(el => {
            const id = el.id;
            const ariaLabel = el.getAttribute('aria-label');
            const ariaLabelledBy = el.getAttribute('aria-labelledby');
            const label = id ? document.querySelector(`label[for="${id}"]`) : null;
            const placeholder = el.getAttribute('placeholder');
            
            return !!(ariaLabel || ariaLabelledBy || label || placeholder);
          });
          
          expect(hasAccessibleName).toBe(true);
        }
      }
    });
  });

  test('should handle locale-specific formatting', async ({ page }) => {
    await test.step('Test Swiss locale formatting', async () => {
      // Test Swiss German formatting
      await page.setExtraHTTPHeaders({
        'Accept-Language': 'de-CH'
      });
      
      await page.reload();
      await page.waitForLoadState('networkidle');
      
      // Enter test data
      await page.fill('[data-testid="current-savings-input"]', '1234567');
      await page.fill('[data-testid="monthly-income-input"]', '8900');
      await page.waitForTimeout(2000);
      
      // Check for Swiss number formatting
      const numberElements = page.locator(':text-matches("CHF\\s*[\\d\',\\s]+")');
      const numberCount = await numberElements.count();
      
      if (numberCount > 0) {
        const numberText = await numberElements.first().textContent();
        
        // Swiss formatting typically uses apostrophes or spaces
        const hasSwissFormatting = numberText?.includes("'") || 
                                  /CHF\s*\d{1,3}\s\d{3}/.test(numberText || '');
        
        // Should use some form of thousands separator
        expect(hasSwissFormatting || numberText?.includes(',')).toBe(true);
      }
    });

    await test.step('Test percentage formatting', async () => {
      // Test percentage display in different locales
      await page.fill('[data-testid="expected-return-input"]', '7.5');
      await page.fill('[data-testid="inflation-rate-input"]', '2.2');
      await page.waitForTimeout(2000);
      
      // Check percentage formatting
      const percentageElements = page.locator(':text-matches("\\d+[.,]\\d+\\s*%")');
      const percentageCount = await percentageElements.count();
      
      if (percentageCount > 0) {
        const percentageText = await percentageElements.first().textContent();
        
        // Should show percentage symbol
        expect(percentageText).toContain('%');
        
        // Should handle decimal separator correctly
        const hasDecimal = percentageText?.includes('.') || percentageText?.includes(',');
        expect(hasDecimal).toBe(true);
      }
    });
  });

  test('should handle language persistence', async ({ page }) => {
    await test.step('Test language preference persistence', async () => {
      // Set language preference
      const langSelector = page.locator('[data-testid="language-selector"]');
      const langCount = await langSelector.count();
      
      if (langCount > 0) {
        // Select German
        await langSelector.selectOption('de');
        await page.waitForTimeout(1000);
        
        // Reload page
        await page.reload();
        await page.waitForLoadState('networkidle');
        
        // Language preference should persist
        const selectedLang = await langSelector.inputValue();
        expect(selectedLang).toBe('de');
        
        // German text should still be visible
        const germanText = page.locator(':text("Alter"), :text("Einkommen"), :text("Ausgaben")');
        const germanCount = await germanText.count();
        expect(germanCount).toBeGreaterThan(0);
      }
    });
  });
});
