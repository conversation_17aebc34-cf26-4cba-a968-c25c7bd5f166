import { test, expect } from '@playwright/test';

/**
 * Comprehensive Swiss Features Tests
 * Tests all Swiss-specific functionality including tax calculations, 
 * pillar systems, cantonal differences, and healthcare costs
 */

test.describe('Swiss Features - Comprehensive Testing', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
  });

  test('should handle all Swiss cantons correctly', async ({ page }) => {
    const cantons = [
      'Zurich', 'Bern', 'Lucerne', 'Uri', 'Schwyz', 'Obwalden', 'Nidwalden',
      'Glarus', 'Zug', 'Fribourg', 'Solothurn', 'Basel-Stadt', 'Basel-Landschaft',
      'Schaffhausen', 'Appenzell Ausserrhoden', 'Appenzell Innerrhoden',
      'St. Gallen', 'Graubünden', 'Aargau', 'Thurgau', 'Ticino', 'Vaud',
      'Valais', 'Neuchâtel', 'Geneva', 'Jura',
    ];

    await test.step('Test canton selection and tax calculations', async () => {
      const cantonSelector = page.locator('[data-testid="canton-selector"], select[name*="canton"], [aria-label*="canton"]');
      
      if (await cantonSelector.count() > 0) {
        for (const canton of cantons.slice(0, 5)) { // Test first 5 cantons for time
          await cantonSelector.selectOption({ label: canton });
          await page.waitForTimeout(1000);
          
          // Set income for tax calculation
          await page.fill('[data-testid="annual-income-input"]', '100000');
          await page.waitForTimeout(2000);
          
          // Verify tax calculation appears
          const taxDisplay = page.locator('[data-testid="tax-amount"], [class*="tax"], :text("CHF"):near(:text("tax"))');
          if (await taxDisplay.count() > 0) {
            const taxText = await taxDisplay.textContent();
            expect(taxText).toContain('CHF');
            
            // Tax should be reasonable (between 10% and 40% of income)
            const taxMatch = taxText?.match(/CHF\s*([\d'.,]+)/);
            if (taxMatch) {
              const taxAmount = parseFloat(taxMatch[1].replace(/[',]/g, ''));
              expect(taxAmount).toBeGreaterThan(5000);  // At least 5% tax
              expect(taxAmount).toBeLessThan(50000); // Less than 50% tax
            }
          }
        }
      }
    });
  });

  test('should calculate Pillar 3a contributions correctly', async ({ page }) => {
    await test.step('Test maximum Pillar 3a contribution for employees', async () => {
      // Set as employee
      const employeeOption = page.locator('[data-testid="employment-type-employee"], input[value="employee"]');
      if (await employeeOption.count() > 0) {
        await employeeOption.click();
      }
      
      // Set high income
      await page.fill('[data-testid="annual-income-input"]', '150000');
      
      // Check Pillar 3a section
      const pillar3aInput = page.locator('[data-testid="pillar3a-input"], input[name*="pillar3a"]');
      if (await pillar3aInput.count() > 0) {
        // Should suggest maximum contribution (6883 CHF for 2024)
        const maxContribution = page.locator('[data-testid="pillar3a-max"], :text("6\'883"), :text("6883")');
        if (await maxContribution.count() > 0) {
          await expect(maxContribution).toBeVisible();
        }
        
        // Test setting maximum contribution
        await pillar3aInput.fill('6883');
        await page.waitForTimeout(1000);
        
        // Verify tax savings
        const taxSavings = page.locator('[data-testid="tax-savings"], :text("savings"):near(:text("CHF"))');
        if (await taxSavings.count() > 0) {
          const savingsText = await taxSavings.textContent();
          expect(savingsText).toContain('CHF');
        }
      }
    });

    await test.step('Test Pillar 3a for self-employed', async () => {
      const selfEmployedOption = page.locator('[data-testid="employment-type-self-employed"], input[value="self-employed"]');
      if (await selfEmployedOption.count() > 0) {
        await selfEmployedOption.click();
        await page.waitForTimeout(1000);
        
        // Self-employed should have higher limit (20% of income, max 34,416 CHF)
        const pillar3aInput = page.locator('[data-testid="pillar3a-input"]');
        if (await pillar3aInput.count() > 0) {
          await pillar3aInput.fill('34416');
          
          // Should accept higher contribution
          const errorMessage = page.locator('[data-testid="pillar3a-error"], .error:near([data-testid="pillar3a-input"])');
          await expect(errorMessage).not.toBeVisible();
        }
      }
    });
  });

  test('should handle Swiss healthcare costs accurately', async ({ page }) => {
    await test.step('Test healthcare premium calculations', async () => {
      // Navigate to healthcare section if it exists
      const healthcareTab = page.locator('[data-testid="healthcare-tab"], button:has-text("Healthcare"), [aria-label*="healthcare"]');
      if (await healthcareTab.count() > 0) {
        await healthcareTab.click();
        await page.waitForTimeout(1000);
      }
      
      // Test different age groups
      const ageGroups = [
        { age: 25, expectedPremium: { min: 200, max: 800 } },
        { age: 45, expectedPremium: { min: 300, max: 1000 } },
        { age: 65, expectedPremium: { min: 400, max: 1200 } },
      ];
      
      for (const group of ageGroups) {
        await page.fill('[data-testid="current-age-input"]', group.age.toString());
        await page.waitForTimeout(1000);
        
        const premiumDisplay = page.locator('[data-testid="healthcare-premium"], :text("premium"):near(:text("CHF"))');
        if (await premiumDisplay.count() > 0) {
          const premiumText = await premiumDisplay.textContent();
          const premiumMatch = premiumText?.match(/CHF\s*([\d'.,]+)/);
          
          if (premiumMatch) {
            const premium = parseFloat(premiumMatch[1].replace(/[',]/g, ''));
            expect(premium).toBeGreaterThan(group.expectedPremium.min);
            expect(premium).toBeLessThan(group.expectedPremium.max);
          }
        }
      }
    });

    await test.step('Test deductible options', async () => {
      const deductibleOptions = [300, 500, 1000, 1500, 2000, 2500];
      
      const deductibleSelector = page.locator('[data-testid="deductible-selector"], select[name*="deductible"]');
      if (await deductibleSelector.count() > 0) {
        for (const deductible of deductibleOptions.slice(0, 3)) {
          await deductibleSelector.selectOption(deductible.toString());
          await page.waitForTimeout(500);
          
          // Higher deductible should result in lower premium
          const premiumDisplay = page.locator('[data-testid="healthcare-premium"]');
          if (await premiumDisplay.count() > 0) {
            const premiumText = await premiumDisplay.textContent();
            expect(premiumText).toContain('CHF');
          }
        }
      }
    });
  });

  test('should calculate Swiss social insurance correctly', async ({ page }) => {
    await test.step('Test AHV/IV/EO contributions', async () => {
      await page.fill('[data-testid="annual-income-input"]', '80000');
      await page.waitForTimeout(2000);
      
      // AHV/IV/EO should be 5.3% of income (employee portion)
      const ahvContribution = page.locator('[data-testid="ahv-contribution"], :text("AHV"):near(:text("CHF"))');
      if (await ahvContribution.count() > 0) {
        const ahvText = await ahvContribution.textContent();
        const ahvMatch = ahvText?.match(/CHF\s*([\d'.,]+)/);
        
        if (ahvMatch) {
          const ahv = parseFloat(ahvMatch[1].replace(/[',]/g, ''));
          const expectedAhv = 80000 * 0.053; // 5.3%
          expect(ahv).toBeCloseTo(expectedAhv, -2); // Within 100 CHF
        }
      }
    });

    await test.step('Test unemployment insurance (ALV)', async () => {
      const alvContribution = page.locator('[data-testid="alv-contribution"], :text("ALV"):near(:text("CHF"))');
      if (await alvContribution.count() > 0) {
        const alvText = await alvContribution.textContent();
        const alvMatch = alvText?.match(/CHF\s*([\d'.,]+)/);
        
        if (alvMatch) {
          const alv = parseFloat(alvMatch[1].replace(/[',]/g, ''));
          // ALV is 1.1% up to 148,200 CHF
          const expectedAlv = Math.min(80000, 148200) * 0.011;
          expect(alv).toBeCloseTo(expectedAlv, -1);
        }
      }
    });
  });

  test('should support Swiss German language and formatting', async ({ page }) => {
    await test.step('Test language switching to German', async () => {
      const languageSelector = page.locator('[data-testid="language-selector"], button:has-text("DE"), [aria-label*="language"]');
      if (await languageSelector.count() > 0) {
        await languageSelector.click();
        
        // Look for German option
        const germanOption = page.locator('button:has-text("Deutsch"), [data-value="de"], option[value="de"]');
        if (await germanOption.count() > 0) {
          await germanOption.click();
          await page.waitForTimeout(1000);
          
          // Verify German text appears
          const germanText = page.locator(':text("Alter"), :text("Einkommen"), :text("Ausgaben")');
          expect(await germanText.count()).toBeGreaterThan(0);
        }
      }
    });

    await test.step('Test Swiss number formatting', async () => {
      await page.fill('[data-testid="current-savings-input"]', '123456');
      await page.waitForTimeout(1000);
      
      // Should display with Swiss formatting (apostrophes for thousands)
      const formattedNumber = page.locator(':text("123\'456")');
      if (await formattedNumber.count() > 0) {
        await expect(formattedNumber).toBeVisible();
      }
    });

    await test.step('Test CHF currency formatting', async () => {
      const currencyDisplays = page.locator(':text("CHF")');
      const currencyCount = await currencyDisplays.count();
      expect(currencyCount).toBeGreaterThan(0);
      
      // Check for proper CHF formatting
      const chfPattern = page.locator(':text-matches("CHF\\s*[\\d\',]+")');
      expect(await chfPattern.count()).toBeGreaterThan(0);
    });
  });

  test('should handle Swiss retirement age scenarios', async ({ page }) => {
    await test.step('Test standard retirement ages', async () => {
      // Test male retirement age (65)
      await page.selectOption('[data-testid="gender-selector"]', 'male');
      await page.fill('[data-testid="retirement-age-input"]', '65');
      
      // Should accept without error
      const errorMessage = page.locator('[data-testid="retirement-age-error"]');
      await expect(errorMessage).not.toBeVisible();
      
      // Test female retirement age (64, transitioning to 65)
      await page.selectOption('[data-testid="gender-selector"]', 'female');
      await page.fill('[data-testid="retirement-age-input"]', '64');
      
      // Should accept without error
      await expect(errorMessage).not.toBeVisible();
    });

    await test.step('Test early retirement scenarios', async () => {
      await page.fill('[data-testid="retirement-age-input"]', '58');
      await page.waitForTimeout(1000);
      
      // Should show warning about early retirement
      const earlyRetirementWarning = page.locator('[data-testid="early-retirement-warning"], :text("early retirement")');
      if (await earlyRetirementWarning.count() > 0) {
        await expect(earlyRetirementWarning).toBeVisible();
      }
    });
  });

  test('should integrate all Swiss features in complete calculation', async ({ page }) => {
    await test.step('Complete Swiss financial planning scenario', async () => {
      // Set personal information
      await page.fill('[data-testid="current-age-input"]', '35');
      await page.fill('[data-testid="retirement-age-input"]', '65');
      
      // Set financial information
      await page.fill('[data-testid="annual-income-input"]', '120000');
      await page.fill('[data-testid="current-savings-input"]', '80000');
      await page.fill('[data-testid="monthly-expenses-input"]', '6000');
      
      // Set Swiss-specific information
      await page.selectOption('[data-testid="canton-selector"]', 'Zurich');
      await page.fill('[data-testid="pillar3a-input"]', '6883');
      
      // Wait for all calculations
      await page.waitForTimeout(3000);
      
      // Verify comprehensive results
      const fireNumber = page.locator('[data-testid="fire-number"]');
      if (await fireNumber.count() > 0) {
        const fireText = await fireNumber.textContent();
        expect(fireText).toContain('CHF');
        
        // FIRE number should be reasonable (20-30x annual expenses)
        const fireMatch = fireText?.match(/CHF\s*([\d'.,]+)/);
        if (fireMatch) {
          const fire = parseFloat(fireMatch[1].replace(/[',]/g, ''));
          const annualExpenses = 6000 * 12;
          expect(fire).toBeGreaterThan(annualExpenses * 15);
          expect(fire).toBeLessThan(annualExpenses * 40);
        }
      }
      
      // Verify tax calculations are included
      const netIncome = page.locator('[data-testid="net-income"], :text("net"):near(:text("CHF"))');
      if (await netIncome.count() > 0) {
        const netText = await netIncome.textContent();
        expect(netText).toContain('CHF');
      }
      
      // Verify charts show Swiss-specific data
      await expect(page.locator('[data-testid="net-worth-chart"]')).toBeVisible();
      await expect(page.locator('[data-testid="tax-optimization-chart"]')).toBeVisible();
    });
  });
});
