import { test, expect } from '@playwright/test';

/**
 * Comprehensive Security Tests
 * Tests data protection, privacy, XSS prevention, and security best practices
 */

test.describe('Security - Comprehensive Testing', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
  });

  test('should protect against XSS attacks', async ({ page }) => {
    await test.step('Test input sanitization', async () => {
      const xssPayloads = [
        '<script>alert("XSS")</script>',
        'javascript:alert("XSS")',
        '<img src="x" onerror="alert(\'XSS\')">',
        '<svg onload="alert(\'XSS\')">',
        '"><script>alert("XSS")</script>',
        '\';alert("XSS");//',
        '<iframe src="javascript:alert(\'XSS\')"></iframe>'
      ];

      for (const payload of xssPayloads) {
        // Test in various input fields
        const inputs = page.locator('input[type="text"], input[type="number"], textarea');
        const inputCount = await inputs.count();

        for (let i = 0; i < Math.min(inputCount, 3); i++) {
          const input = inputs.nth(i);
          
          // Clear and enter XSS payload
          await input.clear();
          await input.fill(payload);
          await page.waitForTimeout(500);
          
          // Check that script didn't execute
          const alertDialogs = page.locator('[role="alert"]:has-text("XSS")');
          await expect(alertDialogs).toHaveCount(0);
          
          // Check that content is properly escaped
          const inputValue = await input.inputValue();
          if (inputValue.includes('<script>')) {
            // Should be escaped or sanitized
            expect(inputValue).not.toContain('<script>alert');
          }
        }
      }
    });

    await test.step('Test URL parameter injection', async () => {
      // Test malicious URL parameters
      const maliciousParams = [
        '?age=<script>alert("XSS")</script>',
        '?savings=javascript:alert("XSS")',
        '?income="><script>alert("XSS")</script>',
        '?canton=<img src=x onerror=alert("XSS")>'
      ];

      for (const param of maliciousParams) {
        await page.goto(`/${param}`);
        await page.waitForTimeout(1000);
        
        // Check that no XSS executed
        const alertDialogs = page.locator('[role="alert"]:has-text("XSS")');
        await expect(alertDialogs).toHaveCount(0);
        
        // Page should still be functional
        const mainContent = page.locator('main, [role="main"], body');
        await expect(mainContent).toBeVisible();
      }
    });
  });

  test('should implement proper data protection', async ({ page }) => {
    await test.step('Test sensitive data handling', async () => {
      // Enter sensitive financial data
      await page.fill('[data-testid="current-savings-input"]', '500000');
      await page.fill('[data-testid="monthly-income-input"]', '15000');
      await page.fill('[data-testid="monthly-expenses-input"]', '8000');
      
      // Check that data is not exposed in URLs
      const currentUrl = page.url();
      expect(currentUrl).not.toContain('500000');
      expect(currentUrl).not.toContain('15000');
      expect(currentUrl).not.toContain('8000');
      
      // Check that data is not logged to console
      const consoleLogs: string[] = [];
      page.on('console', msg => {
        consoleLogs.push(msg.text());
      });
      
      await page.waitForTimeout(2000);
      
      // Sensitive data should not appear in console logs
      const sensitiveDataInLogs = consoleLogs.some(log => 
        log.includes('500000') || log.includes('15000') || log.includes('8000')
      );
      expect(sensitiveDataInLogs).toBe(false);
    });

    await test.step('Test localStorage security', async () => {
      // Set some data
      await page.fill('[data-testid="current-age-input"]', '35');
      await page.fill('[data-testid="current-savings-input"]', '100000');
      await page.waitForTimeout(1000);
      
      // Check localStorage contents
      const localStorageData = await page.evaluate(() => {
        const data: Record<string, any> = {};
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key) {
            data[key] = localStorage.getItem(key);
          }
        }
        return data;
      });
      
      // Sensitive data should be encrypted or not stored in plain text
      const dataString = JSON.stringify(localStorageData);
      
      // Check for obvious sensitive data patterns
      const hasSensitiveData = /\b\d{6,}\b/.test(dataString); // Large numbers
      if (hasSensitiveData) {
        // If sensitive data is stored, it should be encrypted/encoded
        const hasEncryption = dataString.includes('encrypted') || 
                             dataString.includes('encoded') ||
                             /^[A-Za-z0-9+/=]+$/.test(dataString); // Base64-like
        // This is a recommendation check
      }
    });

    await test.step('Test session management', async () => {
      // Check for secure session handling
      const cookies = await page.context().cookies();
      
      for (const cookie of cookies) {
        // Session cookies should have security flags
        if (cookie.name.toLowerCase().includes('session') || 
            cookie.name.toLowerCase().includes('auth')) {
          
          // Should be httpOnly for security
          expect(cookie.httpOnly).toBe(true);
          
          // Should be secure in production
          if (page.url().startsWith('https://')) {
            expect(cookie.secure).toBe(true);
          }
          
          // Should have sameSite protection
          expect(['Strict', 'Lax']).toContain(cookie.sameSite);
        }
      }
    });
  });

  test('should prevent CSRF attacks', async ({ page }) => {
    await test.step('Test CSRF token implementation', async () => {
      // Look for forms that might need CSRF protection
      const forms = page.locator('form');
      const formCount = await forms.count();
      
      for (let i = 0; i < formCount; i++) {
        const form = forms.nth(i);
        const method = await form.getAttribute('method');
        
        if (method && method.toLowerCase() !== 'get') {
          // POST forms should have CSRF protection
          const csrfToken = form.locator('input[name*="csrf"], input[name*="token"], input[type="hidden"]');
          const tokenCount = await csrfToken.count();
          
          // This is a recommendation for forms that modify data
          if (tokenCount > 0) {
            const tokenValue = await csrfToken.first().getAttribute('value');
            expect(tokenValue).toBeTruthy();
            expect(tokenValue!.length).toBeGreaterThan(10);
          }
        }
      }
    });

    await test.step('Test referrer policy', async () => {
      // Check for proper referrer policy
      const referrerPolicy = await page.evaluate(() => {
        const metaReferrer = document.querySelector('meta[name="referrer"]');
        return metaReferrer?.getAttribute('content') || 
               document.referrerPolicy ||
               'default';
      });
      
      // Should have a restrictive referrer policy
      const secureReferrerPolicies = [
        'no-referrer',
        'no-referrer-when-downgrade',
        'same-origin',
        'strict-origin',
        'strict-origin-when-cross-origin'
      ];
      
      expect(secureReferrerPolicies).toContain(referrerPolicy);
    });
  });

  test('should implement content security policy', async ({ page }) => {
    await test.step('Test CSP headers', async () => {
      const response = await page.goto('/');
      const headers = response?.headers() || {};
      
      // Check for CSP header
      const csp = headers['content-security-policy'] || 
                  headers['content-security-policy-report-only'];
      
      if (csp) {
        // CSP should restrict inline scripts
        expect(csp).toContain('script-src');
        
        // Should not allow unsafe-inline for scripts (unless with nonce/hash)
        if (csp.includes("'unsafe-inline'")) {
          // If unsafe-inline is used, should have nonce or hash
          const hasNonceOrHash = csp.includes("'nonce-") || csp.includes("'sha");
          expect(hasNonceOrHash).toBe(true);
        }
        
        // Should have object-src restriction
        expect(csp).toMatch(/object-src\s+[^;]*'none'/);
      }
    });

    await test.step('Test inline script restrictions', async () => {
      // Check that inline scripts are properly handled
      const inlineScripts = page.locator('script:not([src])');
      const scriptCount = await inlineScripts.count();
      
      for (let i = 0; i < scriptCount; i++) {
        const script = inlineScripts.nth(i);
        const hasNonce = await script.getAttribute('nonce');
        const scriptContent = await script.textContent();
        
        // Inline scripts should have nonce or be minimal/safe
        if (scriptContent && scriptContent.length > 50) {
          expect(hasNonce).toBeTruthy();
        }
      }
    });
  });

  test('should protect against clickjacking', async ({ page }) => {
    await test.step('Test X-Frame-Options header', async () => {
      const response = await page.goto('/');
      const headers = response?.headers() || {};
      
      // Check for frame protection
      const xFrameOptions = headers['x-frame-options'];
      const csp = headers['content-security-policy'];
      
      // Should have either X-Frame-Options or CSP frame-ancestors
      const hasFrameProtection = xFrameOptions || 
                                 (csp && csp.includes('frame-ancestors'));
      
      expect(hasFrameProtection).toBeTruthy();
      
      if (xFrameOptions) {
        expect(['DENY', 'SAMEORIGIN']).toContain(xFrameOptions.toUpperCase());
      }
    });

    await test.step('Test iframe embedding restrictions', async () => {
      // Test that the page cannot be embedded in a malicious iframe
      const canBeFramed = await page.evaluate(() => {
        try {
          return window.self !== window.top;
        } catch (e) {
          return false; // Frame busting worked
        }
      });
      
      // In a legitimate context, this should be false
      // (though this test runs in a legitimate context)
    });
  });

  test('should implement secure communication', async ({ page }) => {
    await test.step('Test HTTPS enforcement', async () => {
      // Check for HTTPS redirect or enforcement
      const currentUrl = page.url();
      
      if (currentUrl.startsWith('https://')) {
        // Check for HSTS header
        const response = await page.goto(currentUrl);
        const headers = response?.headers() || {};
        
        const hsts = headers['strict-transport-security'];
        if (hsts) {
          expect(hsts).toContain('max-age=');
          expect(parseInt(hsts.match(/max-age=(\d+)/)?.[1] || '0')).toBeGreaterThan(0);
        }
      }
    });

    await test.step('Test secure resource loading', async () => {
      // Check that all resources are loaded securely
      const resourceUrls: string[] = [];
      
      page.on('request', request => {
        resourceUrls.push(request.url());
      });
      
      await page.reload();
      await page.waitForLoadState('networkidle');
      
      // Filter for external resources
      const externalResources = resourceUrls.filter(url => 
        !url.startsWith(page.url().split('/').slice(0, 3).join('/'))
      );
      
      // External resources should use HTTPS
      for (const url of externalResources) {
        if (!url.startsWith('data:') && !url.startsWith('blob:')) {
          expect(url).toMatch(/^https:/);
        }
      }
    });
  });

  test('should handle authentication securely', async ({ page }) => {
    await test.step('Test admin authentication', async () => {
      // Try to access admin area
      await page.goto('/admin');
      await page.waitForTimeout(2000);
      
      const currentUrl = page.url();
      
      // Should either redirect to login or show auth form
      const isProtected = currentUrl.includes('/login') || 
                         currentUrl.includes('/auth') ||
                         await page.locator('input[type="password"]').count() > 0;
      
      if (!isProtected) {
        // If admin is accessible, check for proper protection
        const adminContent = page.locator('[data-testid="admin-content"], .admin-panel');
        const hasAdminContent = await adminContent.count() > 0;
        
        if (hasAdminContent) {
          // Admin area should have additional security measures
          const securityNotice = page.locator(':text("authenticated"), :text("authorized"), :text("admin")');
          expect(await securityNotice.count()).toBeGreaterThan(0);
        }
      }
    });

    await test.step('Test password field security', async () => {
      const passwordFields = page.locator('input[type="password"]');
      const passwordCount = await passwordFields.count();
      
      for (let i = 0; i < passwordCount; i++) {
        const field = passwordFields.nth(i);
        
        // Password fields should have autocomplete="current-password" or "new-password"
        const autocomplete = await field.getAttribute('autocomplete');
        if (autocomplete) {
          expect(['current-password', 'new-password', 'off']).toContain(autocomplete);
        }
        
        // Should not have value attribute set
        const value = await field.getAttribute('value');
        expect(value).toBeFalsy();
      }
    });
  });

  test('should implement privacy protection', async ({ page }) => {
    await test.step('Test data collection transparency', async () => {
      // Look for privacy policy or data collection notice
      const privacyLinks = page.locator('a:has-text("Privacy"), a:has-text("Data"), [href*="privacy"]');
      const privacyCount = await privacyLinks.count();
      
      if (privacyCount > 0) {
        const privacyLink = privacyLinks.first();
        await expect(privacyLink).toBeVisible();
        
        // Privacy link should be accessible
        const href = await privacyLink.getAttribute('href');
        expect(href).toBeTruthy();
      }
    });

    await test.step('Test third-party tracking protection', async () => {
      const thirdPartyRequests: string[] = [];
      
      page.on('request', request => {
        const url = request.url();
        const pageOrigin = new URL(page.url()).origin;
        const requestOrigin = new URL(url).origin;
        
        if (requestOrigin !== pageOrigin && 
            !url.startsWith('data:') && 
            !url.startsWith('blob:')) {
          thirdPartyRequests.push(url);
        }
      });
      
      await page.reload();
      await page.waitForLoadState('networkidle');
      
      // Check for known tracking domains
      const trackingDomains = [
        'google-analytics.com',
        'googletagmanager.com',
        'facebook.com',
        'doubleclick.net',
        'amazon-adsystem.com'
      ];
      
      const hasTracking = thirdPartyRequests.some(url =>
        trackingDomains.some(domain => url.includes(domain))
      );
      
      // If tracking is present, should have user consent
      if (hasTracking) {
        const consentBanner = page.locator('[data-testid="cookie-consent"], .cookie-banner, :text("cookies"), :text("consent")');
        const hasConsent = await consentBanner.count() > 0;
        // Consent should be implemented for tracking
      }
    });

    await test.step('Test data export/deletion capabilities', async () => {
      // Look for data management options
      const dataOptions = page.locator('button:has-text("Export"), button:has-text("Delete"), button:has-text("Download"), [data-testid*="export"], [data-testid*="delete"]');
      const optionCount = await dataOptions.count();
      
      if (optionCount > 0) {
        // Data management options should be accessible
        const firstOption = dataOptions.first();
        await expect(firstOption).toBeVisible();
        
        // Test export functionality if available
        const exportButton = page.locator('button:has-text("Export"), [data-testid*="export"]');
        const exportCount = await exportButton.count();
        
        if (exportCount > 0) {
          await exportButton.first().click();
          await page.waitForTimeout(1000);
          
          // Should provide some feedback or download
          const feedback = page.locator('[role="alert"], .success, .notification');
          const hasFeedback = await feedback.count() > 0;
          // Export should provide user feedback
        }
      }
    });
  });
});
