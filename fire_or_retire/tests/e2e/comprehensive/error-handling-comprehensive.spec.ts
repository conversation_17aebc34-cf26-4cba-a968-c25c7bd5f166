import { test, expect } from '@playwright/test';

/**
 * Comprehensive Error Handling and Edge Cases Tests
 * Tests application resilience and error recovery
 */

test.describe('Error Handling & Edge Cases - Comprehensive Testing', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
  });

  test('should handle invalid input gracefully', async ({ page }) => {
    await test.step('Test negative values', async () => {
      await page.fill('[data-testid="current-age-input"]', '-5');
      await page.fill('[data-testid="current-savings-input"]', '-1000');
      await page.fill('[data-testid="monthly-income-input"]', '-500');
      
      // Should show validation errors or prevent negative values
      const errors = page.locator('.error, [data-testid*="error"], [aria-invalid="true"]');
      const errorCount = await errors.count();
      
      if (errorCount > 0) {
        await expect(errors.first()).toBeVisible();
      } else {
        // If no explicit errors, values should be corrected or prevented
        const ageValue = await page.inputValue('[data-testid="current-age-input"]');
        const savingsValue = await page.inputValue('[data-testid="current-savings-input"]');
        
        expect(parseFloat(ageValue) || 0).toBeGreaterThanOrEqual(0);
        expect(parseFloat(savingsValue) || 0).toBeGreaterThanOrEqual(0);
      }
    });

    await test.step('Test extremely large values', async () => {
      await page.fill('[data-testid="current-savings-input"]', '999999999999');
      await page.fill('[data-testid="monthly-income-input"]', '1000000');
      await page.fill('[data-testid="current-age-input"]', '200');
      
      // Application should handle large numbers without crashing
      await page.waitForTimeout(3000);
      
      // Page should still be functional
      const pageTitle = await page.title();
      expect(pageTitle).toBeTruthy();
      
      // Check if calculations still work
      const fireNumber = page.locator('[data-testid="fire-number"]');
      if (await fireNumber.count() > 0) {
        const fireText = await fireNumber.textContent();
        expect(fireText).toBeTruthy();
      }
    });

    await test.step('Test non-numeric input', async () => {
      await page.fill('[data-testid="current-age-input"]', 'abc123');
      await page.fill('[data-testid="current-savings-input"]', 'invalid$$$');
      await page.fill('[data-testid="monthly-income-input"]', '!@#$%');
      
      await page.waitForTimeout(1000);
      
      // Should prevent non-numeric input or show errors
      const ageValue = await page.inputValue('[data-testid="current-age-input"]');
      const savingsValue = await page.inputValue('[data-testid="current-savings-input"]');
      const incomeValue = await page.inputValue('[data-testid="monthly-income-input"]');
      
      // Values should either be empty, contain only numbers, or show validation errors
      const numericPattern = /^[\d.,]*$/;
      expect(ageValue).toMatch(numericPattern);
      expect(savingsValue).toMatch(numericPattern);
      expect(incomeValue).toMatch(numericPattern);
    });

    await test.step('Test boundary values', async () => {
      // Test age boundaries
      await page.fill('[data-testid="current-age-input"]', '0');
      await page.fill('[data-testid="retirement-age-input"]', '150');
      
      // Test with current age > retirement age
      await page.fill('[data-testid="current-age-input"]', '70');
      await page.fill('[data-testid="retirement-age-input"]', '65');
      
      await page.waitForTimeout(1000);
      
      // Should handle boundary conditions gracefully
      const warnings = page.locator('[data-testid*="warning"], .warning, [role="alert"]');
      if (await warnings.count() > 0) {
        await expect(warnings.first()).toBeVisible();
      }
    });
  });

  test('should handle network and connectivity issues', async ({ page }) => {
    await test.step('Test offline behavior', async () => {
      // Set up some data first
      await page.fill('[data-testid="current-age-input"]', '30');
      await page.fill('[data-testid="current-savings-input"]', '50000');
      await page.fill('[data-testid="monthly-income-input"]', '8000');
      
      // Wait for initial calculations
      await page.waitForTimeout(2000);
      
      // Simulate offline
      await page.context().setOffline(true);
      
      // Try to interact with the application
      await page.fill('[data-testid="monthly-expenses-input"]', '5000');
      await page.waitForTimeout(1000);
      
      // Application should still function for basic calculations
      const fireNumber = page.locator('[data-testid="fire-number"]');
      if (await fireNumber.count() > 0) {
        await expect(fireNumber).toBeVisible();
      }
      
      // Charts should still be visible
      const charts = page.locator('.enhanced-chart-container svg');
      if (await charts.count() > 0) {
        await expect(charts.first()).toBeVisible();
      }
      
      // Restore online
      await page.context().setOffline(false);
      await page.waitForTimeout(1000);
    });

    await test.step('Test slow network conditions', async () => {
      // Simulate slow network
      await page.route('**/*', route => {
        setTimeout(() => route.continue(), 2000); // 2 second delay
      });
      
      // Try to reload page
      await page.reload();
      await page.waitForLoadState('networkidle', { timeout: 30000 });
      
      // Page should eventually load
      const mainContent = page.locator('main, [role="main"], body');
      await expect(mainContent).toBeVisible();
      
      // Remove route handler
      await page.unroute('**/*');
    });
  });

  test('should handle browser compatibility and feature detection', async ({ page }) => {
    await test.step('Test localStorage availability', async () => {
      // Test if localStorage works
      await page.evaluate(() => {
        try {
          localStorage.setItem('test', 'value');
          localStorage.removeItem('test');
          return true;
        } catch (e) {
          return false;
        }
      });
      
      // Set some data
      await page.fill('[data-testid="current-age-input"]', '35');
      await page.fill('[data-testid="current-savings-input"]', '75000');
      
      // Reload page
      await page.reload();
      await page.waitForLoadState('networkidle');
      
      // Data should persist if localStorage is available
      const ageValue = await page.inputValue('[data-testid="current-age-input"]');
      // Note: We don't assert persistence as it depends on implementation
    });

    await test.step('Test with reduced JavaScript capabilities', async () => {
      // Test basic functionality
      await page.fill('[data-testid="current-age-input"]', '40');
      await page.fill('[data-testid="retirement-age-input"]', '67');
      await page.fill('[data-testid="current-savings-input"]', '100000');
      
      // Page should remain functional
      const inputs = page.locator('input');
      const inputCount = await inputs.count();
      expect(inputCount).toBeGreaterThan(0);
      
      // Form should be submittable
      const submitButton = page.locator('button[type="submit"], [data-testid="calculate-button"]');
      if (await submitButton.count() > 0) {
        await expect(submitButton).toBeEnabled();
      }
    });
  });

  test('should handle memory and performance constraints', async ({ page }) => {
    await test.step('Test with large datasets', async () => {
      // Create scenario with long timeline
      await page.fill('[data-testid="current-age-input"]', '20');
      await page.fill('[data-testid="retirement-age-input"]', '70');
      await page.fill('[data-testid="current-savings-input"]', '10000');
      await page.fill('[data-testid="monthly-income-input"]', '5000');
      await page.fill('[data-testid="monthly-expenses-input"]', '3000');
      
      const startTime = Date.now();
      
      // Wait for calculations and chart rendering
      await page.waitForTimeout(10000);
      
      const endTime = Date.now();
      const processingTime = endTime - startTime;
      
      // Should complete within reasonable time (15 seconds)
      expect(processingTime).toBeLessThan(15000);
      
      // Charts should still be responsive
      const charts = page.locator('.enhanced-chart-container svg');
      if (await charts.count() > 0) {
        await expect(charts.first()).toBeVisible();
        
        // Test chart interaction
        await charts.first().hover();
        await page.waitForTimeout(500);
      }
    });

    await test.step('Test memory usage with multiple calculations', async () => {
      const scenarios = [
        { age: 25, savings: 20000, income: 6000, expenses: 4000 },
        { age: 30, savings: 50000, income: 8000, expenses: 5500 },
        { age: 35, savings: 100000, income: 10000, expenses: 7000 },
        { age: 40, savings: 200000, income: 12000, expenses: 8000 },
        { age: 45, savings: 350000, income: 15000, expenses: 9000 },
      ];
      
      for (const scenario of scenarios) {
        await page.fill('[data-testid="current-age-input"]', scenario.age.toString());
        await page.fill('[data-testid="current-savings-input"]', scenario.savings.toString());
        await page.fill('[data-testid="monthly-income-input"]', scenario.income.toString());
        await page.fill('[data-testid="monthly-expenses-input"]', scenario.expenses.toString());
        
        // Wait for calculations
        await page.waitForTimeout(2000);
        
        // Verify page is still responsive
        const fireNumber = page.locator('[data-testid="fire-number"]');
        if (await fireNumber.count() > 0) {
          await expect(fireNumber).toBeVisible();
        }
      }
    });
  });

  test('should handle concurrent user interactions', async ({ page }) => {
    await test.step('Test rapid input changes', async () => {
      // Rapidly change multiple inputs
      const inputs = [
        { selector: '[data-testid="current-age-input"]', values: ['25', '30', '35', '40'] },
        { selector: '[data-testid="current-savings-input"]', values: ['10000', '50000', '100000', '200000'] },
        { selector: '[data-testid="monthly-income-input"]', values: ['5000', '7000', '9000', '12000'] },
      ];
      
      for (let i = 0; i < 4; i++) {
        for (const input of inputs) {
          await page.fill(input.selector, input.values[i]);
          await page.waitForTimeout(100); // Very short delay
        }
      }
      
      // Wait for final calculations
      await page.waitForTimeout(3000);
      
      // Application should handle rapid changes gracefully
      const fireNumber = page.locator('[data-testid="fire-number"]');
      if (await fireNumber.count() > 0) {
        const fireText = await fireNumber.textContent();
        expect(fireText).toBeTruthy();
        expect(fireText).toContain('CHF');
      }
    });

    await test.step('Test simultaneous chart interactions', async () => {
      // Set up data for charts
      await page.fill('[data-testid="current-age-input"]', '32');
      await page.fill('[data-testid="current-savings-input"]', '60000');
      await page.fill('[data-testid="monthly-income-input"]', '8500');
      await page.waitForTimeout(3000);
      
      const charts = page.locator('.enhanced-chart-container svg');
      const chartCount = await charts.count();
      
      if (chartCount > 1) {
        // Hover over multiple charts quickly
        for (let i = 0; i < Math.min(chartCount, 3); i++) {
          await charts.nth(i).hover();
          await page.waitForTimeout(200);
        }
        
        // Charts should remain functional
        await expect(charts.first()).toBeVisible();
      }
    });
  });

  test('should provide meaningful error messages', async ({ page }) => {
    await test.step('Test validation error messages', async () => {
      // Trigger validation errors
      await page.fill('[data-testid="current-age-input"]', '150');
      await page.fill('[data-testid="retirement-age-input"]', '50');
      
      // Look for error messages
      const errorMessages = page.locator('.error, [data-testid*="error"], [role="alert"]');
      const errorCount = await errorMessages.count();
      
      if (errorCount > 0) {
        const errorText = await errorMessages.first().textContent();
        expect(errorText).toBeTruthy();
        expect(errorText.length).toBeGreaterThan(5); // Should be meaningful
      }
    });

    await test.step('Test calculation error handling', async () => {
      // Set up scenario that might cause calculation issues
      await page.fill('[data-testid="current-age-input"]', '65');
      await page.fill('[data-testid="retirement-age-input"]', '65');
      await page.fill('[data-testid="current-savings-input"]', '0');
      await page.fill('[data-testid="monthly-income-input"]', '0');
      
      await page.waitForTimeout(2000);
      
      // Should handle edge case gracefully
      const warnings = page.locator('[data-testid*="warning"], .warning');
      if (await warnings.count() > 0) {
        const warningText = await warnings.first().textContent();
        expect(warningText).toBeTruthy();
      }
    });
  });
});
