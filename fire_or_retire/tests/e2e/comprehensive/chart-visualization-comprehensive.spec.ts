import { test, expect } from '@playwright/test';

/**
 * Comprehensive Chart Visualization Tests
 * Tests all chart functionality including D3 charts, interactions, and responsiveness
 */

test.describe('Chart Visualization - Comprehensive Testing', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Set up basic data for charts
    await page.fill('[data-testid="current-age-input"]', '30');
    await page.fill('[data-testid="retirement-age-input"]', '65');
    await page.fill('[data-testid="current-savings-input"]', '50000');
    await page.fill('[data-testid="monthly-income-input"]', '8000');
    await page.fill('[data-testid="monthly-expenses-input"]', '5000');
    
    // Wait for charts to render
    await page.waitForTimeout(3000);
  });

  test('should display all required charts with full width', async ({ page }) => {
    await test.step('Verify all charts are present', async () => {
      // Check for main financial charts
      await expect(page.locator('[data-testid="net-worth-chart"]')).toBeVisible();
      await expect(page.locator('[data-testid="savings-rate-chart"]')).toBeVisible();
      await expect(page.locator('[data-testid="fire-progress-chart"]')).toBeVisible();
    });

    await test.step('Verify charts use full width', async () => {
      const chartContainer = page.locator('.enhanced-chart-container').first();
      const containerWidth = await chartContainer.evaluate(el => el.clientWidth);
      
      const chart = chartContainer.locator('svg').first();
      const chartWidth = await chart.evaluate(el => el.clientWidth);
      
      // Chart should use most of the container width (allowing for small margins)
      expect(chartWidth).toBeGreaterThan(containerWidth * 0.9);
    });

    await test.step('Verify charts have proper height', async () => {
      const charts = page.locator('.enhanced-chart-container svg');
      const chartCount = await charts.count();
      
      for (let i = 0; i < chartCount; i++) {
        const chart = charts.nth(i);
        const height = await chart.evaluate(el => el.clientHeight);
        expect(height).toBeGreaterThan(400); // Minimum height requirement
      }
    });
  });

  test('should display years on x-axis for all charts', async ({ page }) => {
    await test.step('Check x-axis year labels', async () => {
      const charts = page.locator('.enhanced-chart-container svg');
      const chartCount = await charts.count();
      
      for (let i = 0; i < chartCount; i++) {
        const chart = charts.nth(i);
        
        // Check for x-axis labels
        const xAxisLabels = chart.locator('.x-axis text');
        const labelCount = await xAxisLabels.count();
        expect(labelCount).toBeGreaterThan(0);
        
        // Verify labels contain years (4-digit numbers)
        const firstLabel = await xAxisLabels.first().textContent();
        expect(firstLabel).toMatch(/^\d{4}$/); // Should be a 4-digit year
        
        // Check that we have multiple year labels
        if (labelCount > 1) {
          const lastLabel = await xAxisLabels.last().textContent();
          expect(lastLabel).toMatch(/^\d{4}$/);
          
          // Verify years are in chronological order
          const firstYear = parseInt(firstLabel || '0');
          const lastYear = parseInt(lastLabel || '0');
          expect(lastYear).toBeGreaterThan(firstYear);
        }
      }
    });

    await test.step('Verify all years in range are displayed', async () => {
      const netWorthChart = page.locator('[data-testid="net-worth-chart"] svg');
      const xAxisLabels = netWorthChart.locator('.x-axis text');
      const labelTexts = await xAxisLabels.allTextContents();
      
      // Should have consecutive years
      const years = labelTexts.map(text => parseInt(text)).filter(year => !isNaN(year));
      years.sort((a, b) => a - b);
      
      // Check for consecutive years (allowing for some gaps in long ranges)
      if (years.length > 2) {
        const yearSpan = years[years.length - 1] - years[0];
        expect(yearSpan).toBeGreaterThan(0);
        expect(yearSpan).toBeLessThan(50); // Reasonable range
      }
    });
  });

  test('should handle chart interactions correctly', async ({ page }) => {
    await test.step('Test hover interactions', async () => {
      const netWorthChart = page.locator('[data-testid="net-worth-chart"] svg');
      
      // Hover over chart area
      await netWorthChart.hover();
      
      // Look for data points or interactive elements
      const dataPoints = netWorthChart.locator('circle, .data-point');
      const dataPointCount = await dataPoints.count();
      
      if (dataPointCount > 0) {
        // Test hovering over data points
        await dataPoints.first().hover();
        
        // Check for tooltip or hover effects
        const tooltip = page.locator('.tooltip, [data-testid="chart-tooltip"], [class*="tooltip"]');
        await expect(tooltip).toBeVisible({ timeout: 5000 });
      }
    });

    await test.step('Test chart responsiveness', async () => {
      // Test different viewport sizes
      const viewports = [
        { width: 1920, height: 1080 }, // Desktop large
        { width: 1280, height: 720 },  // Desktop standard
        { width: 768, height: 1024 },  // Tablet
        { width: 375, height: 667 },    // Mobile
      ];

      for (const viewport of viewports) {
        await page.setViewportSize(viewport);
        await page.waitForTimeout(1000);
        
        // Charts should still be visible and properly sized
        const charts = page.locator('.enhanced-chart-container svg');
        const chartCount = await charts.count();
        
        for (let i = 0; i < chartCount; i++) {
          const chart = charts.nth(i);
          await expect(chart).toBeVisible();
          
          const width = await chart.evaluate(el => el.clientWidth);
          expect(width).toBeGreaterThan(200); // Minimum width
          expect(width).toBeLessThan(viewport.width + 100); // Not larger than viewport
        }
      }
      
      // Reset to standard desktop size
      await page.setViewportSize({ width: 1280, height: 720 });
    });
  });

  test('should handle different data scenarios', async ({ page }) => {
    const scenarios = [
      {
        name: 'Short timeline (5 years)',
        currentAge: 60,
        retirementAge: 65,
        expectedYears: 5,
      },
      {
        name: 'Long timeline (40 years)',
        currentAge: 25,
        retirementAge: 65,
        expectedYears: 40,
      },
      {
        name: 'Medium timeline (20 years)',
        currentAge: 45,
        retirementAge: 65,
        expectedYears: 20,
      },
    ];

    for (const scenario of scenarios) {
      await test.step(`Test ${scenario.name}`, async () => {
        // Set scenario data
        await page.fill('[data-testid="current-age-input"]', scenario.currentAge.toString());
        await page.fill('[data-testid="retirement-age-input"]', scenario.retirementAge.toString());
        
        // Wait for charts to update
        await page.waitForTimeout(2000);
        
        // Verify x-axis shows appropriate year range
        const netWorthChart = page.locator('[data-testid="net-worth-chart"] svg');
        const xAxisLabels = netWorthChart.locator('.x-axis text');
        const labelTexts = await xAxisLabels.allTextContents();
        const years = labelTexts.map(text => parseInt(text)).filter(year => !isNaN(year));
        
        if (years.length > 1) {
          years.sort((a, b) => a - b);
          const actualYearSpan = years[years.length - 1] - years[0];
          
          // Should roughly match expected timeline (allowing for some variance)
          expect(actualYearSpan).toBeGreaterThan(scenario.expectedYears * 0.5);
          expect(actualYearSpan).toBeLessThan(scenario.expectedYears * 2);
        }
      });
    }
  });

  test('should support chart type switching', async ({ page }) => {
    // Look for chart type controls
    const chartTypeButtons = page.locator('[data-testid*="chart-type"], [class*="chart-type"], button[aria-label*="chart"]');
    const buttonCount = await chartTypeButtons.count();
    
    if (buttonCount > 0) {
      await test.step('Test line chart type', async () => {
        const lineButton = page.locator('button:has-text("Line"), [data-testid="line-chart-button"]');
        if (await lineButton.count() > 0) {
          await lineButton.click();
          await page.waitForTimeout(1000);
          
          // Verify line chart elements
          const lines = page.locator('svg path[stroke]');
          expect(await lines.count()).toBeGreaterThan(0);
        }
      });

      await test.step('Test area chart type', async () => {
        const areaButton = page.locator('button:has-text("Area"), [data-testid="area-chart-button"]');
        if (await areaButton.count() > 0) {
          await areaButton.click();
          await page.waitForTimeout(1000);
          
          // Verify area chart elements
          const areas = page.locator('svg path[fill]');
          expect(await areas.count()).toBeGreaterThan(0);
        }
      });
    }
  });

  test('should handle chart performance with large datasets', async ({ page }) => {
    await test.step('Test with extended timeline', async () => {
      // Set a very long timeline to test performance
      await page.fill('[data-testid="current-age-input"]', '20');
      await page.fill('[data-testid="retirement-age-input"]', '70');
      
      const startTime = Date.now();
      
      // Wait for charts to render
      await page.waitForTimeout(5000);
      
      const endTime = Date.now();
      const renderTime = endTime - startTime;
      
      // Should render within reasonable time (10 seconds)
      expect(renderTime).toBeLessThan(10000);
      
      // Charts should still be functional
      const charts = page.locator('.enhanced-chart-container svg');
      const chartCount = await charts.count();
      expect(chartCount).toBeGreaterThan(0);
      
      // X-axis should handle many years gracefully
      const xAxisLabels = charts.first().locator('.x-axis text');
      const labelCount = await xAxisLabels.count();
      expect(labelCount).toBeGreaterThan(0);
    });
  });

  test('should maintain chart quality in dark mode', async ({ page }) => {
    // Toggle dark mode if available
    const darkModeToggle = page.locator('[data-testid="dark-mode-toggle"], button:has-text("Dark"), [aria-label*="dark"]');
    
    if (await darkModeToggle.count() > 0) {
      await darkModeToggle.click();
      await page.waitForTimeout(1000);
      
      // Verify charts are still visible and properly styled
      const charts = page.locator('.enhanced-chart-container svg');
      const chartCount = await charts.count();
      
      for (let i = 0; i < chartCount; i++) {
        const chart = charts.nth(i);
        await expect(chart).toBeVisible();
        
        // Check that text is visible (not same color as background)
        const xAxisText = chart.locator('.x-axis text').first();
        if (await xAxisText.count() > 0) {
          const textColor = await xAxisText.evaluate(el => getComputedStyle(el).fill);
          expect(textColor).not.toBe('rgb(0, 0, 0)'); // Should not be black on dark background
        }
      }
    }
  });
});
