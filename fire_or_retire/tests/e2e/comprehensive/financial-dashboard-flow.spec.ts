import { test, expect } from '@playwright/test';
import { DashboardPage } from '../pages/dashboard-page';

/**
 * Comprehensive Financial Dashboard Flow Tests
 * Tests the complete user journey through the financial dashboard
 */

test.describe('Financial Dashboard - Complete User Flow', () => {
  let dashboardPage: DashboardPage;

  test.beforeEach(async ({ page }) => {
    dashboardPage = new DashboardPage(page);
    await dashboardPage.goto();
    await dashboardPage.waitForLoad();
  });

  test('should complete full financial planning workflow', async ({ page }) => {
    // Step 1: Set basic financial information
    await test.step('Enter basic financial information', async () => {
      await dashboardPage.setCurrentAge(30);
      await dashboardPage.setRetirementAge(65);
      await dashboardPage.setCurrentSavings(50000);
      await dashboardPage.setMonthlyIncome(8000);
      await dashboardPage.setMonthlyExpenses(5000);
    });

    // Step 2: Configure Swiss-specific settings
    await test.step('Configure Swiss tax settings', async () => {
      await dashboardPage.selectCanton('Zurich');
      await dashboardPage.setTaxableIncome(96000);
      await dashboardPage.enablePillar3a(true);
      await dashboardPage.setPillar3aContribution(6883);
    });

    // Step 3: Set investment parameters
    await test.step('Configure investment settings', async () => {
      await dashboardPage.setExpectedReturn(7);
      await dashboardPage.setInflationRate(2);
      await dashboardPage.setWithdrawalRate(4);
    });

    // Step 4: Verify calculations
    await test.step('Verify FIRE calculations', async () => {
      const fireNumber = await dashboardPage.getFireNumber();
      expect(fireNumber).toBeGreaterThan(0);
      
      const yearsToFire = await dashboardPage.getYearsToFire();
      expect(yearsToFire).toBeGreaterThan(0);
      expect(yearsToFire).toBeLessThan(50);
    });

    // Step 5: Check data visualization
    await test.step('Verify charts are displayed', async () => {
      await expect(page.locator('[data-testid="net-worth-chart"]')).toBeVisible();
      await expect(page.locator('[data-testid="savings-rate-chart"]')).toBeVisible();
      await expect(page.locator('[data-testid="fire-progress-chart"]')).toBeVisible();
    });

    // Step 6: Test chart interactions
    await test.step('Test chart interactions', async () => {
      const netWorthChart = page.locator('[data-testid="net-worth-chart"]');
      await netWorthChart.hover();
      
      // Check if tooltip appears
      await expect(page.locator('.tooltip, [data-testid="chart-tooltip"]')).toBeVisible();
      
      // Check if years are displayed on x-axis
      const xAxisLabels = page.locator('.x-axis text');
      await expect(xAxisLabels.first()).toBeVisible();
    });

    // Step 7: Test responsive behavior
    await test.step('Test mobile responsiveness', async () => {
      await page.setViewportSize({ width: 375, height: 667 });
      await page.waitForTimeout(1000);
      
      // Charts should still be visible on mobile
      await expect(page.locator('[data-testid="net-worth-chart"]')).toBeVisible();
      
      // Reset viewport
      await page.setViewportSize({ width: 1280, height: 720 });
    });
  });

  test('should handle data persistence across page reloads', async ({ page }) => {
    // Enter data
    await dashboardPage.setCurrentAge(35);
    await dashboardPage.setMonthlyIncome(9000);
    await dashboardPage.setCurrentSavings(75000);
    
    // Reload page
    await page.reload();
    await dashboardPage.waitForLoad();
    
    // Verify data persisted
    const age = await dashboardPage.getCurrentAge();
    expect(age).toBe(35);
    
    const income = await dashboardPage.getMonthlyIncome();
    expect(income).toBe(9000);
    
    const savings = await dashboardPage.getCurrentSavings();
    expect(savings).toBe(75000);
  });

  test('should validate input constraints and error handling', async ({ page }) => {
    // Test negative values
    await test.step('Test negative value validation', async () => {
      await dashboardPage.setCurrentAge(-5);
      await expect(page.locator('[data-testid="age-error"]')).toBeVisible();
    });

    // Test unrealistic values
    await test.step('Test unrealistic value validation', async () => {
      await dashboardPage.setRetirementAge(150);
      await expect(page.locator('[data-testid="retirement-age-error"]')).toBeVisible();
    });

    // Test required fields
    await test.step('Test required field validation', async () => {
      await dashboardPage.clearCurrentAge();
      await page.click('[data-testid="calculate-button"]');
      await expect(page.locator('[data-testid="required-field-error"]')).toBeVisible();
    });
  });

  test('should support multiple calculation scenarios', async ({ page }) => {
    const scenarios = [
      {
        name: 'Conservative Scenario',
        age: 25,
        income: 6000,
        expenses: 4000,
        savings: 20000,
        expectedReturn: 5,
        inflation: 2.5,
      },
      {
        name: 'Aggressive Scenario',
        age: 30,
        income: 12000,
        expenses: 6000,
        savings: 100000,
        expectedReturn: 9,
        inflation: 2,
      },
      {
        name: 'Moderate Scenario',
        age: 40,
        income: 8000,
        expenses: 5500,
        savings: 200000,
        expectedReturn: 7,
        inflation: 2.2,
      },
    ];

    for (const scenario of scenarios) {
      await test.step(`Test ${scenario.name}`, async () => {
        await dashboardPage.setCurrentAge(scenario.age);
        await dashboardPage.setMonthlyIncome(scenario.income);
        await dashboardPage.setMonthlyExpenses(scenario.expenses);
        await dashboardPage.setCurrentSavings(scenario.savings);
        await dashboardPage.setExpectedReturn(scenario.expectedReturn);
        await dashboardPage.setInflationRate(scenario.inflation);
        
        // Wait for calculations
        await page.waitForTimeout(2000);
        
        // Verify results are reasonable
        const fireNumber = await dashboardPage.getFireNumber();
        expect(fireNumber).toBeGreaterThan(scenario.expenses * 12 * 20); // At least 20x annual expenses
        
        const yearsToFire = await dashboardPage.getYearsToFire();
        expect(yearsToFire).toBeGreaterThan(0);
        expect(yearsToFire).toBeLessThan(60);
      });
    }
  });

  test('should handle currency formatting correctly', async ({ page }) => {
    await dashboardPage.setCurrentSavings(123456.78);
    await dashboardPage.setMonthlyIncome(9876.54);
    
    // Check Swiss CHF formatting
    await expect(page.locator('[data-testid="savings-display"]')).toContainText('CHF');
    await expect(page.locator('[data-testid="income-display"]')).toContainText('CHF');
    
    // Check number formatting (Swiss uses apostrophes for thousands)
    await expect(page.locator('[data-testid="savings-display"]')).toContainText('123\'456');
  });

  test('should support keyboard navigation and accessibility', async ({ page }) => {
    // Test tab navigation
    await page.keyboard.press('Tab');
    await expect(page.locator(':focus')).toBeVisible();
    
    // Test form submission with Enter key
    await dashboardPage.setCurrentAge(30);
    await page.keyboard.press('Enter');
    
    // Check ARIA labels
    const ageInput = page.locator('[data-testid="current-age-input"]');
    await expect(ageInput).toHaveAttribute('aria-label');
    
    // Check for proper heading structure
    await expect(page.locator('h1')).toBeVisible();
    await expect(page.locator('h2')).toHaveCount({ min: 1 });
  });
});
