import { test, expect } from '@playwright/test';

/**
 * Comprehensive Performance Tests
 * Tests application performance, load times, and resource usage
 */

test.describe('Performance - Comprehensive Testing', () => {
  test.beforeEach(async ({ page }) => {
    // Enable performance monitoring
    await page.goto('/', { waitUntil: 'networkidle' });
  });

  test('should meet Core Web Vitals standards', async ({ page }) => {
    await test.step('Measure Largest Contentful Paint (LCP)', async () => {
      const lcp = await page.evaluate(() => {
        return new Promise((resolve) => {
          new PerformanceObserver((list) => {
            const entries = list.getEntries();
            const lastEntry = entries[entries.length - 1];
            resolve(lastEntry.startTime);
          }).observe({ entryTypes: ['largest-contentful-paint'] });
          
          // Fallback timeout
          setTimeout(() => resolve(0), 5000);
        });
      });
      
      // LCP should be under 2.5 seconds for good performance
      expect(lcp).toBeLessThan(2500);
    });

    await test.step('Measure First Input Delay (FID)', async () => {
      // Simulate user interaction
      await page.click('[data-testid="current-age-input"]');
      
      const fid = await page.evaluate(() => {
        return new Promise((resolve) => {
          new PerformanceObserver((list) => {
            const entries = list.getEntries();
            if (entries.length > 0) {
              resolve(entries[0].processingStart - entries[0].startTime);
            }
          }).observe({ entryTypes: ['first-input'] });
          
          // Fallback
          setTimeout(() => resolve(0), 1000);
        });
      });
      
      // FID should be under 100ms for good performance
      if (fid > 0) {
        expect(fid).toBeLessThan(100);
      }
    });

    await test.step('Measure Cumulative Layout Shift (CLS)', async () => {
      // Add some data to trigger layout changes
      await page.fill('[data-testid="current-age-input"]', '30');
      await page.fill('[data-testid="current-savings-input"]', '50000');
      await page.waitForTimeout(3000);
      
      const cls = await page.evaluate(() => {
        return new Promise((resolve) => {
          let clsValue = 0;
          new PerformanceObserver((list) => {
            for (const entry of list.getEntries()) {
              if (!entry.hadRecentInput) {
                clsValue += entry.value;
              }
            }
            resolve(clsValue);
          }).observe({ entryTypes: ['layout-shift'] });
          
          setTimeout(() => resolve(clsValue), 3000);
        });
      });
      
      // CLS should be under 0.1 for good performance
      expect(cls).toBeLessThan(0.1);
    });
  });

  test('should load efficiently with optimal resource usage', async ({ page }) => {
    await test.step('Measure page load performance', async () => {
      const startTime = Date.now();
      
      await page.goto('/', { waitUntil: 'load' });
      
      const loadTime = Date.now() - startTime;
      
      // Page should load within 3 seconds
      expect(loadTime).toBeLessThan(3000);
    });

    await test.step('Analyze network requests', async () => {
      const requests: any[] = [];
      
      page.on('request', request => {
        requests.push({
          url: request.url(),
          method: request.method(),
          resourceType: request.resourceType(),
        });
      });
      
      await page.reload();
      await page.waitForLoadState('networkidle');
      
      // Should not have excessive requests
      expect(requests.length).toBeLessThan(50);
      
      // Check for efficient resource loading
      const jsRequests = requests.filter(r => r.resourceType === 'script');
      const cssRequests = requests.filter(r => r.resourceType === 'stylesheet');
      
      // Should have reasonable number of JS/CSS files
      expect(jsRequests.length).toBeLessThan(20);
      expect(cssRequests.length).toBeLessThan(10);
    });

    await test.step('Measure memory usage', async () => {
      // Trigger memory-intensive operations
      await page.fill('[data-testid="current-age-input"]', '20');
      await page.fill('[data-testid="retirement-age-input"]', '70');
      await page.fill('[data-testid="current-savings-input"]', '10000');
      await page.waitForTimeout(5000);
      
      const memoryInfo = await page.evaluate(() => {
        return (performance as any).memory ? {
          usedJSHeapSize: (performance as any).memory.usedJSHeapSize,
          totalJSHeapSize: (performance as any).memory.totalJSHeapSize,
          jsHeapSizeLimit: (performance as any).memory.jsHeapSizeLimit,
        } : null;
      });
      
      if (memoryInfo) {
        // Memory usage should be reasonable (under 100MB)
        expect(memoryInfo.usedJSHeapSize).toBeLessThan(100 * 1024 * 1024);
      }
    });
  });

  test('should handle chart rendering performance efficiently', async ({ page }) => {
    await test.step('Measure chart rendering time', async () => {
      const startTime = Date.now();
      
      // Set up data for charts
      await page.fill('[data-testid="current-age-input"]', '30');
      await page.fill('[data-testid="retirement-age-input"]', '65');
      await page.fill('[data-testid="current-savings-input"]', '50000');
      await page.fill('[data-testid="monthly-income-input"]', '8000');
      await page.fill('[data-testid="monthly-expenses-input"]', '5000');
      
      // Wait for charts to render
      await expect(page.locator('.enhanced-chart-container svg').first()).toBeVisible();
      
      const renderTime = Date.now() - startTime;
      
      // Charts should render within 5 seconds
      expect(renderTime).toBeLessThan(5000);
    });

    await test.step('Test chart interaction performance', async () => {
      const charts = page.locator('.enhanced-chart-container svg');
      const chartCount = await charts.count();
      
      if (chartCount > 0) {
        const startTime = Date.now();
        
        // Test hover interactions
        for (let i = 0; i < Math.min(chartCount, 3); i++) {
          await charts.nth(i).hover();
          await page.waitForTimeout(100);
        }
        
        const interactionTime = Date.now() - startTime;
        
        // Interactions should be responsive (under 1 second total)
        expect(interactionTime).toBeLessThan(1000);
      }
    });

    await test.step('Test chart responsiveness during data changes', async () => {
      const values = ['25000', '75000', '125000', '200000'];
      
      const startTime = Date.now();
      
      for (const value of values) {
        await page.fill('[data-testid="current-savings-input"]', value);
        await page.waitForTimeout(500);
      }
      
      const updateTime = Date.now() - startTime;
      
      // Chart updates should be smooth (under 3 seconds for all changes)
      expect(updateTime).toBeLessThan(3000);
    });
  });

  test('should maintain performance across different scenarios', async ({ page }) => {
    const scenarios = [
      {
        name: 'Young Professional',
        age: 25,
        retirement: 65,
        savings: 20000,
        income: 6000,
        expenses: 4000,
      },
      {
        name: 'Mid-Career',
        age: 40,
        retirement: 65,
        savings: 200000,
        income: 12000,
        expenses: 8000,
      },
      {
        name: 'Pre-Retirement',
        age: 60,
        retirement: 67,
        savings: 800000,
        income: 15000,
        expenses: 10000,
      },
    ];

    for (const scenario of scenarios) {
      await test.step(`Test performance for ${scenario.name}`, async () => {
        const startTime = Date.now();
        
        await page.fill('[data-testid="current-age-input"]', scenario.age.toString());
        await page.fill('[data-testid="retirement-age-input"]', scenario.retirement.toString());
        await page.fill('[data-testid="current-savings-input"]', scenario.savings.toString());
        await page.fill('[data-testid="monthly-income-input"]', scenario.income.toString());
        await page.fill('[data-testid="monthly-expenses-input"]', scenario.expenses.toString());
        
        // Wait for calculations and rendering
        await page.waitForTimeout(3000);
        
        const processingTime = Date.now() - startTime;
        
        // Each scenario should process within 5 seconds
        expect(processingTime).toBeLessThan(5000);
        
        // Verify results are displayed
        const fireNumber = page.locator('[data-testid="fire-number"]');
        if (await fireNumber.count() > 0) {
          await expect(fireNumber).toBeVisible();
        }
      });
    }
  });

  test('should handle concurrent operations efficiently', async ({ page }) => {
    await test.step('Test multiple simultaneous input changes', async () => {
      const startTime = Date.now();
      
      // Simulate rapid user input
      const promises = [
        page.fill('[data-testid="current-age-input"]', '35'),
        page.fill('[data-testid="current-savings-input"]', '100000'),
        page.fill('[data-testid="monthly-income-input"]', '9000'),
        page.fill('[data-testid="monthly-expenses-input"]', '6000'),
      ];
      
      await Promise.all(promises);
      
      // Wait for all calculations to complete
      await page.waitForTimeout(3000);
      
      const totalTime = Date.now() - startTime;
      
      // Concurrent operations should complete efficiently
      expect(totalTime).toBeLessThan(5000);
    });

    await test.step('Test chart rendering with frequent updates', async () => {
      const startTime = Date.now();
      
      // Rapidly change values to test chart update performance
      const savingsValues = ['50000', '75000', '100000', '125000', '150000'];
      
      for (const value of savingsValues) {
        await page.fill('[data-testid="current-savings-input"]', value);
        await page.waitForTimeout(200);
      }
      
      // Wait for final rendering
      await page.waitForTimeout(2000);
      
      const updateTime = Date.now() - startTime;
      
      // Frequent updates should be handled efficiently
      expect(updateTime).toBeLessThan(4000);
      
      // Charts should still be functional
      const charts = page.locator('.enhanced-chart-container svg');
      if (await charts.count() > 0) {
        await expect(charts.first()).toBeVisible();
      }
    });
  });

  test('should optimize for mobile performance', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    await test.step('Test mobile load performance', async () => {
      const startTime = Date.now();
      
      await page.reload();
      await page.waitForLoadState('networkidle');
      
      const loadTime = Date.now() - startTime;
      
      // Mobile should load within 5 seconds
      expect(loadTime).toBeLessThan(5000);
    });

    await test.step('Test mobile chart performance', async () => {
      await page.fill('[data-testid="current-age-input"]', '30');
      await page.fill('[data-testid="current-savings-input"]', '50000');
      
      const startTime = Date.now();
      
      // Wait for mobile charts to render
      await expect(page.locator('.enhanced-chart-container svg').first()).toBeVisible();
      
      const renderTime = Date.now() - startTime;
      
      // Mobile charts should render within 6 seconds
      expect(renderTime).toBeLessThan(6000);
    });

    await test.step('Test mobile interaction performance', async () => {
      const charts = page.locator('.enhanced-chart-container svg');
      
      if (await charts.count() > 0) {
        const startTime = Date.now();
        
        // Test touch interaction
        await charts.first().tap();
        await page.waitForTimeout(500);
        
        const interactionTime = Date.now() - startTime;
        
        // Mobile interactions should be responsive
        expect(interactionTime).toBeLessThan(1000);
      }
    });
  });

  test('should handle performance under stress conditions', async ({ page }) => {
    await test.step('Test with maximum timeline', async () => {
      const startTime = Date.now();
      
      // Set maximum reasonable timeline
      await page.fill('[data-testid="current-age-input"]', '18');
      await page.fill('[data-testid="retirement-age-input"]', '70');
      await page.fill('[data-testid="current-savings-input"]', '5000');
      await page.fill('[data-testid="monthly-income-input"]', '4000');
      
      // Wait for complex calculations
      await page.waitForTimeout(8000);
      
      const processingTime = Date.now() - startTime;
      
      // Even complex scenarios should complete within 10 seconds
      expect(processingTime).toBeLessThan(10000);
      
      // Verify functionality is maintained
      const fireNumber = page.locator('[data-testid="fire-number"]');
      if (await fireNumber.count() > 0) {
        await expect(fireNumber).toBeVisible();
      }
    });

    await test.step('Test performance degradation over time', async () => {
      // Perform multiple calculation cycles
      const cycles = 5;
      const times: number[] = [];
      
      for (let i = 0; i < cycles; i++) {
        const startTime = Date.now();
        
        await page.fill('[data-testid="current-savings-input"]', (50000 + i * 25000).toString());
        await page.waitForTimeout(2000);
        
        const cycleTime = Date.now() - startTime;
        times.push(cycleTime);
      }
      
      // Performance should not degrade significantly over time
      const firstTime = times[0];
      const lastTime = times[times.length - 1];
      
      // Last cycle should not be more than 50% slower than first
      expect(lastTime).toBeLessThan(firstTime * 1.5);
    });
  });
});
