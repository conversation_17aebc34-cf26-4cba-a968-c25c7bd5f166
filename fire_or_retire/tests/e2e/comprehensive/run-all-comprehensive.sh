#!/bin/bash

# Comprehensive E2E Test Suite Runner
# Executes all 10 comprehensive test suites with detailed reporting

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Test suite definitions
declare -A TEST_SUITES=(
    ["dashboard"]="financial-dashboard-flow.spec.ts|High|5-8 min|Complete user journey through financial planning workflow"
    ["charts"]="chart-visualization-comprehensive.spec.ts|High|8-12 min|Comprehensive testing of D3 charts, interactions, and responsiveness"
    ["swiss"]="swiss-features-comprehensive.spec.ts|High|10-15 min|Swiss-specific functionality including tax, pillar systems, and healthcare"
    ["data-integrity"]="data-integrity-comprehensive.spec.ts|High|15-20 min|Financial calculation accuracy and data validation"
    ["mobile"]="mobile-responsive-comprehensive.spec.ts|Medium|12-18 min|Cross-device compatibility and responsive design testing"
    ["errors"]="error-handling-comprehensive.spec.ts|Medium|6-10 min|Edge cases, error recovery, and resilience testing"
    ["accessibility"]="accessibility-comprehensive.spec.ts|Medium|10-15 min|WCAG 2.1 compliance and inclusive design testing"
    ["i18n"]="i18n-localization-comprehensive.spec.ts|Medium|8-12 min|Swiss multilingual support and localization"
    ["performance"]="performance-comprehensive.spec.ts|Low|8-12 min|Performance benchmarks, Core Web Vitals, and optimization testing"
    ["security"]="security-comprehensive.spec.ts|Low|12-18 min|Security best practices and vulnerability testing"
)

# Global variables
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0
START_TIME=$(date +%s)
RESULTS_DIR="test-results/comprehensive"
LOG_FILE="$RESULTS_DIR/test-execution.log"

# Create results directory
mkdir -p "$RESULTS_DIR"

# Function to print colored output
print_color() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to log messages
log_message() {
    local message="[$(date '+%Y-%m-%d %H:%M:%S')] $1"
    echo "$message" >> "$LOG_FILE"
    echo "$message"
}

# Function to print header
print_header() {
    print_color $CYAN "╔══════════════════════════════════════════════════════════════════════════════╗"
    print_color $CYAN "║                    Swiss Budget Pro - Comprehensive E2E Tests                ║"
    print_color $CYAN "║                              10 Test Suites Available                        ║"
    print_color $CYAN "╚══════════════════════════════════════════════════════════════════════════════╝"
    echo
}

# Function to print test suite info
print_suite_info() {
    local suite_name=$1
    local suite_info=${TEST_SUITES[$suite_name]}
    IFS='|' read -r file priority time description <<< "$suite_info"
    
    print_color $BLUE "┌─ Test Suite: $suite_name"
    print_color $BLUE "├─ File: $file"
    print_color $BLUE "├─ Priority: $priority"
    print_color $BLUE "├─ Estimated Time: $time"
    print_color $BLUE "└─ Description: $description"
    echo
}

# Function to run a single test suite
run_test_suite() {
    local suite_name=$1
    local suite_info=${TEST_SUITES[$suite_name]}
    IFS='|' read -r file priority time description <<< "$suite_info"
    
    print_color $YELLOW "🚀 Starting: $suite_name ($priority priority)"
    log_message "Starting test suite: $suite_name"
    
    local suite_start_time=$(date +%s)
    local test_file="tests/e2e/comprehensive/$file"
    
    # Run the test
    if npx playwright test "$test_file" --reporter=json > "$RESULTS_DIR/${suite_name}-results.json" 2>&1; then
        local suite_end_time=$(date +%s)
        local duration=$((suite_end_time - suite_start_time))
        
        print_color $GREEN "✅ PASSED: $suite_name (${duration}s)"
        log_message "Test suite $suite_name PASSED in ${duration}s"
        ((PASSED_TESTS++))
    else
        local suite_end_time=$(date +%s)
        local duration=$((suite_end_time - suite_start_time))
        
        print_color $RED "❌ FAILED: $suite_name (${duration}s)"
        log_message "Test suite $suite_name FAILED in ${duration}s"
        ((FAILED_TESTS++))
        
        # Save error details
        echo "Error details for $suite_name:" >> "$RESULTS_DIR/${suite_name}-error.log"
        tail -50 "$RESULTS_DIR/${suite_name}-results.json" >> "$RESULTS_DIR/${suite_name}-error.log" 2>/dev/null || true
    fi
    
    ((TOTAL_TESTS++))
    echo
}

# Function to generate summary report
generate_summary() {
    local end_time=$(date +%s)
    local total_duration=$((end_time - START_TIME))
    local hours=$((total_duration / 3600))
    local minutes=$(((total_duration % 3600) / 60))
    local seconds=$((total_duration % 60))
    
    print_color $CYAN "╔══════════════════════════════════════════════════════════════════════════════╗"
    print_color $CYAN "║                              EXECUTION SUMMARY                               ║"
    print_color $CYAN "╚══════════════════════════════════════════════════════════════════════════════╝"
    echo
    
    print_color $BLUE "📊 Test Results:"
    print_color $GREEN "   ✅ Passed: $PASSED_TESTS"
    print_color $RED "   ❌ Failed: $FAILED_TESTS"
    print_color $BLUE "   📋 Total:  $TOTAL_TESTS"
    echo
    
    print_color $BLUE "⏱️  Execution Time:"
    if [ $hours -gt 0 ]; then
        print_color $BLUE "   Duration: ${hours}h ${minutes}m ${seconds}s"
    else
        print_color $BLUE "   Duration: ${minutes}m ${seconds}s"
    fi
    echo
    
    print_color $BLUE "📁 Reports Generated:"
    print_color $BLUE "   📄 Execution Log: $LOG_FILE"
    print_color $BLUE "   📊 Results Directory: $RESULTS_DIR"
    echo
    
    # Overall result
    if [ $FAILED_TESTS -eq 0 ]; then
        print_color $GREEN "🎉 ALL TESTS PASSED! 🎉"
        log_message "All comprehensive tests completed successfully"
        return 0
    else
        print_color $RED "⚠️  SOME TESTS FAILED ⚠️"
        log_message "$FAILED_TESTS out of $TOTAL_TESTS test suites failed"
        return 1
    fi
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS] [SUITES...]"
    echo
    echo "Options:"
    echo "  -h, --help          Show this help message"
    echo "  -p, --priority PRIO Run only tests with specified priority (high|medium|low)"
    echo "  -l, --list          List all available test suites"
    echo "  -v, --verbose       Enable verbose output"
    echo
    echo "Examples:"
    echo "  $0                           # Run all test suites"
    echo "  $0 -p high                   # Run only high priority tests"
    echo "  $0 dashboard charts          # Run specific test suites"
    echo "  $0 -l                        # List all available test suites"
    echo
}

# Function to list test suites
list_suites() {
    print_color $CYAN "Available Test Suites:"
    echo
    
    for suite in "${!TEST_SUITES[@]}"; do
        local suite_info=${TEST_SUITES[$suite]}
        IFS='|' read -r file priority time description <<< "$suite_info"
        
        case $priority in
            "High")   color=$RED ;;
            "Medium") color=$YELLOW ;;
            "Low")    color=$GREEN ;;
            *)        color=$NC ;;
        esac
        
        print_color $color "  $suite ($priority) - $time"
        print_color $NC "    $description"
        echo
    done
}

# Parse command line arguments
PRIORITY=""
VERBOSE=false
SUITES_TO_RUN=()

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_usage
            exit 0
            ;;
        -p|--priority)
            PRIORITY="$2"
            shift 2
            ;;
        -l|--list)
            list_suites
            exit 0
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -*)
            echo "Unknown option: $1"
            show_usage
            exit 1
            ;;
        *)
            SUITES_TO_RUN+=("$1")
            shift
            ;;
    esac
done

# Main execution
main() {
    print_header
    log_message "Starting comprehensive E2E test execution"
    
    # Determine which suites to run
    local suites_to_execute=()
    
    if [ ${#SUITES_TO_RUN[@]} -gt 0 ]; then
        # Run specific suites
        for suite in "${SUITES_TO_RUN[@]}"; do
            if [[ -n "${TEST_SUITES[$suite]}" ]]; then
                suites_to_execute+=("$suite")
            else
                print_color $RED "Error: Unknown test suite '$suite'"
                exit 1
            fi
        done
    elif [ -n "$PRIORITY" ]; then
        # Run suites by priority
        for suite in "${!TEST_SUITES[@]}"; do
            local suite_info=${TEST_SUITES[$suite]}
            IFS='|' read -r file priority time description <<< "$suite_info"
            if [[ "${priority,,}" == "${PRIORITY,,}" ]]; then
                suites_to_execute+=("$suite")
            fi
        done
    else
        # Run all suites
        suites_to_execute=($(printf '%s\n' "${!TEST_SUITES[@]}" | sort))
    fi
    
    if [ ${#suites_to_execute[@]} -eq 0 ]; then
        print_color $RED "No test suites to run!"
        exit 1
    fi
    
    print_color $BLUE "📋 Test Suites to Execute: ${#suites_to_execute[@]}"
    for suite in "${suites_to_execute[@]}"; do
        print_color $BLUE "   • $suite"
    done
    echo
    
    # Execute test suites
    for suite in "${suites_to_execute[@]}"; do
        if [ "$VERBOSE" = true ]; then
            print_suite_info "$suite"
        fi
        run_test_suite "$suite"
    done
    
    # Generate summary
    generate_summary
}

# Check if we're in the right directory
if [ ! -f "package.json" ] || [ ! -d "tests/e2e/comprehensive" ]; then
    print_color $RED "Error: Please run this script from the project root directory"
    exit 1
fi

# Check if Playwright is available
if ! command -v npx &> /dev/null; then
    print_color $RED "Error: npx is not available. Please install Node.js and npm"
    exit 1
fi

# Run main function
main "$@"
