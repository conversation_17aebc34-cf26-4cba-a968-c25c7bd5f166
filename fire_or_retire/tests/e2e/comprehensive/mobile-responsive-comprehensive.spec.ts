import { test, expect } from '@playwright/test';

/**
 * Comprehensive Mobile and Responsive Design Tests
 * Tests application behavior across all device sizes and orientations
 */

test.describe('Mobile & Responsive - Comprehensive Testing', () => {
  const devices = [
    { name: 'iPhone SE', width: 375, height: 667 },
    { name: 'iPhone 12', width: 390, height: 844 },
    { name: 'iPhone 12 Pro Max', width: 428, height: 926 },
    { name: 'iPad', width: 768, height: 1024 },
    { name: 'iPad Pro', width: 1024, height: 1366 },
    { name: 'Desktop Small', width: 1280, height: 720 },
    { name: 'Desktop Large', width: 1920, height: 1080 },
    { name: 'Ultrawide', width: 2560, height: 1440 },
  ];

  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
  });

  test('should be fully functional on all device sizes', async ({ page }) => {
    for (const device of devices) {
      await test.step(`Test on ${device.name} (${device.width}x${device.height})`, async () => {
        await page.setViewportSize({ width: device.width, height: device.height });
        await page.waitForTimeout(1000);
        
        // Test basic functionality
        await page.fill('[data-testid="current-age-input"]', '30');
        await page.fill('[data-testid="retirement-age-input"]', '65');
        await page.fill('[data-testid="current-savings-input"]', '50000');
        
        // Verify inputs are accessible and functional
        const ageInput = page.locator('[data-testid="current-age-input"]');
        await expect(ageInput).toBeVisible();
        await expect(ageInput).toBeEnabled();
        
        // Check that charts are visible and properly sized
        const charts = page.locator('.enhanced-chart-container svg');
        const chartCount = await charts.count();
        
        if (chartCount > 0) {
          for (let i = 0; i < Math.min(chartCount, 3); i++) {
            const chart = charts.nth(i);
            await expect(chart).toBeVisible();
            
            const chartWidth = await chart.evaluate(el => el.clientWidth);
            expect(chartWidth).toBeGreaterThan(200);
            expect(chartWidth).toBeLessThan(device.width + 50);
          }
        }
        
        // Test navigation if present
        const navItems = page.locator('nav a, [role="navigation"] button');
        const navCount = await navItems.count();
        
        if (navCount > 0) {
          // On mobile, navigation might be collapsed
          if (device.width < 768) {
            const mobileMenuButton = page.locator('[data-testid="mobile-menu"], button[aria-label*="menu"]');
            if (await mobileMenuButton.count() > 0) {
              await mobileMenuButton.click();
              await page.waitForTimeout(500);
            }
          }
          
          // Test first navigation item
          const firstNavItem = navItems.first();
          if (await firstNavItem.isVisible()) {
            await firstNavItem.click();
            await page.waitForTimeout(1000);
          }
        }
      });
    }
  });

  test('should handle touch interactions on mobile devices', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    await test.step('Test touch scrolling', async () => {
      // Add some data to make page scrollable
      await page.fill('[data-testid="current-age-input"]', '25');
      await page.fill('[data-testid="retirement-age-input"]', '65');
      await page.fill('[data-testid="current-savings-input"]', '30000');
      await page.fill('[data-testid="monthly-income-input"]', '7000');
      await page.fill('[data-testid="monthly-expenses-input"]', '4500');
      
      await page.waitForTimeout(2000);
      
      // Test scrolling to bottom
      await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight));
      await page.waitForTimeout(500);
      
      // Test scrolling back to top
      await page.evaluate(() => window.scrollTo(0, 0));
      await page.waitForTimeout(500);
    });

    await test.step('Test chart touch interactions', async () => {
      const charts = page.locator('.enhanced-chart-container svg');
      const chartCount = await charts.count();
      
      if (chartCount > 0) {
        const firstChart = charts.first();
        
        // Test touch/tap on chart
        await firstChart.tap();
        await page.waitForTimeout(500);
        
        // Test swipe gesture if supported
        const chartBox = await firstChart.boundingBox();
        if (chartBox) {
          await page.touchscreen.tap(chartBox.x + chartBox.width / 2, chartBox.y + chartBox.height / 2);
          await page.waitForTimeout(500);
        }
      }
    });

    await test.step('Test form interactions on mobile', async () => {
      // Test input focus and keyboard
      const inputs = page.locator('input[type="number"], input[type="text"]');
      const inputCount = await inputs.count();
      
      if (inputCount > 0) {
        const firstInput = inputs.first();
        await firstInput.tap();
        await page.waitForTimeout(500);
        
        // Verify input is focused
        await expect(firstInput).toBeFocused();
        
        // Test typing
        await firstInput.fill('12345');
        await expect(firstInput).toHaveValue('12345');
      }
    });
  });

  test('should adapt layout for different orientations', async ({ page }) => {
    const mobileDevices = [
      { name: 'iPhone 12 Portrait', width: 390, height: 844 },
      { name: 'iPhone 12 Landscape', width: 844, height: 390 },
      { name: 'iPad Portrait', width: 768, height: 1024 },
      { name: 'iPad Landscape', width: 1024, height: 768 },
    ];

    for (const device of mobileDevices) {
      await test.step(`Test ${device.name}`, async () => {
        await page.setViewportSize({ width: device.width, height: device.height });
        await page.waitForTimeout(1000);
        
        // Set up test data
        await page.fill('[data-testid="current-age-input"]', '35');
        await page.fill('[data-testid="current-savings-input"]', '75000');
        await page.waitForTimeout(1000);
        
        // Check layout adaptation
        const mainContent = page.locator('main, [role="main"], .main-content');
        if (await mainContent.count() > 0) {
          await expect(mainContent).toBeVisible();
        }
        
        // Charts should adapt to orientation
        const charts = page.locator('.enhanced-chart-container svg');
        const chartCount = await charts.count();
        
        if (chartCount > 0) {
          const chart = charts.first();
          const chartWidth = await chart.evaluate(el => el.clientWidth);
          const chartHeight = await chart.evaluate(el => el.clientHeight);
          
          // Chart should fit within viewport
          expect(chartWidth).toBeLessThan(device.width + 50);
          expect(chartHeight).toBeGreaterThan(200);
          
          // In landscape mode, charts might be wider
          if (device.width > device.height) {
            expect(chartWidth).toBeGreaterThan(400);
          }
        }
      });
    }
  });

  test('should maintain performance on mobile devices', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });
    
    await test.step('Test page load performance', async () => {
      const startTime = Date.now();
      
      await page.reload();
      await page.waitForLoadState('networkidle');
      
      const loadTime = Date.now() - startTime;
      
      // Should load within 10 seconds on mobile
      expect(loadTime).toBeLessThan(10000);
    });

    await test.step('Test calculation performance', async () => {
      const startTime = Date.now();
      
      // Trigger calculations
      await page.fill('[data-testid="current-age-input"]', '28');
      await page.fill('[data-testid="retirement-age-input"]', '67');
      await page.fill('[data-testid="current-savings-input"]', '45000');
      await page.fill('[data-testid="monthly-income-input"]', '8500');
      await page.fill('[data-testid="monthly-expenses-input"]', '5200');
      
      // Wait for calculations to complete
      await page.waitForTimeout(3000);
      
      const calculationTime = Date.now() - startTime;
      
      // Calculations should complete within 5 seconds
      expect(calculationTime).toBeLessThan(5000);
      
      // Verify results are displayed
      const results = page.locator('[data-testid="fire-number"], [data-testid="years-to-fire"]');
      if (await results.count() > 0) {
        await expect(results.first()).toBeVisible();
      }
    });

    await test.step('Test chart rendering performance', async () => {
      const startTime = Date.now();
      
      // Wait for charts to render
      const charts = page.locator('.enhanced-chart-container svg');
      await expect(charts.first()).toBeVisible({ timeout: 10000 });
      
      const renderTime = Date.now() - startTime;
      
      // Charts should render within 8 seconds on mobile
      expect(renderTime).toBeLessThan(8000);
    });
  });

  test('should handle mobile-specific UI patterns', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });
    
    await test.step('Test mobile navigation patterns', async () => {
      // Look for hamburger menu
      const hamburgerMenu = page.locator('[data-testid="mobile-menu"], button[aria-label*="menu"], .hamburger');
      if (await hamburgerMenu.count() > 0) {
        await hamburgerMenu.click();
        await page.waitForTimeout(500);
        
        // Menu should be visible
        const mobileNav = page.locator('[data-testid="mobile-navigation"], .mobile-nav, nav[aria-expanded="true"]');
        if (await mobileNav.count() > 0) {
          await expect(mobileNav).toBeVisible();
          
          // Test menu item
          const menuItems = mobileNav.locator('a, button');
          if (await menuItems.count() > 0) {
            await menuItems.first().click();
            await page.waitForTimeout(500);
          }
        }
      }
    });

    await test.step('Test mobile form patterns', async () => {
      // Test input labels and accessibility
      const inputs = page.locator('input');
      const inputCount = await inputs.count();
      
      for (let i = 0; i < Math.min(inputCount, 5); i++) {
        const input = inputs.nth(i);
        
        // Input should have proper labeling
        const hasLabel = await input.evaluate(el => {
          const id = el.id;
          const ariaLabel = el.getAttribute('aria-label');
          const ariaLabelledBy = el.getAttribute('aria-labelledby');
          const label = id ? document.querySelector(`label[for="${id}"]`) : null;
          
          return !!(ariaLabel || ariaLabelledBy || label);
        });
        
        expect(hasLabel).toBe(true);
      }
    });

    await test.step('Test mobile chart interactions', async () => {
      // Set up data for charts
      await page.fill('[data-testid="current-age-input"]', '32');
      await page.fill('[data-testid="current-savings-input"]', '60000');
      await page.waitForTimeout(2000);
      
      const charts = page.locator('.enhanced-chart-container svg');
      const chartCount = await charts.count();
      
      if (chartCount > 0) {
        const chart = charts.first();
        
        // Test tap interaction
        await chart.tap();
        await page.waitForTimeout(500);
        
        // Look for mobile-specific chart controls
        const chartControls = page.locator('[data-testid*="chart-control"], .chart-controls button');
        if (await chartControls.count() > 0) {
          await chartControls.first().tap();
          await page.waitForTimeout(500);
        }
      }
    });
  });

  test('should support accessibility features on mobile', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });
    
    await test.step('Test screen reader compatibility', async () => {
      // Check for proper ARIA labels
      const interactiveElements = page.locator('button, input, select, a');
      const elementCount = await interactiveElements.count();
      
      for (let i = 0; i < Math.min(elementCount, 10); i++) {
        const element = interactiveElements.nth(i);
        
        const hasAccessibleName = await element.evaluate(el => {
          const ariaLabel = el.getAttribute('aria-label');
          const ariaLabelledBy = el.getAttribute('aria-labelledby');
          const title = el.getAttribute('title');
          const textContent = el.textContent?.trim();
          
          return !!(ariaLabel || ariaLabelledBy || title || textContent);
        });
        
        expect(hasAccessibleName).toBe(true);
      }
    });

    await test.step('Test keyboard navigation on mobile', async () => {
      // Test tab navigation
      await page.keyboard.press('Tab');
      const focusedElement = page.locator(':focus');
      await expect(focusedElement).toBeVisible();
      
      // Test multiple tab presses
      for (let i = 0; i < 5; i++) {
        await page.keyboard.press('Tab');
        await page.waitForTimeout(200);
        
        const currentFocus = page.locator(':focus');
        if (await currentFocus.count() > 0) {
          await expect(currentFocus).toBeVisible();
        }
      }
    });

    await test.step('Test high contrast and reduced motion', async () => {
      // Test with reduced motion preference
      await page.emulateMedia({ reducedMotion: 'reduce' });
      await page.reload();
      await page.waitForLoadState('networkidle');
      
      // Charts should still be functional
      const charts = page.locator('.enhanced-chart-container svg');
      if (await charts.count() > 0) {
        await expect(charts.first()).toBeVisible();
      }
    });
  });
});
