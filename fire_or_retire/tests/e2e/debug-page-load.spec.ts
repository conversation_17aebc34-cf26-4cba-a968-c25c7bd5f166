import { expect, test } from '@playwright/test';

test.describe('Debug Page Load', () => {
  test('Check what is actually loaded on the page', async ({ page }) => {
    // Listen for console messages
    const consoleMessages: string[] = [];
    page.on('console', msg => {
      consoleMessages.push(`${msg.type()}: ${msg.text()}`);
    });

    // Listen for network failures
    const networkErrors: string[] = [];
    page.on('requestfailed', request => {
      networkErrors.push(`Failed: ${request.url()} - ${request.failure()?.errorText}`);
    });

    // Navigate to the application
    await page.goto('http://localhost:5173/');

    // Wait for page to load (but not network idle due to HMR)
    await page.waitForLoadState('domcontentloaded');

    // Wait a bit for React to render
    await page.waitForTimeout(3000);

    // Take a screenshot
    await page.screenshot({ path: 'debug-page-load.png', fullPage: true });

    // Get page title
    const title = await page.title();
    console.log('Page title:', title);

    // Get page content
    const bodyText = await page.textContent('body');
    console.log('Body text (first 500 chars):', bodyText?.substring(0, 500));

    // Check for specific elements
    const h1Elements = await page.locator('h1').count();
    console.log('Number of h1 elements:', h1Elements);

    if (h1Elements > 0) {
      const h1Text = await page.locator('h1').first().textContent();
      console.log('First h1 text:', h1Text);
    }

    // Check for any error messages
    const errorElements = await page.locator('[data-testid="error"], .error, .error-message').count();
    console.log('Number of error elements:', errorElements);

    // Check if React has loaded
    const reactRoot = await page.locator('#root').count();
    console.log('React root element found:', reactRoot > 0);

    if (reactRoot > 0) {
      const rootContent = await page.locator('#root').textContent();
      console.log('Root content (first 200 chars):', rootContent?.substring(0, 200));
    }

    // Check console errors
    const consoleErrors: string[] = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });

    // Wait a bit more for any async loading
    await page.waitForTimeout(3000);

    console.log('Console messages:', consoleMessages);
    console.log('Network errors:', networkErrors);

    // Check if the page contains any Swiss Budget Pro text
    const hasSwissBudgetPro = await page.locator('text=Swiss Budget Pro').count() > 0;
    console.log('Contains "Swiss Budget Pro" text:', hasSwissBudgetPro);

    // Check for the tagline
    const hasTagline = await page.locator('text=Your Advanced Financial Command Center').count() > 0;
    console.log('Contains tagline text:', hasTagline);

    // Check HTML content
    const htmlContent = await page.content();
    console.log('HTML content (first 1000 chars):', htmlContent.substring(0, 1000));

    // This test is just for debugging, so we'll always pass
    expect(true).toBe(true);
  });

  test('Debug form elements and tabs', async ({ page }) => {
    console.log('🔗 Navigating to Swiss Budget Pro...');
    await page.goto('/');

    console.log('⏳ Waiting for page to load...');
    await page.waitForSelector('h1', { timeout: 15000 });

    // Debug: Check what tabs are available
    const tabs = await page.locator('[data-testid^="tab-"]').all();
    console.log(`🔍 Found ${tabs.length} tabs:`);
    for (const tab of tabs) {
      const testId = await tab.getAttribute('data-testid');
      const text = await tab.textContent();
      console.log(`  - ${testId}: "${text}"`);
    }

    // Debug: Check what inputs are available in overview tab
    console.log('🔍 Checking inputs in overview tab...');
    const overviewTab = page.locator('[data-testid="tab-overview"]');
    if (await overviewTab.count() > 0) {
      await overviewTab.click();
      await page.waitForTimeout(500);

      const inputs = await page.locator('input').all();
      console.log(`🔍 Found ${inputs.length} input elements:`);
      for (let i = 0; i < Math.min(inputs.length, 10); i++) {
        const input = inputs[i];
        const testId = await input.getAttribute('data-testid');
        const type = await input.getAttribute('type');
        const placeholder = await input.getAttribute('placeholder');
        const value = await input.getAttribute('value');
        console.log(`  - Input ${i}: testId="${testId}", type="${type}", placeholder="${placeholder}", value="${value}"`);
      }

      // Specifically check for age input
      const ageInput = page.locator('[data-testid="age-input"]');
      const ageInputCount = await ageInput.count();
      console.log(`🔍 Age input count: ${ageInputCount}`);

      if (ageInputCount > 0) {
        const isVisible = await ageInput.isVisible();
        console.log(`👁️ Age input visible: ${isVisible}`);
      } else {
        // Check for any input with "age" in the label or nearby text
        const ageRelatedInputs = await page.locator('input').filter({ hasText: /age/i }).count();
        console.log(`🔍 Age-related inputs: ${ageRelatedInputs}`);

        // Check for inputs near "age" text
        const ageLabels = await page.locator('label:has-text("age"), text=age').all();
        console.log(`🔍 Found ${ageLabels.length} age labels:`);
        for (const label of ageLabels) {
          const text = await label.textContent();
          console.log(`  - Age label: "${text}"`);
        }
      }

      // Check for monthly income input
      const monthlyIncomeInput = page.locator('[data-testid="monthly-income"]');
      const monthlyIncomeCount = await monthlyIncomeInput.count();
      console.log(`🔍 Monthly income input count: ${monthlyIncomeCount}`);

      if (monthlyIncomeCount > 0) {
        const isVisible = await monthlyIncomeInput.isVisible();
        console.log(`👁️ Monthly income input visible: ${isVisible}`);
      }
    }

    expect(true).toBe(true);
  });
});
