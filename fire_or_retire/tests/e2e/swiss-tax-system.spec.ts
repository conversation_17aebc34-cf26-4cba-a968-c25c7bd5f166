import { test, expect } from '@playwright/test';

test.describe('Swiss Tax System E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    
    // Fill in basic user information
    await page.fill('[data-testid="monthly-income"]', '8000');
    await page.fill('[data-testid="current-age"]', '35');
    await page.fill('[data-testid="current-savings"]', '100000');
    await page.selectOption('[data-testid="canton-select"]', 'ZH');
    
    // Navigate to Analysis tab and Tax Optimization
    await page.click('[data-testid="analysis-tab"]');
    await page.click('[data-testid="tax-optimization-tab"]');
    
    // Wait for the Swiss Tax Planning Dashboard to load
    await page.waitForSelector('[data-testid="swiss-tax-dashboard"]', { timeout: 10000 });
  });

  test('should display Swiss Tax Planning Dashboard with all tabs', async ({ page }) => {
    // Verify dashboard header
    await expect(page.locator('h3')).toContainText('Swiss Tax Planning Dashboard');
    
    // Verify description
    await expect(page.locator('text=Comprehensive Swiss tax planning')).toBeVisible();
    
    // Verify all navigation tabs are present
    await expect(page.locator('[data-testid="overview-tab"]')).toBeVisible();
    await expect(page.locator('[data-testid="optimization-tab"]')).toBeVisible();
    await expect(page.locator('[data-testid="deductions-tab"]')).toBeVisible();
    await expect(page.locator('[data-testid="scenarios-tab"]')).toBeVisible();
    
    // Verify Overview tab is selected by default
    await expect(page.locator('[data-testid="overview-tab"]')).toHaveClass(/bg-blue-600/);
  });

  test('should navigate between tax planning tabs', async ({ page }) => {
    // Test Optimization tab
    await page.click('[data-testid="optimization-tab"]');
    await expect(page.locator('text=Tax Optimization Strategies')).toBeVisible();
    
    // Test Deductions tab
    await page.click('[data-testid="deductions-tab"]');
    await expect(page.locator('text=Swiss Tax Deduction Optimizer')).toBeVisible();
    
    // Test Scenarios tab
    await page.click('[data-testid="scenarios-tab"]');
    await expect(page.locator('text=Tax Planning Scenarios')).toBeVisible();
    
    // Return to Overview tab
    await page.click('[data-testid="overview-tab"]');
    await expect(page.locator('text=Current Tax Situation')).toBeVisible();
  });

  test('should display current tax situation overview', async ({ page }) => {
    // Wait for tax analysis to complete
    await page.waitForSelector('[data-testid="gross-annual-income"]', { timeout: 5000 });
    
    // Verify key financial metrics are displayed
    await expect(page.locator('[data-testid="gross-annual-income"]')).toContainText('CHF');
    await expect(page.locator('[data-testid="social-insurance"]')).toContainText('CHF');
    await expect(page.locator('[data-testid="income-wealth-tax"]')).toContainText('CHF');
    await expect(page.locator('[data-testid="net-annual-income"]')).toContainText('CHF');
    
    // Verify total tax burden is displayed with percentage
    await expect(page.locator('[data-testid="total-tax-burden"]')).toContainText('CHF');
    await expect(page.locator('[data-testid="effective-rate"]')).toContainText('%');
  });

  test('should display social insurance breakdown', async ({ page }) => {
    // Scroll to social insurance section
    await page.locator('[data-testid="social-insurance-breakdown"]').scrollIntoViewIfNeeded();
    
    // Verify all social insurance components
    await expect(page.locator('[data-testid="ahv-contribution"]')).toContainText('CHF');
    await expect(page.locator('[data-testid="ahv-rate"]')).toContainText('4.35%');
    
    await expect(page.locator('[data-testid="alv-contribution"]')).toContainText('CHF');
    await expect(page.locator('[data-testid="alv-rate"]')).toContainText('1.1%');
    
    await expect(page.locator('[data-testid="nbu-contribution"]')).toContainText('CHF');
    await expect(page.locator('[data-testid="nbu-rate"]')).toContainText('1.0%');
    
    await expect(page.locator('[data-testid="pension-fund-contribution"]')).toContainText('CHF');
    await expect(page.locator('[data-testid="pension-fund-rate"]')).toContainText('Age-based');
  });

  test('should show tax optimization strategies', async ({ page }) => {
    await page.click('[data-testid="optimization-tab"]');
    
    // Wait for optimization analysis to complete
    await page.waitForSelector('[data-testid="optimization-strategies"]', { timeout: 5000 });
    
    // Verify strategies are displayed with savings amounts
    const strategies = page.locator('[data-testid="strategy-item"]');
    await expect(strategies).toHaveCountGreaterThan(0);
    
    // Check first strategy has required elements
    const firstStrategy = strategies.first();
    await expect(firstStrategy.locator('[data-testid="strategy-name"]')).toBeVisible();
    await expect(firstStrategy.locator('[data-testid="annual-savings"]')).toContainText('CHF');
    await expect(firstStrategy.locator('[data-testid="risk-level"]')).toBeVisible();
    await expect(firstStrategy.locator('[data-testid="implementation-steps"]')).toBeVisible();
  });

  test('should display Pillar 3a optimization details', async ({ page }) => {
    await page.click('[data-testid="optimization-tab"]');
    await page.waitForSelector('[data-testid="optimization-strategies"]');
    
    // Look for Pillar 3a strategy
    const pillar3aStrategy = page.locator('[data-testid="strategy-pillar3a-max"]');
    await expect(pillar3aStrategy).toBeVisible();
    
    // Verify Pillar 3a specific content
    await expect(pillar3aStrategy.locator('[data-testid="strategy-name"]')).toContainText('Pillar 3a');
    await expect(pillar3aStrategy.locator('[data-testid="max-amount"]')).toContainText('7,056');
    await expect(pillar3aStrategy.locator('[data-testid="risk-level"]')).toContainText('Low Risk');
  });

  test('should use tax deduction optimizer', async ({ page }) => {
    await page.click('[data-testid="deductions-tab"]');
    
    // Wait for deduction optimizer to load
    await page.waitForSelector('[data-testid="optimization-potential"]', { timeout: 5000 });
    
    // Verify optimization potential summary
    await expect(page.locator('[data-testid="current-deductions"]')).toContainText('CHF');
    await expect(page.locator('[data-testid="potential-additional"]')).toContainText('CHF');
    await expect(page.locator('[data-testid="annual-tax-savings"]')).toContainText('CHF');
    
    // Verify deduction category tabs
    await expect(page.locator('[data-testid="pillar3a-tab"]')).toBeVisible();
    await expect(page.locator('[data-testid="professional-tab"]')).toBeVisible();
    await expect(page.locator('[data-testid="insurance-tab"]')).toBeVisible();
  });

  test('should select and view deduction categories', async ({ page }) => {
    await page.click('[data-testid="deductions-tab"]');
    await page.waitForSelector('[data-testid="deduction-categories"]');
    
    // Test Pillar 3a category
    await page.click('[data-testid="pillar3a-tab"]');
    await expect(page.locator('[data-testid="category-name"]')).toContainText('Pillar 3a');
    await expect(page.locator('[data-testid="current-amount"]')).toBeVisible();
    await expect(page.locator('[data-testid="maximum-allowed"]')).toBeVisible();
    await expect(page.locator('[data-testid="potential-savings"]')).toBeVisible();
    
    // Test Professional Expenses category
    await page.click('[data-testid="professional-tab"]');
    await expect(page.locator('[data-testid="category-name"]')).toContainText('Professional Expenses');
    await expect(page.locator('[data-testid="requirements-list"]')).toBeVisible();
    await expect(page.locator('[data-testid="tips-list"]')).toBeVisible();
    await expect(page.locator('[data-testid="documentation-list"]')).toBeVisible();
  });

  test('should compare tax planning scenarios', async ({ page }) => {
    await page.click('[data-testid="scenarios-tab"]');
    
    // Wait for scenarios to load
    await page.waitForSelector('[data-testid="scenario-buttons"]', { timeout: 5000 });
    
    // Verify scenario buttons
    await expect(page.locator('[data-testid="current-scenario"]')).toContainText('Current Situation');
    await expect(page.locator('[data-testid="optimized-scenario"]')).toContainText('Optimized');
    await expect(page.locator('[data-testid="best-canton-scenario"]')).toContainText('Best Canton');
    
    // Test scenario selection
    await page.click('[data-testid="optimized-scenario"]');
    await expect(page.locator('[data-testid="scenario-details"]')).toBeVisible();
    await expect(page.locator('[data-testid="gross-income"]')).toContainText('CHF');
    await expect(page.locator('[data-testid="total-tax-burden"]')).toContainText('CHF');
    await expect(page.locator('[data-testid="net-income"]')).toContainText('CHF');
  });

  test('should display canton tax comparison', async ({ page }) => {
    await page.click('[data-testid="optimization-tab"]');
    await page.waitForSelector('[data-testid="canton-comparison"]');
    
    // Scroll to canton comparison section
    await page.locator('[data-testid="canton-comparison"]').scrollIntoViewIfNeeded();
    
    // Verify table headers
    await expect(page.locator('[data-testid="canton-header"]')).toContainText('Canton');
    await expect(page.locator('[data-testid="income-tax-header"]')).toContainText('Income Tax');
    await expect(page.locator('[data-testid="wealth-tax-header"]')).toContainText('Wealth Tax');
    await expect(page.locator('[data-testid="total-tax-header"]')).toContainText('Total Tax');
    
    // Verify current canton is highlighted
    await expect(page.locator('[data-testid="current-canton-row"]')).toHaveClass(/bg-blue/);
    
    // Verify at least 10 cantons are shown
    const cantonRows = page.locator('[data-testid="canton-row"]');
    await expect(cantonRows).toHaveCountGreaterThanOrEqual(10);
  });

  test('should show tax planning recommendations by timeframe', async ({ page }) => {
    await page.click('[data-testid="optimization-tab"]');
    await page.locator('[data-testid="tax-recommendations"]').scrollIntoViewIfNeeded();
    
    // Verify immediate actions section
    await expect(page.locator('[data-testid="immediate-actions"]')).toBeVisible();
    await expect(page.locator('[data-testid="immediate-actions"]')).toContainText('0-3 months');
    
    // Verify medium-term planning section
    await expect(page.locator('[data-testid="medium-term-planning"]')).toBeVisible();
    await expect(page.locator('[data-testid="medium-term-planning"]')).toContainText('3-12 months');
    
    // Verify long-term strategy section
    await expect(page.locator('[data-testid="long-term-strategy"]')).toBeVisible();
    await expect(page.locator('[data-testid="long-term-strategy"]')).toContainText('1+ years');
  });

  test('should handle real-time calculation updates', async ({ page }) => {
    // Change income and verify updates
    await page.fill('[data-testid="monthly-income"]', '10000');
    
    // Wait for calculations to update
    await page.waitForTimeout(1000);
    
    // Verify tax calculations have updated
    const grossIncome = await page.locator('[data-testid="gross-annual-income"]').textContent();
    expect(grossIncome).toContain('120,000'); // 10,000 * 12
    
    // Verify effective rate recalculated
    await expect(page.locator('[data-testid="effective-rate"]')).toContainText('%');
  });

  test('should maintain Swiss formatting throughout', async ({ page }) => {
    // Verify CHF currency formatting
    const chfElements = page.locator('text=/CHF [0-9,]+/');
    await expect(chfElements.first()).toBeVisible();
    
    // Verify percentage formatting
    const percentageElements = page.locator('text=/[0-9.]+%/');
    await expect(percentageElements.first()).toBeVisible();
    
    // Check for Swiss number formatting (commas for thousands)
    await expect(page.locator('text=/CHF [0-9]{1,3}(,[0-9]{3})*/').first()).toBeVisible();
  });

  test('should be responsive on mobile devices', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Verify dashboard is still accessible
    await expect(page.locator('[data-testid="swiss-tax-dashboard"]')).toBeVisible();
    
    // Verify tabs are touch-friendly
    await page.click('[data-testid="optimization-tab"]');
    await expect(page.locator('[data-testid="optimization-strategies"]')).toBeVisible();
    
    // Verify tables scroll horizontally if needed
    const table = page.locator('[data-testid="canton-comparison-table"]');
    if (await table.isVisible()) {
      await expect(table).toHaveCSS('overflow-x', 'auto');
    }
  });

  test('should work correctly in dark mode', async ({ page }) => {
    // Enable dark mode
    await page.click('[data-testid="dark-mode-toggle"]');
    
    // Verify dark mode styling
    await expect(page.locator('[data-testid="swiss-tax-dashboard"]')).toHaveClass(/bg-gray-800/);
    
    // Verify text remains readable
    await expect(page.locator('h3')).toHaveClass(/text-white/);
    
    // Test tab navigation in dark mode
    await page.click('[data-testid="optimization-tab"]');
    await expect(page.locator('[data-testid="optimization-strategies"]')).toBeVisible();
  });

  test('should handle error cases gracefully', async ({ page }) => {
    // Test with invalid income
    await page.fill('[data-testid="monthly-income"]', '-1000');
    
    // Verify system handles negative values
    await page.waitForTimeout(1000);
    
    // Should either show error message or default to 0
    const errorMessage = page.locator('[data-testid="error-message"]');
    const zeroIncome = page.locator('text=CHF 0');
    
    await expect(errorMessage.or(zeroIncome)).toBeVisible();
  });

  test('should integrate with main FIRE calculator data', async ({ page }) => {
    // Verify data synchronization
    const monthlyIncome = await page.locator('[data-testid="monthly-income"]').inputValue();
    const annualIncome = parseInt(monthlyIncome) * 12;
    
    // Check if annual income in tax dashboard matches
    await expect(page.locator('[data-testid="gross-annual-income"]')).toContainText(annualIncome.toLocaleString());
    
    // Change canton and verify update
    await page.selectOption('[data-testid="canton-select"]', 'GE');
    await page.waitForTimeout(1000);
    
    // Verify tax calculations updated for new canton
    await expect(page.locator('[data-testid="current-canton-row"]')).toContainText('GE');
  });

  test('should perform well with complex calculations', async ({ page }) => {
    // Set high income to trigger complex calculations
    await page.fill('[data-testid="monthly-income"]', '20000');
    await page.fill('[data-testid="current-savings"]', '1000000');
    
    // Measure calculation time
    const startTime = Date.now();
    
    await page.click('[data-testid="optimization-tab"]');
    await page.waitForSelector('[data-testid="optimization-strategies"]');
    
    const endTime = Date.now();
    const calculationTime = endTime - startTime;
    
    // Verify calculations complete within reasonable time (5 seconds)
    expect(calculationTime).toBeLessThan(5000);
    
    // Verify interface remains responsive
    await expect(page.locator('[data-testid="optimization-strategies"]')).toBeVisible();
  });
});
