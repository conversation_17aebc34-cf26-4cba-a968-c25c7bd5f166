# Swiss Budget Pro - E2E Testing with Playwright

This directory contains end-to-end (E2E) tests for Swiss Budget Pro using Playwright. The tests ensure the reliability, performance, and user experience quality of all critical financial planning features.

## 🏗️ Test Structure

```
tests/e2e/
├── config/                    # Global setup and configuration
│   ├── global-setup.ts       # Pre-test setup
│   └── global-teardown.ts    # Post-test cleanup
├── fixtures/                 # Test data and scenarios
│   └── swiss-scenarios.ts    # Swiss financial test scenarios
├── pages/                    # Page Object Models
│   ├── base-page.ts         # Common page functionality
│   └── dashboard-page.ts    # Main dashboard interactions
├── scripts/                 # Test execution scripts
│   └── run-tests.ts         # Advanced test runner with reporting
├── tests/                   # Test suites organized by feature
│   ├── accessibility/       # WCAG compliance and a11y testing
│   ├── critical-path/       # Core user journeys
│   ├── data-management/     # Import/export and persistence
│   ├── error-handling/      # Edge cases and error recovery
│   ├── form-validation/     # Input validation and error handling
│   ├── internationalization/ # Multi-language testing
│   ├── mobile/             # Mobile responsiveness testing
│   ├── performance/        # Performance benchmarks and optimization
│   ├── smoke/              # Basic functionality tests
│   ├── swiss-features/     # Swiss-specific functionality
│   ├── user-journeys/      # Complete workflow testing
│   └── visual/             # Visual regression testing
└── utils/                   # Enhanced test utilities
    └── test-helpers.ts      # Advanced utilities with logging
```

## 🚀 Getting Started

### Prerequisites

- Node.js 18+ installed
- Swiss Budget Pro development environment set up

### Installation

1. Install Playwright and dependencies:

```bash
npm install
npm run test:e2e:install
```

2. Verify installation:

```bash
npx playwright --version
```

### Running Tests

#### Local Development

```bash
# Run all E2E tests
npm run test:e2e

# Run tests with UI mode (interactive)
npm run test:e2e:ui

# Run tests in headed mode (see browser)
npm run test:e2e:headed

# Debug specific test
npm run test:e2e:debug -- tests/e2e/tests/critical-path/financial-planning-journey.spec.ts
```

#### Advanced Test Runner

```bash
# Run smoke tests (fastest)
npm run test:e2e:smoke

# Run critical path tests
npm run test:e2e:critical

# Run regression test suite
npm run test:e2e:regression

# Run full comprehensive test suite
npm run test:e2e:full

# Run mobile responsiveness tests
npm run test:e2e:mobile

# Run performance tests
npm run test:e2e:performance

# Show execution plan without running tests
npm run test:e2e:dry-run
```

#### Specific Test Categories

```bash
# Basic functionality tests
npm run test:e2e:smoke

# Critical path tests only
npx playwright test tests/e2e/tests/critical-path/

# Swiss-specific features
npx playwright test tests/e2e/tests/swiss-features/

# Form validation tests
npm run test:e2e:forms

# Mobile responsiveness tests
npm run test:e2e:mobile

# Data import/export tests
npm run test:e2e:data

# Error handling and edge cases
npm run test:e2e:errors

# Visual regression tests
npm run test:e2e:visual

# Accessibility tests
npm run test:e2e:accessibility

# Performance tests
npm run test:e2e:performance

# Complete user journey tests
npm run test:e2e:journeys

# Internationalization tests
npx playwright test tests/e2e/tests/internationalization/

# Healthcare Cost Optimizer tests
npm run test:e2e:healthcare
npm run test:e2e:healthcare-performance
npm run test:e2e:healthcare-accessibility
npm run test:e2e:healthcare-journeys
npm run test:e2e:healthcare-all
```

#### Browser-Specific Testing

```bash
# Test on specific browser
npx playwright test --project=chromium
npx playwright test --project=firefox
npx playwright test --project=webkit

# Mobile testing
npx playwright test --project="Mobile Chrome"
npx playwright test --project="Mobile Safari"
```

## 📊 Test Reports

### Viewing Reports

```bash
# Open HTML report
npm run test:e2e:report

# Generate and view report after test run
npx playwright test && npx playwright show-report
```

### Report Locations

- **HTML Report**: `playwright-report/index.html`
- **Test Results**: `test-results/`
- **Screenshots**: `test-results/screenshots/`
- **Videos**: `test-results/videos/`

## 🧪 Test Categories

### Critical Path Tests

- Complete financial planning workflow
- Data input and calculation verification
- Data persistence and auto-save
- Export/import functionality

### Swiss-Specific Features

- All 26 canton tax calculations
- Pillar 3a optimization
- Wealth tax calculations
- BVG pension integration
- Swiss financial formatting
- Healthcare cost optimization
- Swiss health insurance comparison
- Healthcare premium subsidies
- Geographic arbitrage for healthcare
- FIRE healthcare integration

### Internationalization

- German ↔ English language switching
- Swiss financial term translations
- Canton name localization
- Currency and date formatting
- Browser language detection

### Performance Tests

- Page load performance
- Calculation speed
- Memory usage
- Network efficiency
- Core Web Vitals

### Accessibility Tests

- WCAG 2.1 AA compliance
- Keyboard navigation
- Screen reader compatibility
- Color contrast validation
- Focus management

## 🔧 Configuration

### Environment Variables

```bash
# Set base URL for testing
PLAYWRIGHT_BASE_URL=http://localhost:5173

# Enable debug mode
DEBUG=pw:api

# Set test timeout
PLAYWRIGHT_TIMEOUT=30000
```

### Browser Configuration

Tests run on multiple browsers by default:

- **Chromium** (Chrome/Edge)
- **Firefox**
- **WebKit** (Safari)
- **Mobile Chrome**
- **Mobile Safari**

### Test Data

Swiss financial scenarios are defined in `fixtures/swiss-scenarios.ts`:

- Zurich Professional (high income, single)
- Vaud Family (married with children)
- Geneva Executive (high net worth)
- Bern Conservative (moderate income, high savings)
- Basel Tech Worker (stock options)

## 🐛 Debugging

### Debug Mode

```bash
# Run specific test in debug mode
npx playwright test --debug tests/e2e/tests/critical-path/financial-planning-journey.spec.ts

# Debug with specific browser
npx playwright test --debug --project=chromium
```

### Screenshots and Videos

Tests automatically capture:

- Screenshots on failure
- Videos on failure
- Full page screenshots for visual verification

### Console Logs

```bash
# Enable verbose logging
DEBUG=pw:api npx playwright test

# Browser console logs
npx playwright test --reporter=line
```

## 📈 Performance Benchmarks

### Expected Performance Thresholds

- **Page Load**: < 3 seconds
- **Chart Rendering**: < 1 second
- **Form Interactions**: < 500ms
- **Data Persistence**: < 200ms
- **Language Switching**: < 1 second

### Core Web Vitals Targets

- **LCP (Largest Contentful Paint)**: < 2.5s
- **FID (First Input Delay)**: < 100ms
- **CLS (Cumulative Layout Shift)**: < 0.1

## 🔄 CI/CD Integration

Tests run automatically on:

- Pull requests to `main` and `develop`
- Pushes to `main` and `develop`
- Feature branch pushes (limited subset)

### GitHub Actions Workflow

The CI pipeline includes:

- Cross-browser testing
- Mobile responsiveness testing
- Performance benchmarking
- Accessibility validation
- Test report generation

## 🛠️ Writing New Tests

### Page Object Model

Use the Page Object Model pattern:

```typescript
import { DashboardPage } from "../../pages/dashboard-page";

test("My new test", async ({ page }) => {
  const dashboardPage = new DashboardPage(page);
  await dashboardPage.goto();
  await dashboardPage.inputScenario(scenario);
  await dashboardPage.expectFIREProjection(expectedYears);
});
```

### Test Data

Use predefined scenarios or generate random data:

```typescript
import {
  swissTestScenarios,
  generateRandomScenario,
} from "../../fixtures/swiss-scenarios";

// Use predefined scenario
const scenario = swissTestScenarios.zurichProfessional;

// Or generate random data
const randomScenario = generateRandomScenario();
```

### Assertions

Use Swiss-specific assertion helpers:

```typescript
// Swiss currency formatting
await dashboardPage.expectSwissCurrency('[data-testid="amount"]', 5000);

// Swiss percentage formatting
await dashboardPage.expectSwissPercentage('[data-testid="rate"]', 35);
```

## 📚 Best Practices

### Test Organization

- Group related tests in describe blocks
- Use descriptive test names
- Follow the AAA pattern (Arrange, Act, Assert)

### Data Management

- Clear localStorage before each test
- Use realistic Swiss financial data
- Test edge cases and error conditions

### Performance

- Wait for calculations to complete
- Use appropriate timeouts
- Avoid unnecessary waits

### Maintenance

- Keep page objects up to date
- Update test data regularly
- Review and optimize flaky tests

## 🆘 Troubleshooting

### Common Issues

1. **Tests timing out**

   - Increase timeout in playwright.config.ts
   - Check for slow network conditions
   - Verify application is running

2. **Element not found**

   - Check data-testid attributes
   - Verify element is visible
   - Wait for page load completion

3. **Flaky tests**
   - Add proper waits
   - Check for race conditions
   - Use stable selectors

### Getting Help

- Check Playwright documentation: https://playwright.dev/
- Review test logs and screenshots
- Run tests in headed mode for debugging
- Use Playwright Inspector for step-by-step debugging

## 📝 Contributing

When adding new tests:

1. Follow the existing structure and patterns
2. Add appropriate test data to fixtures
3. Update page objects as needed
4. Include both positive and negative test cases
5. Ensure tests are deterministic and not flaky
6. Add documentation for complex test scenarios
