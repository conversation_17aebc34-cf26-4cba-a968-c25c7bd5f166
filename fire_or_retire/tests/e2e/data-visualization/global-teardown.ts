/**
 * Global Teardown for Data Visualization Tests
 * Cleanup and generate comprehensive test reports
 */

import { FullConfig } from '@playwright/test';
import fs from 'fs';
import path from 'path';

async function globalTeardown(config: FullConfig) {
  console.log('🧹 Starting Data Visualization Test Cleanup...');

  const testResultsDir = 'test-results';
  const reportPath = path.join(testResultsDir, 'visualization-summary.json');
  const htmlReportPath = path.join(
    testResultsDir,
    'visualization-summary.html',
  );

  try {
    // Collect test results from various sources
    const testResults = await collectTestResults(testResultsDir);

    // Generate comprehensive summary
    const summary = generateTestSummary(testResults);

    // Save JSON summary
    fs.writeFileSync(reportPath, JSON.stringify(summary, null, 2));
    console.log(`📊 Test summary saved to: ${reportPath}`);

    // Generate HTML report
    const htmlReport = generateHTMLReport(summary);
    fs.writeFileSync(htmlReportPath, htmlReport);
    console.log(`📄 HTML report saved to: ${htmlReportPath}`);

    // Log summary to console
    logTestSummary(summary);

    // Cleanup temporary files
    await cleanupTempFiles(testResultsDir);

    // Archive test artifacts if needed
    if (process.env.CI) {
      await archiveTestArtifacts(testResultsDir);
    }
  } catch (error) {
    console.error('❌ Teardown failed:', error);
  }

  console.log('✅ Data Visualization Test Cleanup Complete!\n');
}

async function collectTestResults(resultsDir: string) {
  const results = {
    junit: null,
    json: null,
    screenshots: [],
    videos: [],
    traces: [],
    performance: [],
  };

  try {
    // Read JUnit results
    const junitPath = path.join(resultsDir, 'visualization-junit.xml');
    if (fs.existsSync(junitPath)) {
      results.junit = fs.readFileSync(junitPath, 'utf8');
    }

    // Read JSON results
    const jsonPath = path.join(resultsDir, 'visualization-results.json');
    if (fs.existsSync(jsonPath)) {
      results.json = JSON.parse(fs.readFileSync(jsonPath, 'utf8'));
    }

    // Collect screenshots
    const screenshotsDir = path.join(resultsDir, 'screenshots');
    if (fs.existsSync(screenshotsDir)) {
      results.screenshots = fs
        .readdirSync(screenshotsDir)
        .filter(file => file.endsWith('.png'))
        .map(file => path.join(screenshotsDir, file));
    }

    // Collect videos
    const videosDir = path.join(resultsDir, 'videos');
    if (fs.existsSync(videosDir)) {
      results.videos = fs
        .readdirSync(videosDir)
        .filter(file => file.endsWith('.webm'))
        .map(file => path.join(videosDir, file));
    }

    // Collect traces
    const tracesDir = path.join(resultsDir, 'traces');
    if (fs.existsSync(tracesDir)) {
      results.traces = fs
        .readdirSync(tracesDir)
        .filter(file => file.endsWith('.zip'))
        .map(file => path.join(tracesDir, file));
    }
  } catch (error) {
    console.warn('⚠️  Error collecting test results:', error);
  }

  return results;
}

function generateTestSummary(testResults: any) {
  const summary = {
    timestamp: new Date().toISOString(),
    environment: {
      node: process.version,
      platform: process.platform,
      ci: !!process.env.CI,
    },
    coverage: {
      chartInteractions: 0,
      mobileSupport: 0,
      accessibility: 0,
      performance: 0,
      crossBrowser: 0,
    },
    statistics: {
      totalTests: 0,
      passed: 0,
      failed: 0,
      skipped: 0,
      duration: 0,
    },
    artifacts: {
      screenshots: testResults.screenshots.length,
      videos: testResults.videos.length,
      traces: testResults.traces.length,
    },
    performance: {
      averageLoadTime: 0,
      averageRenderTime: 0,
      memoryUsage: 0,
      frameRate: 0,
    },
    accessibility: {
      wcagCompliance: 'Unknown',
      keyboardNavigation: 'Unknown',
      screenReaderSupport: 'Unknown',
      colorContrast: 'Unknown',
    },
    browserSupport: {
      chrome: 'Unknown',
      firefox: 'Unknown',
      safari: 'Unknown',
      edge: 'Unknown',
    },
    recommendations: [],
  };

  // Parse JSON results if available
  if (testResults.json) {
    const jsonResults = testResults.json;

    summary.statistics.totalTests = jsonResults.stats?.total || 0;
    summary.statistics.passed = jsonResults.stats?.passed || 0;
    summary.statistics.failed = jsonResults.stats?.failed || 0;
    summary.statistics.skipped = jsonResults.stats?.skipped || 0;
    summary.statistics.duration = jsonResults.stats?.duration || 0;

    // Calculate coverage based on test results
    if (jsonResults.suites) {
      const suites = jsonResults.suites;

      // Chart interactions coverage
      const chartTests = suites.filter((s: any) =>
        s.title.includes('Chart Interactions'),
      );
      summary.coverage.chartInteractions = calculateCoverage(chartTests);

      // Mobile support coverage
      const mobileTests = suites.filter((s: any) => s.title.includes('Mobile'));
      summary.coverage.mobileSupport = calculateCoverage(mobileTests);

      // Accessibility coverage
      const a11yTests = suites.filter((s: any) =>
        s.title.includes('Accessibility'),
      );
      summary.coverage.accessibility = calculateCoverage(a11yTests);

      // Performance coverage
      const perfTests = suites.filter((s: any) =>
        s.title.includes('Performance'),
      );
      summary.coverage.performance = calculateCoverage(perfTests);
    }
  }

  // Generate recommendations based on results
  summary.recommendations = generateRecommendations(summary);

  return summary;
}

function calculateCoverage(tests: any[]): number {
  if (!tests.length) return 0;

  const totalTests = tests.reduce(
    (sum, suite) => sum + (suite.tests?.length || 0),
    0,
  );
  const passedTests = tests.reduce((sum, suite) => {
    return (
      sum + (suite.tests?.filter((t: any) => t.status === 'passed').length || 0)
    );
  }, 0);

  return totalTests > 0 ? Math.round((passedTests / totalTests) * 100) : 0;
}

function generateRecommendations(summary: any): string[] {
  const recommendations = [];

  if (summary.statistics.failed > 0) {
    recommendations.push(
      `🔧 ${summary.statistics.failed} tests failed - review test results and fix issues`,
    );
  }

  if (summary.coverage.accessibility < 90) {
    recommendations.push(
      '♿ Improve accessibility test coverage - aim for 90%+ coverage',
    );
  }

  if (summary.coverage.performance < 80) {
    recommendations.push(
      '⚡ Enhance performance testing - monitor render times and memory usage',
    );
  }

  if (summary.coverage.mobileSupport < 85) {
    recommendations.push(
      '📱 Expand mobile testing coverage - test on more devices and orientations',
    );
  }

  if (summary.artifacts.screenshots > 10) {
    recommendations.push(
      '📸 High number of failure screenshots - investigate common failure patterns',
    );
  }

  if (summary.statistics.duration > 300000) {
    // 5 minutes
    recommendations.push(
      '⏱️  Test suite duration is high - consider parallelization or optimization',
    );
  }

  return recommendations;
}

function generateHTMLReport(summary: any): string {
  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Visualization Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; }
        .stat-value { font-size: 2em; font-weight: bold; color: #007bff; }
        .stat-label { color: #6c757d; margin-top: 5px; }
        .coverage { margin-bottom: 30px; }
        .coverage-bar { background: #e9ecef; height: 20px; border-radius: 10px; overflow: hidden; margin: 10px 0; }
        .coverage-fill { height: 100%; background: linear-gradient(90deg, #28a745, #ffc107, #dc3545); transition: width 0.3s; }
        .recommendations { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 20px; }
        .recommendation { margin: 10px 0; padding: 10px; background: white; border-radius: 4px; }
        .success { color: #28a745; }
        .warning { color: #ffc107; }
        .error { color: #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 Data Visualization Test Report</h1>
            <p>Generated on ${new Date(summary.timestamp).toLocaleString()}</p>
        </div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-value ${summary.statistics.failed === 0 ? 'success' : 'error'}">${summary.statistics.passed}</div>
                <div class="stat-label">Tests Passed</div>
            </div>
            <div class="stat-card">
                <div class="stat-value ${summary.statistics.failed === 0 ? 'success' : 'error'}">${summary.statistics.failed}</div>
                <div class="stat-label">Tests Failed</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">${summary.statistics.totalTests}</div>
                <div class="stat-label">Total Tests</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">${Math.round(summary.statistics.duration / 1000)}s</div>
                <div class="stat-label">Duration</div>
            </div>
        </div>

        <div class="coverage">
            <h2>Test Coverage</h2>
            ${Object.entries(summary.coverage)
              .map(
                ([key, value]) => `
                <div>
                    <strong>${key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}:</strong> ${value}%
                    <div class="coverage-bar">
                        <div class="coverage-fill" style="width: ${value}%"></div>
                    </div>
                </div>
            `,
              )
              .join('')}
        </div>

        <div class="recommendations">
            <h2>Recommendations</h2>
            ${summary.recommendations
              .map(
                (rec: string) => `
                <div class="recommendation">${rec}</div>
            `,
              )
              .join('')}
        </div>

        <div class="artifacts">
            <h2>Test Artifacts</h2>
            <ul>
                <li>Screenshots: ${summary.artifacts.screenshots}</li>
                <li>Videos: ${summary.artifacts.videos}</li>
                <li>Traces: ${summary.artifacts.traces}</li>
            </ul>
        </div>
    </div>
</body>
</html>
  `;
}

function logTestSummary(summary: any) {
  console.log('\n📊 DATA VISUALIZATION TEST SUMMARY');
  console.log('=====================================');
  console.log(`📅 Timestamp: ${summary.timestamp}`);
  console.log(`🧪 Total Tests: ${summary.statistics.totalTests}`);
  console.log(`✅ Passed: ${summary.statistics.passed}`);
  console.log(`❌ Failed: ${summary.statistics.failed}`);
  console.log(`⏭️  Skipped: ${summary.statistics.skipped}`);
  console.log(
    `⏱️  Duration: ${Math.round(summary.statistics.duration / 1000)}s`,
  );
  console.log('\n📈 COVERAGE:');
  Object.entries(summary.coverage).forEach(([key, value]) => {
    console.log(`  ${key}: ${value}%`);
  });
  console.log('\n📦 ARTIFACTS:');
  console.log(`  Screenshots: ${summary.artifacts.screenshots}`);
  console.log(`  Videos: ${summary.artifacts.videos}`);
  console.log(`  Traces: ${summary.artifacts.traces}`);

  if (summary.recommendations.length > 0) {
    console.log('\n💡 RECOMMENDATIONS:');
    summary.recommendations.forEach((rec: string) => {
      console.log(`  ${rec}`);
    });
  }
  console.log('=====================================\n');
}

async function cleanupTempFiles(resultsDir: string) {
  // Clean up temporary files but keep important artifacts
  const tempFiles = ['test-data.json', '.tmp'];

  tempFiles.forEach(file => {
    const filePath = path.join(resultsDir, file);
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
      console.log(`🗑️  Cleaned up: ${file}`);
    }
  });
}

async function archiveTestArtifacts(resultsDir: string) {
  // In CI environment, archive important artifacts
  console.log('📦 Archiving test artifacts for CI...');

  // This would typically involve uploading to artifact storage
  // For now, just log the action
  console.log('✅ Test artifacts archived');
}

export default globalTeardown;

// Utility functions for test analysis
export class TestAnalytics {
  static analyzeFailurePatterns(testResults: any) {
    const patterns = {
      browserSpecific: [],
      deviceSpecific: [],
      featureSpecific: [],
      timeoutRelated: [],
    };

    // Analyze failure patterns from test results
    if (testResults.json?.suites) {
      testResults.json.suites.forEach((suite: any) => {
        suite.tests?.forEach((test: any) => {
          if (test.status === 'failed') {
            // Categorize failures
            if (
              test.title.includes('chrome') ||
              test.title.includes('firefox')
            ) {
              patterns.browserSpecific.push(test);
            }
            if (
              test.title.includes('mobile') ||
              test.title.includes('tablet')
            ) {
              patterns.deviceSpecific.push(test);
            }
            if (test.error?.message?.includes('timeout')) {
              patterns.timeoutRelated.push(test);
            }
          }
        });
      });
    }

    return patterns;
  }

  static generatePerformanceReport(summary: any) {
    return {
      loadTimeAnalysis:
        summary.performance.averageLoadTime > 2000
          ? 'Needs Improvement'
          : 'Good',
      renderTimeAnalysis:
        summary.performance.averageRenderTime > 500
          ? 'Needs Improvement'
          : 'Good',
      memoryAnalysis:
        summary.performance.memoryUsage > 100 ? 'High Usage' : 'Normal',
      recommendations: [
        summary.performance.averageLoadTime > 2000 &&
          'Optimize chart loading performance',
        summary.performance.averageRenderTime > 500 &&
          'Improve chart rendering efficiency',
        summary.performance.memoryUsage > 100 && 'Reduce memory consumption',
      ].filter(Boolean),
    };
  }
}
