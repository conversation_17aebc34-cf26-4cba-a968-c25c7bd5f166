/**
 * Global Setup for Data Visualization Tests
 * Prepares test environment and validates prerequisites
 */

import { chromium, FullConfig } from '@playwright/test';
import fs from 'fs';
import path from 'path';

async function globalSetup(config: FullConfig) {
  console.log('🚀 Setting up Data Visualization Test Environment...');

  // Create test results directories
  const testResultsDir = 'test-results';
  const screenshotsDir = path.join(testResultsDir, 'screenshots');
  const reportsDir = path.join(testResultsDir, 'visualization-report');
  
  [testResultsDir, screenshotsDir, reportsDir].forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      console.log(`📁 Created directory: ${dir}`);
    }
  });

  // Launch browser for environment validation
  const browser = await chromium.launch();
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    // Validate application is running
    console.log('🔍 Validating application availability...');
    const baseURL = config.projects[0].use?.baseURL || 'http://localhost:3000';
    
    await page.goto(baseURL, { timeout: 30000 });
    await page.waitForLoadState('networkidle');
    
    // Check if the application loads correctly
    const title = await page.title();
    console.log(`✅ Application loaded: ${title}`);

    // Validate D3.js library is available
    console.log('🔍 Validating D3.js library...');
    const d3Available = await page.evaluate(() => {
      return typeof (window as any).d3 !== 'undefined' || 
             document.querySelector('script[src*="d3"]') !== null;
    });
    
    if (d3Available) {
      console.log('✅ D3.js library detected');
    } else {
      console.log('⚠️  D3.js library not detected - charts may not render');
    }

    // Validate SVG support
    console.log('🔍 Validating SVG support...');
    const svgSupport = await page.evaluate(() => {
      return document.implementation.hasFeature('http://www.w3.org/TR/SVG11/feature#BasicStructure', '1.1');
    });
    
    if (svgSupport) {
      console.log('✅ SVG support confirmed');
    } else {
      console.warn('⚠️  SVG support not detected');
    }

    // Validate Canvas support (for export functionality)
    console.log('🔍 Validating Canvas support...');
    const canvasSupport = await page.evaluate(() => {
      const canvas = document.createElement('canvas');
      return !!(canvas.getContext && canvas.getContext('2d'));
    });
    
    if (canvasSupport) {
      console.log('✅ Canvas support confirmed');
    } else {
      console.warn('⚠️  Canvas support not detected - export may not work');
    }

    // Validate touch events support (for mobile testing)
    console.log('🔍 Validating touch support...');
    const touchSupport = await page.evaluate(() => {
      return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    });
    
    if (touchSupport) {
      console.log('✅ Touch events support confirmed');
    } else {
      console.log('ℹ️  Touch events not supported (desktop environment)');
    }

    // Check for performance APIs
    console.log('🔍 Validating performance APIs...');
    const performanceAPIs = await page.evaluate(() => {
      return {
        performance: typeof performance !== 'undefined',
        memory: typeof (performance as any).memory !== 'undefined',
        observer: typeof PerformanceObserver !== 'undefined',
        timing: typeof performance.timing !== 'undefined',
      };
    });
    
    console.log('📊 Performance APIs:', performanceAPIs);

    // Validate accessibility features
    console.log('🔍 Validating accessibility features...');
    const a11yFeatures = await page.evaluate(() => {
      return {
        ariaSupport: document.implementation.hasFeature('http://www.w3.org/TR/wai-aria/', '1.0'),
        focusManagement: typeof document.activeElement !== 'undefined',
        screenReaderAPIs: typeof window.speechSynthesis !== 'undefined',
      };
    });
    
    console.log('♿ Accessibility features:', a11yFeatures);

    // Test basic chart rendering capability
    console.log('🔍 Testing basic chart rendering...');
    await page.addScriptTag({
      content: `
        // Create a simple SVG to test rendering
        const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
        svg.setAttribute('width', '100');
        svg.setAttribute('height', '100');
        svg.setAttribute('id', 'test-svg');
        
        const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        circle.setAttribute('cx', '50');
        circle.setAttribute('cy', '50');
        circle.setAttribute('r', '20');
        circle.setAttribute('fill', 'blue');
        
        svg.appendChild(circle);
        document.body.appendChild(svg);
      `,
    });

    const testSVG = await page.locator('#test-svg').isVisible();
    if (testSVG) {
      console.log('✅ Basic SVG rendering confirmed');
    } else {
      console.warn('⚠️  SVG rendering test failed');
    }

    // Create test data for visualization tests
    console.log('📝 Preparing test data...');
    const testData = {
      profiles: {
        young: { age: 25, income: 6000, expenses: 3500, savings: 50000 },
        mid: { age: 35, income: 10000, expenses: 6000, savings: 200000 },
        senior: { age: 55, income: 12000, expenses: 8000, savings: 800000 },
      },
      timeframes: ['1M', '3M', '6M', '1Y', '2Y', 'ALL'],
      chartTypes: ['line', 'area', 'bar', 'scatter'],
      metrics: ['netWorth', 'savingsRate', 'fireProgress', 'monthlyIncome'],
    };

    // Store test data for use in tests
    const testDataPath = path.join(testResultsDir, 'test-data.json');
    fs.writeFileSync(testDataPath, JSON.stringify(testData, null, 2));
    console.log(`💾 Test data saved to: ${testDataPath}`);

    // Environment validation summary
    console.log('\n📋 Environment Validation Summary:');
    console.log('================================');
    console.log(`✅ Application: Available at ${baseURL}`);
    console.log(`✅ D3.js: ${d3Available ? 'Available' : 'Not detected'}`);
    console.log(`✅ SVG: ${svgSupport ? 'Supported' : 'Not supported'}`);
    console.log(`✅ Canvas: ${canvasSupport ? 'Supported' : 'Not supported'}`);
    console.log(`✅ Touch: ${touchSupport ? 'Supported' : 'Not supported'}`);
    console.log(`✅ Performance APIs: ${performanceAPIs.performance ? 'Available' : 'Limited'}`);
    console.log(`✅ Accessibility: ${a11yFeatures.ariaSupport ? 'Supported' : 'Limited'}`);
    console.log('================================\n');

  } catch (error) {
    console.error('❌ Environment validation failed:', error);
    throw error;
  } finally {
    await context.close();
    await browser.close();
  }

  console.log('✅ Data Visualization Test Environment Setup Complete!\n');
}

export default globalSetup;
