/**
 * E2E Tests for Mobile Data Visualization
 * Tests touch interactions and mobile-optimized chart features
 */

import { test, expect, devices } from '@playwright/test';
import { SwissBudgetProPage } from '../../page-objects/pages/SwissBudgetProPage';
import { DataVisualizationPage } from '../../page-objects/pages/DataVisualizationPage';

test.describe('Mobile Data Visualization', () => {
  let swissBudgetPage: SwissBudgetProPage;
  let dataVizPage: DataVisualizationPage;

  test.beforeEach(async ({ page, browser }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 }); // iPhone SE size
    
    swissBudgetPage = new SwissBudgetProPage(page);
    dataVizPage = new DataVisualizationPage(page);
    
    await swissBudgetPage.goto();
    await swissBudgetPage.verifyPageLoaded();
    
    // Set up test data
    await swissBudgetPage.fillBasicFinancialInfo({
      monthlyIncome: 6000,
      monthlyExpenses: 4000,
      currentSavings: 80000,
      canton: 'ZH',
      age: 28,
      retirementAge: 55,
    });
    
    await dataVizPage.navigateToVisualization();
  });

  test('E2E-MOBILE-VIZ-001: Mobile Chart Rendering', async ({ page }) => {
    test.step('Verify mobile-optimized charts load', async () => {
      await dataVizPage.waitForChartsToLoad();
      
      // Verify charts are sized appropriately for mobile
      const chartWidth = await page.locator('svg').first().getAttribute('width');
      const numericWidth = parseInt(chartWidth || '0');
      
      expect(numericWidth).toBeLessThan(400); // Should fit mobile screen
      expect(numericWidth).toBeGreaterThan(250); // Should be usable
    });

    test.step('Verify mobile controls are visible', async () => {
      // Check for mobile-specific UI elements
      const mobileIndicators = page.locator('text=👆 Tap data points, text=👈👉 Swipe to pan');
      
      if (await mobileIndicators.first().isVisible()) {
        await expect(mobileIndicators.first()).toBeVisible();
      }
      
      // Verify touch-friendly button sizes
      const buttons = page.locator('button');
      const buttonCount = await buttons.count();
      
      for (let i = 0; i < Math.min(buttonCount, 5); i++) {
        const button = buttons.nth(i);
        if (await button.isVisible()) {
          const box = await button.boundingBox();
          if (box) {
            // Touch targets should be at least 44px (iOS guideline)
            expect(Math.min(box.width, box.height)).toBeGreaterThanOrEqual(40);
          }
        }
      }
    });

    test.step('Verify responsive layout adjustments', async () => {
      // Check if layout stacks vertically on mobile
      const chartContainer = page.locator('.enhanced-chart-container, [data-testid="chart-container"]');
      
      if (await chartContainer.isVisible()) {
        const containerClass = await chartContainer.getAttribute('class');
        // Should use mobile-friendly grid layout
        expect(containerClass).toMatch(/grid-cols-1|flex-col|space-y/);
      }
    });
  });

  test('E2E-MOBILE-VIZ-002: Touch Interactions', async ({ page }) => {
    test.step('Test tap interactions on data points', async () => {
      await dataVizPage.waitForChartsToLoad();
      
      // Find a data point and tap it
      const dataPoint = page.locator('svg circle.data-point, svg circle').first();
      await expect(dataPoint).toBeVisible();
      
      // Perform tap gesture
      await dataPoint.tap();
      await page.waitForTimeout(500);
      
      // Verify tap response (tooltip, selection, etc.)
      const tooltip = page.locator('.tooltip, [data-testid="tooltip"]');
      if (await tooltip.isVisible()) {
        await expect(tooltip).toContainText(/CHF|%/);
      }
      
      // Check for selection indicators
      const selectedInfo = page.locator('[data-testid="selected-data-point"]');
      if (await selectedInfo.isVisible()) {
        await expect(selectedInfo).toBeVisible();
      }
    });

    test.step('Test swipe gestures for panning', async () => {
      const chart = page.locator('svg').first();
      const chartBox = await chart.boundingBox();
      
      if (chartBox) {
        // Perform swipe gesture from left to right
        await page.touchscreen.tap(chartBox.x + 50, chartBox.y + chartBox.height / 2);
        await page.touchscreen.tap(chartBox.x + chartBox.width - 50, chartBox.y + chartBox.height / 2);
        
        await page.waitForTimeout(300);
        
        // Verify chart responds to swipe (if pan functionality is implemented)
        // This would depend on the specific implementation
      }
    });

    test.step('Test pinch-to-zoom gestures', async ({ page, browserName }) => {
      // Skip on WebKit as it has limited touch simulation support
      test.skip(browserName === 'webkit', 'WebKit has limited touch simulation');
      
      const chart = page.locator('svg').first();
      const chartBox = await chart.boundingBox();
      
      if (chartBox) {
        const centerX = chartBox.x + chartBox.width / 2;
        const centerY = chartBox.y + chartBox.height / 2;
        
        // Simulate pinch gesture (if zoom functionality is implemented)
        // This is a simplified simulation
        await page.touchscreen.tap(centerX - 50, centerY);
        await page.touchscreen.tap(centerX + 50, centerY);
        
        await page.waitForTimeout(300);
        
        // Verify zoom response would be implementation-specific
      }
    });

    test.step('Test long press interactions', async () => {
      const dataPoint = page.locator('svg circle.data-point, svg circle').first();
      
      if (await dataPoint.isVisible()) {
        // Simulate long press
        const box = await dataPoint.boundingBox();
        if (box) {
          await page.touchscreen.tap(box.x + box.width / 2, box.y + box.height / 2);
          await page.waitForTimeout(800); // Long press duration
          
          // Verify long press response (context menu, detailed view, etc.)
          const contextMenu = page.locator('[data-testid="context-menu"], .context-menu');
          if (await contextMenu.isVisible()) {
            await expect(contextMenu).toBeVisible();
          }
        }
      }
    });
  });

  test('E2E-MOBILE-VIZ-003: Mobile Chart Controls', async ({ page }) => {
    test.step('Test mobile timeframe selector', async () => {
      await dataVizPage.waitForChartsToLoad();
      
      // Test timeframe buttons on mobile
      const timeframes = ['1M', '3M', '6M', '1Y'];
      
      for (const timeframe of timeframes) {
        const button = page.locator(`button:has-text("${timeframe}")`);
        
        if (await button.isVisible()) {
          await button.tap();
          await page.waitForTimeout(300);
          
          // Verify selection
          await expect(button).toHaveClass(/bg-blue-600|active|selected/);
          
          // Verify chart updates
          await dataVizPage.waitForChartUpdate();
        }
      }
    });

    test.step('Test mobile export functionality', async () => {
      // Open export menu
      const exportButton = page.locator('button:has-text("Export")');
      
      if (await exportButton.isVisible()) {
        await exportButton.tap();
        await page.waitForTimeout(200);
        
        // Test mobile-friendly export options
        const exportOptions = page.locator('button:has-text("PNG"), button:has-text("CSV")');
        const optionCount = await exportOptions.count();
        
        if (optionCount > 0) {
          // Test PNG export on mobile
          const downloadPromise = page.waitForEvent('download');
          await exportOptions.first().tap();
          
          const download = await downloadPromise;
          expect(download.suggestedFilename()).toMatch(/\.(png|csv)$/);
        }
      }
    });

    test.step('Test mobile settings panel', async () => {
      // Look for settings or configuration panel
      const settingsButton = page.locator('button:has-text("Settings"), button[aria-label*="settings"]');
      
      if (await settingsButton.isVisible()) {
        await settingsButton.tap();
        await page.waitForTimeout(200);
        
        // Verify mobile-optimized settings layout
        const settingsPanel = page.locator('[data-testid="dashboard-settings"]');
        if (await settingsPanel.isVisible()) {
          // Settings should be stacked vertically on mobile
          const settingsClass = await settingsPanel.getAttribute('class');
          expect(settingsClass).toMatch(/grid-cols-1|flex-col/);
        }
      }
    });
  });

  test('E2E-MOBILE-VIZ-004: Mobile Performance', async ({ page }) => {
    test.step('Test mobile chart loading performance', async () => {
      const startTime = Date.now();
      await dataVizPage.waitForChartsToLoad();
      const loadTime = Date.now() - startTime;
      
      // Mobile should load charts within 3 seconds
      expect(loadTime).toBeLessThan(3000);
      console.log(`Mobile chart load time: ${loadTime}ms`);
    });

    test.step('Test mobile memory usage', async () => {
      // Generate sample data to test with larger dataset
      await dataVizPage.generateSampleData();
      await dataVizPage.waitForChartsToLoad();
      
      // Check memory usage (if available)
      const metrics = await page.evaluate(() => {
        const memory = (performance as any).memory;
        return memory ? {
          used: memory.usedJSHeapSize,
          total: memory.totalJSHeapSize,
          limit: memory.jsHeapSizeLimit,
        } : null;
      });
      
      if (metrics) {
        console.log('Mobile memory usage:', metrics);
        // Mobile should use less than 50MB
        expect(metrics.used).toBeLessThan(50 * 1024 * 1024);
      }
    });

    test.step('Test mobile interaction responsiveness', async () => {
      const dataPoint = page.locator('svg circle.data-point, svg circle').first();
      
      if (await dataPoint.isVisible()) {
        // Measure tap response time
        const startTime = Date.now();
        await dataPoint.tap();
        
        // Wait for visual feedback
        await page.waitForTimeout(100);
        const responseTime = Date.now() - startTime;
        
        // Touch response should be under 100ms
        expect(responseTime).toBeLessThan(200);
        console.log(`Touch response time: ${responseTime}ms`);
      }
    });
  });

  test('E2E-MOBILE-VIZ-005: Mobile Accessibility', async ({ page }) => {
    test.step('Test mobile screen reader compatibility', async () => {
      // Check for proper ARIA labels on mobile
      const charts = page.locator('svg[aria-label], svg[role="img"]');
      const chartCount = await charts.count();
      
      if (chartCount > 0) {
        const firstChart = charts.first();
        const ariaLabel = await firstChart.getAttribute('aria-label');
        expect(ariaLabel).toBeTruthy();
        expect(ariaLabel).toMatch(/chart|graph|visualization/i);
      }
    });

    test.step('Test mobile keyboard navigation', async () => {
      // Test tab navigation on mobile (external keyboard)
      await page.keyboard.press('Tab');
      
      const focusedElement = page.locator(':focus');
      if (await focusedElement.isVisible()) {
        await expect(focusedElement).toBeVisible();
        
        // Verify focus indicators are visible
        const outline = await focusedElement.evaluate(el => 
          window.getComputedStyle(el).outline,
        );
        expect(outline).not.toBe('none');
      }
    });

    test.step('Test mobile color contrast', async () => {
      // Check if charts maintain good contrast on mobile
      const chartElements = page.locator('svg path, svg circle, svg text');
      const elementCount = await chartElements.count();
      
      if (elementCount > 0) {
        const firstElement = chartElements.first();
        const styles = await firstElement.evaluate(el => {
          const computed = window.getComputedStyle(el);
          return {
            fill: computed.fill,
            stroke: computed.stroke,
            color: computed.color,
          };
        });
        
        // Verify colors are not transparent or too light
        expect(styles.fill).not.toBe('none');
        expect(styles.stroke).not.toBe('none');
      }
    });
  });

  test('E2E-MOBILE-VIZ-006: Mobile Error Handling', async ({ page }) => {
    test.step('Test mobile network interruption', async () => {
      // Simulate poor network conditions
      await page.context().setOffline(true);
      
      // Try to interact with charts
      await dataVizPage.selectTimeframe('1Y');
      await page.waitForTimeout(1000);
      
      // Verify graceful degradation
      const errorMessage = page.locator('[data-testid="error-message"], .error-message');
      if (await errorMessage.isVisible()) {
        await expect(errorMessage).toContainText(/network|offline|connection/i);
      }
      
      // Restore network
      await page.context().setOffline(false);
      await page.waitForTimeout(1000);
      
      // Verify recovery
      await dataVizPage.waitForChartsToLoad();
    });

    test.step('Test mobile orientation change', async () => {
      // Test portrait to landscape
      await page.setViewportSize({ width: 667, height: 375 }); // Landscape
      await page.waitForTimeout(500);
      
      // Verify charts adapt to landscape
      await dataVizPage.waitForChartUpdate();
      
      const chartWidth = await page.locator('svg').first().getAttribute('width');
      expect(parseInt(chartWidth || '0')).toBeGreaterThan(400);
      
      // Test landscape to portrait
      await page.setViewportSize({ width: 375, height: 667 }); // Portrait
      await page.waitForTimeout(500);
      
      // Verify charts adapt back to portrait
      await dataVizPage.waitForChartUpdate();
    });

    test.step('Test mobile memory pressure', async () => {
      // Generate large dataset to test memory handling
      for (let i = 0; i < 3; i++) {
        await dataVizPage.generateSampleData();
        await page.waitForTimeout(500);
      }
      
      // Verify app remains responsive
      await dataVizPage.selectTimeframe('ALL');
      await dataVizPage.waitForChartUpdate();
      
      // Verify charts still render correctly
      const dataPoints = await page.locator('svg circle.data-point, svg circle').count();
      expect(dataPoints).toBeGreaterThan(0);
    });
  });
});

// Additional mobile-specific test configurations
test.describe('Mobile Data Visualization - Device Specific', () => {
  test('iPhone 12 Pro - Portrait Mode', async ({ browser }) => {
    const context = await browser.newContext({
      ...devices['iPhone 12 Pro'],
    });
    const page = await context.newPage();
    
    const dataVizPage = new DataVisualizationPage(page);
    await page.goto('/');
    await dataVizPage.navigateToVisualization();
    await dataVizPage.waitForChartsToLoad();
    
    // iPhone-specific tests
    await dataVizPage.testTouchInteractions();
    await dataVizPage.verifyChartData();
    
    await context.close();
  });

  test('Samsung Galaxy S21 - Landscape Mode', async ({ browser }) => {
    const context = await browser.newContext({
      ...devices['Galaxy S21'],
      viewport: { width: 854, height: 390 }, // Landscape
    });
    const page = await context.newPage();
    
    const dataVizPage = new DataVisualizationPage(page);
    await page.goto('/');
    await dataVizPage.navigateToVisualization();
    await dataVizPage.waitForChartsToLoad();
    
    // Android landscape-specific tests
    await dataVizPage.verifyChartData();
    
    await context.close();
  });

  test('iPad Air - Tablet Mode', async ({ browser }) => {
    const context = await browser.newContext({
      ...devices['iPad Air'],
    });
    const page = await context.newPage();
    
    const dataVizPage = new DataVisualizationPage(page);
    await page.goto('/');
    await dataVizPage.navigateToVisualization();
    await dataVizPage.waitForChartsToLoad();
    
    // Tablet-specific tests
    await dataVizPage.verifyChartData();
    
    await context.close();
  });
});
