/**
 * E2E Tests for Data Visualization Chart Interactions
 * Tests interactive features of the enhanced D3.js charts
 */

import { test, expect } from '@playwright/test';
import { SwissBudgetProPage } from '../../page-objects/pages/SwissBudgetProPage';
import { DataVisualizationPage } from '../../page-objects/pages/DataVisualizationPage';

test.describe('Data Visualization - Chart Interactions', () => {
  let swissBudgetPage: SwissBudgetProPage;
  let dataVizPage: DataVisualizationPage;

  test.beforeEach(async ({ page }) => {
    swissBudgetPage = new SwissBudgetProPage(page);
    dataVizPage = new DataVisualizationPage(page);
    
    await swissBudgetPage.goto();
    await swissBudgetPage.verifyPageLoaded();
    
    // Set up test data for visualization
    await swissBudgetPage.fillBasicFinancialInfo({
      monthlyIncome: 8000,
      monthlyExpenses: 5000,
      currentSavings: 100000,
      canton: 'ZH',
      age: 30,
      retirementAge: 60,
    });
    
    // Navigate to data visualization section
    await dataVizPage.navigateToVisualization();
  });

  test('E2E-VIZ-001: Chart Loading and Initial Render', async ({ page }) => {
    test.step('Verify charts load correctly', async () => {
      await dataVizPage.waitForChartsToLoad();
      
      // Verify main dashboard is visible
      await expect(dataVizPage.dashboardHeader).toBeVisible();
      await expect(dataVizPage.dashboardHeader).toContainText('Data Visualization Dashboard');
      
      // Verify chart containers are present
      await expect(dataVizPage.chartContainer).toBeVisible();
      await expect(dataVizPage.chartControls).toBeVisible();
    });

    test.step('Verify chart SVG elements are rendered', async () => {
      // Wait for D3.js charts to render
      await page.waitForSelector('svg', { timeout: 10000 });
      
      const svgElements = await page.locator('svg').count();
      expect(svgElements).toBeGreaterThan(0);
      
      // Verify chart has data points
      const dataPoints = await page.locator('svg circle.data-point').count();
      expect(dataPoints).toBeGreaterThan(0);
    });

    test.step('Verify chart performance metrics', async () => {
      // Measure chart render time
      const startTime = Date.now();
      await dataVizPage.waitForChartsToLoad();
      const renderTime = Date.now() - startTime;
      
      // Chart should render within 2 seconds
      expect(renderTime).toBeLessThan(2000);
      
      console.log(`Chart render time: ${renderTime}ms`);
    });
  });

  test('E2E-VIZ-002: Interactive Chart Controls', async ({ page }) => {
    test.step('Test timeframe selector', async () => {
      await dataVizPage.waitForChartsToLoad();
      
      // Test different timeframe options
      const timeframes = ['1M', '3M', '6M', '1Y', '2Y', 'ALL'];
      
      for (const timeframe of timeframes) {
        await dataVizPage.selectTimeframe(timeframe);
        await page.waitForTimeout(500); // Wait for chart update
        
        // Verify timeframe is selected
        const selectedButton = page.locator(`button:has-text("${timeframe}")`);
        await expect(selectedButton).toHaveClass(/bg-blue-600/);
        
        // Verify chart updates
        await dataVizPage.waitForChartUpdate();
      }
    });

    test.step('Test chart type selector', async () => {
      const chartTypes = ['line', 'area', 'bar', 'scatter'];
      
      for (const chartType of chartTypes) {
        await dataVizPage.selectChartType(chartType);
        await page.waitForTimeout(500);
        
        // Verify chart type is selected
        const selectedButton = page.locator(`button[title*="${chartType}"]`);
        await expect(selectedButton).toHaveClass(/bg-blue-600/);
        
        // Verify chart renders with correct type
        await dataVizPage.verifyChartType(chartType);
      }
    });

    test.step('Test metric toggles', async () => {
      const metrics = ['Net Worth', 'Savings Rate', 'FIRE Progress'];
      
      for (const metric of metrics) {
        // Toggle metric off
        await dataVizPage.toggleMetric(metric);
        await page.waitForTimeout(300);
        
        // Verify metric is deselected
        const metricButton = page.locator(`button:has-text("${metric}")`);
        await expect(metricButton).not.toHaveClass(/bg-opacity-20/);
        
        // Toggle metric back on
        await dataVizPage.toggleMetric(metric);
        await page.waitForTimeout(300);
        
        // Verify metric is selected
        await expect(metricButton).toHaveClass(/bg-opacity-20/);
      }
    });
  });

  test('E2E-VIZ-003: Chart Data Point Interactions', async ({ page }) => {
    test.step('Test hover interactions', async () => {
      await dataVizPage.waitForChartsToLoad();
      
      // Find first data point
      const dataPoint = page.locator('svg circle.data-point').first();
      await expect(dataPoint).toBeVisible();
      
      // Hover over data point
      await dataPoint.hover();
      
      // Verify tooltip appears
      await expect(dataVizPage.tooltip).toBeVisible({ timeout: 2000 });
      
      // Verify tooltip contains data
      const tooltipText = await dataVizPage.tooltip.textContent();
      expect(tooltipText).toMatch(/CHF|%/); // Should contain currency or percentage
      
      // Move away and verify tooltip disappears
      await page.locator('body').hover();
      await expect(dataVizPage.tooltip).not.toBeVisible();
    });

    test.step('Test click interactions', async () => {
      // Click on data point
      const dataPoint = page.locator('svg circle.data-point').first();
      await dataPoint.click();
      
      // Verify data point selection (if implemented)
      // This would depend on the specific implementation
      await page.waitForTimeout(500);
      
      // Verify any selection indicators or detailed views
      const selectedDataInfo = page.locator('[data-testid="selected-data-point"]');
      if (await selectedDataInfo.isVisible()) {
        await expect(selectedDataInfo).toContainText(/Date:|Value:/);
      }
    });

    test.step('Test keyboard navigation', async () => {
      // Focus on chart area
      await page.locator('svg').first().focus();
      
      // Test arrow key navigation (if implemented)
      await page.keyboard.press('ArrowRight');
      await page.waitForTimeout(200);
      
      await page.keyboard.press('ArrowLeft');
      await page.waitForTimeout(200);
      
      // Test Enter key for selection
      await page.keyboard.press('Enter');
      await page.waitForTimeout(200);
    });
  });

  test('E2E-VIZ-004: Chart Export Functionality', async ({ page }) => {
    test.step('Test PNG export', async () => {
      await dataVizPage.waitForChartsToLoad();
      
      // Open export menu
      await dataVizPage.openExportMenu();
      
      // Set up download handler
      const downloadPromise = page.waitForEvent('download');
      
      // Click PNG export
      await dataVizPage.exportAs('png');
      
      // Verify download
      const download = await downloadPromise;
      expect(download.suggestedFilename()).toMatch(/\.png$/);
      
      // Verify file size is reasonable (not empty)
      const path = await download.path();
      if (path) {
        const fs = require('fs');
        const stats = fs.statSync(path);
        expect(stats.size).toBeGreaterThan(1000); // At least 1KB
      }
    });

    test.step('Test SVG export', async () => {
      await dataVizPage.openExportMenu();
      
      const downloadPromise = page.waitForEvent('download');
      await dataVizPage.exportAs('svg');
      
      const download = await downloadPromise;
      expect(download.suggestedFilename()).toMatch(/\.svg$/);
    });

    test.step('Test CSV data export', async () => {
      await dataVizPage.openExportMenu();
      
      const downloadPromise = page.waitForEvent('download');
      await dataVizPage.exportAs('csv');
      
      const download = await downloadPromise;
      expect(download.suggestedFilename()).toMatch(/\.csv$/);
      
      // Verify CSV content structure
      const path = await download.path();
      if (path) {
        const fs = require('fs');
        const content = fs.readFileSync(path, 'utf8');
        expect(content).toMatch(/date|value/i); // Should have headers
        expect(content.split('\n').length).toBeGreaterThan(1); // Should have data rows
      }
    });

    test.step('Test JSON data export', async () => {
      await dataVizPage.openExportMenu();
      
      const downloadPromise = page.waitForEvent('download');
      await dataVizPage.exportAs('json');
      
      const download = await downloadPromise;
      expect(download.suggestedFilename()).toMatch(/\.json$/);
      
      // Verify JSON structure
      const path = await download.path();
      if (path) {
        const fs = require('fs');
        const content = fs.readFileSync(path, 'utf8');
        const data = JSON.parse(content);
        expect(Array.isArray(data)).toBeTruthy();
        expect(data.length).toBeGreaterThan(0);
      }
    });
  });

  test('E2E-VIZ-005: Responsive Chart Behavior', async ({ page }) => {
    test.step('Test desktop viewport', async () => {
      await page.setViewportSize({ width: 1280, height: 720 });
      await dataVizPage.waitForChartsToLoad();
      
      // Verify charts are properly sized for desktop
      const chartWidth = await page.locator('svg').first().getAttribute('width');
      expect(parseInt(chartWidth || '0')).toBeGreaterThan(400);
    });

    test.step('Test tablet viewport', async () => {
      await page.setViewportSize({ width: 768, height: 1024 });
      await page.waitForTimeout(500); // Wait for responsive update
      
      // Verify charts adapt to tablet size
      const chartWidth = await page.locator('svg').first().getAttribute('width');
      expect(parseInt(chartWidth || '0')).toBeLessThan(800);
      expect(parseInt(chartWidth || '0')).toBeGreaterThan(300);
    });

    test.step('Test mobile viewport', async () => {
      await page.setViewportSize({ width: 375, height: 667 });
      await page.waitForTimeout(500);
      
      // Verify mobile optimization
      const chartWidth = await page.locator('svg').first().getAttribute('width');
      expect(parseInt(chartWidth || '0')).toBeLessThan(400);
      
      // Verify mobile controls are visible
      await expect(dataVizPage.mobileControls).toBeVisible();
    });
  });

  test('E2E-VIZ-006: Chart Animation and Performance', async ({ page }) => {
    test.step('Test chart animations', async () => {
      await dataVizPage.waitForChartsToLoad();
      
      // Change chart type to trigger animation
      await dataVizPage.selectChartType('area');
      
      // Verify animation occurs (check for transition classes or styles)
      const animatedElement = page.locator('svg path[stroke-dasharray]');
      if (await animatedElement.isVisible()) {
        // Animation is present
        await page.waitForTimeout(1500); // Wait for animation to complete
        
        // Verify animation completes
        const finalDashOffset = await animatedElement.getAttribute('stroke-dashoffset');
        expect(finalDashOffset).toBe('0');
      }
    });

    test.step('Test performance with large datasets', async () => {
      // Generate sample data
      await dataVizPage.generateSampleData();
      await dataVizPage.waitForChartsToLoad();
      
      // Measure render performance
      const startTime = Date.now();
      await dataVizPage.selectTimeframe('ALL'); // Show all data
      await dataVizPage.waitForChartUpdate();
      const renderTime = Date.now() - startTime;
      
      // Should handle large datasets efficiently
      expect(renderTime).toBeLessThan(3000); // 3 seconds max
      
      console.log(`Large dataset render time: ${renderTime}ms`);
    });

    test.step('Test memory usage', async () => {
      // This would require browser performance APIs
      const metrics = await page.evaluate(() => {
        return {
          memory: (performance as any).memory?.usedJSHeapSize || 0,
          timing: performance.timing,
        };
      });
      
      console.log('Memory usage:', metrics.memory);
      
      // Basic memory check (if available)
      if (metrics.memory > 0) {
        expect(metrics.memory).toBeLessThan(100 * 1024 * 1024); // Less than 100MB
      }
    });
  });

  test('E2E-VIZ-007: Error Handling and Edge Cases', async ({ page }) => {
    test.step('Test with no data', async () => {
      // Clear all data
      await dataVizPage.clearAllData();
      
      // Verify empty state is shown
      await expect(dataVizPage.emptyStateMessage).toBeVisible();
      await expect(dataVizPage.emptyStateMessage).toContainText('No historical data available');
      
      // Verify generate sample data button works
      await dataVizPage.generateSampleDataButton.click();
      await dataVizPage.waitForChartsToLoad();
      
      // Verify charts now show data
      const dataPoints = await page.locator('svg circle.data-point').count();
      expect(dataPoints).toBeGreaterThan(0);
    });

    test.step('Test with invalid data', async () => {
      // This would test error boundaries and validation
      // Implementation depends on how invalid data is handled
      
      // Try to input invalid financial data
      await swissBudgetPage.fillBasicFinancialInfo({
        monthlyIncome: -1000, // Invalid negative income
        monthlyExpenses: 5000,
        currentSavings: 100000,
        canton: 'ZH',
        age: 30,
        retirementAge: 60,
      });
      
      // Verify error handling
      const errorMessage = page.locator('[data-testid="error-message"]');
      if (await errorMessage.isVisible()) {
        await expect(errorMessage).toContainText(/invalid|error/i);
      }
    });

    test.step('Test network interruption simulation', async () => {
      // Simulate offline condition
      await page.context().setOffline(true);
      
      // Try to export data
      await dataVizPage.openExportMenu();
      await dataVizPage.exportAs('csv');
      
      // Verify graceful handling of offline state
      // This depends on implementation
      
      // Restore network
      await page.context().setOffline(false);
    });
  });
});
