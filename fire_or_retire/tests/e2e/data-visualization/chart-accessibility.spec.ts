/**
 * E2E Accessibility Tests for Data Visualization
 * Tests WCAG compliance, screen reader compatibility, and keyboard navigation
 */

import { test, expect } from '@playwright/test';
import { SwissBudgetProPage } from '../../page-objects/pages/SwissBudgetProPage';
import { DataVisualizationPage } from '../../page-objects/pages/DataVisualizationPage';

test.describe('Data Visualization Accessibility', () => {
  let swissBudgetPage: SwissBudgetProPage;
  let dataVizPage: DataVisualizationPage;

  test.beforeEach(async ({ page }) => {
    swissBudgetPage = new SwissBudgetProPage(page);
    dataVizPage = new DataVisualizationPage(page);
    
    await swissBudgetPage.goto();
    await swissBudgetPage.verifyPageLoaded();
    
    // Set up test data
    await swissBudgetPage.fillBasicFinancialInfo({
      monthlyIncome: 7500,
      monthlyExpenses: 4500,
      currentSavings: 120000,
      canton: 'ZH',
      age: 32,
      retirementAge: 62,
    });
    
    await dataVizPage.navigateToVisualization();
    await dataVizPage.waitForChartsToLoad();
  });

  test('E2E-A11Y-VIZ-001: ARIA Labels and Roles', async ({ page }) => {
    test.step('Verify chart ARIA labels', async () => {
      // Check for proper ARIA labels on charts
      const charts = page.locator('svg[aria-label], svg[role="img"]');
      const chartCount = await charts.count();
      
      expect(chartCount).toBeGreaterThan(0);
      
      for (let i = 0; i < chartCount; i++) {
        const chart = charts.nth(i);
        
        // Verify ARIA label exists and is descriptive
        const ariaLabel = await chart.getAttribute('aria-label');
        expect(ariaLabel).toBeTruthy();
        expect(ariaLabel).toMatch(/chart|graph|visualization|financial/i);
        
        // Verify role is appropriate
        const role = await chart.getAttribute('role');
        if (role) {
          expect(role).toMatch(/img|graphics-document|graphics-object/);
        }
      }
    });

    test.step('Verify interactive element ARIA attributes', async () => {
      // Check chart controls have proper ARIA attributes
      const buttons = page.locator('button');
      const buttonCount = await buttons.count();
      
      for (let i = 0; i < Math.min(buttonCount, 10); i++) {
        const button = buttons.nth(i);
        
        if (await button.isVisible()) {
          // Verify button has accessible name
          const ariaLabel = await button.getAttribute('aria-label');
          const textContent = await button.textContent();
          
          expect(ariaLabel || textContent).toBeTruthy();
          
          // Check for pressed state on toggle buttons
          const ariaPressed = await button.getAttribute('aria-pressed');
          if (ariaPressed !== null) {
            expect(ariaPressed).toMatch(/true|false/);
          }
        }
      }
    });

    test.step('Verify data point accessibility', async () => {
      // Check if data points have accessible descriptions
      const dataPoints = page.locator('svg circle.data-point, svg circle');
      const pointCount = await dataPoints.count();
      
      if (pointCount > 0) {
        const firstPoint = dataPoints.first();
        
        // Check for title or aria-label on data points
        const title = await firstPoint.locator('title').textContent();
        const ariaLabel = await firstPoint.getAttribute('aria-label');
        
        if (title || ariaLabel) {
          expect(title || ariaLabel).toMatch(/\d|CHF|%/); // Should contain data
        }
      }
    });
  });

  test('E2E-A11Y-VIZ-002: Keyboard Navigation', async ({ page }) => {
    test.step('Test tab navigation through controls', async () => {
      // Start from the beginning of the page
      await page.keyboard.press('Tab');
      
      let focusedElement = page.locator(':focus');
      let tabCount = 0;
      const maxTabs = 20; // Prevent infinite loop
      
      while (tabCount < maxTabs) {
        const tagName = await focusedElement.evaluate(el => el.tagName.toLowerCase());
        const isVisible = await focusedElement.isVisible();
        
        if (isVisible && ['button', 'input', 'select', 'a'].includes(tagName)) {
          // Verify focus indicator is visible
          const outline = await focusedElement.evaluate(el => {
            const styles = window.getComputedStyle(el);
            return styles.outline !== 'none' || styles.boxShadow !== 'none';
          });
          
          expect(outline).toBeTruthy();
          
          console.log(`Tab ${tabCount}: ${tagName} - ${await focusedElement.textContent()}`);
        }
        
        await page.keyboard.press('Tab');
        focusedElement = page.locator(':focus');
        tabCount++;
        
        // Break if we've cycled back to the beginning
        const currentElement = await focusedElement.evaluate(el => el);
        if (tabCount > 5 && currentElement === await page.locator('body').evaluate(el => el)) {
          break;
        }
      }
      
      expect(tabCount).toBeGreaterThan(3); // Should have multiple focusable elements
    });

    test.step('Test keyboard interaction with charts', async () => {
      // Focus on chart area
      const chart = page.locator('svg').first();
      await chart.focus();
      
      // Test arrow key navigation (if implemented)
      await page.keyboard.press('ArrowRight');
      await page.waitForTimeout(200);
      
      await page.keyboard.press('ArrowLeft');
      await page.waitForTimeout(200);
      
      await page.keyboard.press('ArrowUp');
      await page.waitForTimeout(200);
      
      await page.keyboard.press('ArrowDown');
      await page.waitForTimeout(200);
      
      // Test Enter key for activation
      await page.keyboard.press('Enter');
      await page.waitForTimeout(200);
      
      // Test Escape key for dismissing interactions
      await page.keyboard.press('Escape');
      await page.waitForTimeout(200);
    });

    test.step('Test keyboard shortcuts', async () => {
      // Test common keyboard shortcuts (if implemented)
      
      // Ctrl+E for export
      await page.keyboard.press('Control+e');
      await page.waitForTimeout(300);
      
      // Check if export menu opened
      const exportMenu = page.locator('[data-testid="export-menu"]');
      if (await exportMenu.isVisible()) {
        await expect(exportMenu).toBeVisible();
        
        // Close with Escape
        await page.keyboard.press('Escape');
        await expect(exportMenu).not.toBeVisible();
      }
      
      // Ctrl+F for fullscreen (if implemented)
      await page.keyboard.press('Control+f');
      await page.waitForTimeout(300);
    });
  });

  test('E2E-A11Y-VIZ-003: Screen Reader Compatibility', async ({ page }) => {
    test.step('Verify chart descriptions for screen readers', async () => {
      // Check for descriptive text alternatives
      const charts = page.locator('svg');
      const chartCount = await charts.count();
      
      for (let i = 0; i < chartCount; i++) {
        const chart = charts.nth(i);
        
        // Check for aria-describedby
        const describedBy = await chart.getAttribute('aria-describedby');
        if (describedBy) {
          const description = page.locator(`#${describedBy}`);
          await expect(description).toBeVisible();
          
          const descText = await description.textContent();
          expect(descText).toMatch(/chart|data|financial|trend/i);
        }
        
        // Check for embedded description
        const desc = chart.locator('desc');
        if (await desc.count() > 0) {
          const descText = await desc.textContent();
          expect(descText).toBeTruthy();
        }
      }
    });

    test.step('Verify data table alternatives', async () => {
      // Look for data table alternatives for charts
      const dataTable = page.locator('table[aria-label*="chart"], table[aria-label*="data"]');
      
      if (await dataTable.isVisible()) {
        // Verify table has proper headers
        const headers = dataTable.locator('th');
        const headerCount = await headers.count();
        expect(headerCount).toBeGreaterThan(0);
        
        // Verify table has data rows
        const rows = dataTable.locator('tbody tr');
        const rowCount = await rows.count();
        expect(rowCount).toBeGreaterThan(0);
      }
    });

    test.step('Test live region announcements', async () => {
      // Check for live regions that announce changes
      const liveRegions = page.locator('[aria-live], [role="status"], [role="alert"]');
      const liveRegionCount = await liveRegions.count();
      
      if (liveRegionCount > 0) {
        // Trigger a change that should announce
        await dataVizPage.selectTimeframe('6M');
        await page.waitForTimeout(500);
        
        // Check if live region was updated
        const liveRegion = liveRegions.first();
        const liveText = await liveRegion.textContent();
        
        if (liveText) {
          expect(liveText).toMatch(/updated|changed|selected/i);
        }
      }
    });
  });

  test('E2E-A11Y-VIZ-004: Color and Contrast', async ({ page }) => {
    test.step('Verify color contrast ratios', async () => {
      // Test chart elements for sufficient contrast
      const chartElements = page.locator('svg text, svg path, svg circle');
      const elementCount = await chartElements.count();
      
      for (let i = 0; i < Math.min(elementCount, 10); i++) {
        const element = chartElements.nth(i);
        
        if (await element.isVisible()) {
          const styles = await element.evaluate(el => {
            const computed = window.getComputedStyle(el);
            return {
              color: computed.color || computed.fill || computed.stroke,
              backgroundColor: computed.backgroundColor,
            };
          });
          
          // Basic check that colors are not transparent
          expect(styles.color).not.toBe('rgba(0, 0, 0, 0)');
          expect(styles.color).not.toBe('transparent');
        }
      }
    });

    test.step('Test high contrast mode compatibility', async () => {
      // Simulate high contrast mode
      await page.addStyleTag({
        content: `
          @media (prefers-contrast: high) {
            * {
              filter: contrast(2) !important;
            }
          }
        `,
      });
      
      await page.waitForTimeout(500);
      
      // Verify charts are still visible and functional
      await dataVizPage.verifyChartData();
      
      // Test interactions still work
      await dataVizPage.selectTimeframe('1Y');
      await dataVizPage.waitForChartUpdate();
    });

    test.step('Test color-blind accessibility', async () => {
      // Test with different color vision simulations
      const colorFilters = [
        'url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cfilter id=\'protanopia\'%3E%3CfeColorMatrix values=\'0.567,0.433,0,0,0 0.558,0.442,0,0,0 0,0.242,0.758,0,0 0,0,0,1,0\'/%3E%3C/filter%3E%3C/svg%3E#protanopia")', // Protanopia
        'url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cfilter id=\'deuteranopia\'%3E%3CfeColorMatrix values=\'0.625,0.375,0,0,0 0.7,0.3,0,0,0 0,0.3,0.7,0,0 0,0,0,1,0\'/%3E%3C/filter%3E%3C/svg%3E#deuteranopia")', // Deuteranopia
      ];
      
      for (const filter of colorFilters) {
        await page.addStyleTag({
          content: `svg { filter: ${filter}; }`,
        });
        
        await page.waitForTimeout(300);
        
        // Verify charts are still distinguishable
        await dataVizPage.verifyChartData();
        
        // Remove filter for next test
        await page.addStyleTag({
          content: 'svg { filter: none; }',
        });
      }
    });
  });

  test('E2E-A11Y-VIZ-005: Focus Management', async ({ page }) => {
    test.step('Test focus trapping in modals', async () => {
      // Open export menu (if it's a modal)
      await dataVizPage.openExportMenu();
      
      const exportMenu = page.locator('[data-testid="export-menu"]');
      if (await exportMenu.isVisible()) {
        // Test that focus is trapped within the menu
        const focusableElements = exportMenu.locator('button, input, select, a, [tabindex]:not([tabindex="-1"])');
        const elementCount = await focusableElements.count();
        
        if (elementCount > 0) {
          // Tab through all elements
          for (let i = 0; i < elementCount + 1; i++) {
            await page.keyboard.press('Tab');
            await page.waitForTimeout(100);
          }
          
          // Focus should cycle back to first element
          const focusedElement = page.locator(':focus');
          const firstElement = focusableElements.first();
          
          const focusedText = await focusedElement.textContent();
          const firstText = await firstElement.textContent();
          
          expect(focusedText).toBe(firstText);
        }
        
        // Close menu with Escape
        await page.keyboard.press('Escape');
      }
    });

    test.step('Test focus restoration', async () => {
      // Focus on a specific element
      const timeframeButton = page.locator('button:has-text("1Y")');
      await timeframeButton.focus();
      
      const initialFocusedText = await page.locator(':focus').textContent();
      
      // Open and close a modal/menu
      await dataVizPage.openExportMenu();
      await page.keyboard.press('Escape');
      
      // Focus should return to original element
      await page.waitForTimeout(200);
      const restoredFocusedText = await page.locator(':focus').textContent();
      
      expect(restoredFocusedText).toBe(initialFocusedText);
    });

    test.step('Test skip links', async () => {
      // Look for skip links
      const skipLinks = page.locator('a[href*="#"], button[aria-label*="skip"]');
      const skipLinkCount = await skipLinks.count();
      
      if (skipLinkCount > 0) {
        const skipLink = skipLinks.first();
        await skipLink.click();
        
        // Verify focus moved to target
        const focusedElement = page.locator(':focus');
        await expect(focusedElement).toBeVisible();
      }
    });
  });

  test('E2E-A11Y-VIZ-006: Reduced Motion Support', async ({ page }) => {
    test.step('Test prefers-reduced-motion compliance', async () => {
      // Set reduced motion preference
      await page.emulateMedia({ reducedMotion: 'reduce' });
      
      // Reload charts with reduced motion
      await dataVizPage.selectChartType('area');
      await page.waitForTimeout(500);
      
      // Verify animations are reduced or disabled
      const animatedElements = page.locator('svg path[stroke-dasharray], svg *[style*="transition"]');
      const animatedCount = await animatedElements.count();
      
      if (animatedCount > 0) {
        for (let i = 0; i < animatedCount; i++) {
          const element = animatedElements.nth(i);
          
          const animationDuration = await element.evaluate(el => {
            const styles = window.getComputedStyle(el);
            return styles.animationDuration || styles.transitionDuration;
          });
          
          // Animations should be very short or disabled
          expect(animationDuration).toMatch(/0s|0\.1s|none/);
        }
      }
    });

    test.step('Test static alternatives for animations', async () => {
      await page.emulateMedia({ reducedMotion: 'reduce' });
      
      // Verify charts still render correctly without animations
      await dataVizPage.waitForChartsToLoad();
      await dataVizPage.verifyChartData();
      
      // Test that functionality is preserved
      await dataVizPage.selectTimeframe('6M');
      await dataVizPage.waitForChartUpdate();
      await dataVizPage.verifyChartData();
    });
  });

  test('E2E-A11Y-VIZ-007: Error Handling Accessibility', async ({ page }) => {
    test.step('Test error message accessibility', async () => {
      // Trigger an error condition (if possible)
      await dataVizPage.clearAllData();
      
      // Look for error messages
      const errorMessages = page.locator('[role="alert"], .error-message, [aria-live="assertive"]');
      const errorCount = await errorMessages.count();
      
      if (errorCount > 0) {
        const errorMessage = errorMessages.first();
        
        // Verify error is announced to screen readers
        const ariaLive = await errorMessage.getAttribute('aria-live');
        const role = await errorMessage.getAttribute('role');
        
        expect(ariaLive === 'assertive' || role === 'alert').toBeTruthy();
        
        // Verify error message is descriptive
        const errorText = await errorMessage.textContent();
        expect(errorText).toMatch(/error|invalid|required|missing/i);
      }
    });

    test.step('Test form validation accessibility', async () => {
      // Test form validation if present
      const forms = page.locator('form');
      const formCount = await forms.count();
      
      if (formCount > 0) {
        const form = forms.first();
        const inputs = form.locator('input[required], select[required]');
        const inputCount = await inputs.count();
        
        if (inputCount > 0) {
          const input = inputs.first();
          
          // Clear required field and submit
          await input.fill('');
          await page.keyboard.press('Tab'); // Trigger validation
          
          // Check for validation message
          const validationMessage = await input.evaluate(el => (el as HTMLInputElement).validationMessage);
          if (validationMessage) {
            expect(validationMessage).toBeTruthy();
          }
          
          // Check for aria-invalid
          const ariaInvalid = await input.getAttribute('aria-invalid');
          if (ariaInvalid) {
            expect(ariaInvalid).toBe('true');
          }
        }
      }
    });
  });
});
