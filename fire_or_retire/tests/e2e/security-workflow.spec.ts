/**
 * Swiss Budget Pro - Security Workflow E2E Tests
 * 
 * End-to-end tests for the complete security workflow including
 * encryption, privacy controls, and security monitoring.
 */

import { test, expect, Page } from '@playwright/test';

test.describe('Security Workflow', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('/');
    
    // Wait for the application to load
    await page.waitForLoadState('networkidle');
  });

  test.describe('Security Dashboard Access', () => {
    test('should navigate to security dashboard', async ({ page }) => {
      // Navigate to security dashboard (assuming it's accessible via menu or direct URL)
      await page.goto('/security');
      
      // Verify security dashboard loads
      await expect(page.locator('h1')).toContainText('Security Dashboard');
      await expect(page.locator('text=Monitor security status')).toBeVisible();
      
      // Verify all tabs are present
      await expect(page.locator('text=Overview')).toBeVisible();
      await expect(page.locator('text=Security Events')).toBeVisible();
      await expect(page.locator('text=Privacy Controls')).toBeVisible();
      await expect(page.locator('text=Encryption')).toBeVisible();
    });

    test('should display security status overview', async ({ page }) => {
      await page.goto('/security');
      
      // Verify security status is displayed
      await expect(page.locator('text=Security Status')).toBeVisible();
      await expect(page.locator('[data-testid="security-status"]')).toBeVisible();
      
      // Verify metrics are displayed
      await expect(page.locator('text=Active Threats')).toBeVisible();
      await expect(page.locator('text=Compliance Score')).toBeVisible();
      await expect(page.locator('text=Total Events')).toBeVisible();
      
      // Verify quick stats cards
      await expect(page.locator('text=Threats Resolved')).toBeVisible();
      await expect(page.locator('text=Encryption Operations')).toBeVisible();
      await expect(page.locator('text=Data Items Tracked')).toBeVisible();
      await expect(page.locator('text=Avg Response Time')).toBeVisible();
    });
  });

  test.describe('Privacy Controls Management', () => {
    test('should manage privacy settings', async ({ page }) => {
      await page.goto('/security');
      
      // Navigate to privacy controls tab
      await page.click('text=Privacy Controls');
      await expect(page.locator('text=Privacy Controls')).toBeVisible();
      
      // Verify all data categories are present
      await expect(page.locator('text=Financial Data')).toBeVisible();
      await expect(page.locator('text=Personal Info')).toBeVisible();
      await expect(page.locator('text=Usage Analytics')).toBeVisible();
      await expect(page.locator('text=Preferences')).toBeVisible();
      await expect(page.locator('text=Cache Data')).toBeVisible();
      await expect(page.locator('text=Temporary Data')).toBeVisible();
    });

    test('should update privacy settings', async ({ page }) => {
      await page.goto('/security');
      await page.click('text=Privacy Controls');
      
      // Find and toggle a privacy setting
      const usageAnalyticsSection = page.locator('text=Usage Analytics').locator('..');
      const enabledCheckbox = usageAnalyticsSection.locator('input[type="checkbox"]').first();
      
      // Get initial state
      const initialState = await enabledCheckbox.isChecked();
      
      // Toggle the setting
      await enabledCheckbox.click();
      
      // Verify the state changed
      const newState = await enabledCheckbox.isChecked();
      expect(newState).toBe(!initialState);
      
      // Verify the change is persisted (reload page)
      await page.reload();
      await page.click('text=Privacy Controls');
      
      const persistedState = await usageAnalyticsSection.locator('input[type="checkbox"]').first().isChecked();
      expect(persistedState).toBe(newState);
    });

    test('should update retention periods', async ({ page }) => {
      await page.goto('/security');
      await page.click('text=Privacy Controls');
      
      // Find a retention period input
      const retentionInput = page.locator('input[type="number"]').first();
      
      // Update retention period
      await retentionInput.fill('180');
      await retentionInput.blur();
      
      // Verify the change is saved
      await page.reload();
      await page.click('text=Privacy Controls');
      
      const updatedValue = await page.locator('input[type="number"]').first().inputValue();
      expect(updatedValue).toBe('180');
    });
  });

  test.describe('Security Events Monitoring', () => {
    test('should display security events', async ({ page }) => {
      await page.goto('/security');
      
      // Navigate to security events tab
      await page.click('text=Security Events');
      await expect(page.locator('text=Recent Security Events')).toBeVisible();
      
      // Verify events table is present
      await expect(page.locator('table')).toBeVisible();
      await expect(page.locator('th:has-text("Timestamp")')).toBeVisible();
      await expect(page.locator('th:has-text("Type")')).toBeVisible();
      await expect(page.locator('th:has-text("Severity")')).toBeVisible();
      await expect(page.locator('th:has-text("Description")')).toBeVisible();
      await expect(page.locator('th:has-text("Status")')).toBeVisible();
    });

    test('should export security data', async ({ page }) => {
      await page.goto('/security');
      await page.click('text=Security Events');
      
      // Set up download handler
      const downloadPromise = page.waitForEvent('download');
      
      // Click export button
      await page.click('text=Export');
      
      // Wait for download
      const download = await downloadPromise;
      
      // Verify download properties
      expect(download.suggestedFilename()).toMatch(/swiss-budget-security-export-\d{4}-\d{2}-\d{2}\.json/);
    });
  });

  test.describe('Encryption Status', () => {
    test('should display encryption information', async ({ page }) => {
      await page.goto('/security');
      
      // Navigate to encryption tab
      await page.click('text=Encryption');
      await expect(page.locator('text=Encryption Status')).toBeVisible();
      
      // Verify encryption metrics
      await expect(page.locator('text=Total Operations')).toBeVisible();
      await expect(page.locator('text=Avg Encryption Time')).toBeVisible();
      await expect(page.locator('text=Avg Decryption Time')).toBeVisible();
      
      // Verify encryption status message
      await expect(page.locator('text=AES-256-GCM Encryption Active')).toBeVisible();
      await expect(page.locator('text=bank-level security')).toBeVisible();
    });
  });

  test.describe('Data Encryption Workflow', () => {
    test('should encrypt and decrypt financial data', async ({ page }) => {
      // Navigate to main application
      await page.goto('/');
      
      // Enter some financial data
      await page.fill('[data-testid="monthly-income"]', '8000');
      await page.fill('[data-testid="monthly-expenses"]', '5000');
      await page.fill('[data-testid="current-savings"]', '50000');
      
      // Save the data (this should trigger encryption)
      await page.click('[data-testid="save-data"]');
      
      // Verify data is saved
      await expect(page.locator('text=Data saved successfully')).toBeVisible();
      
      // Reload page to verify data persistence
      await page.reload();
      await page.waitForLoadState('networkidle');
      
      // Verify data is restored (decrypted)
      await expect(page.locator('[data-testid="monthly-income"]')).toHaveValue('8000');
      await expect(page.locator('[data-testid="monthly-expenses"]')).toHaveValue('5000');
      await expect(page.locator('[data-testid="current-savings"]')).toHaveValue('50000');
    });

    test('should handle encryption errors gracefully', async ({ page }) => {
      // Mock encryption failure by corrupting localStorage
      await page.evaluate(() => {
        localStorage.setItem('swiss-budget-encrypted-data', 'corrupted-data');
      });
      
      // Reload page
      await page.reload();
      
      // Should handle corruption gracefully
      await expect(page.locator('text=Error loading data')).toBeVisible();
      
      // Should allow user to start fresh
      await page.click('text=Start Fresh');
      await expect(page.locator('[data-testid="monthly-income"]')).toHaveValue('');
    });
  });

  test.describe('Security Monitoring Integration', () => {
    test('should log security events during normal usage', async ({ page }) => {
      // Perform various actions that should generate security events
      await page.goto('/');
      
      // Data access
      await page.fill('[data-testid="monthly-income"]', '7000');
      
      // Data modification
      await page.fill('[data-testid="monthly-expenses"]', '4500');
      
      // Data export
      await page.click('[data-testid="export-data"]');
      
      // Navigate to security dashboard
      await page.goto('/security');
      await page.click('text=Security Events');
      
      // Verify events were logged
      await expect(page.locator('table tbody tr')).toHaveCount.greaterThan(0);
      
      // Verify event types
      await expect(page.locator('text=data access')).toBeVisible();
      await expect(page.locator('text=data modification')).toBeVisible();
      await expect(page.locator('text=data export')).toBeVisible();
    });

    test('should detect suspicious activity', async ({ page }) => {
      // Simulate rapid data access (potential threat)
      await page.goto('/');
      
      for (let i = 0; i < 10; i++) {
        await page.fill('[data-testid="monthly-income"]', `${7000 + i}`);
        await page.waitForTimeout(100); // Small delay between actions
      }
      
      // Check security dashboard for threat detection
      await page.goto('/security');
      
      // Should show warning status if threat detected
      const securityStatus = page.locator('[data-testid="security-status"]');
      const statusText = await securityStatus.textContent();
      
      // Status should be WARNING or CRITICAL if threats detected
      expect(statusText).toMatch(/(WARNING|CRITICAL)/);
    });
  });

  test.describe('Compliance and Audit', () => {
    test('should maintain audit trail', async ({ page }) => {
      await page.goto('/security');
      await page.click('text=Security Events');
      
      // Verify audit trail contains required information
      const eventRows = page.locator('table tbody tr');
      const firstRow = eventRows.first();
      
      // Verify timestamp is present and recent
      const timestamp = await firstRow.locator('td').first().textContent();
      expect(timestamp).toMatch(/\d{1,2}\/\d{1,2}\/\d{4}/); // Date format
      
      // Verify event details are logged
      await expect(firstRow.locator('td').nth(1)).not.toBeEmpty(); // Type
      await expect(firstRow.locator('td').nth(2)).not.toBeEmpty(); // Severity
      await expect(firstRow.locator('td').nth(3)).not.toBeEmpty(); // Description
    });

    test('should show compliance status', async ({ page }) => {
      await page.goto('/security');
      
      // Verify compliance score is displayed
      await expect(page.locator('text=Compliance Score')).toBeVisible();
      
      // Compliance score should be a percentage
      const complianceScore = await page.locator('[data-testid="compliance-score"]').textContent();
      expect(complianceScore).toMatch(/\d+%/);
    });
  });

  test.describe('Performance Requirements', () => {
    test('should meet encryption performance targets', async ({ page }) => {
      await page.goto('/security');
      await page.click('text=Encryption');
      
      // Check encryption performance metrics
      const avgEncryptionTime = await page.locator('[data-testid="avg-encryption-time"]').textContent();
      const avgDecryptionTime = await page.locator('[data-testid="avg-decryption-time"]').textContent();
      
      // Extract numeric values (assuming format like "50ms")
      const encryptionMs = parseInt(avgEncryptionTime?.replace('ms', '') || '0');
      const decryptionMs = parseInt(avgDecryptionTime?.replace('ms', '') || '0');
      
      // Verify performance targets (<100ms)
      expect(encryptionMs).toBeLessThan(100);
      expect(decryptionMs).toBeLessThan(100);
    });

    test('should load security dashboard quickly', async ({ page }) => {
      const startTime = Date.now();
      
      await page.goto('/security');
      await page.waitForLoadState('networkidle');
      
      const loadTime = Date.now() - startTime;
      
      // Security dashboard should load within 2 seconds
      expect(loadTime).toBeLessThan(2000);
    });
  });

  test.describe('Error Handling', () => {
    test('should handle missing Web Crypto API gracefully', async ({ page }) => {
      // Mock missing Web Crypto API
      await page.addInitScript(() => {
        delete (window as any).crypto;
      });
      
      await page.goto('/');
      
      // Should show appropriate error message
      await expect(page.locator('text=Encryption not available')).toBeVisible();
      
      // Should still allow basic functionality
      await expect(page.locator('[data-testid="monthly-income"]')).toBeVisible();
    });

    test('should handle localStorage quota exceeded', async ({ page }) => {
      // Mock localStorage quota exceeded
      await page.addInitScript(() => {
        const originalSetItem = localStorage.setItem;
        localStorage.setItem = function() {
          throw new Error('QuotaExceededError');
        };
      });
      
      await page.goto('/');
      
      // Try to save data
      await page.fill('[data-testid="monthly-income"]', '8000');
      await page.click('[data-testid="save-data"]');
      
      // Should show appropriate error message
      await expect(page.locator('text=Storage quota exceeded')).toBeVisible();
      
      // Should suggest data cleanup
      await expect(page.locator('text=Clear old data')).toBeVisible();
    });
  });
});
