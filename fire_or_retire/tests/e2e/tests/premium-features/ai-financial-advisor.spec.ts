import { test, expect } from '@playwright/test';

test.describe('AI Financial Advisor - Premium Feature', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('/');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
    
    // Set up basic financial data for testing
    await page.fill('[data-testid="current-age"]', '35');
    await page.fill('[data-testid="retirement-age"]', '65');
    await page.fill('[data-testid="current-savings"]', '100000');
    await page.fill('[data-testid="monthly-income"]', '8000');
    await page.fill('[data-testid="monthly-expenses"]', '5000');
    
    // Navigate to Premium tab
    await page.click('[data-testid="tab-premium"]');
    await page.waitForTimeout(1000);
  });

  test('should display AI Financial Advisor component', async ({ page }) => {
    // Check if AI Financial Advisor section is visible
    await expect(page.locator('h3:has-text("🤖 AI Financial Advisor")')).toBeVisible();
    
    // Check for description text
    await expect(page.locator('text=Personalized financial insights and recommendations')).toBeVisible();
  });

  test('should generate AI analysis automatically', async ({ page }) => {
    // Wait for AI analysis to complete
    await page.waitForSelector('[data-testid="ai-analysis-complete"]', { timeout: 10000 });
    
    // Check if overall risk profile is displayed
    await expect(page.locator('h3:has-text("📊 AI Financial Health Score")')).toBeVisible();
    
    // Verify risk score is displayed
    await expect(page.locator('[data-testid="overall-score"]')).toBeVisible();
    await expect(page.locator('[data-testid="fire-readiness"]')).toBeVisible();
    await expect(page.locator('[data-testid="risk-level"]')).toBeVisible();
    await expect(page.locator('[data-testid="optimization-potential"]')).toBeVisible();
  });

  test('should display personalized FIRE plan', async ({ page }) => {
    // Wait for analysis to complete
    await page.waitForSelector('[data-testid="ai-analysis-complete"]', { timeout: 10000 });
    
    // Check if personalized plan section is visible
    await expect(page.locator('h3:has-text("🎯 Your Personalized FIRE Plan")')).toBeVisible();
    
    // Verify plan steps are displayed
    const planSteps = page.locator('[data-testid="plan-step"]');
    await expect(planSteps).toHaveCount(6); // Should have 6 plan steps
    
    // Check if first step contains goal information
    await expect(planSteps.first()).toContainText('Primary Goal');
  });

  test('should show AI insights with different categories', async ({ page }) => {
    // Wait for analysis to complete
    await page.waitForSelector('[data-testid="ai-analysis-complete"]', { timeout: 10000 });
    
    // Check if insights section is visible
    await expect(page.locator('h3:has-text("💡 AI Insights & Recommendations")')).toBeVisible();
    
    // Test category filter
    const categorySelect = page.locator('[data-testid="insights-category-filter"]');
    await expect(categorySelect).toBeVisible();
    
    // Test different categories
    await categorySelect.selectOption('optimization');
    await page.waitForTimeout(500);
    
    await categorySelect.selectOption('risk');
    await page.waitForTimeout(500);
    
    await categorySelect.selectOption('swiss');
    await page.waitForTimeout(500);
    
    // Verify insights are displayed
    const insights = page.locator('[data-testid="ai-insight"]');
    await expect(insights.first()).toBeVisible();
  });

  test('should have interactive AI chat functionality', async ({ page }) => {
    // Wait for analysis to complete
    await page.waitForSelector('[data-testid="ai-analysis-complete"]', { timeout: 10000 });
    
    // Check if chat section is visible
    await expect(page.locator('h3:has-text("💬 Ask Your AI Financial Advisor")')).toBeVisible();
    
    // Test chat input
    const chatInput = page.locator('[data-testid="ai-chat-input"]');
    await expect(chatInput).toBeVisible();
    await expect(chatInput).toHaveAttribute('placeholder', /Ask about investments/);
    
    // Test sending a message
    await chatInput.fill('What should I do about my Pillar 3a?');
    await page.click('[data-testid="ai-chat-send"]');
    
    // Wait for AI response
    await page.waitForSelector('[data-testid="ai-message"]', { timeout: 5000 });
    
    // Verify message appears in chat
    await expect(page.locator('[data-testid="user-message"]')).toContainText('Pillar 3a');
    await expect(page.locator('[data-testid="ai-message"]')).toBeVisible();
  });

  test('should handle different financial scenarios', async ({ page }) => {
    // Test with high savings rate scenario
    await page.fill('[data-testid="monthly-income"]', '10000');
    await page.fill('[data-testid="monthly-expenses"]', '4000');
    
    // Refresh analysis
    await page.click('[data-testid="refresh-ai-analysis"]');
    await page.waitForSelector('[data-testid="ai-analysis-complete"]', { timeout: 10000 });
    
    // Verify high savings rate is recognized
    await expect(page.locator('text=Excellent Savings Rate')).toBeVisible();
    
    // Test with low savings rate scenario
    await page.fill('[data-testid="monthly-income"]', '6000');
    await page.fill('[data-testid="monthly-expenses"]', '5500');
    
    // Refresh analysis
    await page.click('[data-testid="refresh-ai-analysis"]');
    await page.waitForSelector('[data-testid="ai-analysis-complete"]', { timeout: 10000 });
    
    // Verify low savings rate warning
    await expect(page.locator('text=Low Savings Rate')).toBeVisible();
  });

  test('should display Swiss-specific recommendations', async ({ page }) => {
    // Wait for analysis to complete
    await page.waitForSelector('[data-testid="ai-analysis-complete"]', { timeout: 10000 });
    
    // Filter for Swiss-specific insights
    await page.selectOption('[data-testid="insights-category-filter"]', 'swiss');
    
    // Verify Swiss-specific content
    await expect(page.locator('text=Pillar 3a')).toBeVisible();
    await expect(page.locator('text=CHF')).toBeVisible();
    
    // Check for Swiss badge
    await expect(page.locator('[data-testid="swiss-badge"]')).toBeVisible();
  });

  test('should show confidence scores and priorities', async ({ page }) => {
    // Wait for analysis to complete
    await page.waitForSelector('[data-testid="ai-analysis-complete"]', { timeout: 10000 });
    
    // Check if insights have priority indicators
    const insights = page.locator('[data-testid="ai-insight"]');
    await expect(insights.first()).toBeVisible();
    
    // Verify priority levels are displayed
    await expect(page.locator('text=HIGH')).toBeVisible();
    await expect(page.locator('text=confidence')).toBeVisible();
    
    // Check for actionable badges
    await expect(page.locator('text=Actionable')).toBeVisible();
  });

  test('should handle chat with different question types', async ({ page }) => {
    // Wait for analysis to complete
    await page.waitForSelector('[data-testid="ai-analysis-complete"]', { timeout: 10000 });
    
    const chatInput = page.locator('[data-testid="ai-chat-input"]');
    const sendButton = page.locator('[data-testid="ai-chat-send"]');
    
    // Test investment question
    await chatInput.fill('How should I invest my money?');
    await sendButton.click();
    await page.waitForSelector('[data-testid="ai-message"]', { timeout: 5000 });
    await expect(page.locator('[data-testid="ai-message"]').last()).toContainText('portfolio');
    
    // Test tax question
    await chatInput.fill('How can I optimize my taxes?');
    await sendButton.click();
    await page.waitForSelector('[data-testid="ai-message"]', { timeout: 5000 });
    await expect(page.locator('[data-testid="ai-message"]').last()).toContainText('tax');
    
    // Test retirement question
    await chatInput.fill('When can I retire?');
    await sendButton.click();
    await page.waitForSelector('[data-testid="ai-message"]', { timeout: 5000 });
    await expect(page.locator('[data-testid="ai-message"]').last()).toContainText('retire');
  });

  test('should be responsive on mobile devices', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Wait for analysis to complete
    await page.waitForSelector('[data-testid="ai-analysis-complete"]', { timeout: 10000 });
    
    // Check if components are still visible and functional
    await expect(page.locator('h3:has-text("🤖 AI Financial Advisor")')).toBeVisible();
    await expect(page.locator('[data-testid="overall-score"]')).toBeVisible();
    
    // Test chat functionality on mobile
    const chatInput = page.locator('[data-testid="ai-chat-input"]');
    await expect(chatInput).toBeVisible();
    await chatInput.fill('Mobile test question');
    await page.click('[data-testid="ai-chat-send"]');
    
    // Verify response on mobile
    await page.waitForSelector('[data-testid="ai-message"]', { timeout: 5000 });
    await expect(page.locator('[data-testid="ai-message"]')).toBeVisible();
  });

  test('should handle error states gracefully', async ({ page }) => {
    // Test with invalid data
    await page.fill('[data-testid="monthly-income"]', '0');
    await page.fill('[data-testid="monthly-expenses"]', '0');
    
    // Refresh analysis
    await page.click('[data-testid="refresh-ai-analysis"]');
    
    // Should handle gracefully without crashing
    await page.waitForTimeout(3000);
    await expect(page.locator('h3:has-text("🤖 AI Financial Advisor")')).toBeVisible();
  });

  test('should persist chat history', async ({ page }) => {
    // Wait for analysis to complete
    await page.waitForSelector('[data-testid="ai-analysis-complete"]', { timeout: 10000 });
    
    // Send a message
    await page.fill('[data-testid="ai-chat-input"]', 'Test message for persistence');
    await page.click('[data-testid="ai-chat-send"]');
    await page.waitForSelector('[data-testid="ai-message"]', { timeout: 5000 });
    
    // Navigate away and back
    await page.click('[data-testid="tab-dashboard"]');
    await page.waitForTimeout(1000);
    await page.click('[data-testid="tab-premium"]');
    await page.waitForTimeout(1000);
    
    // Check if message is still there
    await expect(page.locator('[data-testid="user-message"]')).toContainText('Test message for persistence');
  });
});
