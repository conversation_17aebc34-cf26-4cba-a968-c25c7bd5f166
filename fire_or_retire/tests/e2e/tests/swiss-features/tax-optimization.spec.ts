import { expect, test } from '@playwright/test';
import { swissCantons, swissTestScenarios } from '../../fixtures/swiss-scenarios';
import { DashboardPage } from '../../pages/dashboard-page';

test.describe('Swiss Tax Optimization Features', () => {
  let dashboardPage: DashboardPage;

  test.beforeEach(async ({ page }) => {
    dashboardPage = new DashboardPage(page);
    await dashboardPage.goto();
    await dashboardPage.waitForPageLoad();
  });

  test('Pillar 3a optimization across different income levels', async () => {
    console.log('🎯 Testing Pillar 3a optimization');
    
    // Test with high income scenario
    const highIncomeScenario = swissTestScenarios.genevaExecutive;
    await dashboardPage.inputScenario(highIncomeScenario);
    
    // Navigate to tax optimization tab
    await dashboardPage.navigateToTaxOptimizationTab();
    
    // Verify Pillar 3a recommendations are shown
    await expect(dashboardPage.page.locator('[data-testid="pillar3a-optimization"]')).toBeVisible();
    
    // Check that the recommendation shows maximum contribution
    const pillar3aText = await dashboardPage.page.locator('[data-testid="pillar3a-recommendation"]').textContent();
    expect(pillar3aText).toContain('7\'056'); // Maximum for employees
    
    // Test with lower income scenario
    const lowIncomeScenario = swissTestScenarios.bernConservative;
    await dashboardPage.inputScenario(lowIncomeScenario);
    
    // Verify recommendations adjust for lower income
    await dashboardPage.navigateToTaxOptimizationTab();
    const updatedText = await dashboardPage.page.locator('[data-testid="pillar3a-recommendation"]').textContent();
    expect(updatedText).toContain('CHF');
  });

  test('Canton comparison and relocation analysis', async () => {
    console.log('🎯 Testing canton comparison features');
    
    const scenario = swissTestScenarios.zurichProfessional;
    await dashboardPage.inputScenario(scenario);
    
    // Navigate to Swiss Relocation tab
    await dashboardPage.navigateToSwissRelocationTab();
    
    // Verify relocation analysis is shown
    await expect(dashboardPage.page.locator('[data-testid="relocation-analysis"]')).toBeVisible();
    
    // Test different cantons and verify tax differences
    const cantonTests = ['ZH', 'ZG', 'GE', 'VD'];
    
    for (const canton of cantonTests) {
      await dashboardPage.selectCanton(canton);
      await dashboardPage.waitForCalculations();
      
      // Verify tax calculation updates
      const taxAmount = await dashboardPage.getMonthlyTaxAmount();
      expect(taxAmount).toBeGreaterThan(0);
      
      console.log(`✅ Canton ${canton}: CHF ${taxAmount} monthly tax`);
    }
    
    // Verify Zug shows as tax-efficient option
    await dashboardPage.selectCanton('ZG');
    await dashboardPage.waitForCalculations();
    const zugTax = await dashboardPage.getMonthlyTaxAmount();
    
    await dashboardPage.selectCanton('GE');
    await dashboardPage.waitForCalculations();
    const genevaTax = await dashboardPage.getMonthlyTaxAmount();
    
    expect(zugTax).toBeLessThan(genevaTax); // Zug should have lower taxes
  });

  test('Wealth tax calculations for high net worth individuals', async () => {
    console.log('🎯 Testing wealth tax calculations');
    
    const highNetWorthScenario = swissTestScenarios.genevaExecutive;
    await dashboardPage.inputScenario(highNetWorthScenario);
    
    // Navigate to tax optimization tab
    await dashboardPage.navigateToTaxOptimizationTab();
    
    // Verify wealth tax is calculated and displayed
    await expect(dashboardPage.page.locator('[data-testid="wealth-tax-analysis"]')).toBeVisible();
    
    // Test different cantons with wealth tax
    const wealthTaxCantons = ['ZH', 'GE', 'VD', 'BE'];
    
    for (const canton of wealthTaxCantons) {
      await dashboardPage.selectCanton(canton);
      await dashboardPage.waitForCalculations();
      
      // Verify wealth tax component is shown
      const wealthTaxElement = dashboardPage.page.locator('[data-testid="wealth-tax-amount"]');
      if (await wealthTaxElement.count() > 0) {
        const wealthTaxText = await wealthTaxElement.textContent();
        expect(wealthTaxText).toContain('CHF');
        console.log(`✅ Canton ${canton}: Wealth tax calculated`);
      }
    }
  });

  test('Civil status impact on tax calculations', async () => {
    console.log('🎯 Testing civil status tax implications');
    
    const scenario = swissTestScenarios.vaudFamily;
    await dashboardPage.inputScenario(scenario);
    
    // Test married status
    await dashboardPage.selectCivilStatus('married');
    await dashboardPage.waitForCalculations();
    const marriedTax = await dashboardPage.getMonthlyTaxAmount();
    
    // Test single status
    await dashboardPage.selectCivilStatus('single');
    await dashboardPage.waitForCalculations();
    const singleTax = await dashboardPage.getMonthlyTaxAmount();
    
    // Married status should generally result in lower effective tax rate
    expect(marriedTax).toBeLessThan(singleTax);
    console.log(`✅ Tax difference: Married CHF ${marriedTax} vs Single CHF ${singleTax}`);
  });

  test('Real-time tax calculation updates', async () => {
    console.log('🎯 Testing real-time tax updates');
    
    await dashboardPage.navigateToOverviewTab();
    
    // Start with base income
    await dashboardPage.enterMonthlyIncome(5000);
    await dashboardPage.waitForCalculations();
    const baseTax = await dashboardPage.getMonthlyTaxAmount();
    
    // Increase income and verify tax increases
    await dashboardPage.enterMonthlyIncome(10000);
    await dashboardPage.waitForCalculations();
    const higherTax = await dashboardPage.getMonthlyTaxAmount();
    
    expect(higherTax).toBeGreaterThan(baseTax);
    
    // Test progressive taxation
    await dashboardPage.enterMonthlyIncome(15000);
    await dashboardPage.waitForCalculations();
    const highestTax = await dashboardPage.getMonthlyTaxAmount();
    
    expect(highestTax).toBeGreaterThan(higherTax);
    
    console.log(`✅ Progressive taxation verified: ${baseTax} → ${higherTax} → ${highestTax}`);
  });

  test('Tax optimization recommendations', async () => {
    console.log('🎯 Testing tax optimization recommendations');
    
    const scenario = swissTestScenarios.baselTechWorker;
    await dashboardPage.inputScenario(scenario);
    
    // Navigate to FIRE Acceleration tab for optimization recommendations
    await dashboardPage.navigateToFireAccelerationTab();
    
    // Verify optimization recommendations are shown
    await expect(dashboardPage.page.locator('[data-testid="optimization-recommendations"]')).toBeVisible();
    
    // Check for specific Swiss optimization strategies
    const recommendationsText = await dashboardPage.page.locator('[data-testid="optimization-list"]').textContent();
    
    // Should include Pillar 3a optimization
    expect(recommendationsText).toContain('Pillar 3a');
    
    // Should include canton comparison if beneficial
    if (recommendationsText.includes('relocation')) {
      console.log('✅ Relocation recommendation found');
    }
    
    // Should include tax-efficient investment strategies
    if (recommendationsText.includes('investment')) {
      console.log('✅ Investment optimization recommendation found');
    }
  });

  test('All 26 cantons tax calculation verification', async () => {
    console.log('🎯 Testing all 26 Swiss cantons');
    
    const baseScenario = swissTestScenarios.zurichProfessional;
    await dashboardPage.inputScenario(baseScenario);
    
    const cantonResults: Array<{ code: string; name: string; tax: number }> = [];
    
    for (const canton of swissCantons) {
      await dashboardPage.selectCanton(canton.code);
      await dashboardPage.waitForCalculations();
      
      const taxAmount = await dashboardPage.getMonthlyTaxAmount();
      cantonResults.push({
        code: canton.code,
        name: canton.name,
        tax: taxAmount,
      });
      
      // Verify tax calculation is reasonable
      expect(taxAmount).toBeGreaterThan(0);
      expect(taxAmount).toBeLessThan(15000); // Sanity check for high income
    }
    
    // Verify we tested all 26 cantons
    expect(cantonResults.length).toBe(26);
    
    // Verify tax variations exist (not all the same)
    const uniqueTaxAmounts = new Set(cantonResults.map(r => r.tax));
    expect(uniqueTaxAmounts.size).toBeGreaterThan(10); // Should have variety
    
    console.log('✅ All 26 cantons tested successfully');
  });

  test('Swiss financial formatting verification', async () => {
    console.log('🎯 Testing Swiss financial formatting');
    
    const scenario = swissTestScenarios.zurichProfessional;
    await dashboardPage.inputScenario(scenario);
    
    // Check that amounts are displayed in Swiss format
    const fireProjectionText = await dashboardPage.fireProjection.textContent();
    expect(fireProjectionText).toMatch(/CHF\s*[\d']+/); // Swiss apostrophe separator
    
    const taxAmountText = await dashboardPage.monthlyTaxAmount.textContent();
    expect(taxAmountText).toMatch(/CHF\s*[\d']+/);
    
    // Verify percentage formatting
    const savingsRateText = await dashboardPage.savingsRateDisplay.textContent();
    expect(savingsRateText).toMatch(/\d+\.\d%/); // Swiss decimal formatting
    
    console.log('✅ Swiss formatting verified');
  });
});
