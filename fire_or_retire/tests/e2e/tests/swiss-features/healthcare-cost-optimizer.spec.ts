import { expect, test } from '@playwright/test';
import { DashboardPage } from '../../pages/dashboard-page';
import { HealthcareOptimizerPage } from '../../pages/healthcare-optimizer-page';
import { swissTestScenarios } from '../../fixtures/swiss-scenarios';

test.describe('Healthcare Cost Optimizer - Swiss Features', () => {
  let dashboardPage: DashboardPage;
  let healthcarePage: HealthcareOptimizerPage;

  test.describe.configure({ mode: 'serial', timeout: 60000 });

  test.beforeEach(async ({ page }) => {
    dashboardPage = new DashboardPage(page);
    healthcarePage = new HealthcareOptimizerPage(page);
    await dashboardPage.goto();
    await dashboardPage.clearLocalStorage();
  });

  test('Healthcare tab navigation and basic functionality', async ({ page }) => {
    await test.step('Navigate to healthcare tab', async () => {
      await dashboardPage.clickTab('healthcare');
      await expect(page.locator('h1:has-text("Swiss Healthcare Cost Optimizer")')).toBeVisible();
    });

    await test.step('Verify healthcare tab components', async () => {
      // Check main navigation tabs
      await expect(page.locator('button:has-text("Health Profile")')).toBeVisible();
      await expect(page.locator('button:has-text("Optimization")')).toBeVisible();
      await expect(page.locator('button:has-text("FIRE Integration")')).toBeVisible();
      await expect(page.locator('button:has-text("Action Plan")')).toBeVisible();
    });

    await test.step('Verify health profile form elements', async () => {
      // Basic information section
      await expect(page.locator('input[type="number"]').first()).toBeVisible(); // Age input
      await expect(page.locator('select').first()).toBeVisible(); // Canton select
      await expect(page.locator('input[type="number"]').nth(1)).toBeVisible(); // Income input
      
      // Health status dropdown
      await expect(page.locator('select:has-text("Good")')).toBeVisible();
    });
  });

  test('Complete health profile input and optimization', async ({ page }) => {
    await dashboardPage.clickTab('healthcare');

    await test.step('Fill health profile form', async () => {
      await healthcarePage.fillHealthProfile({
        age: 35,
        canton: 'ZH',
        income: 80000,
        healthStatus: 'good',
        familySize: 1,
        hasChildren: false,
        currentPremium: 350,
        currentDeductible: 1000,
        expectedMedicalExpenses: 1200,
        riskTolerance: 'medium',
      });
    });

    await test.step('Navigate to optimization results', async () => {
      await page.click('button:has-text("Optimization")');
      await page.waitForSelector('[data-testid="optimization-results"]', { timeout: 10000 });
    });

    await test.step('Verify optimization calculations', async () => {
      // Check potential savings display
      await expect(page.locator('text=Potential Annual Savings')).toBeVisible();
      
      // Verify savings amount is displayed
      const savingsElement = page.locator('[data-testid="total-savings"]');
      await expect(savingsElement).toBeVisible();
      
      // Check that savings is a positive number
      const savingsText = await savingsElement.textContent();
      const savingsAmount = parseInt(savingsText?.replace(/[^\d]/g, '') || '0');
      expect(savingsAmount).toBeGreaterThan(0);
    });

    await test.step('Verify deductible optimization', async () => {
      await expect(page.locator('text=Optimal Deductible Strategy')).toBeVisible();
      await expect(page.locator('[data-testid="recommended-deductible"]')).toBeVisible();
      await expect(page.locator('[data-testid="annual-savings"]')).toBeVisible();
      await expect(page.locator('[data-testid="confidence-level"]')).toBeVisible();
    });

    await test.step('Verify insurance recommendations', async () => {
      await expect(page.locator('text=Top Insurance Recommendations')).toBeVisible();
      
      // Should show at least 3 recommendations
      const recommendations = page.locator('[data-testid="insurance-recommendation"]');
      await expect(recommendations).toHaveCount(3);
      
      // Each recommendation should have key information
      const firstRec = recommendations.first();
      await expect(firstRec.locator('[data-testid="insurer-name"]')).toBeVisible();
      await expect(firstRec.locator('[data-testid="monthly-premium"]')).toBeVisible();
      await expect(firstRec.locator('[data-testid="annual-savings"]')).toBeVisible();
    });
  });

  test('FIRE integration analysis', async ({ page }) => {
    await dashboardPage.clickTab('healthcare');
    
    await test.step('Fill health profile', async () => {
      await healthcarePage.fillHealthProfile({
        age: 35,
        canton: 'ZH',
        income: 80000,
        healthStatus: 'good',
        familySize: 1,
        hasChildren: false,
        currentPremium: 350,
        currentDeductible: 1000,
        expectedMedicalExpenses: 1200,
        riskTolerance: 'medium',
      });
    });

    await test.step('Navigate to FIRE integration', async () => {
      await page.click('button:has-text("FIRE Integration")');
      await page.waitForSelector('[data-testid="fire-analysis"]', { timeout: 10000 });
    });

    await test.step('Verify FIRE impact analysis', async () => {
      // Check FIRE impact summary
      await expect(page.locator('text=Healthcare Impact on FIRE')).toBeVisible();
      await expect(page.locator('[data-testid="additional-fire-number"]')).toBeVisible();
      await expect(page.locator('[data-testid="fire-delay-months"]')).toBeVisible();
      await expect(page.locator('[data-testid="healthcare-percentage"]')).toBeVisible();
    });

    await test.step('Verify healthcare projections', async () => {
      await expect(page.locator('text=Healthcare Cost Projections')).toBeVisible();
      await expect(page.locator('[data-testid="average-annual-cost"]')).toBeVisible();
      await expect(page.locator('[data-testid="subsidy-optimization"]')).toBeVisible();
    });

    await test.step('Verify optimization strategies', async () => {
      await expect(page.locator('text=FIRE Optimization Strategies')).toBeVisible();
      
      // Should show multiple strategy recommendations
      const strategies = page.locator('[data-testid="strategy-recommendation"]');
      await expect(strategies.count()).toBeGreaterThan(0);
    });
  });

  test('Canton comparison and geographic arbitrage', async ({ page }) => {
    await dashboardPage.clickTab('healthcare');

    await test.step('Test different canton scenarios', async () => {
      const cantons = ['ZH', 'GE', 'VD', 'BE', 'BS'];
      
      for (const canton of cantons) {
        await healthcarePage.selectCanton(canton);
        await page.waitForTimeout(1000); // Allow calculations to update
        
        // Verify that premium data updates for different cantons
        await page.click('button:has-text("Optimization")');
        await page.waitForSelector('[data-testid="optimization-results"]', { timeout: 5000 });
        
        // Check that recommendations are displayed
        const recommendations = page.locator('[data-testid="insurance-recommendation"]');
        await expect(recommendations.count()).toBeGreaterThan(0);
        
        // Go back to profile to test next canton
        await page.click('button:has-text("Health Profile")');
      }
    });
  });

  test('Age group premium variations', async ({ page }) => {
    await dashboardPage.clickTab('healthcare');

    await test.step('Test young adult premiums (19-25)', async () => {
      await healthcarePage.fillHealthProfile({
        age: 23,
        canton: 'ZH',
        income: 50000,
        healthStatus: 'excellent',
        familySize: 1,
        hasChildren: false,
        currentPremium: 280,
        currentDeductible: 2500,
        expectedMedicalExpenses: 800,
        riskTolerance: 'high',
      });

      await page.click('button:has-text("Optimization")');
      await page.waitForSelector('[data-testid="optimization-results"]');
      
      // Young adults should have lower premiums
      const premiums = page.locator('[data-testid="monthly-premium"]');
      const firstPremium = await premiums.first().textContent();
      const premiumAmount = parseInt(firstPremium?.replace(/[^\d]/g, '') || '0');
      expect(premiumAmount).toBeLessThan(350); // Should be lower than adult premiums
    });

    await test.step('Test adult premiums (26+)', async () => {
      await page.click('button:has-text("Health Profile")');
      await healthcarePage.updateAge(45);

      await page.click('button:has-text("Optimization")');
      await page.waitForSelector('[data-testid="optimization-results"]');
      
      // Adult premiums should be higher
      const premiums = page.locator('[data-testid="monthly-premium"]');
      const firstPremium = await premiums.first().textContent();
      const premiumAmount = parseInt(firstPremium?.replace(/[^\d]/g, '') || '0');
      expect(premiumAmount).toBeGreaterThan(300);
    });
  });

  test('Risk tolerance and deductible optimization', async ({ page }) => {
    await dashboardPage.clickTab('healthcare');

    await test.step('Test low risk tolerance', async () => {
      await healthcarePage.fillHealthProfile({
        age: 35,
        canton: 'ZH',
        income: 80000,
        healthStatus: 'fair',
        familySize: 1,
        hasChildren: false,
        currentPremium: 350,
        currentDeductible: 300,
        expectedMedicalExpenses: 2500,
        riskTolerance: 'low',
      });

      await page.click('button:has-text("Optimization")');
      await page.waitForSelector('[data-testid="recommended-deductible"]');
      
      // Low risk tolerance should recommend lower deductibles
      const recommendedDeductible = await page.locator('[data-testid="recommended-deductible"]').textContent();
      const deductibleAmount = parseInt(recommendedDeductible?.replace(/[^\d]/g, '') || '0');
      expect(deductibleAmount).toBeLessThanOrEqual(1000);
    });

    await test.step('Test high risk tolerance', async () => {
      await page.click('button:has-text("Health Profile")');
      await healthcarePage.updateRiskTolerance('high');
      await healthcarePage.updateHealthStatus('excellent');
      await healthcarePage.updateExpectedExpenses(600);

      await page.click('button:has-text("Optimization")');
      await page.waitForSelector('[data-testid="recommended-deductible"]');
      
      // High risk tolerance should recommend higher deductibles
      const recommendedDeductible = await page.locator('[data-testid="recommended-deductible"]').textContent();
      const deductibleAmount = parseInt(recommendedDeductible?.replace(/[^\d]/g, '') || '0');
      expect(deductibleAmount).toBeGreaterThanOrEqual(1500);
    });
  });

  test('Family size impact on calculations', async ({ page }) => {
    await dashboardPage.clickTab('healthcare');

    await test.step('Test single person scenario', async () => {
      await healthcarePage.fillHealthProfile({
        age: 35,
        canton: 'ZH',
        income: 80000,
        healthStatus: 'good',
        familySize: 1,
        hasChildren: false,
        currentPremium: 350,
        currentDeductible: 1000,
        expectedMedicalExpenses: 1200,
        riskTolerance: 'medium',
      });

      await page.click('button:has-text("FIRE Integration")');
      await page.waitForSelector('[data-testid="fire-analysis"]');
      
      const singlePersonCost = await page.locator('[data-testid="average-annual-cost"]').textContent();
      const singleCost = parseInt(singlePersonCost?.replace(/[^\d]/g, '') || '0');
      
      // Go back and test family scenario
      await page.click('button:has-text("Health Profile")');
      await healthcarePage.updateFamilySize(4);
      await healthcarePage.updateHasChildren(true);

      await page.click('button:has-text("FIRE Integration")');
      await page.waitForSelector('[data-testid="fire-analysis"]');
      
      const familyCost = await page.locator('[data-testid="average-annual-cost"]').textContent();
      const familyCostAmount = parseInt(familyCost?.replace(/[^\d]/g, '') || '0');
      
      // Family costs should be higher
      expect(familyCostAmount).toBeGreaterThan(singleCost);
    });
  });

  test('Error handling and validation', async ({ page }) => {
    await dashboardPage.clickTab('healthcare');

    await test.step('Test invalid age input', async () => {
      await healthcarePage.updateAge(150);
      // Should show validation error or reset to valid range
      const ageInput = page.locator('input[type="number"]').first();
      const ageValue = await ageInput.inputValue();
      expect(parseInt(ageValue)).toBeLessThanOrEqual(100);
    });

    await test.step('Test invalid income input', async () => {
      await healthcarePage.updateIncome(-1000);
      // Should show validation error or reset to valid range
      const incomeInput = page.locator('input[type="number"]').nth(1);
      const incomeValue = await incomeInput.inputValue();
      expect(parseInt(incomeValue)).toBeGreaterThanOrEqual(0);
    });

    await test.step('Test invalid family size', async () => {
      await healthcarePage.updateFamilySize(15);
      // Should be limited to reasonable family size
      const familyInput = page.locator('input[type="number"]').nth(2);
      const familyValue = await familyInput.inputValue();
      expect(parseInt(familyValue)).toBeLessThanOrEqual(10);
    });
  });

  test('Loading states and performance', async ({ page }) => {
    await dashboardPage.clickTab('healthcare');

    await test.step('Measure healthcare calculation performance', async () => {
      const startTime = Date.now();
      
      await healthcarePage.fillHealthProfile({
        age: 35,
        canton: 'ZH',
        income: 80000,
        healthStatus: 'good',
        familySize: 1,
        hasChildren: false,
        currentPremium: 350,
        currentDeductible: 1000,
        expectedMedicalExpenses: 1200,
        riskTolerance: 'medium',
      });

      await page.click('button:has-text("Optimization")');
      await page.waitForSelector('[data-testid="optimization-results"]');
      
      const calculationTime = Date.now() - startTime;
      expect(calculationTime).toBeLessThan(5000); // Should complete in under 5 seconds
    });

    await test.step('Verify loading states', async () => {
      // Check if loading indicator appears during calculations
      await page.click('button:has-text("Health Profile")');
      await healthcarePage.updateCanton('GE');
      
      // Look for loading indicator (if implemented)
      const loadingIndicator = page.locator('[data-testid="calculating-indicator"]');
      if (await loadingIndicator.isVisible()) {
        await expect(loadingIndicator).toBeHidden({ timeout: 10000 });
      }
    });
  });

  test('Data persistence and navigation', async ({ page }) => {
    await dashboardPage.clickTab('healthcare');

    await test.step('Fill form and verify persistence', async () => {
      await healthcarePage.fillHealthProfile({
        age: 35,
        canton: 'ZH',
        income: 80000,
        healthStatus: 'good',
        familySize: 1,
        hasChildren: false,
        currentPremium: 350,
        currentDeductible: 1000,
        expectedMedicalExpenses: 1200,
        riskTolerance: 'medium',
      });

      // Navigate to different tab and back
      await page.click('button:has-text("Optimization")');
      await page.click('button:has-text("Health Profile")');
      
      // Verify data is still there
      const ageInput = page.locator('input[type="number"]').first();
      expect(await ageInput.inputValue()).toBe('35');
      
      const cantonSelect = page.locator('select').first();
      expect(await cantonSelect.inputValue()).toBe('ZH');
    });

    await test.step('Navigate between main tabs', async () => {
      // Test navigation to other main tabs and back
      await dashboardPage.clickTab('planning');
      await dashboardPage.clickTab('healthcare');
      
      // Healthcare tab should still be functional
      await expect(page.locator('h1:has-text("Swiss Healthcare Cost Optimizer")')).toBeVisible();
    });
  });
});
