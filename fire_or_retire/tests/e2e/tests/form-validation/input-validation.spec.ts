import { expect, test } from '@playwright/test';
import { DashboardPage } from '../../pages/dashboard-page';

test.describe('Form Input Validation Tests', () => {
  let dashboardPage: DashboardPage;

  test.beforeEach(async ({ page }) => {
    dashboardPage = new DashboardPage(page);
    await dashboardPage.goto();
    await dashboardPage.clearLocalStorage();
  });

  test.describe('Income Input Validation', () => {
    test('validates negative income values', async ({ page }) => {
      console.log('💰 Testing negative income validation');
      
      await dashboardPage.navigateToIncomeSubTab();
      
      // Test negative monthly income
      await dashboardPage.enterMonthlyIncome(-1000);
      
      // Should show error message
      const errorMessage = page.locator('[data-testid="monthly-income-error"]');
      await expect(errorMessage).toBeVisible();
      await expect(errorMessage).toContainText('must be positive');
      
      // Calculations should not proceed with invalid data
      const fireProjection = page.locator('[data-testid="fire-projection"]');
      const projectionText = await fireProjection.textContent();
      expect(projectionText).not.toContain('Age');
    });

    test('validates extremely high income values', async ({ page }) => {
      console.log('💰 Testing extremely high income validation');
      
      await dashboardPage.navigateToIncomeSubTab();
      
      // Test unrealistic income (over 1 million CHF monthly)
      await dashboardPage.enterMonthlyIncome(1000000);
      
      // Should show warning message
      const warningMessage = page.locator('[data-testid="monthly-income-warning"]');
      await expect(warningMessage).toBeVisible();
      await expect(warningMessage).toContainText('unusually high');
    });

    test('validates decimal income values', async ({ page }) => {
      console.log('💰 Testing decimal income validation');
      
      await dashboardPage.navigateToIncomeSubTab();
      
      // Test decimal income
      await dashboardPage.enterMonthlyIncome(8500.50);
      
      // Should accept decimal values
      const incomeInput = page.locator('[data-testid="monthly-income"]');
      const value = await incomeInput.inputValue();
      expect(value).toBe('8500.5');
      
      // Calculations should proceed normally
      await dashboardPage.waitForCalculations();
      const fireProjection = page.locator('[data-testid="fire-projection"]');
      await expect(fireProjection).toBeVisible();
    });

    test('validates empty income input', async ({ page }) => {
      console.log('💰 Testing empty income validation');
      
      await dashboardPage.navigateToIncomeSubTab();
      
      // Clear income input
      const incomeInput = page.locator('[data-testid="monthly-income"]');
      await incomeInput.clear();
      
      // Should default to 0 or show appropriate message
      await page.waitForTimeout(1000);
      const value = await incomeInput.inputValue();
      expect(value === '' || value === '0').toBeTruthy();
    });
  });

  test.describe('Age Input Validation', () => {
    test('validates minimum age requirements', async ({ page }) => {
      console.log('👶 Testing minimum age validation');
      
      await dashboardPage.navigateToGoalsSubTab();
      
      // Test age below minimum (e.g., 16)
      await dashboardPage.enterAge(15);
      
      const errorMessage = page.locator('[data-testid="age-error"]');
      await expect(errorMessage).toBeVisible();
      await expect(errorMessage).toContainText('minimum age');
    });

    test('validates maximum age requirements', async ({ page }) => {
      console.log('👴 Testing maximum age validation');
      
      await dashboardPage.navigateToGoalsSubTab();
      
      // Test unrealistic age
      await dashboardPage.enterAge(150);
      
      const errorMessage = page.locator('[data-testid="age-error"]');
      await expect(errorMessage).toBeVisible();
      await expect(errorMessage).toContainText('maximum age');
    });

    test('validates retirement age logic', async ({ page }) => {
      console.log('🎯 Testing retirement age logic');
      
      await dashboardPage.navigateToGoalsSubTab();
      
      // Test age close to retirement (e.g., 64)
      await dashboardPage.enterAge(64);
      
      // Should show appropriate FIRE projection
      await dashboardPage.waitForCalculations();
      const fireProjection = page.locator('[data-testid="fire-projection"]');
      const projectionText = await fireProjection.textContent();
      
      // Should indicate early retirement or already at retirement age
      expect(projectionText).toMatch(/(Age 65|already|soon)/i);
    });
  });

  test.describe('Swiss-Specific Validation', () => {
    test('validates Pillar 3a contribution limits', async ({ page }) => {
      console.log('🏛️ Testing Pillar 3a validation');
      
      await dashboardPage.navigateToSavingsSubTab();
      
      // Test contribution above annual limit (CHF 7,056 for 2024)
      const pillar3aInput = page.locator('[data-testid="pillar3a-contribution"]');
      if (await pillar3aInput.count() > 0) {
        await pillar3aInput.fill('50000');
        
        const errorMessage = page.locator('[data-testid="pillar3a-error"]');
        await expect(errorMessage).toBeVisible();
        await expect(errorMessage).toContainText('exceeds annual limit');
      }
    });

    test('validates canton selection', async ({ page }) => {
      console.log('🏔️ Testing canton validation');
      
      await dashboardPage.navigateToTaxOptimizationSubTab();
      
      // Verify all Swiss cantons are available
      const cantonSelect = page.locator('[data-testid="canton-select"]');
      await cantonSelect.click();
      
      // Check for major cantons
      const zurichOption = page.locator('option[value="ZH"]');
      const genevaOption = page.locator('option[value="GE"]');
      const bernOption = page.locator('option[value="BE"]');
      
      await expect(zurichOption).toBeVisible();
      await expect(genevaOption).toBeVisible();
      await expect(bernOption).toBeVisible();
    });

    test('validates civil status impact on calculations', async ({ page }) => {
      console.log('💑 Testing civil status validation');
      
      await dashboardPage.navigateToTaxOptimizationSubTab();
      
      // Set up scenario
      await dashboardPage.navigateToIncomeSubTab();
      await dashboardPage.enterMonthlyIncome(10000);
      
      await dashboardPage.navigateToTaxOptimizationSubTab();
      await dashboardPage.selectCanton('ZH');
      
      // Test single status
      await dashboardPage.selectCivilStatus('single');
      await dashboardPage.waitForCalculations();
      const singleTax = await dashboardPage.getMonthlyTaxAmount();
      
      // Test married status
      await dashboardPage.selectCivilStatus('married');
      await dashboardPage.waitForCalculations();
      const marriedTax = await dashboardPage.getMonthlyTaxAmount();
      
      // Married status should generally result in lower taxes
      expect(marriedTax).toBeLessThan(singleTax);
    });
  });

  test.describe('Real-time Validation Feedback', () => {
    test('provides immediate feedback on input changes', async ({ page }) => {
      console.log('⚡ Testing real-time validation feedback');
      
      await dashboardPage.navigateToIncomeSubTab();
      
      const incomeInput = page.locator('[data-testid="monthly-income"]');
      
      // Type invalid value character by character
      await incomeInput.clear();
      await incomeInput.type('-');
      
      // Should show immediate feedback
      await page.waitForTimeout(500);
      const errorMessage = page.locator('[data-testid="monthly-income-error"]');
      
      // Continue typing to make it valid
      await incomeInput.type('5000');
      await page.waitForTimeout(500);
      
      // Error should disappear
      await expect(errorMessage).toBeHidden();
    });

    test('validates form submission with multiple errors', async ({ page }) => {
      console.log('📝 Testing form submission with multiple errors');
      
      // Create multiple validation errors
      await dashboardPage.navigateToIncomeSubTab();
      await dashboardPage.enterMonthlyIncome(-1000);
      
      await dashboardPage.navigateToGoalsSubTab();
      await dashboardPage.enterAge(150);
      
      // Try to proceed to calculations
      await dashboardPage.navigateToDashboardTab();
      
      // Should show summary of all errors
      const errorSummary = page.locator('[data-testid="validation-summary"]');
      if (await errorSummary.count() > 0) {
        await expect(errorSummary).toBeVisible();
        await expect(errorSummary).toContainText('Please fix the following errors');
      }
    });
  });

  test.describe('Input Formatting and Constraints', () => {
    test('formats currency inputs correctly', async ({ page }) => {
      console.log('💱 Testing currency formatting');
      
      await dashboardPage.navigateToIncomeSubTab();
      
      // Enter large number
      await dashboardPage.enterMonthlyIncome(12345);
      
      // Should format with Swiss number formatting
      const incomeInput = page.locator('[data-testid="monthly-income"]');
      await page.waitForTimeout(1000);
      
      // Check if formatting is applied (Swiss uses apostrophes for thousands)
      const displayValue = await incomeInput.inputValue();
      expect(displayValue).toMatch(/12[',\s]?345/);
    });

    test('handles copy-paste of formatted numbers', async ({ page }) => {
      console.log('📋 Testing copy-paste of formatted numbers');
      
      await dashboardPage.navigateToIncomeSubTab();
      
      const incomeInput = page.locator('[data-testid="monthly-income"]');
      
      // Paste formatted number
      await incomeInput.clear();
      await incomeInput.fill("8'500.50");
      
      // Should parse correctly
      await dashboardPage.waitForCalculations();
      const fireProjection = page.locator('[data-testid="fire-projection"]');
      await expect(fireProjection).toBeVisible();
    });
  });
});
