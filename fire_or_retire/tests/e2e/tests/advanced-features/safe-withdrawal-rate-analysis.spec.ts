import { test, expect } from '@playwright/test';

test.describe('Safe Withdrawal Rate Analysis - Advanced Feature', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('/');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
    
    // Set up financial data for withdrawal rate analysis
    await page.fill('[data-testid="current-age"]', '45');
    await page.fill('[data-testid="retirement-age"]', '65');
    await page.fill('[data-testid="current-savings"]', '300000');
    await page.fill('[data-testid="monthly-income"]', '12000');
    await page.fill('[data-testid="monthly-expenses"]', '7000');
    
    // Navigate to Advanced tab
    await page.click('[data-testid="tab-advanced"]');
    await page.waitForTimeout(1000);
  });

  test('should display Safe Withdrawal Rate Analysis component', async ({ page }) => {
    // Check if Safe Withdrawal Rate section is visible
    await expect(page.locator('h3:has-text("📊 Safe Withdrawal Rate Analysis")')).toBeVisible();
    
    // Check for description text
    await expect(page.locator('text=Advanced analysis of withdrawal rates')).toBeVisible();
  });

  test('should show analysis configuration options', async ({ page }) => {
    // Check if configuration section is visible
    await expect(page.locator('h3:has-text("⚙️ Analysis Parameters")')).toBeVisible();
    
    // Verify retirement length selector
    const retirementLengthSelect = page.locator('[data-testid="retirement-length-select"]');
    await expect(retirementLengthSelect).toBeVisible();
    
    // Verify stock allocation slider
    const stockAllocationSlider = page.locator('[data-testid="stock-allocation-slider"]');
    await expect(stockAllocationSlider).toBeVisible();
    
    // Verify refresh button
    const refreshButton = page.locator('[data-testid="refresh-withdrawal-analysis"]');
    await expect(refreshButton).toBeVisible();
  });

  test('should generate Monte Carlo simulation results', async ({ page }) => {
    // Wait for analysis to complete
    await page.waitForSelector('[data-testid="withdrawal-analysis-complete"]', { timeout: 15000 });
    
    // Check if current rate assessment is displayed
    await expect(page.locator('h3:has-text("🎯 Current Rate Assessment")')).toBeVisible();
    
    // Verify current and recommended rates are shown
    await expect(page.locator('[data-testid="current-withdrawal-rate"]')).toBeVisible();
    await expect(page.locator('[data-testid="recommended-withdrawal-rate"]')).toBeVisible();
  });

  test('should display risk factors analysis', async ({ page }) => {
    // Wait for analysis to complete
    await page.waitForSelector('[data-testid="withdrawal-analysis-complete"]', { timeout: 15000 });
    
    // Check if risk factors section is visible
    await expect(page.locator('h3:has-text("⚠️ Risk Factors")')).toBeVisible();
    
    // Verify all 4 risk factors are displayed
    const riskFactors = [
      'Retirement Length',
      'Market Volatility',
      'Inflation Impact',
      'Sequence Risk',
    ];
    
    for (const factor of riskFactors) {
      await expect(page.locator(`text=${factor}`)).toBeVisible();
    }
    
    // Check if risk factor percentages are displayed
    await expect(page.locator('text=/%/')).toBeVisible();
  });

  test('should show withdrawal rate scenarios with success rates', async ({ page }) => {
    // Wait for analysis to complete
    await page.waitForSelector('[data-testid="withdrawal-analysis-complete"]', { timeout: 15000 });
    
    // Check if scenarios section is visible
    await expect(page.locator('h3:has-text("📈 Withdrawal Rate Scenarios")')).toBeVisible();
    
    // Verify multiple withdrawal rate scenarios are displayed
    const scenarios = page.locator('[data-testid="withdrawal-scenario"]');
    await expect(scenarios).toHaveCount(8); // Should have 8 scenarios (2.5% to 6.0%)
    
    // Check if success rates are displayed
    await expect(page.locator('text=Success Rate')).toBeVisible();
    
    // Verify scenario recommendations
    const recommendations = ['CONSERVATIVE', 'MODERATE', 'AGGRESSIVE', 'RISKY'];
    let foundRecommendation = false;
    
    for (const rec of recommendations) {
      const recElement = page.locator(`text=${rec} APPROACH`);
      if (await recElement.isVisible()) {
        foundRecommendation = true;
        break;
      }
    }
    
    expect(foundRecommendation).toBe(true);
  });

  test('should handle different retirement length configurations', async ({ page }) => {
    // Test 25-year retirement
    await page.selectOption('[data-testid="retirement-length-select"]', '25');
    await page.click('[data-testid="refresh-withdrawal-analysis"]');
    await page.waitForSelector('[data-testid="withdrawal-analysis-complete"]', { timeout: 15000 });
    
    // Verify analysis updates
    await expect(page.locator('text=25 years')).toBeVisible();
    
    // Test 40-year retirement
    await page.selectOption('[data-testid="retirement-length-select"]', '40');
    await page.click('[data-testid="refresh-withdrawal-analysis"]');
    await page.waitForSelector('[data-testid="withdrawal-analysis-complete"]', { timeout: 15000 });
    
    // Verify analysis updates
    await expect(page.locator('text=40 years')).toBeVisible();
  });

  test('should handle portfolio allocation changes', async ({ page }) => {
    // Test 100% stocks allocation
    const stockSlider = page.locator('[data-testid="stock-allocation-slider"]');
    await stockSlider.fill('100');
    
    // Verify allocation display updates
    await expect(page.locator('text=100% Stocks, 0% Bonds')).toBeVisible();
    
    // Refresh analysis
    await page.click('[data-testid="refresh-withdrawal-analysis"]');
    await page.waitForSelector('[data-testid="withdrawal-analysis-complete"]', { timeout: 15000 });
    
    // Test conservative allocation
    await stockSlider.fill('30');
    await expect(page.locator('text=30% Stocks, 70% Bonds')).toBeVisible();
    
    // Refresh analysis
    await page.click('[data-testid="refresh-withdrawal-analysis"]');
    await page.waitForSelector('[data-testid="withdrawal-analysis-complete"]', { timeout: 15000 });
  });

  test('should display detailed scenario metrics', async ({ page }) => {
    // Wait for analysis to complete
    await page.waitForSelector('[data-testid="withdrawal-analysis-complete"]', { timeout: 15000 });
    
    // Check first scenario for detailed metrics
    const firstScenario = page.locator('[data-testid="withdrawal-scenario"]').first();
    
    // Verify scenario contains all required metrics
    await expect(firstScenario.locator('text=Median Portfolio')).toBeVisible();
    await expect(firstScenario.locator('text=Worst Case')).toBeVisible();
    await expect(firstScenario.locator('text=Years to Depletion')).toBeVisible();
    
    // Verify CHF formatting
    await expect(firstScenario.locator('text=CHF')).toBeVisible();
  });

  test('should show key insights section', async ({ page }) => {
    // Wait for analysis to complete
    await page.waitForSelector('[data-testid="withdrawal-analysis-complete"]', { timeout: 15000 });
    
    // Check if key insights section is visible
    await expect(page.locator('h3:has-text("💡 Key Insights")')).toBeVisible();
    
    // Verify insights contain relevant information
    await expect(page.locator('text=4% rule')).toBeVisible();
    await expect(page.locator('text=success rate')).toBeVisible();
    await expect(page.locator('text=bond tent')).toBeVisible();
  });

  test('should validate 4% rule analysis', async ({ page }) => {
    // Wait for analysis to complete
    await page.waitForSelector('[data-testid="withdrawal-analysis-complete"]', { timeout: 15000 });
    
    // Find the 4% scenario
    const fourPercentScenario = page.locator('text=4.0% Withdrawal Rate').locator('..');
    await expect(fourPercentScenario).toBeVisible();
    
    // Verify it shows success rate
    await expect(fourPercentScenario.locator('text=Success Rate')).toBeVisible();
    
    // Check if it's marked as traditional approach
    await expect(fourPercentScenario.locator('text=Traditional safe withdrawal rate')).toBeVisible();
  });

  test('should handle extreme scenarios', async ({ page }) => {
    // Test very conservative scenario (2.5%)
    await page.waitForSelector('[data-testid="withdrawal-analysis-complete"]', { timeout: 15000 });
    
    const conservativeScenario = page.locator('text=2.5% Withdrawal Rate').locator('..');
    await expect(conservativeScenario).toBeVisible();
    await expect(conservativeScenario.locator('text=CONSERVATIVE APPROACH')).toBeVisible();
    
    // Test aggressive scenario (6.0%)
    const aggressiveScenario = page.locator('text=6.0% Withdrawal Rate').locator('..');
    await expect(aggressiveScenario).toBeVisible();
    await expect(aggressiveScenario.locator('text=RISKY APPROACH')).toBeVisible();
  });

  test('should show current rate assessment accurately', async ({ page }) => {
    // Set specific withdrawal rate
    await page.fill('[data-testid="safe-withdrawal-rate"]', '4.5');
    
    // Refresh analysis
    await page.click('[data-testid="refresh-withdrawal-analysis"]');
    await page.waitForSelector('[data-testid="withdrawal-analysis-complete"]', { timeout: 15000 });
    
    // Verify current rate is displayed correctly
    await expect(page.locator('text=4.5%')).toBeVisible();
    await expect(page.locator('text=Your current 4.5% withdrawal rate')).toBeVisible();
  });

  test('should be responsive on mobile devices', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Wait for analysis to complete
    await page.waitForSelector('[data-testid="withdrawal-analysis-complete"]', { timeout: 15000 });
    
    // Check if components are still visible and functional
    await expect(page.locator('h3:has-text("📊 Safe Withdrawal Rate Analysis")')).toBeVisible();
    await expect(page.locator('[data-testid="current-withdrawal-rate"]')).toBeVisible();
    
    // Test configuration controls on mobile
    const retirementLengthSelect = page.locator('[data-testid="retirement-length-select"]');
    await expect(retirementLengthSelect).toBeVisible();
    await retirementLengthSelect.selectOption('35');
  });

  test('should handle loading states properly', async ({ page }) => {
    // Click refresh to trigger loading state
    await page.click('[data-testid="refresh-withdrawal-analysis"]');
    
    // Should show loading indicator
    await expect(page.locator('text=Running Monte Carlo simulations')).toBeVisible();
    
    // Wait for completion
    await page.waitForSelector('[data-testid="withdrawal-analysis-complete"]', { timeout: 15000 });
    
    // Loading should be gone
    await expect(page.locator('text=Running Monte Carlo simulations')).not.toBeVisible();
  });

  test('should show portfolio value projections', async ({ page }) => {
    // Wait for analysis to complete
    await page.waitForSelector('[data-testid="withdrawal-analysis-complete"]', { timeout: 15000 });
    
    // Check if portfolio projections are shown
    const scenarios = page.locator('[data-testid="withdrawal-scenario"]');
    const firstScenario = scenarios.first();
    
    // Should show median and worst case values
    await expect(firstScenario.locator('[data-testid="median-portfolio"]')).toBeVisible();
    await expect(firstScenario.locator('[data-testid="worst-case-portfolio"]')).toBeVisible();
    
    // Values should be in CHF
    await expect(firstScenario.locator('text=CHF')).toBeVisible();
  });

  test('should handle edge cases gracefully', async ({ page }) => {
    // Test with very low expenses
    await page.fill('[data-testid="monthly-expenses"]', '100');
    
    // Refresh analysis
    await page.click('[data-testid="refresh-withdrawal-analysis"]');
    
    // Should handle gracefully without crashing
    await page.waitForTimeout(5000);
    await expect(page.locator('h3:has-text("📊 Safe Withdrawal Rate Analysis")')).toBeVisible();
  });
});
