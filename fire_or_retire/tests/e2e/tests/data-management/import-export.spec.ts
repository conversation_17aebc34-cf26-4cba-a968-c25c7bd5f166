import { expect, test } from '@playwright/test';
import { DashboardPage } from '../../pages/dashboard-page';
import { swissTestScenarios } from '../../fixtures/swiss-scenarios';
import * as fs from 'fs';
import * as path from 'path';

test.describe('Data Import/Export Tests', () => {
  let dashboardPage: DashboardPage;

  test.beforeEach(async ({ page }) => {
    dashboardPage = new DashboardPage(page);
    await dashboardPage.goto();
    await dashboardPage.clearLocalStorage();
  });

  test.describe('Data Export Functionality', () => {
    test('exports complete financial plan as JSON', async ({ page }) => {
      console.log('📤 Testing JSON export functionality');
      
      // Set up a complete scenario
      const scenario = swissTestScenarios.zurichProfessional;
      await dashboardPage.inputScenario(scenario);
      
      // Navigate to data management
      await dashboardPage.navigateToDataSubTab();
      
      // Trigger export
      const downloadPromise = page.waitForEvent('download');
      const exportButton = page.locator('[data-testid="export-json"]');
      
      if (await exportButton.count() > 0) {
        await exportButton.click();
        const download = await downloadPromise;
        
        // Verify download properties
        expect(download.suggestedFilename()).toMatch(/swiss-budget-pro.*\.json$/);
        
        // Save and verify file content
        const downloadPath = path.join(__dirname, '../../temp', download.suggestedFilename());
        await download.saveAs(downloadPath);
        
        // Verify file exists and has content
        expect(fs.existsSync(downloadPath)).toBeTruthy();
        const fileContent = fs.readFileSync(downloadPath, 'utf8');
        const exportedData = JSON.parse(fileContent);
        
        // Verify exported data structure
        expect(exportedData).toHaveProperty('version');
        expect(exportedData).toHaveProperty('exportDate');
        expect(exportedData).toHaveProperty('financialData');
        expect(exportedData.financialData).toHaveProperty('income');
        expect(exportedData.financialData).toHaveProperty('personalInfo');
        
        // Clean up
        fs.unlinkSync(downloadPath);
      }
    });

    test('exports financial report as PDF', async ({ page }) => {
      console.log('📄 Testing PDF export functionality');
      
      // Set up scenario
      const scenario = swissTestScenarios.vaudFamily;
      await dashboardPage.inputScenario(scenario);
      
      // Navigate to reports
      await dashboardPage.navigateToReportsSubTab();
      
      // Generate and export PDF report
      const downloadPromise = page.waitForEvent('download');
      const pdfExportButton = page.locator('[data-testid="export-pdf-report"]');
      
      if (await pdfExportButton.count() > 0) {
        await pdfExportButton.click();
        const download = await downloadPromise;
        
        // Verify PDF download
        expect(download.suggestedFilename()).toMatch(/financial-report.*\.pdf$/);
        
        // Verify file size (PDF should have reasonable size)
        const downloadPath = path.join(__dirname, '../../temp', download.suggestedFilename());
        await download.saveAs(downloadPath);
        
        const stats = fs.statSync(downloadPath);
        expect(stats.size).toBeGreaterThan(1000); // At least 1KB
        expect(stats.size).toBeLessThan(5000000); // Less than 5MB
        
        // Clean up
        fs.unlinkSync(downloadPath);
      }
    });

    test('exports data in CSV format for spreadsheet analysis', async ({ page }) => {
      console.log('📊 Testing CSV export functionality');
      
      // Set up scenario
      const scenario = swissTestScenarios.genevaExecutive;
      await dashboardPage.inputScenario(scenario);
      
      // Navigate to data management
      await dashboardPage.navigateToDataSubTab();
      
      // Export as CSV
      const downloadPromise = page.waitForEvent('download');
      const csvExportButton = page.locator('[data-testid="export-csv"]');
      
      if (await csvExportButton.count() > 0) {
        await csvExportButton.click();
        const download = await downloadPromise;
        
        // Verify CSV download
        expect(download.suggestedFilename()).toMatch(/financial-data.*\.csv$/);
        
        // Verify CSV content
        const downloadPath = path.join(__dirname, '../../temp', download.suggestedFilename());
        await download.saveAs(downloadPath);
        
        const csvContent = fs.readFileSync(downloadPath, 'utf8');
        
        // Verify CSV structure
        const lines = csvContent.split('\n');
        expect(lines.length).toBeGreaterThan(1); // Header + data
        expect(lines[0]).toContain('Category'); // Should have headers
        expect(csvContent).toContain('Income'); // Should contain financial data
        
        // Clean up
        fs.unlinkSync(downloadPath);
      }
    });
  });

  test.describe('Data Import Functionality', () => {
    test('imports valid JSON financial plan', async ({ page }) => {
      console.log('📥 Testing JSON import functionality');
      
      // Create test data file
      const testData = {
        version: '1.0.0',
        exportDate: new Date().toISOString(),
        financialData: {
          income: {
            monthly: 9500,
            bonus: 18000,
          },
          personalInfo: {
            age: 32,
            canton: 'BS',
            civilStatus: 'single',
          },
        },
      };
      
      const testFilePath = path.join(__dirname, '../../temp', 'test-import.json');
      
      // Ensure temp directory exists
      const tempDir = path.dirname(testFilePath);
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }
      
      fs.writeFileSync(testFilePath, JSON.stringify(testData, null, 2));
      
      // Navigate to data management
      await dashboardPage.navigateToDataSubTab();
      
      // Import file
      const importButton = page.locator('[data-testid="import-json"]');
      
      if (await importButton.count() > 0) {
        await importButton.click();
        
        // Handle file upload
        const fileInput = page.locator('input[type="file"]');
        await fileInput.setInputFiles(testFilePath);
        
        // Wait for import to complete
        await page.waitForTimeout(2000);
        
        // Verify data was imported
        await dashboardPage.navigateToIncomeSubTab();
        const incomeValue = await page.locator('[data-testid="monthly-income"]').inputValue();
        expect(incomeValue).toBe('9500');
        
        await dashboardPage.navigateToGoalsSubTab();
        const ageValue = await page.locator('[data-testid="age-input"]').inputValue();
        expect(ageValue).toBe('32');
        
        // Clean up
        fs.unlinkSync(testFilePath);
      }
    });

    test('handles invalid JSON import gracefully', async ({ page }) => {
      console.log('❌ Testing invalid JSON import handling');
      
      // Create invalid JSON file
      const invalidJsonPath = path.join(__dirname, '../../temp', 'invalid.json');
      
      // Ensure temp directory exists
      const tempDir = path.dirname(invalidJsonPath);
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }
      
      fs.writeFileSync(invalidJsonPath, '{ invalid json content');
      
      // Navigate to data management
      await dashboardPage.navigateToDataSubTab();
      
      const importButton = page.locator('[data-testid="import-json"]');
      
      if (await importButton.count() > 0) {
        await importButton.click();
        
        // Upload invalid file
        const fileInput = page.locator('input[type="file"]');
        await fileInput.setInputFiles(invalidJsonPath);
        
        // Should show error message
        const errorMessage = page.locator('[data-testid="import-error"]');
        await expect(errorMessage).toBeVisible();
        await expect(errorMessage).toContainText('invalid');
        
        // Clean up
        fs.unlinkSync(invalidJsonPath);
      }
    });

    test('imports CSV data correctly', async ({ page }) => {
      console.log('📊 Testing CSV import functionality');
      
      // Create test CSV file
      const csvContent = `Category,Value,Description
Monthly Income,8500,Primary salary
Age,29,Current age
Canton,ZH,Tax canton`;
      
      const csvFilePath = path.join(__dirname, '../../temp', 'test-import.csv');
      
      // Ensure temp directory exists
      const tempDir = path.dirname(csvFilePath);
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }
      
      fs.writeFileSync(csvFilePath, csvContent);
      
      // Navigate to data management
      await dashboardPage.navigateToDataSubTab();
      
      const csvImportButton = page.locator('[data-testid="import-csv"]');
      
      if (await csvImportButton.count() > 0) {
        await csvImportButton.click();
        
        // Upload CSV file
        const fileInput = page.locator('input[type="file"]');
        await fileInput.setInputFiles(csvFilePath);
        
        // Wait for import processing
        await page.waitForTimeout(2000);
        
        // Verify data mapping interface appears
        const mappingInterface = page.locator('[data-testid="csv-mapping"]');
        if (await mappingInterface.count() > 0) {
          await expect(mappingInterface).toBeVisible();
          
          // Complete the mapping process
          const confirmButton = page.locator('[data-testid="confirm-csv-import"]');
          if (await confirmButton.count() > 0) {
            await confirmButton.click();
            await page.waitForTimeout(1000);
          }
        }
        
        // Clean up
        fs.unlinkSync(csvFilePath);
      }
    });
  });

  test.describe('Data Backup and Restore', () => {
    test('creates automatic backups', async ({ page }) => {
      console.log('💾 Testing automatic backup functionality');
      
      // Set up scenario
      const scenario = swissTestScenarios.bernConservative;
      await dashboardPage.inputScenario(scenario);
      
      // Wait for auto-save
      await dashboardPage.waitForAutoSave();
      
      // Check if backup was created in localStorage or IndexedDB
      const backupExists = await page.evaluate(() => {
        const backup = localStorage.getItem('swiss-budget-pro-backup');
        return backup !== null;
      });
      
      expect(backupExists).toBeTruthy();
    });

    test('restores from backup after data loss', async ({ page }) => {
      console.log('🔄 Testing backup restore functionality');
      
      // Set up initial data
      const scenario = swissTestScenarios.baselTechWorker;
      await dashboardPage.inputScenario(scenario);
      await dashboardPage.waitForAutoSave();
      
      // Simulate data loss
      await page.evaluate(() => {
        localStorage.removeItem('swiss-budget-pro-data');
      });
      
      // Reload page
      await page.reload();
      await page.waitForSelector('h1', { timeout: 10000 });
      
      // Check if restore option is offered
      const restorePrompt = page.locator('[data-testid="restore-backup"]');
      
      if (await restorePrompt.count() > 0) {
        await restorePrompt.click();
        await page.waitForTimeout(2000);
        
        // Verify data was restored
        await dashboardPage.navigateToIncomeSubTab();
        const incomeValue = await page.locator('[data-testid="monthly-income"]').inputValue();
        expect(parseInt(incomeValue)).toBeGreaterThan(0);
      }
    });

    test('manages multiple backup versions', async ({ page }) => {
      console.log('📚 Testing multiple backup versions');
      
      // Create first backup
      await dashboardPage.inputScenario(swissTestScenarios.zurichProfessional);
      await dashboardPage.waitForAutoSave();
      
      // Modify data and create second backup
      await dashboardPage.navigateToIncomeSubTab();
      await dashboardPage.enterMonthlyIncome(12000);
      await dashboardPage.waitForAutoSave();
      
      // Navigate to backup management
      await dashboardPage.navigateToDataSubTab();
      
      const backupHistory = page.locator('[data-testid="backup-history"]');
      
      if (await backupHistory.count() > 0) {
        await expect(backupHistory).toBeVisible();
        
        // Should show multiple backup versions
        const backupItems = page.locator('[data-testid="backup-item"]');
        const backupCount = await backupItems.count();
        expect(backupCount).toBeGreaterThan(0);
      }
    });
  });

  test.describe('Data Validation and Integrity', () => {
    test('validates imported data integrity', async ({ page }) => {
      console.log('🔍 Testing data integrity validation');
      
      // Create data with potential issues
      const problematicData = {
        version: '1.0.0',
        exportDate: new Date().toISOString(),
        financialData: {
          income: {
            monthly: -5000, // Invalid negative income
            bonus: 'invalid', // Invalid data type
          },
          personalInfo: {
            age: 200, // Invalid age
            canton: 'INVALID', // Invalid canton
            civilStatus: 'unknown', // Invalid status
          },
        },
      };
      
      const testFilePath = path.join(__dirname, '../../temp', 'problematic-data.json');
      
      // Ensure temp directory exists
      const tempDir = path.dirname(testFilePath);
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }
      
      fs.writeFileSync(testFilePath, JSON.stringify(problematicData, null, 2));
      
      // Attempt import
      await dashboardPage.navigateToDataSubTab();
      
      const importButton = page.locator('[data-testid="import-json"]');
      
      if (await importButton.count() > 0) {
        await importButton.click();
        
        const fileInput = page.locator('input[type="file"]');
        await fileInput.setInputFiles(testFilePath);
        
        // Should show validation errors
        const validationErrors = page.locator('[data-testid="validation-errors"]');
        await expect(validationErrors).toBeVisible();
        
        // Should list specific issues
        await expect(validationErrors).toContainText('income');
        await expect(validationErrors).toContainText('age');
        
        // Clean up
        fs.unlinkSync(testFilePath);
      }
    });

    test('preserves data relationships during export/import cycle', async ({ page }) => {
      console.log('🔗 Testing data relationship preservation');
      
      // Set up complex scenario with relationships
      const scenario = swissTestScenarios.genevaExecutive;
      await dashboardPage.inputScenario(scenario);
      
      // Export data
      await dashboardPage.navigateToDataSubTab();
      
      const exportButton = page.locator('[data-testid="export-json"]');
      
      if (await exportButton.count() > 0) {
        const downloadPromise = page.waitForEvent('download');
        await exportButton.click();
        const download = await downloadPromise;
        
        const downloadPath = path.join(__dirname, '../../temp', download.suggestedFilename());
        await download.saveAs(downloadPath);
        
        // Clear current data
        await dashboardPage.clearLocalStorage();
        await page.reload();
        
        // Import the exported data
        const importButton = page.locator('[data-testid="import-json"]');
        await importButton.click();
        
        const fileInput = page.locator('input[type="file"]');
        await fileInput.setInputFiles(downloadPath);
        
        await page.waitForTimeout(2000);
        
        // Verify all relationships are preserved
        await dashboardPage.navigateToTaxOptimizationSubTab();
        await dashboardPage.waitForCalculations();
        
        // Tax calculations should work correctly with imported data
        const monthlyTax = await dashboardPage.getMonthlyTaxAmount();
        expect(monthlyTax).toBeGreaterThan(0);
        
        // Clean up
        fs.unlinkSync(downloadPath);
      }
    });
  });
});
