import { expect, test } from '@playwright/test';
import { swissTestScenarios } from '../../fixtures/swiss-scenarios';
import { DashboardPage } from '../../pages/dashboard-page';
import { TestHelpers } from '../../utils/test-helpers';

test.describe('Internationalization - Language Switching', () => {
  let dashboardPage: DashboardPage;
  let testHelpers: TestHelpers;

  test.beforeEach(async ({ page }) => {
    dashboardPage = new DashboardPage(page);
    testHelpers = new TestHelpers(page);
    await dashboardPage.goto();
    await dashboardPage.waitForPageLoad();
  });

  test('Language switcher functionality', async () => {
    console.log('🌍 Testing language switcher');
    
    // Verify language switcher is visible
    const languageSwitcher = dashboardPage.page.locator('[data-testid="language-switcher"]');
    await expect(languageSwitcher).toBeVisible();
    
    // Test switching to German
    await languageSwitcher.click();
    await dashboardPage.page.locator('[data-testid="language-option-de"]').click();
    
    // Wait for language change to take effect
    await dashboardPage.page.waitForTimeout(1000);
    
    // Verify app title changed to German
    const appTitle = await dashboardPage.page.locator('[data-testid="app-title"]').textContent();
    expect(appTitle).toContain('Swiss Budget Pro'); // Should remain the same
    
    // Verify some UI elements are in German
    const overviewTabText = await dashboardPage.overviewTab.textContent();
    expect(overviewTabText).toMatch(/Übersicht|Overview/); // Should be German or fallback
    
    console.log('✅ Language switching to German verified');
  });

  test('German language content verification', async () => {
    console.log('🇩🇪 Testing German language content');
    
    // Switch to German
    await dashboardPage.page.locator('[data-testid="language-switcher"]').click();
    await dashboardPage.page.locator('[data-testid="language-option-de"]').click();
    await dashboardPage.page.waitForTimeout(1000);
    
    // Input a scenario to trigger calculations
    const scenario = swissTestScenarios.zurichProfessional;
    await dashboardPage.inputScenario(scenario);
    
    // Navigate to different tabs and verify German content
    await dashboardPage.navigateToTaxOptimizationTab();
    
    // Check for German financial terms
    const pageContent = await dashboardPage.page.textContent();
    
    // Should contain German financial terms
    const germanTermsFound = [
      'Steuer', // Tax
      'CHF', // Currency should remain
      'Säule', // Pillar
      'Kanton', // Canton
    ].some(term => pageContent.includes(term));
    
    if (germanTermsFound) {
      console.log('✅ German financial terms found');
    }
    
    // Verify numbers still use Swiss formatting
    const taxAmountText = await dashboardPage.monthlyTaxAmount.textContent();
    expect(taxAmountText).toMatch(/CHF\s*[\d']+/);
  });

  test('Language persistence across page reloads', async () => {
    console.log('🔄 Testing language persistence');
    
    // Switch to German
    await dashboardPage.page.locator('[data-testid="language-switcher"]').click();
    await dashboardPage.page.locator('[data-testid="language-option-de"]').click();
    await dashboardPage.page.waitForTimeout(1000);
    
    // Reload the page
    await dashboardPage.page.reload();
    await dashboardPage.waitForPageLoad();
    
    // Verify language is still German
    const languageSwitcher = dashboardPage.page.locator('[data-testid="language-switcher"]');
    const currentLanguage = await languageSwitcher.textContent();
    
    // Should show German is selected
    expect(currentLanguage).toMatch(/DE|Deutsch/i);
    
    console.log('✅ Language persistence verified');
  });

  test('Canton names localization', async () => {
    console.log('🗺️ Testing canton names localization');
    
    const scenario = swissTestScenarios.zurichProfessional;
    await dashboardPage.inputScenario(scenario);
    
    // Test in English first
    await dashboardPage.navigateToTaxOptimizationTab();
    const cantonSelectEn = dashboardPage.cantonSelect;
    await cantonSelectEn.click();
    
    // Get canton options in English
    const cantonOptionsEn = await dashboardPage.page.locator('[data-testid="canton-option"]').allTextContents();
    
    // Switch to German
    await dashboardPage.page.locator('[data-testid="language-switcher"]').click();
    await dashboardPage.page.locator('[data-testid="language-option-de"]').click();
    await dashboardPage.page.waitForTimeout(1000);
    
    // Check canton options in German
    await cantonSelectEn.click();
    const cantonOptionsDe = await dashboardPage.page.locator('[data-testid="canton-option"]').allTextContents();
    
    // Verify we have canton options in both languages
    expect(cantonOptionsEn.length).toBeGreaterThan(20); // Should have all 26 cantons
    expect(cantonOptionsDe.length).toBeGreaterThan(20);
    
    console.log('✅ Canton names localization verified');
  });

  test('Financial terms translation', async () => {
    console.log('💰 Testing financial terms translation');
    
    const scenario = swissTestScenarios.genevaExecutive;
    await dashboardPage.inputScenario(scenario);
    
    // Navigate to FIRE Acceleration tab for financial terms
    await dashboardPage.navigateToFireAccelerationTab();
    
    // Switch to German
    await dashboardPage.page.locator('[data-testid="language-switcher"]').click();
    await dashboardPage.page.locator('[data-testid="language-option-de"]').click();
    await dashboardPage.page.waitForTimeout(1000);
    
    // Check for translated financial terms
    const pageContent = await dashboardPage.page.textContent();
    
    // Common German financial terms that should appear
    const germanFinancialTerms = [
      'Einkommen', // Income
      'Ausgaben', // Expenses
      'Sparen', // Savings
      'Investition', // Investment
      'Rente', // Pension
      'Steuer', // Tax
    ];
    
    let translatedTermsFound = 0;
    for (const term of germanFinancialTerms) {
      if (pageContent.includes(term)) {
        translatedTermsFound++;
        console.log(`✅ Found German term: ${term}`);
      }
    }
    
    // Should find at least some translated terms
    expect(translatedTermsFound).toBeGreaterThan(0);
  });

  test('Number and currency formatting in different languages', async () => {
    console.log('🔢 Testing number formatting across languages');
    
    const scenario = swissTestScenarios.zurichProfessional;
    await dashboardPage.inputScenario(scenario);
    
    // Test in English
    const fireProjectionEn = await dashboardPage.fireProjection.textContent();
    const taxAmountEn = await dashboardPage.monthlyTaxAmount.textContent();
    
    // Switch to German
    await dashboardPage.page.locator('[data-testid="language-switcher"]').click();
    await dashboardPage.page.locator('[data-testid="language-option-de"]').click();
    await dashboardPage.page.waitForTimeout(1000);
    
    // Test in German
    const fireProjectionDe = await dashboardPage.fireProjection.textContent();
    const taxAmountDe = await dashboardPage.monthlyTaxAmount.textContent();
    
    // Both should use Swiss formatting (CHF with apostrophes)
    expect(fireProjectionEn).toMatch(/CHF\s*[\d']+/);
    expect(fireProjectionDe).toMatch(/CHF\s*[\d']+/);
    expect(taxAmountEn).toMatch(/CHF\s*[\d']+/);
    expect(taxAmountDe).toMatch(/CHF\s*[\d']+/);
    
    console.log('✅ Number formatting consistent across languages');
  });

  test('Error messages localization', async () => {
    console.log('⚠️ Testing error messages localization');
    
    // Switch to German
    await dashboardPage.page.locator('[data-testid="language-switcher"]').click();
    await dashboardPage.page.locator('[data-testid="language-option-de"]').click();
    await dashboardPage.page.waitForTimeout(1000);
    
    // Trigger validation errors
    await dashboardPage.navigateToOverviewTab();
    
    // Enter invalid income
    await dashboardPage.enterMonthlyIncome(-1000);
    
    // Check for error message
    const errorMessage = dashboardPage.page.locator('[data-testid="income-error"]');
    if (await errorMessage.count() > 0) {
      const errorText = await errorMessage.textContent();
      
      // Should be in German or at least show the error
      expect(errorText).toBeTruthy();
      console.log(`✅ Error message shown: ${errorText}`);
    }
    
    // Enter invalid age
    await dashboardPage.enterAge(150);
    
    const ageError = dashboardPage.page.locator('[data-testid="age-error"]');
    if (await ageError.count() > 0) {
      const ageErrorText = await ageError.textContent();
      expect(ageErrorText).toBeTruthy();
      console.log(`✅ Age error message shown: ${ageErrorText}`);
    }
  });

  test('Browser language detection', async ({ browser }) => {
    console.log('🌐 Testing browser language detection');
    
    // Create a new context with German locale
    const contextDe = await browser.newContext({
      locale: 'de-CH', // Swiss German
    });
    
    const pageDe = await contextDe.newPage();
    const dashboardPageDe = new DashboardPage(pageDe);
    
    await dashboardPageDe.goto();
    await dashboardPageDe.waitForPageLoad();
    
    // Should detect German locale and set language accordingly
    const languageSwitcher = pageDe.locator('[data-testid="language-switcher"]');
    if (await languageSwitcher.count() > 0) {
      const currentLanguage = await languageSwitcher.textContent();
      
      // Should show German is detected/selected
      console.log(`Current language detected: ${currentLanguage}`);
    }
    
    await contextDe.close();
    console.log('✅ Browser language detection tested');
  });
});
