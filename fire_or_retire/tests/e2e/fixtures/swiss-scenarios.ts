/**
 * Swiss Budget Pro Test Scenarios
 * Realistic financial scenarios for testing Swiss-specific features
 */

export interface SwissTestScenario {
  name: string;
  description: string;
  personalInfo: {
    age: number;
    canton: string;
    civilStatus: 'single' | 'married' | 'divorced' | 'widowed';
    children?: number;
  };
  income: {
    monthly: number;
    bonus?: number;
    pillar3a?: number;
    otherIncome?: number;
  };
  expenses: {
    housing: number;
    food: number;
    transport: number;
    insurance: number;
    healthcare: number;
    other: number;
  };
  assets?: {
    savings: number;
    investments: number;
    realEstate?: number;
  };
  expectedResults: {
    fireAge: number;
    monthlyTax: number;
    savingsRate: number;
    netWorth10Years: number;
  };
}

export const swissTestScenarios: Record<string, SwissTestScenario> = {
  zurichProfessional: {
    name: 'Zurich Young Professional',
    description: 'Single professional in Zurich with high income',
    personalInfo: {
      age: 28,
      canton: 'ZH',
      civilStatus: 'single',
    },
    income: {
      monthly: 8500,
      bonus: 15000,
      pillar3a: 7056,
    },
    expenses: {
      housing: 2200,
      food: 800,
      transport: 300,
      insurance: 450,
      healthcare: 350,
      other: 1000,
    },
    assets: {
      savings: 50000,
      investments: 25000,
    },
    expectedResults: {
      fireAge: 55,
      monthlyTax: 42,
      savingsRate: 0.35,
      netWorth10Years: 850000,
    },
  },

  vaudFamily: {
    name: 'Vaud Family Planner',
    description: 'Married couple with children in Vaud',
    personalInfo: {
      age: 35,
      canton: 'VD',
      civilStatus: 'married',
      children: 2,
    },
    income: {
      monthly: 12000,
      bonus: 20000,
      pillar3a: 14112, // Married couple limit
    },
    expenses: {
      housing: 2800,
      food: 1200,
      transport: 500,
      insurance: 800,
      healthcare: 600,
      other: 1500,
    },
    assets: {
      savings: 80000,
      investments: 120000,
      realEstate: 200000,
    },
    expectedResults: {
      fireAge: 55,
      monthlyTax: 1800,
      savingsRate: 0.28,
      netWorth10Years: 1200000,
    },
  },

  genevaExecutive: {
    name: 'Geneva Senior Executive',
    description: 'High-income executive in Geneva',
    personalInfo: {
      age: 45,
      canton: 'GE',
      civilStatus: 'married',
    },
    income: {
      monthly: 18000,
      bonus: 50000,
      pillar3a: 14112,
      otherIncome: 2000,
    },
    expenses: {
      housing: 4000,
      food: 1500,
      transport: 800,
      insurance: 1200,
      healthcare: 800,
      other: 2500,
    },
    assets: {
      savings: 200000,
      investments: 500000,
      realEstate: 800000,
    },
    expectedResults: {
      fireAge: 58,
      monthlyTax: 3500,
      savingsRate: 0.25,
      netWorth10Years: 2500000,
    },
  },

  bernConservative: {
    name: 'Bern Conservative Saver',
    description: 'Conservative saver in Bern with moderate income',
    personalInfo: {
      age: 40,
      canton: 'BE',
      civilStatus: 'single',
    },
    income: {
      monthly: 6500,
      bonus: 8000,
      pillar3a: 7056,
    },
    expenses: {
      housing: 1600,
      food: 600,
      transport: 250,
      insurance: 400,
      healthcare: 300,
      other: 800,
    },
    assets: {
      savings: 120000,
      investments: 80000,
    },
    expectedResults: {
      fireAge: 62,
      monthlyTax: 800,
      savingsRate: 0.42,
      netWorth10Years: 650000,
    },
  },

  baselTechWorker: {
    name: 'Basel Tech Worker',
    description: 'Tech professional in Basel with stock options',
    personalInfo: {
      age: 32,
      canton: 'BS',
      civilStatus: 'single',
    },
    income: {
      monthly: 9500,
      bonus: 25000,
      pillar3a: 7056,
      otherIncome: 5000, // Stock options
    },
    expenses: {
      housing: 2000,
      food: 700,
      transport: 200,
      insurance: 500,
      healthcare: 400,
      other: 1200,
    },
    assets: {
      savings: 60000,
      investments: 150000,
    },
    expectedResults: {
      fireAge: 48,
      monthlyTax: 1400,
      savingsRate: 0.38,
      netWorth10Years: 1100000,
    },
  },
};

/**
 * Swiss Canton Data for Testing
 */
export const swissCantons = [
  { code: 'ZH', name: 'Zurich', taxMultiplier: 1.0 },
  { code: 'BE', name: 'Bern', taxMultiplier: 1.54 },
  { code: 'LU', name: 'Lucerne', taxMultiplier: 1.5 },
  { code: 'UR', name: 'Uri', taxMultiplier: 1.2 },
  { code: 'SZ', name: 'Schwyz', taxMultiplier: 1.25 },
  { code: 'OW', name: 'Obwalden', taxMultiplier: 1.1 },
  { code: 'NW', name: 'Nidwalden', taxMultiplier: 1.15 },
  { code: 'GL', name: 'Glarus', taxMultiplier: 1.3 },
  { code: 'ZG', name: 'Zug', taxMultiplier: 0.8 },
  { code: 'FR', name: 'Fribourg', taxMultiplier: 1.4 },
  { code: 'SO', name: 'Solothurn', taxMultiplier: 1.35 },
  { code: 'BS', name: 'Basel-Stadt', taxMultiplier: 1.05 },
  { code: 'BL', name: 'Basel-Landschaft', taxMultiplier: 1.2 },
  { code: 'SH', name: 'Schaffhausen', taxMultiplier: 1.25 },
  { code: 'AR', name: 'Appenzell Ausserrhoden', taxMultiplier: 1.1 },
  { code: 'AI', name: 'Appenzell Innerrhoden', taxMultiplier: 1.0 },
  { code: 'SG', name: 'St. Gallen', taxMultiplier: 1.3 },
  { code: 'GR', name: 'Graubünden', taxMultiplier: 1.2 },
  { code: 'AG', name: 'Aargau', taxMultiplier: 1.25 },
  { code: 'TG', name: 'Thurgau', taxMultiplier: 1.2 },
  { code: 'TI', name: 'Ticino', taxMultiplier: 1.4 },
  { code: 'VD', name: 'Vaud', taxMultiplier: 1.6 },
  { code: 'VS', name: 'Valais', taxMultiplier: 1.3 },
  { code: 'NE', name: 'Neuchâtel', taxMultiplier: 1.5 },
  { code: 'GE', name: 'Geneva', taxMultiplier: 1.45 },
  { code: 'JU', name: 'Jura', taxMultiplier: 1.4 },
];

/**
 * Test Data Generators
 */
export const generateRandomScenario = (): SwissTestScenario => {
  const cantons = swissCantons;
  const randomCanton = cantons[Math.floor(Math.random() * cantons.length)];
  const age = Math.floor(Math.random() * 30) + 25; // 25-55
  const income = Math.floor(Math.random() * 10000) + 5000; // 5k-15k CHF

  return {
    name: `Random ${randomCanton.name} Scenario`,
    description: 'Randomly generated scenario for testing',
    personalInfo: {
      age,
      canton: randomCanton.code,
      civilStatus: Math.random() > 0.5 ? 'single' : 'married',
    },
    income: {
      monthly: income,
      bonus: Math.floor(income * 1.5),
      pillar3a: 7056,
    },
    expenses: {
      housing: Math.floor(income * 0.25),
      food: Math.floor(income * 0.1),
      transport: Math.floor(income * 0.05),
      insurance: Math.floor(income * 0.06),
      healthcare: Math.floor(income * 0.04),
      other: Math.floor(income * 0.1),
    },
    expectedResults: {
      fireAge: age + 20,
      monthlyTax: Math.floor(income * 0.15),
      savingsRate: 0.3,
      netWorth10Years: income * 120,
    },
  };
};
