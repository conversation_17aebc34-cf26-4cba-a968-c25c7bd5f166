import { Page, Locator, expect } from '@playwright/test';
import { BasePage } from './base-page';

export interface HealthProfileData {
  age: number;
  canton: string;
  income: number;
  healthStatus: 'excellent' | 'good' | 'fair' | 'poor';
  familySize: number;
  hasChildren: boolean;
  currentPremium: number;
  currentDeductible: number;
  expectedMedicalExpenses: number;
  riskTolerance: 'low' | 'medium' | 'high';
}

export class HealthcareOptimizerPage extends BasePage {
  // Health Profile Form Elements
  private readonly ageInput: Locator;
  private readonly cantonSelect: Locator;
  private readonly incomeInput: Locator;
  private readonly healthStatusSelect: Locator;
  private readonly familySizeInput: Locator;
  private readonly hasChildrenCheckbox: Locator;
  private readonly currentPremiumInput: Locator;
  private readonly currentDeductibleSelect: Locator;
  private readonly expectedExpensesInput: Locator;
  private readonly riskToleranceSelect: Locator;

  // Navigation Elements
  private readonly profileTab: Locator;
  private readonly optimizationTab: Locator;
  private readonly fireTab: Locator;
  private readonly actionPlanTab: Locator;

  // Results Elements
  private readonly optimizationResults: Locator;
  private readonly totalSavings: Locator;
  private readonly recommendedDeductible: Locator;
  private readonly annualSavings: Locator;
  private readonly confidenceLevel: Locator;
  private readonly insuranceRecommendations: Locator;
  private readonly fireAnalysis: Locator;
  private readonly additionalFireNumber: Locator;
  private readonly fireDelayMonths: Locator;
  private readonly healthcarePercentage: Locator;
  private readonly averageAnnualCost: Locator;
  private readonly subsidyOptimization: Locator;

  constructor(page: Page) {
    super(page);

    // Health Profile Form Elements
    this.ageInput = page.locator('input[type="number"]').first();
    this.cantonSelect = page.locator('select').first();
    this.incomeInput = page.locator('input[type="number"]').nth(1);
    this.healthStatusSelect = page.locator('select').nth(1);
    this.familySizeInput = page.locator('input[type="number"]').nth(2);
    this.hasChildrenCheckbox = page.locator('input[type="checkbox"]');
    this.currentPremiumInput = page.locator('input[type="number"]').nth(3);
    this.currentDeductibleSelect = page.locator('select').nth(2);
    this.expectedExpensesInput = page.locator('input[type="number"]').nth(4);
    this.riskToleranceSelect = page.locator('select').nth(3);

    // Navigation Elements
    this.profileTab = page.locator('button:has-text("Health Profile")');
    this.optimizationTab = page.locator('button:has-text("Optimization")');
    this.fireTab = page.locator('button:has-text("FIRE Integration")');
    this.actionPlanTab = page.locator('button:has-text("Action Plan")');

    // Results Elements
    this.optimizationResults = page.locator('[data-testid="optimization-results"]');
    this.totalSavings = page.locator('[data-testid="total-savings"]');
    this.recommendedDeductible = page.locator('[data-testid="recommended-deductible"]');
    this.annualSavings = page.locator('[data-testid="annual-savings"]');
    this.confidenceLevel = page.locator('[data-testid="confidence-level"]');
    this.insuranceRecommendations = page.locator('[data-testid="insurance-recommendation"]');
    this.fireAnalysis = page.locator('[data-testid="fire-analysis"]');
    this.additionalFireNumber = page.locator('[data-testid="additional-fire-number"]');
    this.fireDelayMonths = page.locator('[data-testid="fire-delay-months"]');
    this.healthcarePercentage = page.locator('[data-testid="healthcare-percentage"]');
    this.averageAnnualCost = page.locator('[data-testid="average-annual-cost"]');
    this.subsidyOptimization = page.locator('[data-testid="subsidy-optimization"]');
  }

  /**
   * Fill the complete health profile form
   */
  async fillHealthProfile(data: HealthProfileData): Promise<void> {
    await this.updateAge(data.age);
    await this.selectCanton(data.canton);
    await this.updateIncome(data.income);
    await this.updateHealthStatus(data.healthStatus);
    await this.updateFamilySize(data.familySize);
    await this.updateHasChildren(data.hasChildren);
    await this.updateCurrentPremium(data.currentPremium);
    await this.updateCurrentDeductible(data.currentDeductible);
    await this.updateExpectedExpenses(data.expectedMedicalExpenses);
    await this.updateRiskTolerance(data.riskTolerance);

    // Wait for calculations to complete
    await this.page.waitForTimeout(2000);
  }

  /**
   * Update age input
   */
  async updateAge(age: number): Promise<void> {
    await this.ageInput.clear();
    await this.ageInput.fill(age.toString());
    await this.ageInput.blur();
  }

  /**
   * Select canton from dropdown
   */
  async selectCanton(canton: string): Promise<void> {
    await this.cantonSelect.selectOption(canton);
  }

  /**
   * Update income input
   */
  async updateIncome(income: number): Promise<void> {
    await this.incomeInput.clear();
    await this.incomeInput.fill(income.toString());
    await this.incomeInput.blur();
  }

  /**
   * Update health status
   */
  async updateHealthStatus(status: 'excellent' | 'good' | 'fair' | 'poor'): Promise<void> {
    await this.healthStatusSelect.selectOption(status);
  }

  /**
   * Update family size
   */
  async updateFamilySize(size: number): Promise<void> {
    await this.familySizeInput.clear();
    await this.familySizeInput.fill(size.toString());
    await this.familySizeInput.blur();
  }

  /**
   * Update has children checkbox
   */
  async updateHasChildren(hasChildren: boolean): Promise<void> {
    if (hasChildren) {
      await this.hasChildrenCheckbox.check();
    } else {
      await this.hasChildrenCheckbox.uncheck();
    }
  }

  /**
   * Update current premium
   */
  async updateCurrentPremium(premium: number): Promise<void> {
    await this.currentPremiumInput.clear();
    await this.currentPremiumInput.fill(premium.toString());
    await this.currentPremiumInput.blur();
  }

  /**
   * Update current deductible
   */
  async updateCurrentDeductible(deductible: number): Promise<void> {
    await this.currentDeductibleSelect.selectOption(deductible.toString());
  }

  /**
   * Update expected medical expenses
   */
  async updateExpectedExpenses(expenses: number): Promise<void> {
    await this.expectedExpensesInput.clear();
    await this.expectedExpensesInput.fill(expenses.toString());
    await this.expectedExpensesInput.blur();
  }

  /**
   * Update risk tolerance
   */
  async updateRiskTolerance(tolerance: 'low' | 'medium' | 'high'): Promise<void> {
    await this.riskToleranceSelect.selectOption(tolerance);
  }

  /**
   * Navigate to optimization results tab
   */
  async goToOptimization(): Promise<void> {
    await this.optimizationTab.click();
    await this.optimizationResults.waitFor({ state: 'visible', timeout: 10000 });
  }

  /**
   * Navigate to FIRE integration tab
   */
  async goToFireIntegration(): Promise<void> {
    await this.fireTab.click();
    await this.fireAnalysis.waitFor({ state: 'visible', timeout: 10000 });
  }

  /**
   * Navigate to action plan tab
   */
  async goToActionPlan(): Promise<void> {
    await this.actionPlanTab.click();
  }

  /**
   * Get total potential savings amount
   */
  async getTotalSavings(): Promise<number> {
    await this.goToOptimization();
    const savingsText = await this.totalSavings.textContent();
    return parseInt(savingsText?.replace(/[^\d]/g, '') || '0');
  }

  /**
   * Get recommended deductible amount
   */
  async getRecommendedDeductible(): Promise<number> {
    await this.goToOptimization();
    const deductibleText = await this.recommendedDeductible.textContent();
    return parseInt(deductibleText?.replace(/[^\d]/g, '') || '0');
  }

  /**
   * Get annual savings from optimization
   */
  async getAnnualSavings(): Promise<number> {
    await this.goToOptimization();
    const savingsText = await this.annualSavings.textContent();
    return parseInt(savingsText?.replace(/[^\d]/g, '') || '0');
  }

  /**
   * Get confidence level percentage
   */
  async getConfidenceLevel(): Promise<number> {
    await this.goToOptimization();
    const confidenceText = await this.confidenceLevel.textContent();
    return parseInt(confidenceText?.replace(/[^\d]/g, '') || '0');
  }

  /**
   * Get number of insurance recommendations
   */
  async getInsuranceRecommendationCount(): Promise<number> {
    await this.goToOptimization();
    return await this.insuranceRecommendations.count();
  }

  /**
   * Get additional FIRE number needed
   */
  async getAdditionalFireNumber(): Promise<number> {
    await this.goToFireIntegration();
    const fireNumberText = await this.additionalFireNumber.textContent();
    return parseInt(fireNumberText?.replace(/[^\d]/g, '') || '0');
  }

  /**
   * Get FIRE delay in months
   */
  async getFireDelayMonths(): Promise<number> {
    await this.goToFireIntegration();
    const delayText = await this.fireDelayMonths.textContent();
    return parseInt(delayText?.replace(/[^\d]/g, '') || '0');
  }

  /**
   * Get healthcare percentage of FIRE budget
   */
  async getHealthcarePercentage(): Promise<number> {
    await this.goToFireIntegration();
    const percentageText = await this.healthcarePercentage.textContent();
    return parseInt(percentageText?.replace(/[^\d]/g, '') || '0');
  }

  /**
   * Get average annual healthcare cost in FIRE
   */
  async getAverageAnnualCost(): Promise<number> {
    await this.goToFireIntegration();
    const costText = await this.averageAnnualCost.textContent();
    return parseInt(costText?.replace(/[^\d]/g, '') || '0');
  }

  /**
   * Verify optimization results are displayed
   */
  async expectOptimizationResults(): Promise<void> {
    await this.goToOptimization();
    await expect(this.totalSavings).toBeVisible();
    await expect(this.recommendedDeductible).toBeVisible();
    await expect(this.annualSavings).toBeVisible();
    await expect(this.confidenceLevel).toBeVisible();
  }

  /**
   * Verify FIRE analysis is displayed
   */
  async expectFireAnalysis(): Promise<void> {
    await this.goToFireIntegration();
    await expect(this.additionalFireNumber).toBeVisible();
    await expect(this.fireDelayMonths).toBeVisible();
    await expect(this.healthcarePercentage).toBeVisible();
    await expect(this.averageAnnualCost).toBeVisible();
  }

  /**
   * Verify insurance recommendations are displayed
   */
  async expectInsuranceRecommendations(minCount: number = 1): Promise<void> {
    await this.goToOptimization();
    await expect(this.insuranceRecommendations).toHaveCount(minCount);
  }

  /**
   * Wait for calculations to complete
   */
  async waitForCalculations(): Promise<void> {
    // Wait for any loading indicators to disappear
    const loadingIndicator = this.page.locator('[data-testid="calculating-indicator"]');
    if (await loadingIndicator.isVisible()) {
      await loadingIndicator.waitFor({ state: 'hidden', timeout: 10000 });
    }
    
    // Additional wait for calculations
    await this.page.waitForTimeout(1000);
  }

  /**
   * Verify form validation errors
   */
  async expectValidationError(field: string): Promise<void> {
    const errorSelector = `[data-testid="${field}-error"]`;
    await expect(this.page.locator(errorSelector)).toBeVisible();
  }

  /**
   * Get current form values for verification
   */
  async getCurrentFormValues(): Promise<Partial<HealthProfileData>> {
    return {
      age: parseInt(await this.ageInput.inputValue()),
      canton: await this.cantonSelect.inputValue(),
      income: parseInt(await this.incomeInput.inputValue()),
      healthStatus: await this.healthStatusSelect.inputValue() as any,
      familySize: parseInt(await this.familySizeInput.inputValue()),
      hasChildren: await this.hasChildrenCheckbox.isChecked(),
      currentPremium: parseInt(await this.currentPremiumInput.inputValue()),
      currentDeductible: parseInt(await this.currentDeductibleSelect.inputValue()),
      expectedMedicalExpenses: parseInt(await this.expectedExpensesInput.inputValue()),
      riskTolerance: await this.riskToleranceSelect.inputValue() as any,
    };
  }

  /**
   * Test scenario with specific profile
   */
  async testScenario(scenario: HealthProfileData, expectedSavings?: number): Promise<void> {
    await this.fillHealthProfile(scenario);
    await this.expectOptimizationResults();
    
    if (expectedSavings) {
      const actualSavings = await this.getTotalSavings();
      expect(actualSavings).toBeGreaterThanOrEqual(expectedSavings * 0.8); // Allow 20% variance
      expect(actualSavings).toBeLessThanOrEqual(expectedSavings * 1.2);
    }
  }
}
