# Swiss Budget Pro E2E Testing Suite

A comprehensive end-to-end testing framework for Swiss Budget Pro, designed to validate Swiss-specific financial calculations, user workflows, and cross-platform compatibility.

## 🎯 Overview

This E2E testing suite provides:

- **Complete FIRE calculation validation** across Swiss cantons
- **Swiss tax optimization testing** with real tax rates
- **Healthcare cost optimization verification**
- **Cross-browser and mobile compatibility testing**
- **WCAG 2.1 AA accessibility compliance validation**
- **Performance benchmarking and monitoring**
- **Swiss localization testing** (DE/FR/IT/EN)

## 🚀 Quick Start

### Prerequisites

```bash
# Install Node.js dependencies
npm install

# Install Playwright browsers
npx playwright install

# Setup BDD testing (Python)
npm run test:bdd:setup
```

### Running Tests

#### Playwright E2E Tests

```bash
# Run critical flow tests
npm run test:e2e:critical
npm run test:e2e:mobile
npm run test:e2e:accessibility
npm run test:e2e:performance

# Swiss-specific test runner
npm run test:e2e:swiss
npm run test:e2e:swiss-mobile
npm run test:e2e:swiss-all

# Advanced test runner with options
node scripts/run-e2e-tests.js critical chromium --headed --trace
```

#### BDD (Behave) Tests

```bash
# Run BDD test suites
npm run test:bdd                    # Critical scenarios
npm run test:bdd:fire              # FIRE calculation tests
npm run test:bdd:tax               # Tax optimization tests
npm run test:bdd:healthcare        # Healthcare optimization tests
npm run test:bdd:mobile            # Mobile responsiveness tests
npm run test:bdd:accessibility     # Accessibility compliance tests

# Run with specific options
npm run test:bdd:headless          # Headless mode
npm run test:bdd:parallel          # Parallel execution
npm run test:bdd:all               # All BDD tests

# Custom BDD execution
cd tests/bdd && python run_bdd_tests.py critical --browser firefox
cd tests/bdd && python run_bdd_tests.py --tags @swiss,@mobile
```

#### Comprehensive Testing

```bash
# Run all test types
npm run test:all                   # Unit + E2E + BDD critical
npm run test:comprehensive        # Full test suite
```

## 📁 Test Structure

```
tests/
├── e2e/                         # Playwright E2E Tests
│   ├── critical-flows/          # Core financial calculations
│   │   ├── fire-calculation.spec.ts
│   │   ├── tax-optimization.spec.ts
│   │   └── healthcare-optimization.spec.ts
│   ├── mobile/                  # Mobile and responsive testing
│   │   └── responsive-design.spec.ts
│   ├── accessibility/           # WCAG compliance testing
│   │   └── wcag-compliance.spec.ts
│   ├── performance/             # Performance benchmarking
│   │   └── load-performance.spec.ts
│   └── localization/            # Swiss language testing
├── bdd/                         # Behavior Driven Development Tests
│   ├── features/                # Gherkin feature files
│   │   ├── fire_calculation/
│   │   │   ├── basic_fire.feature
│   │   │   ├── advanced_fire.feature
│   │   │   └── edge_cases.feature
│   │   ├── swiss_tax/
│   │   │   ├── canton_comparison.feature
│   │   │   ├── pillar3a_optimization.feature
│   │   │   └── tax_scenarios.feature
│   │   ├── healthcare/
│   │   │   ├── deductible_optimization.feature
│   │   │   ├── premium_comparison.feature
│   │   │   └── family_planning.feature
│   │   ├── user_experience/
│   │   │   ├── onboarding.feature
│   │   │   ├── data_management.feature
│   │   │   └── accessibility.feature
│   │   └── mobile/
│   │       ├── responsive_design.feature
│   │       └── touch_interactions.feature
│   ├── steps/                   # Step definitions
│   │   ├── fire_calculation_steps.py
│   │   ├── swiss_tax_steps.py
│   │   ├── healthcare_steps.py
│   │   ├── user_interface_steps.py
│   │   └── common_steps.py
│   ├── pages/                   # Page objects for BDD
│   │   ├── base_page.py
│   │   ├── dashboard_page.py
│   │   ├── fire_calculator_page.py
│   │   ├── tax_optimizer_page.py
│   │   └── healthcare_optimizer_page.py
│   ├── fixtures/                # BDD test data
│   │   ├── swiss_personas.json
│   │   ├── financial_scenarios.json
│   │   └── test_data.json
│   ├── utils/                   # BDD utilities
│   │   ├── browser_manager.py
│   │   ├── swiss_calculations.py
│   │   ├── data_helpers.py
│   │   └── reporting.py
│   ├── environment.py           # Behave configuration
│   ├── behave.ini              # Behave settings
│   ├── requirements.txt        # Python dependencies
│   └── run_bdd_tests.py        # BDD test runner
├── fixtures/                    # Shared test data
│   ├── swiss-test-data.json
│   └── swiss-financial-data.json
├── page-objects/               # Playwright Page Object Model
│   └── pages/
│       └── SwissBudgetProPage.ts
└── setup/                      # Global setup and teardown
    ├── global-setup.ts
    └── global-teardown.ts
```

## 🧪 Test Categories

### Playwright E2E Tests

**Technology: TypeScript + Playwright**

#### Critical Flow Tests

**Priority: P0 - Must Pass**

- **FIRE Calculation Tests** (`E2E-FIRE-001` to `E2E-FIRE-007`)

  - Complete FIRE planning journey validation
  - Multi-canton tax impact analysis
  - Edge case handling (negative savings, extreme incomes)
  - Real-time calculation updates
  - Data persistence and recovery

- **Tax Optimization Tests** (`E2E-TAX-001` to `E2E-TAX-007`)

  - Multi-canton tax comparison
  - Pillar 3a optimization validation
  - Progressive tax bracket verification
  - Wealth tax considerations
  - Cross-border tax implications

- **Healthcare Optimization Tests** (`E2E-HEALTH-001` to `E2E-HEALTH-007`)
  - Deductible optimization for different risk profiles
  - Premium comparison across cantons
  - Family healthcare planning
  - Cost-benefit analysis validation

### Mobile and Responsive Tests

**Priority: P1 - High**

- **Responsive Design Tests** (`E2E-MOBILE-001` to `E2E-MOBILE-006`)
  - Mobile phone functionality (iPhone, Android)
  - Tablet optimization (iPad, Android tablets)
  - Responsive breakpoint testing
  - Touch interaction validation
  - Mobile performance benchmarks

### Accessibility Tests

**Priority: P1 - High**

- **WCAG 2.1 AA Compliance** (`E2E-A11Y-001` to `E2E-A11Y-006`)
  - Keyboard navigation compliance
  - Screen reader compatibility
  - Color contrast validation
  - Form accessibility
  - Swiss-specific accessibility features

### Performance Tests

**Priority: P2 - Medium**

- **Load Performance** (`E2E-PERF-001` to `E2E-PERF-006`)
  - Page load benchmarks
  - Calculation performance validation
  - Memory usage monitoring
  - Network optimization testing
  - Bundle size analysis

### BDD (Behavior Driven Development) Tests

**Technology: Python + Behave + Playwright**

#### Business-Readable Scenarios

**Priority: P0 - Stakeholder Validation**

- **FIRE Calculation Scenarios** (Gherkin format)

  - Young professional FIRE journey validation
  - High earner optimization in low-tax cantons
  - Conservative FIRE approach with lower withdrawal rates
  - Edge cases and impossible scenarios
  - Real-time calculation updates

- **Swiss Tax Optimization Scenarios**

  - Multi-canton tax comparison workflows
  - Pillar 3a optimization validation
  - Progressive tax bracket analysis
  - Married vs single taxation scenarios
  - Self-employed tax optimization

- **Healthcare Optimization Scenarios**
  - Deductible optimization for different risk profiles
  - Premium comparison across Swiss cantons
  - Family healthcare planning workflows
  - Healthcare cost impact on FIRE calculations
  - Emergency scenario planning

#### User Experience Scenarios

**Priority: P1 - User Journey Validation**

- **Onboarding and Data Management**

  - First-time user onboarding flow
  - Data input validation and error handling
  - Data persistence and recovery scenarios
  - Export and reporting workflows

- **Accessibility and Mobile**
  - Screen reader navigation scenarios
  - Keyboard-only interaction workflows
  - Mobile touch interaction scenarios
  - Responsive design validation across devices

#### Swiss Compliance Scenarios

**Priority: P1 - Regulatory Validation**

- **Swiss Financial Regulations**
  - Canton-specific tax rule validation
  - Swiss healthcare regulation compliance
  - Pillar 3a contribution limit validation
  - Swiss number formatting and localization

## 🎛️ Configuration

### Browser Support

- **Desktop**: Chrome, Firefox, Safari, Edge
- **Mobile**: iOS Safari, Chrome Android
- **Tablets**: iPad Pro, Galaxy Tab S4

### Swiss Locales

- **German (de-CH)**: Primary language
- **French (fr-CH)**: Secondary language
- **Italian (it-CH)**: Tertiary language
- **English (en-CH)**: International support

### Test Data Profiles

#### User Profiles

- **Young Professional**: Age 28, CHF 85K income, FIRE by 50
- **Mid-Career Family**: Age 42, CHF 180K income, traditional retirement
- **High Earner**: Age 35, CHF 250K income, early retirement in Zug
- **Conservative Saver**: Age 50, CHF 120K income, secure retirement

#### Healthcare Profiles

- **Low Risk**: Healthy young adult, high deductible optimal
- **Medium Risk**: Average health, moderate deductible
- **High Risk**: Chronic conditions, low deductible beneficial

## 📊 Reporting and Analysis

### Generated Reports

- **HTML Report**: Interactive test results with screenshots
- **Swiss Budget Pro Report**: Custom financial calculation analysis
- **Performance Report**: Benchmark validation and trends
- **Accessibility Report**: WCAG compliance summary

### Report Locations

```
test-results/
├── html-report/                 # Interactive HTML report
├── reports/
│   ├── swiss-budget-pro-report.md
│   ├── performance-report.json
│   └── accessibility-summary.json
├── screenshots/                 # Failure screenshots
├── videos/                      # Test execution videos
└── traces/                      # Playwright traces
```

## 🔧 Development and Debugging

### Running Tests in Debug Mode

```bash
# Debug specific test
npx playwright test fire-calculation.spec.ts --debug

# Run with trace viewer
npx playwright test --trace on
npx playwright show-trace test-results/traces/trace.zip

# Run headed mode for visual debugging
npx playwright test --headed --slowMo=1000
```

### Writing New Tests

#### Test Naming Convention

- **Test IDs**: `E2E-[CATEGORY]-[NUMBER]` (e.g., `E2E-FIRE-001`)
- **Categories**: FIRE, TAX, HEALTH, MOBILE, A11Y, PERF
- **Descriptive Names**: Include scenario and expected outcome

#### Page Object Usage

```typescript
import { SwissBudgetProPage } from '../../page-objects/pages/SwissBudgetProPage';

test('My new test', async ({ page }) => {
  const swissBudgetPage = new SwissBudgetProPage(page);

  await swissBudgetPage.goto();
  await swissBudgetPage.fillBasicFinancialInfo({
    monthlyIncome: 8000,
    monthlyExpenses: 5000,
    currentSavings: 100000,
    canton: 'ZH',
  });

  await swissBudgetPage.calculateFIRE();
  const results = await swissBudgetPage.getFIREResults();

  expect(results.fireYears).toBeGreaterThan(0);
});
```

## 🎯 Performance Benchmarks

### Target Performance Metrics

- **Page Load**: < 3 seconds
- **FIRE Calculation**: < 500ms
- **Tax Calculation**: < 300ms
- **Healthcare Optimization**: < 200ms
- **Mobile Load**: < 5 seconds

### Core Web Vitals Targets

- **LCP (Largest Contentful Paint)**: < 2.5s
- **FID (First Input Delay)**: < 100ms
- **CLS (Cumulative Layout Shift)**: < 0.1

## 🔒 Security and Privacy Testing

### Data Protection Validation

- GDPR compliance verification
- Data encryption testing
- Session management validation
- Privacy control functionality

## 🌍 Swiss-Specific Features

### Canton Testing

All 26 Swiss cantons are tested for:

- Tax rate accuracy
- Healthcare premium variations
- Cost of living differences
- Regulatory compliance

### Financial Regulation Compliance

- Swiss Federal Tax Administration rates
- Cantonal tax variations
- Pillar 3a contribution limits
- Healthcare deductible options

## 📈 Continuous Integration

### GitHub Actions Integration

```yaml
# .github/workflows/e2e-tests.yml
name: E2E Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: npm ci
      - run: npx playwright install
      - run: npm run test:e2e:critical
```

### Test Execution Strategy

- **PR Validation**: Critical flow tests
- **Staging Deployment**: Full test suite
- **Production Deployment**: Smoke tests
- **Nightly Runs**: Complete validation with all browsers

## 🆘 Troubleshooting

### Common Issues

#### Tests Failing Locally

```bash
# Clear test cache
rm -rf test-results/
npx playwright install --force

# Update test data
npm run test:update-fixtures
```

#### Flaky Tests

- Check for timing issues in calculations
- Verify network stability
- Review element selectors for changes

#### Performance Issues

- Monitor memory usage during tests
- Check for resource leaks
- Validate network request optimization

### Getting Help

- Review test logs in `test-results/`
- Check screenshots for visual issues
- Use trace viewer for detailed debugging
- Consult Swiss financial calculation documentation

## 📝 Contributing

### Adding New Tests

1. Follow the test naming convention
2. Use existing page objects when possible
3. Add test data to fixtures if needed
4. Include performance assertions
5. Verify accessibility compliance

### Test Data Updates

- Update `swiss-test-data.json` for new scenarios
- Verify `swiss-financial-data.json` with current rates
- Test with realistic Swiss financial profiles

---

**Swiss Budget Pro E2E Testing Suite v1.0.0**  
_Ensuring reliable Swiss financial planning through comprehensive testing_
