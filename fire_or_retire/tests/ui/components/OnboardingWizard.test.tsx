/**
 * Basic UI Tests for OnboardingWizard Component
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import OnboardingWizard from '../../../src/components/improved/OnboardingWizard';

// Mock react-i18next
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

describe('OnboardingWizard Component', () => {
  const mockOnComplete = vi.fn();
  const mockOnSkip = vi.fn();

  describe('Basic Rendering', () => {
    it('renders welcome step initially', () => {
      render(
        <OnboardingWizard
          onComplete={mockOnComplete}
          onSkip={mockOnSkip}
          darkMode={false}
        />,
      );

      expect(screen.getByText('🚀')).toBeInTheDocument();
      expect(
        screen.getByText(/Swiss Budget Pro helps you plan/),
      ).toBeInTheDocument();
    });

    it('shows progress indicator', () => {
      render(
        <OnboardingWizard
          onComplete={mockOnComplete}
          onSkip={mockOnSkip}
          darkMode={false}
        />,
      );

      // Should show step progress
      const progressElements = screen.getAllByText(/Step \d+ of \d+/);
      expect(progressElements.length).toBeGreaterThan(0);
    });

    it('renders skip button', () => {
      render(
        <OnboardingWizard
          onComplete={mockOnComplete}
          onSkip={mockOnSkip}
          darkMode={false}
        />,
      );

      expect(screen.getByText('Skip setup')).toBeInTheDocument();
    });

    it('applies dark mode styles correctly', () => {
      const { container } = render(
        <OnboardingWizard
          onComplete={mockOnComplete}
          onSkip={mockOnSkip}
          darkMode={true}
        />,
      );

      // Check for dark mode classes in the component
      const darkElements = container.querySelectorAll(
        '[class*="gray-800"], [class*="gray-900"]',
      );
      expect(darkElements.length).toBeGreaterThan(0);
    });

    it('applies light mode styles correctly', () => {
      const { container } = render(
        <OnboardingWizard
          onComplete={mockOnComplete}
          onSkip={mockOnSkip}
          darkMode={false}
        />,
      );

      // Check for light mode classes in the component
      const lightElements = container.querySelectorAll(
        '[class*="bg-white"], [class*="bg-gray-50"]',
      );
      expect(lightElements.length).toBeGreaterThan(0);
    });
  });
});
