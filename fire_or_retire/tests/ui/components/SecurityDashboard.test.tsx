/**
 * Swiss Budget Pro - Security Dashboard Component Tests
 * 
 * Comprehensive tests for the security dashboard UI component
 * ensuring proper rendering and user interactions.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { SecurityDashboard } from '../../../src/components/security/SecurityDashboard';
import { securityMonitor, SecurityEventType, SecuritySeverity } from '../../../src/security/security-monitor';
import { privacyControls, DataCategory } from '../../../src/security/privacy-controls';

// Mock the security modules
vi.mock('../../../src/security/security-monitor', () => ({
  securityMonitor: {
    getSecurityStatus: vi.fn(),
    getMetrics: vi.fn(),
    getEvents: vi.fn(),
    logEvent: vi.fn(),
  },
  SecurityEventType: {
    DATA_ACCESS: 'data_access',
    PRIVACY_SETTINGS_CHANGED: 'privacy_settings_changed',
    DATA_EXPORT: 'data_export',
  },
  SecuritySeverity: {
    LOW: 'low',
    MEDIUM: 'medium',
    HIGH: 'high',
    CRITICAL: 'critical',
  },
}));

vi.mock('../../../src/security/privacy-controls', () => ({
  privacyControls: {
    getPrivacySettings: vi.fn(),
    updatePrivacySettings: vi.fn(),
    getDataInventory: vi.fn(),
  },
  DataCategory: {
    FINANCIAL_DATA: 'financial_data',
    PERSONAL_INFO: 'personal_info',
    USAGE_ANALYTICS: 'usage_analytics',
    PREFERENCES: 'preferences',
    CACHE_DATA: 'cache_data',
    TEMPORARY_DATA: 'temporary_data',
  },
}));

vi.mock('../../../src/security/encryption', () => ({
  encryption: {
    getMetrics: vi.fn(),
  },
  SecurityUtils: {
    generateSecurePassphrase: vi.fn(),
    estimatePassphraseStrength: vi.fn(),
    isWebCryptoAvailable: vi.fn(),
  },
}));

// Mock URL.createObjectURL and related APIs
Object.defineProperty(global, 'URL', {
  value: {
    createObjectURL: vi.fn(() => 'mock-blob-url'),
    revokeObjectURL: vi.fn(),
  },
  writable: true,
});

// Mock Blob
Object.defineProperty(global, 'Blob', {
  value: class MockBlob {
    constructor(content: any[], options: any) {
      this.content = content;
      this.options = options;
    }
    content: any[];
    options: any;
  },
  writable: true,
});

describe('SecurityDashboard', () => {
  const mockSecurityStatus = {
    overall: 'secure' as const,
    threats: 0,
    lastScan: new Date(),
    recommendations: [],
    complianceScore: 95,
  };

  const mockMetrics = {
    totalEvents: 42,
    eventsByType: {
      [SecurityEventType.DATA_ACCESS]: 10,
    },
    eventsBySeverity: {
      [SecuritySeverity.LOW]: 30,
      [SecuritySeverity.MEDIUM]: 8,
      [SecuritySeverity.HIGH]: 3,
      [SecuritySeverity.CRITICAL]: 1,
    },
    threatsDetected: 2,
    threatsResolved: 1,
    averageResponseTime: 120,
    lastThreatDetected: new Date(),
  };

  const mockEvents = [
    {
      id: 'event-1',
      timestamp: new Date(),
      type: SecurityEventType.DATA_ACCESS,
      severity: SecuritySeverity.LOW,
      description: 'User accessed financial data',
      details: {},
      resolved: true,
      userAgent: 'Test Browser',
      sessionId: 'session-1',
    },
    {
      id: 'event-2',
      timestamp: new Date(),
      type: SecurityEventType.PRIVACY_SETTINGS_CHANGED,
      severity: SecuritySeverity.MEDIUM,
      description: 'Privacy settings updated',
      details: {},
      resolved: false,
      userAgent: 'Test Browser',
      sessionId: 'session-1',
    },
  ];

  const mockPrivacySettings = {
    [DataCategory.FINANCIAL_DATA]: {
      enabled: true,
      retentionDays: 2555,
      encryptionRequired: true,
      exportAllowed: true,
    },
    [DataCategory.PERSONAL_INFO]: {
      enabled: true,
      retentionDays: 365,
      encryptionRequired: true,
      exportAllowed: true,
    },
    [DataCategory.USAGE_ANALYTICS]: {
      enabled: false,
      retentionDays: 90,
      anonymized: true,
      exportAllowed: true,
    },
    [DataCategory.PREFERENCES]: {
      enabled: true,
      retentionDays: 730,
      encryptionRequired: false,
      exportAllowed: true,
    },
    [DataCategory.CACHE_DATA]: {
      enabled: true,
      retentionDays: 30,
      autoCleanup: true,
      exportAllowed: false,
    },
    [DataCategory.TEMPORARY_DATA]: {
      enabled: true,
      retentionDays: 1,
      autoCleanup: true,
      exportAllowed: false,
    },
  };

  beforeEach(() => {
    // Setup default mocks
    vi.mocked(securityMonitor.getSecurityStatus).mockReturnValue(mockSecurityStatus);
    vi.mocked(securityMonitor.getMetrics).mockReturnValue(mockMetrics);
    vi.mocked(securityMonitor.getEvents).mockReturnValue(mockEvents);
    vi.mocked(privacyControls.getPrivacySettings).mockReturnValue(mockPrivacySettings);
    vi.mocked(privacyControls.getDataInventory).mockReturnValue([]);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Rendering', () => {
    it('should render security dashboard with header', () => {
      render(<SecurityDashboard />);
      
      expect(screen.getByText('Security Dashboard')).toBeInTheDocument();
      expect(screen.getByText('Monitor security status, manage privacy settings, and review audit trails.')).toBeInTheDocument();
    });

    it('should render navigation tabs', () => {
      render(<SecurityDashboard />);
      
      expect(screen.getByText('Overview')).toBeInTheDocument();
      expect(screen.getByText('Security Events')).toBeInTheDocument();
      expect(screen.getByText('Privacy Controls')).toBeInTheDocument();
      expect(screen.getByText('Encryption')).toBeInTheDocument();
    });

    it('should render overview tab by default', () => {
      render(<SecurityDashboard />);
      
      expect(screen.getByText('Security Status')).toBeInTheDocument();
      expect(screen.getByText('SECURE')).toBeInTheDocument();
      expect(screen.getByText('0')).toBeInTheDocument(); // Active Threats
      expect(screen.getByText('95%')).toBeInTheDocument(); // Compliance Score
    });

    it('should apply custom className', () => {
      const { container } = render(<SecurityDashboard className="custom-class" />);
      
      expect(container.firstChild).toHaveClass('custom-class');
    });
  });

  describe('Tab Navigation', () => {
    it('should switch to security events tab', () => {
      render(<SecurityDashboard />);
      
      fireEvent.click(screen.getByText('Security Events'));
      
      expect(screen.getByText('Recent Security Events')).toBeInTheDocument();
      expect(screen.getByText('Export')).toBeInTheDocument();
    });

    it('should switch to privacy controls tab', () => {
      render(<SecurityDashboard />);
      
      fireEvent.click(screen.getByText('Privacy Controls'));
      
      expect(screen.getByText('Privacy Controls')).toBeInTheDocument();
      expect(screen.getByText('Financial Data')).toBeInTheDocument();
      expect(screen.getByText('Personal Info')).toBeInTheDocument();
    });

    it('should switch to encryption tab', () => {
      render(<SecurityDashboard />);
      
      fireEvent.click(screen.getByText('Encryption'));
      
      expect(screen.getByText('Encryption Status')).toBeInTheDocument();
      expect(screen.getByText('AES-256-GCM Encryption Active')).toBeInTheDocument();
    });

    it('should highlight active tab', () => {
      render(<SecurityDashboard />);
      
      const overviewTab = screen.getByText('Overview').closest('button');
      const eventsTab = screen.getByText('Security Events').closest('button');
      
      expect(overviewTab).toHaveClass('border-blue-500', 'text-blue-600');
      expect(eventsTab).toHaveClass('border-transparent', 'text-gray-500');
      
      fireEvent.click(screen.getByText('Security Events'));
      
      expect(overviewTab).toHaveClass('border-transparent', 'text-gray-500');
      expect(eventsTab).toHaveClass('border-blue-500', 'text-blue-600');
    });
  });

  describe('Security Status Display', () => {
    it('should display warning status with recommendations', () => {
      const warningStatus = {
        ...mockSecurityStatus,
        overall: 'warning' as const,
        threats: 2,
        recommendations: ['Review suspicious activities', 'Update security settings'],
      };
      
      vi.mocked(securityMonitor.getSecurityStatus).mockReturnValue(warningStatus);
      
      render(<SecurityDashboard />);
      
      expect(screen.getByText('WARNING')).toBeInTheDocument();
      expect(screen.getByText('2')).toBeInTheDocument(); // Active Threats
      expect(screen.getByText('Recommendations')).toBeInTheDocument();
      expect(screen.getByText('Review suspicious activities')).toBeInTheDocument();
      expect(screen.getByText('Update security settings')).toBeInTheDocument();
    });

    it('should display critical status', () => {
      const criticalStatus = {
        ...mockSecurityStatus,
        overall: 'critical' as const,
        threats: 5,
        recommendations: ['Immediate action required'],
      };
      
      vi.mocked(securityMonitor.getSecurityStatus).mockReturnValue(criticalStatus);
      
      render(<SecurityDashboard />);
      
      expect(screen.getByText('CRITICAL')).toBeInTheDocument();
      expect(screen.getByText('5')).toBeInTheDocument(); // Active Threats
    });

    it('should display security metrics', () => {
      render(<SecurityDashboard />);
      
      expect(screen.getByText('1')).toBeInTheDocument(); // Threats Resolved
      expect(screen.getByText('42')).toBeInTheDocument(); // Total Events
      expect(screen.getByText('120s')).toBeInTheDocument(); // Avg Response Time
    });
  });

  describe('Security Events Table', () => {
    it('should display security events in table', () => {
      render(<SecurityDashboard />);
      
      fireEvent.click(screen.getByText('Security Events'));
      
      expect(screen.getByText('User accessed financial data')).toBeInTheDocument();
      expect(screen.getByText('Privacy settings updated')).toBeInTheDocument();
      expect(screen.getByText('data access')).toBeInTheDocument();
      expect(screen.getByText('privacy settings changed')).toBeInTheDocument();
    });

    it('should show event resolution status', () => {
      render(<SecurityDashboard />);
      
      fireEvent.click(screen.getByText('Security Events'));
      
      // Should show resolved and unresolved icons
      const resolvedIcons = screen.getAllByTestId('check-circle');
      const unresolvedIcons = screen.getAllByTestId('x-circle');
      
      expect(resolvedIcons.length + unresolvedIcons.length).toBe(mockEvents.length);
    });

    it('should handle data export', () => {
      render(<SecurityDashboard />);
      
      fireEvent.click(screen.getByText('Security Events'));
      fireEvent.click(screen.getByText('Export'));
      
      expect(vi.mocked(securityMonitor.logEvent)).toHaveBeenCalledWith(
        SecurityEventType.DATA_EXPORT,
        SecuritySeverity.LOW,
        'Security data exported',
        { exportType: 'security_dashboard' },
      );
    });
  });

  describe('Privacy Controls', () => {
    it('should display privacy settings for all data categories', () => {
      render(<SecurityDashboard />);
      
      fireEvent.click(screen.getByText('Privacy Controls'));
      
      expect(screen.getByText('Financial Data')).toBeInTheDocument();
      expect(screen.getByText('Personal Info')).toBeInTheDocument();
      expect(screen.getByText('Usage Analytics')).toBeInTheDocument();
      expect(screen.getByText('Preferences')).toBeInTheDocument();
      expect(screen.getByText('Cache Data')).toBeInTheDocument();
      expect(screen.getByText('Temporary Data')).toBeInTheDocument();
    });

    it('should show correct initial settings', () => {
      render(<SecurityDashboard />);
      
      fireEvent.click(screen.getByText('Privacy Controls'));
      
      // Financial data should be enabled
      const financialEnabledCheckbox = screen.getByLabelText('Enabled') as HTMLInputElement;
      expect(financialEnabledCheckbox.checked).toBe(true);
      
      // Usage analytics should be disabled
      const analyticsSection = screen.getByText('Usage Analytics').closest('div');
      const analyticsCheckbox = analyticsSection?.querySelector('input[type="checkbox"]') as HTMLInputElement;
      expect(analyticsCheckbox?.checked).toBe(false);
    });

    it('should handle privacy setting changes', async () => {
      render(<SecurityDashboard />);
      
      fireEvent.click(screen.getByText('Privacy Controls'));
      
      // Find and click the first enabled checkbox (should be financial data)
      const enabledCheckboxes = screen.getAllByLabelText('Enabled');
      fireEvent.click(enabledCheckboxes[0]);
      
      await waitFor(() => {
        expect(vi.mocked(privacyControls.updatePrivacySettings)).toHaveBeenCalled();
        expect(vi.mocked(securityMonitor.logEvent)).toHaveBeenCalledWith(
          SecurityEventType.PRIVACY_SETTINGS_CHANGED,
          SecuritySeverity.LOW,
          expect.stringContaining('Privacy setting changed'),
          expect.any(Object),
        );
      });
    });

    it('should handle retention period changes', async () => {
      render(<SecurityDashboard />);
      
      fireEvent.click(screen.getByText('Privacy Controls'));
      
      // Find and change a retention period input
      const retentionInputs = screen.getAllByDisplayValue('2555');
      fireEvent.change(retentionInputs[0], { target: { value: '1000' } });
      
      await waitFor(() => {
        expect(vi.mocked(privacyControls.updatePrivacySettings)).toHaveBeenCalledWith(
          DataCategory.FINANCIAL_DATA,
          { retentionDays: 1000 },
        );
      });
    });
  });

  describe('Encryption Status', () => {
    it('should display encryption metrics', () => {
      const mockEncryptionMetrics = [
        { encryptionTime: 50, decryptionTime: 45, keyDerivationTime: 100, dataSize: 1024 },
        { encryptionTime: 60, decryptionTime: 55, keyDerivationTime: 110, dataSize: 2048 },
      ];
      
      vi.doMock('../../../src/security/encryption', () => ({
        encryption: {
          getMetrics: () => mockEncryptionMetrics,
        },
      }));
      
      render(<SecurityDashboard />);
      
      fireEvent.click(screen.getByText('Encryption'));
      
      expect(screen.getByText('2')).toBeInTheDocument(); // Total Operations
      expect(screen.getByText('55ms')).toBeInTheDocument(); // Avg Encryption Time
      expect(screen.getByText('50ms')).toBeInTheDocument(); // Avg Decryption Time
    });

    it('should show encryption status message', () => {
      render(<SecurityDashboard />);
      
      fireEvent.click(screen.getByText('Encryption'));
      
      expect(screen.getByText('AES-256-GCM Encryption Active')).toBeInTheDocument();
      expect(screen.getByText('All sensitive data is encrypted with bank-level security using AES-256-GCM encryption.')).toBeInTheDocument();
    });
  });

  describe('Real-time Updates', () => {
    it('should set up interval for data refresh', () => {
      const setIntervalSpy = vi.spyOn(global, 'setInterval');
      
      render(<SecurityDashboard />);
      
      expect(setIntervalSpy).toHaveBeenCalledWith(expect.any(Function), 30000);
    });

    it('should clean up interval on unmount', () => {
      const clearIntervalSpy = vi.spyOn(global, 'clearInterval');
      
      const { unmount } = render(<SecurityDashboard />);
      unmount();
      
      expect(clearIntervalSpy).toHaveBeenCalled();
    });
  });
});
