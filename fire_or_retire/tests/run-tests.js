#!/usr/bin/env node

/**
 * Swiss Budget Pro Test Runner
 * Comprehensive test suite runner for all Swiss Budget Pro functionality
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Simple test framework implementation
class TestFramework {
  constructor() {
    this.tests = [];
    this.describes = [];
    this.currentDescribe = null;
    this.beforeEachCallbacks = [];
    this.results = {
      passed: 0,
      failed: 0,
      total: 0,
      failures: []
    };
  }

  describe(name, callback) {
    const previousDescribe = this.currentDescribe;
    this.currentDescribe = name;
    this.beforeEachCallbacks = [];

    console.log(`\n📋 ${name}`);
    callback();

    this.currentDescribe = previousDescribe;
  }

  beforeEach(callback) {
    this.beforeEachCallbacks.push(callback);
  }

  test(name, callback) {
    this.results.total++;

    try {
      // Run beforeEach callbacks
      this.beforeEachCallbacks.forEach(cb => cb());

      // Run the test
      callback();

      this.results.passed++;
      console.log(`  ✅ ${name}`);
    } catch (error) {
      this.results.failed++;
      this.results.failures.push({
        describe: this.currentDescribe,
        test: name,
        error: error.message
      });
      console.log(`  ❌ ${name}`);
      console.log(`     Error: ${error.message}`);
    }
  }

  expect(actual) {
    return {
      toBe: (expected) => {
        if (actual !== expected) {
          throw new Error(`Expected ${actual} to be ${expected}`);
        }
      },
      toEqual: (expected) => {
        if (JSON.stringify(actual) !== JSON.stringify(expected)) {
          throw new Error(`Expected ${JSON.stringify(actual)} to equal ${JSON.stringify(expected)}`);
        }
      },
      toBeGreaterThan: (expected) => {
        if (actual <= expected) {
          throw new Error(`Expected ${actual} to be greater than ${expected}`);
        }
      },
      toBeLessThan: (expected) => {
        if (actual >= expected) {
          throw new Error(`Expected ${actual} to be less than ${expected}`);
        }
      },
      toBeGreaterThanOrEqual: (expected) => {
        if (actual < expected) {
          throw new Error(`Expected ${actual} to be greater than or equal to ${expected}`);
        }
      },
      toBeLessThanOrEqual: (expected) => {
        if (actual > expected) {
          throw new Error(`Expected ${actual} to be less than or equal to ${expected}`);
        }
      },
      toHaveProperty: (property) => {
        if (!(property in actual)) {
          throw new Error(`Expected object to have property ${property}`);
        }
      },
      toHaveLength: (length) => {
        if (actual.length !== length) {
          throw new Error(`Expected array to have length ${length}, got ${actual.length}`);
        }
      },
      toBeDefined: () => {
        if (actual === undefined) {
          throw new Error(`Expected value to be defined`);
        }
      },
      toBeUndefined: () => {
        if (actual !== undefined) {
          throw new Error(`Expected value to be undefined`);
        }
      },
      toBeInstanceOf: (constructor) => {
        if (!(actual instanceof constructor)) {
          throw new Error(`Expected value to be instance of ${constructor.name}`);
        }
      },
      toContain: (substring) => {
        if (!actual.includes(substring)) {
          throw new Error(`Expected "${actual}" to contain "${substring}"`);
        }
      }
    };
  }

  printResults() {
    console.log('\n' + '='.repeat(60));
    console.log('🧪 TEST RESULTS SUMMARY');
    console.log('='.repeat(60));
    console.log(`Total Tests: ${this.results.total}`);
    console.log(`✅ Passed: ${this.results.passed}`);
    console.log(`❌ Failed: ${this.results.failed}`);
    console.log(`📊 Success Rate: ${((this.results.passed / this.results.total) * 100).toFixed(1)}%`);

    if (this.results.failures.length > 0) {
      console.log('\n❌ FAILED TESTS:');
      this.results.failures.forEach((failure, index) => {
        console.log(`${index + 1}. ${failure.describe} - ${failure.test}`);
        console.log(`   Error: ${failure.error}`);
      });
    }

    console.log('\n' + '='.repeat(60));

    return this.results.failed === 0;
  }
}

// Global test framework instance
const framework = new TestFramework();
global.describe = framework.describe.bind(framework);
global.test = framework.test.bind(framework);
global.beforeEach = framework.beforeEach.bind(framework);
global.expect = framework.expect.bind(framework);

// Mock module system for browser-like environment
global.module = { exports: {} };

// Test runner
async function runTests() {
  console.log('🚀 Starting Swiss Budget Pro Test Suite');
  console.log('Testing: Tax Optimization Engine + Economic Data Integration + Integration Tests');

  try {
    // Load and run Swiss Tax Engine tests
    console.log('\n🇨🇭 Loading Swiss Tax Engine Tests...');
    const taxEngineTests = fs.readFileSync(path.join(__dirname, 'swiss-tax-engine.test.js'), 'utf8');
    eval(taxEngineTests);

    // Load and run Economic Data Service tests
    console.log('\n📈 Loading Economic Data Service Tests...');
    const economicDataTests = fs.readFileSync(path.join(__dirname, 'economic-data-service.test.js'), 'utf8');
    eval(economicDataTests);

    // Load and run Integration tests
    console.log('\n🔗 Loading Integration Tests...');
    const integrationTests = fs.readFileSync(path.join(__dirname, 'integration.test.js'), 'utf8');
    eval(integrationTests);

    // Print final results
    const success = framework.printResults();

    if (success) {
      console.log('🎉 All tests passed! Swiss Budget Pro is ready for production.');
      process.exit(0);
    } else {
      console.log('⚠️  Some tests failed. Please review and fix the issues.');
      process.exit(1);
    }

  } catch (error) {
    console.error('💥 Test runner error:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// Performance monitoring
function measurePerformance(name, fn) {
  const start = Date.now();
  const result = fn();
  const end = Date.now();
  console.log(`⏱️  ${name}: ${end - start}ms`);
  return result;
}

// Run the tests
if (import.meta.url === `file://${process.argv[1]}`) {
  measurePerformance('Complete Test Suite', () => {
    runTests();
  });
}

export { runTests, TestFramework };

