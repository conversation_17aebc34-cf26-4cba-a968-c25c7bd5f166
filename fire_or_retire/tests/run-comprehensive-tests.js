#!/usr/bin/env node

/**
 * Comprehensive Test Runner for Swiss Budget Pro
 * 
 * This script runs all Jest and Behave tests for the comprehensive calculator
 * with detailed reporting and performance metrics.
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const os = require('os');

class ComprehensiveTestRunner {
  constructor() {
    this.results = {
      jest: { passed: 0, failed: 0, total: 0, duration: 0 },
      behave: { passed: 0, failed: 0, total: 0, duration: 0 },
      overall: { startTime: Date.now(), endTime: null }
    };
    
    this.testSuites = {
      jest: [
        'tests/unit/calculations/financial-calculations.test.ts',
        'tests/unit/calculations/monte-carlo-simulation.test.ts',
        'tests/unit/calculations/swiss-tax-calculations.test.ts',
        'tests/unit/components/DataVisualizationIntegration.test.tsx',
        'tests/unit/components/DataVisualizationDashboard.test.tsx',
        'tests/unit/components/EnhancedD3Chart.test.tsx',
        'tests/unit/performance/calculation-performance.test.ts',
        'tests/unit/security/encryption.test.ts',
        'tests/unit/utils/swiss-social-insurance.test.ts'
      ],
      behave: [
        'tests/bdd/features/advanced_user_journeys/complete_fire_planning.feature',
        'tests/bdd/features/swiss_edge_cases/canton_specific_scenarios.feature',
        'tests/bdd/features/data_management/import_export_workflows.feature',
        'tests/bdd/features/fire_calculation/basic_fire.feature',
        'tests/bdd/features/swiss_tax/canton_comparison.feature',
        'tests/bdd/features/premium_features/ai_financial_advisor.feature'
      ]
    };
  }

  async runAllTests() {
    console.log('🚀 Starting Comprehensive Test Suite for Swiss Budget Pro');
    console.log('=' * 80);
    
    try {
      // Run Jest tests
      console.log('\n📊 Running Jest Unit Tests...');
      await this.runJestTests();
      
      // Run Behave tests
      console.log('\n🎭 Running Behave BDD Tests...');
      await this.runBehaveTests();
      
      // Generate comprehensive report
      this.generateReport();
      
    } catch (error) {
      console.error('❌ Test execution failed:', error.message);
      process.exit(1);
    }
  }

  async runJestTests() {
    const startTime = Date.now();
    
    try {
      // Check if Vitest is available (modern Jest alternative)
      const testCommand = this.checkTestRunner();
      
      console.log(`Using test runner: ${testCommand}`);
      
      for (const testFile of this.testSuites.jest) {
        if (fs.existsSync(testFile)) {
          console.log(`\n  🧪 Running ${path.basename(testFile)}...`);
          
          try {
            const result = execSync(`${testCommand} ${testFile} --reporter=verbose`, {
              encoding: 'utf8',
              timeout: 60000 // 60 second timeout per test file
            });
            
            this.parseJestResults(result, testFile);
            console.log(`  ✅ ${path.basename(testFile)} completed`);
            
          } catch (error) {
            console.log(`  ❌ ${path.basename(testFile)} failed`);
            this.results.jest.failed++;
            console.log(`     Error: ${error.message.split('\n')[0]}`);
          }
        } else {
          console.log(`  ⚠️  ${testFile} not found, skipping...`);
        }
      }
      
    } catch (error) {
      console.error('Jest test execution failed:', error.message);
    }
    
    this.results.jest.duration = Date.now() - startTime;
  }

  async runBehaveTests() {
    const startTime = Date.now();
    
    try {
      // Check if Behave is available
      try {
        execSync('behave --version', { encoding: 'utf8' });
      } catch (error) {
        console.log('⚠️  Behave not installed, skipping BDD tests');
        console.log('   Install with: pip install behave selenium');
        return;
      }
      
      // Set up Behave environment
      const behaveDir = path.join(__dirname, 'bdd');
      process.chdir(behaveDir);
      
      for (const featureFile of this.testSuites.behave) {
        const relativePath = path.relative(behaveDir, featureFile);
        
        if (fs.existsSync(relativePath)) {
          console.log(`\n  🎭 Running ${path.basename(featureFile)}...`);
          
          try {
            const result = execSync(`behave ${relativePath} --format=json --outfile=results.json`, {
              encoding: 'utf8',
              timeout: 120000 // 2 minute timeout per feature
            });
            
            this.parseBehaveResults(relativePath);
            console.log(`  ✅ ${path.basename(featureFile)} completed`);
            
          } catch (error) {
            console.log(`  ❌ ${path.basename(featureFile)} failed`);
            this.results.behave.failed++;
            console.log(`     Error: ${error.message.split('\n')[0]}`);
          }
        } else {
          console.log(`  ⚠️  ${relativePath} not found, skipping...`);
        }
      }
      
    } catch (error) {
      console.error('Behave test execution failed:', error.message);
    }
    
    this.results.behave.duration = Date.now() - startTime;
  }

  checkTestRunner() {
    // Check for available test runners in order of preference
    const runners = ['vitest', 'jest', 'npm test'];
    
    for (const runner of runners) {
      try {
        execSync(`${runner} --version`, { encoding: 'utf8', stdio: 'ignore' });
        return runner;
      } catch (error) {
        continue;
      }
    }
    
    throw new Error('No test runner found. Please install Vitest or Jest.');
  }

  parseJestResults(output, testFile) {
    // Parse Jest/Vitest output for test results
    const lines = output.split('\n');
    let passed = 0;
    let failed = 0;
    
    for (const line of lines) {
      if (line.includes('✓') || line.includes('PASS')) {
        passed++;
      } else if (line.includes('✗') || line.includes('FAIL')) {
        failed++;
      }
    }
    
    // If no specific counts found, assume success if no error
    if (passed === 0 && failed === 0) {
      passed = 1; // At least the file ran
    }
    
    this.results.jest.passed += passed;
    this.results.jest.failed += failed;
    this.results.jest.total += (passed + failed);
  }

  parseBehaveResults(featureFile) {
    // Parse Behave JSON results if available
    const resultsFile = path.join(process.cwd(), 'results.json');
    
    try {
      if (fs.existsSync(resultsFile)) {
        const results = JSON.parse(fs.readFileSync(resultsFile, 'utf8'));
        
        for (const feature of results) {
          for (const element of feature.elements || []) {
            if (element.type === 'scenario') {
              const steps = element.steps || [];
              const failedSteps = steps.filter(step => step.result?.status === 'failed');
              
              if (failedSteps.length === 0) {
                this.results.behave.passed++;
              } else {
                this.results.behave.failed++;
              }
              
              this.results.behave.total++;
            }
          }
        }
        
        // Clean up results file
        fs.unlinkSync(resultsFile);
      } else {
        // Fallback: assume success if no results file
        this.results.behave.passed++;
        this.results.behave.total++;
      }
      
    } catch (error) {
      console.log(`Warning: Could not parse Behave results for ${featureFile}`);
      this.results.behave.total++;
    }
  }

  generateReport() {
    this.results.overall.endTime = Date.now();
    const totalDuration = this.results.overall.endTime - this.results.overall.startTime;
    
    console.log('\n' + '=' * 80);
    console.log('📋 COMPREHENSIVE TEST RESULTS SUMMARY');
    console.log('=' * 80);
    
    // Jest Results
    console.log('\n📊 Jest Unit Tests:');
    console.log(`   ✅ Passed: ${this.results.jest.passed}`);
    console.log(`   ❌ Failed: ${this.results.jest.failed}`);
    console.log(`   📊 Total:  ${this.results.jest.total}`);
    console.log(`   ⏱️  Duration: ${(this.results.jest.duration / 1000).toFixed(2)}s`);
    
    if (this.results.jest.total > 0) {
      const jestSuccessRate = (this.results.jest.passed / this.results.jest.total * 100).toFixed(1);
      console.log(`   📈 Success Rate: ${jestSuccessRate}%`);
    }
    
    // Behave Results
    console.log('\n🎭 Behave BDD Tests:');
    console.log(`   ✅ Passed: ${this.results.behave.passed}`);
    console.log(`   ❌ Failed: ${this.results.behave.failed}`);
    console.log(`   📊 Total:  ${this.results.behave.total}`);
    console.log(`   ⏱️  Duration: ${(this.results.behave.duration / 1000).toFixed(2)}s`);
    
    if (this.results.behave.total > 0) {
      const behaveSuccessRate = (this.results.behave.passed / this.results.behave.total * 100).toFixed(1);
      console.log(`   📈 Success Rate: ${behaveSuccessRate}%`);
    }
    
    // Overall Results
    const totalPassed = this.results.jest.passed + this.results.behave.passed;
    const totalFailed = this.results.jest.failed + this.results.behave.failed;
    const totalTests = this.results.jest.total + this.results.behave.total;
    
    console.log('\n🎯 Overall Results:');
    console.log(`   ✅ Total Passed: ${totalPassed}`);
    console.log(`   ❌ Total Failed: ${totalFailed}`);
    console.log(`   📊 Total Tests:  ${totalTests}`);
    console.log(`   ⏱️  Total Duration: ${(totalDuration / 1000).toFixed(2)}s`);
    
    if (totalTests > 0) {
      const overallSuccessRate = (totalPassed / totalTests * 100).toFixed(1);
      console.log(`   📈 Overall Success Rate: ${overallSuccessRate}%`);
      
      // Performance metrics
      const testsPerSecond = (totalTests / (totalDuration / 1000)).toFixed(2);
      console.log(`   ⚡ Tests per Second: ${testsPerSecond}`);
    }
    
    // System Information
    console.log('\n💻 System Information:');
    console.log(`   🖥️  Platform: ${os.platform()} ${os.arch()}`);
    console.log(`   🧠 Memory: ${(os.totalmem() / 1024 / 1024 / 1024).toFixed(1)} GB total`);
    console.log(`   🔧 Node.js: ${process.version}`);
    console.log(`   📁 Working Directory: ${process.cwd()}`);
    
    // Test Coverage Summary
    console.log('\n📈 Test Coverage Summary:');
    console.log('   🧪 Unit Tests: Financial calculations, components, performance');
    console.log('   🎭 BDD Tests: User journeys, Swiss scenarios, data management');
    console.log('   🔒 Security Tests: Encryption, privacy controls, data protection');
    console.log('   🏛️  Swiss Tests: Tax calculations, canton comparisons, social insurance');
    console.log('   📊 Integration Tests: Data visualization, cross-component functionality');
    
    // Recommendations
    console.log('\n💡 Recommendations:');
    
    if (totalFailed > 0) {
      console.log('   ⚠️  Some tests failed. Review failed test output above.');
      console.log('   🔧 Consider running individual test files for detailed debugging.');
    }
    
    if (this.results.jest.total === 0) {
      console.log('   📝 No Jest tests were executed. Check test file paths and dependencies.');
    }
    
    if (this.results.behave.total === 0) {
      console.log('   📝 No Behave tests were executed. Install behave and selenium dependencies.');
    }
    
    if (totalTests > 0 && totalFailed === 0) {
      console.log('   🎉 All tests passed! Great job on maintaining code quality.');
      console.log('   🚀 Consider adding more edge case tests for comprehensive coverage.');
    }
    
    console.log('\n' + '=' * 80);
    
    // Exit with appropriate code
    if (totalFailed > 0) {
      console.log('❌ Test suite completed with failures');
      process.exit(1);
    } else {
      console.log('✅ Test suite completed successfully');
      process.exit(0);
    }
  }
}

// Run the comprehensive test suite
if (require.main === module) {
  const runner = new ComprehensiveTestRunner();
  runner.runAllTests().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

module.exports = ComprehensiveTestRunner;
