import { Page, Locator, expect } from '@playwright/test';

/**
 * Page Object Model for Data Visualization Dashboard
 * Provides methods to interact with chart components and controls
 */
export class DataVisualizationPage {
  readonly page: Page;
  
  // Dashboard elements
  readonly dashboardHeader: Locator;
  readonly chartContainer: Locator;
  readonly chartControls: Locator;
  readonly viewSelector: Locator;
  
  // Chart control elements
  readonly timeframeButtons: Locator;
  readonly chartTypeButtons: Locator;
  readonly metricToggleButtons: Locator;
  readonly exportButton: Locator;
  readonly exportMenu: Locator;
  readonly fullscreenButton: Locator;
  
  // Chart elements
  readonly svgCharts: Locator;
  readonly dataPoints: Locator;
  readonly tooltip: Locator;
  readonly chartPaths: Locator;
  
  // Mobile elements
  readonly mobileControls: Locator;
  readonly mobileOptimizedChart: Locator;
  
  // Performance monitor elements
  readonly performanceMonitor: Locator;
  readonly performanceMetrics: Locator;
  readonly performanceAlerts: Locator;
  
  // Data management elements
  readonly generateSampleDataButton: Locator;
  readonly clearDataButton: Locator;
  readonly emptyStateMessage: Locator;
  readonly selectedDataPointInfo: Locator;
  
  // Settings elements
  readonly dashboardSettings: Locator;
  readonly settingsCheckboxes: Locator;

  constructor(page: Page) {
    this.page = page;
    
    // Dashboard elements
    this.dashboardHeader = page.locator('h2:has-text("Data Visualization Dashboard")');
    this.chartContainer = page.locator('.enhanced-chart-container, [data-testid="chart-container"]');
    this.chartControls = page.locator('[data-testid="chart-controls"], .chart-controls');
    this.viewSelector = page.locator('[data-testid="view-selector"]');
    
    // Chart control elements
    this.timeframeButtons = page.locator('button:has-text("1M"), button:has-text("3M"), button:has-text("6M"), button:has-text("1Y"), button:has-text("2Y"), button:has-text("ALL")');
    this.chartTypeButtons = page.locator('button[title*="Chart"], button:has-text("Line"), button:has-text("Area"), button:has-text("Bar")');
    this.metricToggleButtons = page.locator('button:has-text("Net Worth"), button:has-text("Savings Rate"), button:has-text("FIRE Progress")');
    this.exportButton = page.locator('button:has-text("Export")');
    this.exportMenu = page.locator('[data-testid="export-menu"]');
    this.fullscreenButton = page.locator('button[title="Toggle Fullscreen"]');
    
    // Chart elements
    this.svgCharts = page.locator('svg');
    this.dataPoints = page.locator('svg circle.data-point, svg circle[class*="data-point"]');
    this.tooltip = page.locator('.tooltip, [data-testid="tooltip"]');
    this.chartPaths = page.locator('svg path');
    
    // Mobile elements
    this.mobileControls = page.locator('[data-testid="mobile-controls"]');
    this.mobileOptimizedChart = page.locator('[data-testid="mobile-chart"]');
    
    // Performance monitor elements
    this.performanceMonitor = page.locator('[data-testid="performance-monitor"]');
    this.performanceMetrics = page.locator('[data-testid="performance-metrics"]');
    this.performanceAlerts = page.locator('[data-testid="performance-alerts"]');
    
    // Data management elements
    this.generateSampleDataButton = page.locator('button:has-text("Generate Sample Data")');
    this.clearDataButton = page.locator('button:has-text("Clear Data")');
    this.emptyStateMessage = page.locator('text=No historical data available');
    this.selectedDataPointInfo = page.locator('[data-testid="selected-data-point"]');
    
    // Settings elements
    this.dashboardSettings = page.locator('[data-testid="dashboard-settings"]');
    this.settingsCheckboxes = page.locator('input[type="checkbox"]');
  }

  /**
   * Navigate to the data visualization section
   */
  async navigateToVisualization() {
    // Look for visualization tab or section
    const vizTab = this.page.locator('button:has-text("Visualization"), a:has-text("Charts"), [data-testid="viz-tab"]');
    
    if (await vizTab.isVisible()) {
      await vizTab.click();
    } else {
      // If no specific tab, scroll to visualization section
      await this.dashboardHeader.scrollIntoViewIfNeeded();
    }
    
    await this.waitForChartsToLoad();
  }

  /**
   * Wait for charts to load and render
   */
  async waitForChartsToLoad() {
    // Wait for SVG elements to be present
    await expect(this.svgCharts.first()).toBeVisible({ timeout: 10000 });
    
    // Wait for data points to render
    await this.page.waitForFunction(() => {
      const svgs = document.querySelectorAll('svg');
      return svgs.length > 0 && document.querySelectorAll('svg circle, svg path').length > 0;
    }, { timeout: 10000 });
    
    // Additional wait for animations to settle
    await this.page.waitForTimeout(500);
  }

  /**
   * Wait for chart update after interaction
   */
  async waitForChartUpdate() {
    await this.page.waitForTimeout(300); // Wait for debounced updates
    await this.waitForChartsToLoad();
  }

  /**
   * Select a timeframe option
   */
  async selectTimeframe(timeframe: string) {
    const button = this.page.locator(`button:has-text("${timeframe}")`);
    await expect(button).toBeVisible();
    await button.click();
  }

  /**
   * Select a chart type
   */
  async selectChartType(chartType: string) {
    // Map chart types to button selectors
    const typeMap: Record<string, string> = {
      'line': 'Line Chart',
      'area': 'Area Chart', 
      'bar': 'Bar Chart',
      'scatter': 'Scatter Plot',
    };
    
    const buttonText = typeMap[chartType] || chartType;
    const button = this.page.locator(`button:has-text("${buttonText}"), button[title*="${buttonText}"]`);
    
    await expect(button).toBeVisible();
    await button.click();
  }

  /**
   * Toggle a metric on/off
   */
  async toggleMetric(metricName: string) {
    const button = this.page.locator(`button:has-text("${metricName}")`);
    await expect(button).toBeVisible();
    await button.click();
  }

  /**
   * Open the export menu
   */
  async openExportMenu() {
    await expect(this.exportButton).toBeVisible();
    await this.exportButton.click();
    
    // Wait for menu to appear
    await this.page.waitForTimeout(200);
  }

  /**
   * Export chart in specified format
   */
  async exportAs(format: string) {
    const formatMap: Record<string, string> = {
      'png': 'PNG Image',
      'svg': 'SVG Vector',
      'pdf': 'PDF Document',
      'csv': 'CSV Data',
      'json': 'JSON Data',
    };
    
    const menuItem = formatMap[format] || format;
    const exportOption = this.page.locator(`button:has-text("${menuItem}")`);
    
    await expect(exportOption).toBeVisible();
    await exportOption.click();
  }

  /**
   * Verify chart type is correctly rendered
   */
  async verifyChartType(chartType: string) {
    switch (chartType) {
      case 'line':
        await expect(this.page.locator('svg path[stroke][fill="none"]')).toBeVisible();
        break;
      case 'area':
        await expect(this.page.locator('svg path[fill]:not([fill="none"])')).toBeVisible();
        break;
      case 'bar':
        await expect(this.page.locator('svg rect')).toBeVisible();
        break;
      case 'scatter':
        await expect(this.dataPoints).toBeVisible();
        break;
    }
  }

  /**
   * Generate sample data for testing
   */
  async generateSampleData() {
    if (await this.generateSampleDataButton.isVisible()) {
      await this.generateSampleDataButton.click();
      await this.waitForChartsToLoad();
    }
  }

  /**
   * Clear all chart data
   */
  async clearAllData() {
    if (await this.clearDataButton.isVisible()) {
      await this.clearDataButton.click();
    } else {
      // Alternative: clear via settings or reset
      const resetButton = this.page.locator('button:has-text("Reset"), button:has-text("Clear")');
      if (await resetButton.isVisible()) {
        await resetButton.click();
      }
    }
  }

  /**
   * Switch dashboard view (Enhanced/Mobile/Performance)
   */
  async switchView(viewName: string) {
    const viewButton = this.page.locator(`button:has-text("${viewName}")`);
    await expect(viewButton).toBeVisible();
    await viewButton.click();
    await this.page.waitForTimeout(500);
  }

  /**
   * Enable/disable performance monitoring
   */
  async togglePerformanceMonitor(enable: boolean = true) {
    const checkbox = this.page.locator('input[type="checkbox"] + span:has-text("Performance Monitor")').locator('..');
    const input = checkbox.locator('input');
    
    const isChecked = await input.isChecked();
    if (isChecked !== enable) {
      await checkbox.click();
    }
  }

  /**
   * Get performance metrics
   */
  async getPerformanceMetrics() {
    if (await this.performanceMetrics.isVisible()) {
      const metricsText = await this.performanceMetrics.textContent();
      return this.parsePerformanceMetrics(metricsText || '');
    }
    return null;
  }

  /**
   * Check for performance alerts
   */
  async hasPerformanceAlerts() {
    return await this.performanceAlerts.isVisible();
  }

  /**
   * Test touch interactions (for mobile testing)
   */
  async testTouchInteractions() {
    if (await this.mobileOptimizedChart.isVisible()) {
      const chart = this.mobileOptimizedChart;
      
      // Simulate touch events
      await chart.tap();
      await this.page.waitForTimeout(200);
      
      // Simulate swipe
      const box = await chart.boundingBox();
      if (box) {
        await this.page.touchscreen.tap(box.x + box.width * 0.3, box.y + box.height * 0.5);
        await this.page.touchscreen.tap(box.x + box.width * 0.7, box.y + box.height * 0.5);
      }
    }
  }

  /**
   * Verify chart accessibility
   */
  async verifyAccessibility() {
    // Check for ARIA labels
    const svgWithLabel = this.page.locator('svg[aria-label], svg[role="img"]');
    if (await svgWithLabel.count() > 0) {
      await expect(svgWithLabel.first()).toBeVisible();
    }
    
    // Check for keyboard navigation
    await this.svgCharts.first().focus();
    await this.page.keyboard.press('Tab');
    
    // Verify focus indicators
    const focusedElement = this.page.locator(':focus');
    await expect(focusedElement).toBeVisible();
  }

  /**
   * Take screenshot of charts for visual testing
   */
  async takeChartScreenshot(name: string) {
    await this.chartContainer.screenshot({
      path: `test-results/screenshots/chart-${name}-${Date.now()}.png`,
    });
  }

  /**
   * Verify chart data integrity
   */
  async verifyChartData() {
    const dataPointCount = await this.dataPoints.count();
    expect(dataPointCount).toBeGreaterThan(0);
    
    // Verify data points have valid coordinates
    const firstPoint = this.dataPoints.first();
    const cx = await firstPoint.getAttribute('cx');
    const cy = await firstPoint.getAttribute('cy');
    
    expect(parseFloat(cx || '0')).toBeGreaterThan(0);
    expect(parseFloat(cy || '0')).toBeGreaterThan(0);
  }

  /**
   * Parse performance metrics from text
   */
  private parsePerformanceMetrics(text: string) {
    const metrics: Record<string, number> = {};
    
    // Extract numeric values from performance text
    const renderTimeMatch = text.match(/Render Time:\s*(\d+)\s*ms/);
    if (renderTimeMatch) {
      metrics.renderTime = parseInt(renderTimeMatch[1]);
    }
    
    const memoryMatch = text.match(/Memory Usage:\s*(\d+)\s*MB/);
    if (memoryMatch) {
      metrics.memoryUsage = parseInt(memoryMatch[1]);
    }
    
    const fpsMatch = text.match(/Frame Rate:\s*(\d+)\s*fps/);
    if (fpsMatch) {
      metrics.frameRate = parseInt(fpsMatch[1]);
    }
    
    return metrics;
  }

  /**
   * Wait for animations to complete
   */
  async waitForAnimations() {
    // Wait for CSS transitions and D3 animations
    await this.page.waitForFunction(() => {
      const elements = document.querySelectorAll('*');
      for (const el of elements) {
        const style = window.getComputedStyle(el);
        if (style.transitionDuration !== '0s' || style.animationDuration !== '0s') {
          return false;
        }
      }
      return true;
    }, { timeout: 5000 });
  }
}
