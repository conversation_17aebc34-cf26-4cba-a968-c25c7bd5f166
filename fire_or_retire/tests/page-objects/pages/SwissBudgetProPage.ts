/**
 * Page Object Model for Swiss Budget Pro Main Application
 * Provides high-level interface for interacting with the application
 */

import { Page, Locator, expect } from '@playwright/test';

export class SwissBudgetProPage {
  readonly page: Page;
  
  // Header elements
  readonly appTitle: Locator;
  readonly languageToggle: Locator;
  readonly themeToggle: Locator;
  readonly exportButton: Locator;
  
  // Navigation elements
  readonly inputTab: Locator;
  readonly analysisTab: Locator;
  readonly visualizationTab: Locator;
  readonly reportsTab: Locator;
  
  // Input form elements
  readonly monthlyIncomeInput: Locator;
  readonly monthlyExpensesInput: Locator;
  readonly currentSavingsInput: Locator;
  readonly cantonSelect: Locator;
  readonly ageInput: Locator;
  readonly retirementAgeInput: Locator;
  
  // Calculation results
  readonly fireYearsResult: Locator;
  readonly fireNumberResult: Locator;
  readonly savingsRateResult: Locator;
  readonly monthlyTargetResult: Locator;
  
  // Swiss-specific elements
  readonly pillar3aSection: Locator;
  readonly taxOptimizationSection: Locator;
  readonly healthcareSection: Locator;
  
  // Action buttons
  readonly calculateButton: Locator;
  readonly resetButton: Locator;
  readonly saveButton: Locator;
  readonly loadButton: Locator;
  
  // Status and feedback
  readonly loadingIndicator: Locator;
  readonly errorMessage: Locator;
  readonly successMessage: Locator;

  constructor(page: Page) {
    this.page = page;
    
    // Header elements
    this.appTitle = page.locator('[data-testid="app-title"]').or(page.locator('h1:has-text("Swiss Budget Pro")'));
    this.languageToggle = page.locator('[data-testid="language-toggle"]').or(page.locator('button:has-text("DE")'));
    this.themeToggle = page.locator('[data-testid="theme-toggle"]').or(page.locator('button[aria-label*="theme"]'));
    this.exportButton = page.locator('[data-testid="export-button"]').or(page.locator('button:has-text("Export")'));
    
    // Navigation tabs
    this.inputTab = page.locator('[data-testid="input-tab"]').or(page.locator('button:has-text("Input")'));
    this.analysisTab = page.locator('[data-testid="analysis-tab"]').or(page.locator('button:has-text("Analysis")'));
    this.visualizationTab = page.locator('[data-testid="visualization-tab"]').or(page.locator('button:has-text("Charts")'));
    this.reportsTab = page.locator('[data-testid="reports-tab"]').or(page.locator('button:has-text("Reports")'));
    
    // Input form elements
    this.monthlyIncomeInput = page.locator('[data-testid="monthly-income"]').or(page.locator('input[name="monthlyIncome"]'));
    this.monthlyExpensesInput = page.locator('[data-testid="monthly-expenses"]').or(page.locator('input[name="monthlyExpenses"]'));
    this.currentSavingsInput = page.locator('[data-testid="current-savings"]').or(page.locator('input[name="currentSavings"]'));
    this.cantonSelect = page.locator('[data-testid="canton-select"]').or(page.locator('select[name="canton"]'));
    this.ageInput = page.locator('[data-testid="age"]').or(page.locator('input[name="age"]'));
    this.retirementAgeInput = page.locator('[data-testid="retirement-age"]').or(page.locator('input[name="retirementAge"]'));
    
    // Results elements
    this.fireYearsResult = page.locator('[data-testid="fire-years"]').or(page.locator(':text("Years to FIRE")').locator('..').locator('.result-value'));
    this.fireNumberResult = page.locator('[data-testid="fire-number"]').or(page.locator(':text("FIRE Number")').locator('..').locator('.result-value'));
    this.savingsRateResult = page.locator('[data-testid="savings-rate"]').or(page.locator(':text("Savings Rate")').locator('..').locator('.result-value'));
    this.monthlyTargetResult = page.locator('[data-testid="monthly-target"]').or(page.locator(':text("Monthly Target")').locator('..').locator('.result-value'));
    
    // Swiss-specific sections
    this.pillar3aSection = page.locator('[data-testid="pillar3a-section"]').or(page.locator(':text("Pillar 3a")').locator('..'));
    this.taxOptimizationSection = page.locator('[data-testid="tax-optimization"]').or(page.locator(':text("Tax Optimization")').locator('..'));
    this.healthcareSection = page.locator('[data-testid="healthcare-section"]').or(page.locator(':text("Healthcare")').locator('..'));
    
    // Action buttons
    this.calculateButton = page.locator('[data-testid="calculate-button"]').or(page.locator('button:has-text("Calculate")'));
    this.resetButton = page.locator('[data-testid="reset-button"]').or(page.locator('button:has-text("Reset")'));
    this.saveButton = page.locator('[data-testid="save-button"]').or(page.locator('button:has-text("Save")'));
    this.loadButton = page.locator('[data-testid="load-button"]').or(page.locator('button:has-text("Load")'));
    
    // Status indicators
    this.loadingIndicator = page.locator('[data-testid="loading"]').or(page.locator('.loading, .spinner'));
    this.errorMessage = page.locator('[data-testid="error-message"]').or(page.locator('.error-message, .alert-error'));
    this.successMessage = page.locator('[data-testid="success-message"]').or(page.locator('.success-message, .alert-success'));
  }

  /**
   * Navigate to the Swiss Budget Pro application
   */
  async goto() {
    await this.page.goto('/');
    await this.waitForPageLoad();
  }

  /**
   * Wait for the page to fully load
   */
  async waitForPageLoad() {
    // Wait for the app title to be visible
    await expect(this.appTitle).toBeVisible({ timeout: 30000 });
    
    // Wait for network to be idle (no requests for 500ms)
    await this.page.waitForLoadState('networkidle');
    
    // Wait for any loading indicators to disappear
    await this.loadingIndicator.waitFor({ state: 'hidden', timeout: 10000 }).catch(() => {
      // Loading indicator might not exist, which is fine
    });
  }

  /**
   * Fill in basic financial information
   */
  async fillBasicFinancialInfo(data: {
    monthlyIncome: number;
    monthlyExpenses: number;
    currentSavings: number;
    canton?: string;
    age?: number;
    retirementAge?: number;
  }) {
    // Fill monthly income
    await this.monthlyIncomeInput.fill(data.monthlyIncome.toString());
    
    // Fill monthly expenses
    await this.monthlyExpensesInput.fill(data.monthlyExpenses.toString());
    
    // Fill current savings
    await this.currentSavingsInput.fill(data.currentSavings.toString());
    
    // Select canton if provided
    if (data.canton) {
      await this.cantonSelect.selectOption(data.canton);
    }
    
    // Fill age if provided
    if (data.age) {
      await this.ageInput.fill(data.age.toString());
    }
    
    // Fill retirement age if provided
    if (data.retirementAge) {
      await this.retirementAgeInput.fill(data.retirementAge.toString());
    }
  }

  /**
   * Trigger FIRE calculation
   */
  async calculateFIRE() {
    await this.calculateButton.click();
    
    // Wait for calculation to complete
    await this.waitForCalculationComplete();
  }

  /**
   * Wait for calculation to complete
   */
  async waitForCalculationComplete() {
    // Wait for loading indicator to appear and then disappear
    await this.loadingIndicator.waitFor({ state: 'visible', timeout: 5000 }).catch(() => {
      // Loading indicator might not appear if calculation is fast
    });
    
    await this.loadingIndicator.waitFor({ state: 'hidden', timeout: 30000 }).catch(() => {
      // Loading indicator might not exist
    });
    
    // Wait for results to be visible
    await expect(this.fireYearsResult).toBeVisible({ timeout: 10000 });
  }

  /**
   * Get FIRE calculation results
   */
  async getFIREResults() {
    await expect(this.fireYearsResult).toBeVisible();
    
    const fireYears = await this.fireYearsResult.textContent();
    const fireNumber = await this.fireNumberResult.textContent();
    const savingsRate = await this.savingsRateResult.textContent();
    const monthlyTarget = await this.monthlyTargetResult.textContent();
    
    return {
      fireYears: this.parseNumericValue(fireYears),
      fireNumber: this.parseNumericValue(fireNumber),
      savingsRate: this.parseNumericValue(savingsRate),
      monthlyTarget: this.parseNumericValue(monthlyTarget),
    };
  }

  /**
   * Navigate to a specific tab
   */
  async navigateToTab(tab: 'input' | 'analysis' | 'visualization' | 'reports') {
    const tabLocator = {
      input: this.inputTab,
      analysis: this.analysisTab,
      visualization: this.visualizationTab,
      reports: this.reportsTab,
    }[tab];
    
    await tabLocator.click();
    await this.page.waitForTimeout(500); // Wait for tab transition
  }

  /**
   * Switch language
   */
  async switchLanguage(language: 'de' | 'fr' | 'it' | 'en') {
    await this.languageToggle.click();
    
    // Wait for language menu and select language
    const languageOption = this.page.locator(`[data-testid="lang-${language}"]`).or(
      this.page.locator(`button:has-text("${language.toUpperCase()}")`),
    );
    
    await languageOption.click();
    
    // Wait for language change to take effect
    await this.page.waitForTimeout(1000);
  }

  /**
   * Toggle theme (dark/light mode)
   */
  async toggleTheme() {
    await this.themeToggle.click();
    await this.page.waitForTimeout(500); // Wait for theme transition
  }

  /**
   * Check if error message is displayed
   */
  async hasErrorMessage(): Promise<boolean> {
    return await this.errorMessage.isVisible();
  }

  /**
   * Get error message text
   */
  async getErrorMessage(): Promise<string | null> {
    if (await this.hasErrorMessage()) {
      return await this.errorMessage.textContent();
    }
    return null;
  }

  /**
   * Check if success message is displayed
   */
  async hasSuccessMessage(): Promise<boolean> {
    return await this.successMessage.isVisible();
  }

  /**
   * Reset all form data
   */
  async resetForm() {
    await this.resetButton.click();
    
    // Wait for form to be reset
    await this.page.waitForTimeout(500);
  }

  /**
   * Save current data
   */
  async saveData() {
    await this.saveButton.click();
    
    // Wait for save operation to complete
    await this.waitForSuccessMessage();
  }

  /**
   * Load saved data
   */
  async loadData() {
    await this.loadButton.click();
    
    // Wait for data to be loaded
    await this.page.waitForTimeout(1000);
  }

  /**
   * Wait for success message to appear
   */
  async waitForSuccessMessage() {
    await expect(this.successMessage).toBeVisible({ timeout: 10000 });
  }

  /**
   * Export data/report
   */
  async exportData() {
    await this.exportButton.click();
    
    // Wait for export to complete
    await this.page.waitForTimeout(2000);
  }

  /**
   * Parse numeric value from text content
   */
  private parseNumericValue(text: string | null): number | null {
    if (!text) return null;
    
    // Remove currency symbols, commas, and other non-numeric characters
    const numericText = text.replace(/[^\d.-]/g, '');
    const value = parseFloat(numericText);
    
    return isNaN(value) ? null : value;
  }

  /**
   * Take a screenshot for debugging
   */
  async takeScreenshot(name: string) {
    await this.page.screenshot({ 
      path: `test-results/screenshots/${name}-${Date.now()}.png`,
      fullPage: true, 
    });
  }

  /**
   * Verify page is loaded and functional
   */
  async verifyPageLoaded() {
    await expect(this.appTitle).toBeVisible();
    await expect(this.monthlyIncomeInput).toBeVisible();
    await expect(this.calculateButton).toBeVisible();
  }
}
