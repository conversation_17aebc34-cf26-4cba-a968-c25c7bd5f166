# Swiss Budget Pro - Production Dockerfile
# Multi-stage build for optimized production image

# Build stage
FROM node:20-alpine AS builder

# Set working directory
WORKDIR /app

# Copy package files first for better caching
COPY package*.json ./

# Install all dependencies (including dev dependencies for build)
RUN npm ci --silent

# Copy source code
COPY . .

# Build the application with type checking to identify issues
RUN npm run build || npm run build:skip-types

# Production stage
FROM node:20-alpine AS production

# Install wget for health checks
RUN apk add --no-cache wget

# Create app directory
WORKDIR /app

# Copy built application from builder stage
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/package*.json ./

# Install vite globally for preview server
RUN npm install -g vite serve

# Create a simple startup script
RUN echo '#!/bin/sh' > /app/start.sh && \
    echo 'echo "🚀 Starting Swiss Budget Pro (Fire or Retire Calculator)"' >> /app/start.sh && \
    echo 'echo "📊 Financial Planning Application"' >> /app/start.sh && \
    echo 'echo "🌐 Available at: http://fire.localhost"' >> /app/start.sh && \
    echo 'echo "⚡ Starting preview server..."' >> /app/start.sh && \
    echo 'cd /app && serve -s dist -l 4173' >> /app/start.sh && \
    chmod +x /app/start.sh

# Expose port 4173 (Vite preview server)
EXPOSE 4173

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:4173/ || exit 1

# Set labels for better container management
LABEL maintainer="Swiss Budget Pro Team"
LABEL description="Swiss Budget Pro - Advanced Financial Planning Application"
LABEL version="1.0.0"

# Start the application
CMD ["/app/start.sh"]
